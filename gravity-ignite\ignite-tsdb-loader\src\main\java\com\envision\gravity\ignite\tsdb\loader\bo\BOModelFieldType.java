package com.envision.gravity.ignite.tsdb.loader.bo;

import java.util.HashMap;
import java.util.Map;

/** <AUTHOR> 2024/3/26 */
public enum BOModelFieldType {
    ATTRIBUTE("ATTRIBUTE"),
    TELEMETRY("TELEMETRY"),
    METRIC("METRIC"),
    SERVICE("SERVICE"),
    UNKNOWN("UNKNOWN");

    private static final Map<String, BOModelFieldType> VALUE_MAP = new HashMap<>();

    static {
        for (BOModelFieldType type : BOModelFieldType.values()) {
            VALUE_MAP.put(type.value, type);
        }
    }

    public static BOModelFieldType of(String value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private final String value;

    BOModelFieldType(String value) {
        this.value = value;
    }
}
