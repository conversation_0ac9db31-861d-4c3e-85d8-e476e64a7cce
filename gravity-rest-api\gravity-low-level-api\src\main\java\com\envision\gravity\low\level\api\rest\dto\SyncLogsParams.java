package com.envision.gravity.low.level.api.rest.dto;

import com.envision.gravity.common.response.SyncResponseCodeEnum;
import com.envision.gravity.common.vo.sync.BaseSyncReq;
import com.envision.gravity.low.level.api.rest.enums.SyncOPEnum;
import com.envision.gravity.low.level.api.rest.enums.SyncSourceEnum;
import com.envision.gravity.low.level.api.rest.enums.SyncStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncLogsParams {
    private String syncId;
    private String orgId;
    private Set<String> ids;
    private Map<String, ? extends BaseSyncReq> idToEntityMap;
    private SyncSourceEnum syncSource;
    private SyncOPEnum operation;
    private SyncStatusEnum status;
    private SyncResponseCodeEnum responseCode;
    private String responseDetail;
}
