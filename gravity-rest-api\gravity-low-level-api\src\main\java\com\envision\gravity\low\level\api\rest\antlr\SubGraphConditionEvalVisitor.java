package com.envision.gravity.low.level.api.rest.antlr;

import com.envision.gravity.low.level.api.rest.enums.*;
import com.envision.gravity.low.level.api.rest.exception.UnsupportSearchException;
import com.envision.gravity.low.level.api.rest.util.ExpressionUtils;
import com.envision.gravity.low.level.api.rest.util.SpecialStringProcessor;

import org.springframework.stereotype.Component;

import java.util.*;

/** @Author: qi.jiang2 @Date: 2024/04/17 10:44 @Description: */
@Component
public class SubGraphConditionEvalVisitor extends ConditionBaseVisitor<String> {

    private final Map<String, SubGraphSearchSupportFieldEnum> supportFieldMap = new HashMap<>(8);

    private List<String> localeList = new ArrayList<>();

    private int maxStep = 10;

    private boolean baseConditionWithVid = false;

    private boolean hasOrder = false;

    private boolean fromVidOnlyOne = true;

    private List<String> fromVidConditions = new ArrayList<>();

    private List<String> toVidConditions = new ArrayList<>();

    private List<String> vids = new ArrayList<>();

    public void setBaseConditionWithVid(boolean baseConditionWithVid) {
        this.baseConditionWithVid = baseConditionWithVid;
    }

    public boolean isHasOrder() {
        return hasOrder;
    }

    public boolean isFromVidOnlyOne() {
        return fromVidOnlyOne;
    }

    public List<String> getVids() {
        return vids;
    }

    public int getMaxStep() {
        return maxStep;
    }

    public List<String> getFromVidConditions() {
        return fromVidConditions;
    }

    public List<String> getToVidConditions() {
        return toVidConditions;
    }

    public void setLocaleList(List<String> localeList) {
        this.localeList = localeList;
    }

    public SubGraphConditionEvalVisitor() {
        for (SubGraphSearchSupportFieldEnum supportFieldEnum :
                SubGraphSearchSupportFieldEnum.values()) {
            supportFieldMap.put(supportFieldEnum.getSupportField(), supportFieldEnum);
        }
    }

    @Override
    public String visitParse(ConditionParser.ParseContext ctx) {
        return visit(ctx.getChild(0));
    }

    @Override
    public String visitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        String replaceRight = ExpressionUtils.likeEscape(right);
        return getLikeExpression(fieldStr, op, replaceRight, locale, true);
    }

    @Override
    public String visitComparatorExpr(ConditionParser.ComparatorExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format("SupportField: %s not support comparator: %s", fieldStr, op));
            }
            fromVidConditions.add(
                    SubGraphSearchSupportFieldEnum.FROM_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right);
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format("SupportField: %s not support comparator: %s", fieldStr, op));
            }
            toVidConditions.add(
                    SubGraphSearchSupportFieldEnum.TO_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right);
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.VID.getSupportField())) {
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format("SupportField: %s not support comparator: %s", fieldStr, op));
            }
            String vidCondition =
                    "("
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right
                            + " or "
                            + SubGraphSearchSupportFieldEnum.TO_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right
                            + " or start_vid "
                            + op
                            + " "
                            + right
                            + ")";
            vids.add(right);
            if (baseConditionWithVid) {
                return vidCondition;
            }
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.MAX_STEP.getSupportField())) {
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format("SupportField: %s not support comparator: %s", fieldStr, op));
            }
            maxStep = Integer.parseInt(right);
            return "";
        }
        return getComparatorExpression(fieldStr, op, right, "", false);
    }

    @Override
    public String visitIsExistsExpr(ConditionParser.IsExistsExprContext ctx) {
        StringBuilder res = new StringBuilder();
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        right = right.substring(1, right.length() - 1);
        String[] fieldStrList = right.split(",");
        for (int i = 0; i < fieldStrList.length; i++) {
            String fieldStr = fieldStrList[i];
            if (!fieldStr.contains(Constants.DOT)) {
                throw new UnsupportSearchException(
                        String.format(
                                "The exists and not exist can only be used with field.key! Field: %s",
                                fieldStr));
            }
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            if (!supportField.equals(SubGraphSearchSupportFieldEnum.TAGS.getSupportField())) {
                throw new UnsupportSearchException(
                        String.format(
                                "Search subGraph exists and not exists operation only support field: %s, currentField: %s",
                                SubGraphSearchSupportFieldEnum.TAGS.getSupportField(),
                                supportField));
            }
            String sqlField = supportFieldEnum.getSqlField();
            if (op.equals("exists")) {
                res.append("(")
                        .append(supportFieldEnum.getTable())
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("')");
            } else {
                res.append("((not ")
                        .append(supportFieldEnum.getTable())
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("') or ")
                        .append(supportFieldEnum.getTable())
                        .append(sqlField)
                        .append(" is null)");
            }

            if (i < fieldStrList.length - 1) {
                res.append(" and ");
            }
        }
        return res.toString();
    }

    @Override
    public String visitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        return getComparatorExpression(fieldStr, op, right, locale, true);
    }

    @Override
    public String visitInExpr(ConditionParser.InExprContext ctx) {
        String left = "";
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visit(ctx.left);
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            fromVidConditions.add(
                    SubGraphSearchSupportFieldEnum.FROM_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right);
            if (right.contains(",")) {
                fromVidOnlyOne = false;
            }
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            toVidConditions.add(
                    SubGraphSearchSupportFieldEnum.TO_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right);
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.VID.getSupportField())) {
            String vidCondition =
                    "("
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right
                            + " or "
                            + SubGraphSearchSupportFieldEnum.TO_VID.getTable()
                            + SubGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                            + " "
                            + op
                            + " "
                            + right
                            + " or start_vid "
                            + op
                            + " "
                            + right
                            + ")";
            vids.addAll(Arrays.asList(right.substring(1, right.length() - 1).split(",")));
            if (baseConditionWithVid) {
                return vidCondition;
            }
            return "";
        }
        if (fieldStr.equals(SubGraphSearchSupportFieldEnum.MAX_STEP.getSupportField())
                || fieldStr.equals(
                        SubGraphSearchSupportFieldEnum.GRAPH_DISPLAY_NAME.getSupportField())) {
            throw new UnsupportSearchException(
                    String.format("SupportField: %s not support comparator: %s", fieldStr, op));
        }
        op += " ";
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
            SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", supportField));
            }
            if (!PgDataTypeEnum.JSONB.getName().equals(supportFieldEnum.getDataType())) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s type not jsonb, can not search by field.key",
                                supportField));
            }
            left =
                    supportFieldEnum.getTable()
                            + supportFieldEnum.getSqlField()
                            + " ->> '"
                            + key
                            + "' ";
        } else {
            SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            left = supportFieldEnum.getTable() + supportFieldEnum.getSqlField() + " ";
            op = op + " ";
        }

        return left + op + right;
    }

    @Override
    public String visitLikeExpr(ConditionParser.LikeExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String replaceRight = ExpressionUtils.likeEscape(right);
        return getLikeExpression(fieldStr, op, replaceRight, "", false);
    }

    private String getLikeExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left;
        String key = "";
        String supportField;
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            supportField = fieldStr.substring(0, dotIndex);
            key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
        } else {
            supportField = fieldStr;
        }
        SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
        if (supportFieldEnum == null) {
            throw new UnsupportSearchException(
                    String.format("Unsupported search by field: %s", supportField));
        }
        if (!supportField.equals(
                        SubGraphSearchSupportFieldEnum.GRAPH_DISPLAY_NAME.getSupportField())
                && !supportField.equals(SubGraphSearchSupportFieldEnum.TAGS.getSupportField())) {
            throw new UnsupportSearchException(
                    String.format(
                            "Search subGraph like operation only support fields: %s, currentField: %s",
                            Arrays.asList(
                                    SubGraphSearchSupportFieldEnum.TAGS.getSupportField(),
                                    SubGraphSearchSupportFieldEnum.GRAPH_DISPLAY_NAME
                                            .getSupportField()),
                            supportField));
        }
        String jsonbSqlField;
        if (key.isEmpty()) {
            left =
                    new StringBuilder(
                            supportFieldEnum.getTable() + supportFieldEnum.getSqlField() + " ");
            jsonbSqlField = supportFieldEnum.getTable() + supportFieldEnum.getSqlField() + " ";
        } else {
            left =
                    new StringBuilder(
                            supportFieldEnum.getTable()
                                    + supportFieldEnum.getSqlField()
                                    + "->>'"
                                    + key
                                    + "' ");
            jsonbSqlField =
                    supportFieldEnum.getTable()
                            + supportFieldEnum.getSqlField()
                            + "->'"
                            + key
                            + "' ";
        }
        if (isI18n) {
            return getI18nWithDefault(jsonbSqlField, locale, op, right);
        }

        return left + op + " " + right;
    }

    private String getI18nWithDefault(
            String jsonbSqlField, String locale, String op, String right) {
        StringBuilder left = new StringBuilder();
        if (locale.isEmpty() || "*".equals(locale)) {
            left.append("(");
            for (int i = 0; i < localeList.size(); i++) {
                left.append(jsonbSqlField)
                        .append("->> '")
                        .append(localeList.get(i))
                        .append("' ")
                        .append(op)
                        .append(" ")
                        .append(right);
                if (i < localeList.size() - 1) {
                    left.append(" or ");
                }
            }
            left.append(" or (");
            for (int i = 0; i < localeList.size(); i++) {
                left.append(jsonbSqlField)
                        .append("->> '")
                        .append(localeList.get(i))
                        .append("' isnull and ");
            }
            left.append(jsonbSqlField)
                    .append("->> '")
                    .append(Constants.DEFAULT_LANGUAGE)
                    .append("' ")
                    .append(op)
                    .append(" ")
                    .append(right)
                    .append("))");
        } else {
            if (!localeList.contains(locale) && !Constants.DEFAULT_LANGUAGE.equals(locale)) {
                locale = Constants.DEFAULT_LANGUAGE;
            }
            left =
                    new StringBuilder(
                            "("
                                    + jsonbSqlField
                                    + "->> '"
                                    + locale
                                    + "' "
                                    + op
                                    + " "
                                    + right
                                    + " or ("
                                    + jsonbSqlField
                                    + "->> '"
                                    + Constants.DEFAULT_LANGUAGE
                                    + "' "
                                    + op
                                    + " "
                                    + right
                                    + " and "
                                    + jsonbSqlField
                                    + "->> '"
                                    + locale
                                    + "' isnull))");
        }
        return left.toString();
    }

    private String getComparatorExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left;
        String key = "";
        String supportField;
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            supportField = fieldStr.substring(0, dotIndex);
            key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
            SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", supportField));
            }
            if (!PgDataTypeEnum.JSONB.getName().equals(supportFieldEnum.getDataType())) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s type not jsonb, can not search by field.key",
                                supportField));
            }
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)
                    && !CalcOperatorEnum.NEQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s not support comparator: %s", supportField, op));
            }
            boolean isNEQ = CalcOperatorEnum.NEQ.getSymbol().equals(op);
            String sqlField = supportFieldEnum.getSqlField();
            left = new StringBuilder(supportFieldEnum.getTable() + sqlField + " ");
            op = "@> ";
            if (right.startsWith(Constants.APOSTROPHE) && right.endsWith(Constants.APOSTROPHE)) {
                right = right.substring(1, right.length() - 1);
                right = ExpressionUtils.jsonCompareEscape(right);
                // right = "'{\"" + key + "\":\"" + right + "\"}'";
                right = SpecialStringProcessor.processJsonValueWithDollarChar(key, right);
            } else {
                right = "'{\"" + key + "\":" + right + "}'";
            }
            if (isNEQ) {
                return "((not " + left + op + right + ") or " + left + " is null)";
            }
        } else {
            supportField = fieldStr;
            if (SubGraphSearchSupportFieldEnum.ORDER
                    .getSupportField()
                    .equalsIgnoreCase(supportField)) {
                hasOrder = true;
            }
            SubGraphSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            left =
                    new StringBuilder(
                            supportFieldEnum.getTable() + supportFieldEnum.getSqlField() + " ");
            op = op + " ";
        }

        String jsonbSqlField;
        if (key.isEmpty()) {
            jsonbSqlField =
                    supportFieldMap.get(supportField).getTable()
                            + supportFieldMap.get(supportField).getSqlField()
                            + " ";
        } else {
            jsonbSqlField =
                    supportFieldMap.get(supportField).getTable()
                            + supportFieldMap.get(supportField).getSqlField()
                            + "->'"
                            + key
                            + "' ";
        }

        if (isI18n) {
            return getI18nWithDefault(jsonbSqlField, locale, op, right);
        }
        return left + op + right;
    }

    @Override
    public String visitBinaryExpr(ConditionParser.BinaryExprContext ctx) {
        String left = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (left.isEmpty()) {
            return right;
        }
        if (right.isEmpty()) {
            return left;
        }
        return left + " " + op + " " + right;
    }

    @Override
    public String visitParenExpr(ConditionParser.ParenExprContext ctx) {
        String left = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        return left + op + right;
    }

    @Override
    public String visitField(ConditionParser.FieldContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitFields(ConditionParser.FieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenFields(ConditionParser.ParenFieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenValues(ConditionParser.ParenValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValues(ConditionParser.ValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValue(ConditionParser.ValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitStringValue(ConditionParser.StringValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLike(ConditionParser.LikeContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitComparator(ConditionParser.ComparatorContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitBooleanValue(ConditionParser.BooleanValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitIsExists(ConditionParser.IsExistsContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitBinary(ConditionParser.BinaryContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitTimestamp(ConditionParser.TimestampContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLeftParen(ConditionParser.LeftParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitRightParen(ConditionParser.RightParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitI18n(ConditionParser.I18nContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitIsIn(ConditionParser.IsInContext ctx) {
        return ctx.getText();
    }
}
