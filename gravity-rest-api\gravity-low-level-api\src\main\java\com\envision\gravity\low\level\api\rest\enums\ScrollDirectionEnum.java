package com.envision.gravity.low.level.api.rest.enums;

/** @Author: qi.jiang2 @Date: 2024/03/19 16:32 @Description: */
public enum ScrollDirectionEnum {

    // FORWARD
    FORWARD("forward", ">"),

    // BACKWARD
    BACKWARD("backward", "<");

    private final String name;

    private final String symbol;

    ScrollDirectionEnum(String name, String symbol) {

        this.name = name;
        this.symbol = symbol;
    }

    public String getName() {
        return name;
    }

    public String getSymbol() {
        return symbol;
    }
}
