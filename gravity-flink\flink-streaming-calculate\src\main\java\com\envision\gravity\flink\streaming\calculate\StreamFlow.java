package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.common.utils.FlinkCommonUtils;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcRouter;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcWindowCollector;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.flink.offset.DBOffsetsInitializer;
import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.stream.*;
import com.envision.gravity.flink.streaming.calculate.stream.serde.*;

import java.time.Duration;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.CountTrigger;
import org.apache.flink.streaming.api.windowing.triggers.ProcessingTimeoutTrigger;
import org.apache.flink.streaming.api.windowing.triggers.PurgingTrigger;

public class StreamFlow {

    public static void main(String[] args) {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // env.getConfig().enableObjectReuse();
        // env.getConfig().disableGenericTypes(); // 注释掉以支持泛型类型
        FlinkCommonUtils.tryRunAsLocalModel(args, env);
        // TODO test
        env.setParallelism(2);

        // CDC 流独立处理，不连接到主流
        DataStream<ConvertedCdcRecord> cdcStream =
                env.addSource(initPgCdcSource())
                        .name("pgcdc")
                        .setParallelism(1)
                        .process(new CalcCdcRouter())
                        .name("cdc-router")
                        .broadcast()
                        .keyBy(record -> record.getTable() + "_" + record.getPrimaryKey())
                        .countWindow(CalcLionConfig.getCalcStreamPgCdcWindowBatchSize())
                        .trigger(
                                PurgingTrigger.of(
                                        ProcessingTimeoutTrigger.of(
                                                CountTrigger.of(
                                                        CalcLionConfig
                                                                .getCalcStreamPgCdcWindowBatchSize()),
                                                Duration.ofSeconds(
                                                        CalcLionConfig
                                                                .getCalcStreamPgCdcWindowTimeoutSeconds()))))
                        .process(new CalcCdcWindowCollector());

        // CDC 流独立处理元数据更新
        cdcStream.process(new StreamCalcCdcProcessor()).name("cdc processor");

        int kafkaBatchSize = CalcLionConfig.getCalcStreamKafkaSourceBatchSize();
        int kafkaTimeoutSeconds = CalcLionConfig.getCalcStreamKafkaSourceBatchTimeoutSeconds();

        // Kafka Source Configuration
        KafkaSource<Tuple2<String, OffsetInfo>> source =
                KafkaSource.<Tuple2<String, OffsetInfo>>builder()
                        .setBootstrapServers(CalcLionConfig.getCalcCommonKafkaServers())
                        .setGroupId(CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup())
                        .setStartingOffsets(
                                new DBOffsetsInitializer(
                                        CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup()))
                        .setDeserializer(new KafkaRecordDeserializer())
                        .setProperty("enable.auto.commit", "true")
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setTopicPattern(
                                Pattern.compile(
                                        CalcLionConfig.getCalcStreamKafkaSourceTopicPattern()))
                        .build();

        int parallelism = env.getParallelism();
        int[] keySelectorArray = KeySelectUtil.createRebalanceKeys(parallelism);
        System.out.println("current parallelism: " + parallelism);

        SingleOutputStreamOperator<Tuple2<LegacyMsg, OffsetInfo>> kafkaStream =
                env.fromSource(source, WatermarkStrategy.noWatermarks(), "kafka source")
                        .process(
                                new KafkaMessageProcessor(
                                        CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup()))
                        .returns(
                                Types.TUPLE(
                                        Types.POJO(LegacyMsg.class), Types.POJO(OffsetInfo.class)));

        SingleOutputStreamOperator<Tuple2<LegacyMsgList, OffsetInfo>> dataStream =
                kafkaStream
                        .keyBy(
                                tuple2 ->
                                        (keySelectorArray[tuple2.f1.getGroupKey() % (parallelism)]))
                        .countWindow(kafkaBatchSize)
                        .trigger(
                                PurgingTrigger.of(
                                        ProcessingTimeoutTrigger.of(
                                                CountTrigger.of(kafkaBatchSize),
                                                Duration.ofSeconds(kafkaTimeoutSeconds))))
                        .process(new WindowCollector())
                        .returns(
                                Types.TUPLE(
                                        Types.POJO(LegacyMsgList.class),
                                        Types.POJO(OffsetInfo.class)));

        KafkaSink<Tuple2<CalcResultMsg, OffsetInfo>> kafkaSink =
                KafkaSink.<Tuple2<CalcResultMsg, OffsetInfo>>builder()
                        .setBootstrapServers(CalcLionConfig.getCalcCommonKafkaServers())
                        .setRecordSerializer(
                                new KafkaRecordSerializer(
                                        CalcLionConfig.getCalcCommonKafkaSinkTopicPattern()))
                        .build();

        dataStream
                .process(new StreamCalcProcessor())
                .returns(Types.TUPLE(Types.POJO(CalcResultMsg.class), Types.POJO(OffsetInfo.class)))
                .sinkTo(kafkaSink)
                .name("kafka sink");

        // Offset save in ignite
        dataStream
                .process(new OffsetExtractFunction())
                .returns(Types.POJO(OffsetInfo.class))
                .windowAll(
                        TumblingProcessingTimeWindows.of(
                                Time.of(
                                        CalcLionConfig.getCalcStreamKafkaOffsetWindowTimeSeconds(),
                                        TimeUnit.SECONDS)))
                .aggregate(new OffsetAggrFunction())
                .addSink(new OffsetSink(CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup()))
                .setParallelism(1)
                .name("offset recorder");

        try {
            env.execute("stream processing flow");
        } catch (Exception e) {
            System.out.println("Error executing job, catch exception: ");
        }
    }

    private static SourceFunction<String> initPgCdcSource() {
        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", CalcLionConfig.getCalcMetaPgCdcSchemaList());
        properties.setProperty("table.include.list", CalcLionConfig.getCalcMetaPgCdcTableList());
        //        properties.setProperty(
        //                "column.include.list", CalculateConfigOptions.getPgCdcColumnList());
        properties.setProperty("max.batch.size", CalcLionConfig.getCalcMetaPgCdcMaxBatchSize());
        properties.setProperty("max.queue.size", CalcLionConfig.getCalcMetaPgCdcMaxQueueSize());

        return PostgreSQLSource.<String>builder()
                .hostname(CalcLionConfig.getPgHostname())
                .port(CalcLionConfig.getPgPort())
                .database(CalcLionConfig.getPgDatabase()) // monitor postgres database
                .username(CalcLionConfig.getPgUsername())
                .password(CalcLionConfig.getPgPassword())
                .decodingPluginName("pgoutput")
                .slotName(CalcLionConfig.getCalcStreamPgCdcSlotName())
                .debeziumProperties(properties)
                .deserializer(new JsonDebeziumDeserializationSchema())
                .build();
    }
}
