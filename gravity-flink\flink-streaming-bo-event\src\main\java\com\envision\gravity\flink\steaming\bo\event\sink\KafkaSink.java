package com.envision.gravity.flink.steaming.bo.event.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.steaming.bo.event.config.KafkaClientConfig;
import com.envision.gravity.flink.steaming.bo.event.config.LionConfig;
import com.envision.gravity.flink.steaming.bo.event.entity.EventMsg;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Slf4j
public class KafkaSink extends RichSinkFunction<EventMsg> {
    private static final long serialVersionUID = 3529411861962134538L;
    private transient KafkaProducer<String, String> kafkaProducer;
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper().setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
    private static final String KAFKA_EVENT_TOPIC = LionConfig.getEventKafkaTopic();

    @Override
    public void open(Configuration parameters) {
        try {
            kafkaProducer = KafkaClientConfig.getKafkaProducerInstance();
        } catch (Exception e) {
            log.error("Init kafka producer error.", e);
            throw new GravityRuntimeException("Init kafka producer error.", e);
        }
    }

    @Override
    public void close() {
        try {
            KafkaClientConfig.closeKafkaProducer();
        } catch (Exception e) {
            log.error("Close kafka client error.", e);
            throw new GravityRuntimeException("Close kafka client error.", e);
        }
    }

    @Override
    public void invoke(EventMsg value, Context context) {
        if (value == null) {
            return;
        }

        try {
            String orgId = value.getOrgId();
            String eventSource = value.getEventSource();
            String eventType = value.getEventType();
            String key = value.getKey();
            String jsonValue = OBJECT_MAPPER.writeValueAsString(value);

            sendKafkaRecord(orgId, eventSource, eventType, key, jsonValue);
        } catch (JsonProcessingException e) {
            log.error(
                    "Error serializing BO event, Topic: {}, Key: {}, Value: {}",
                    KAFKA_EVENT_TOPIC,
                    value.getKey(),
                    value,
                    e);
        } catch (Exception e) {
            log.error(
                    "Unexpected exception sending BO event, Topic: {}, Key: {}, Value: {}",
                    KAFKA_EVENT_TOPIC,
                    value.getKey(),
                    value,
                    e);
            throw new GravityRuntimeException("Unexpected exception sending BO event!");
        }
    }

    private void sendKafkaRecord(
            String orgId, String eventSource, String eventType, String key, String jsonValue) {
        ProducerRecord<String, String> record =
                new ProducerRecord<>(KAFKA_EVENT_TOPIC, null, key, jsonValue, null);

        kafkaProducer.send(
                record,
                (metadata, exception) -> {
                    if (exception != null) {
                        log.error(
                                "Send BO event error, orgId: {}, eventSource: {}, eventType: {}, key: {}, value: {}",
                                orgId,
                                eventSource,
                                eventType,
                                key,
                                jsonValue,
                                exception);
                    } else {
                        log.info(
                                "Send BO event success, orgId: {}, eventSource: {}, eventType: {}, key: {}, value: {}, "
                                        + "partition: {}, offset: {}",
                                orgId,
                                eventSource,
                                eventType,
                                key,
                                jsonValue,
                                metadata.partition(),
                                metadata.offset());
                    }
                });

        kafkaProducer.flush();
    }
}
