package com.envision.gravity.flink.streaming.bo.view.operator.entity;

import com.envision.gravity.flink.streaming.bo.view.operator.model.ModelDetailOrigin;
import com.envision.gravity.flink.streaming.bo.view.operator.model.Source;

import java.sql.Timestamp;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelDetailOriginCdcRecord {
    private ModelDetailOrigin before;
    private ModelDetailOrigin after;
    private Source source;
    private String op;
    private Timestamp tsMs;
    @JsonIgnore private Object transaction;
}
