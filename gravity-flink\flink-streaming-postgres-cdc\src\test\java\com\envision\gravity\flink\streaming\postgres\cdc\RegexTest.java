package com.envision.gravity.flink.streaming.postgres.cdc;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/5/31
 * @description
 */
class RegexTest {
    @Test
    public void testRegexMatch() {
        String regex = ".*_[A-Za-z0-9]{16}$";

        String[] testStrings = {
            "example_1234567890abcdef",
            "test_abc123def456ghi7",
            "invalid_example_1234567",
            "another_example_1234567890abcde" // 15 characters, should not match
        };

        for (String testString : testStrings) {

            if (testString.matches(regex)) {
                System.out.println(testString + " matches.");
            } else {
                System.out.println(testString + " does not match.");
            }
        }
    }
}
