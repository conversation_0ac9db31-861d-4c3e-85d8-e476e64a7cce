package com.envision.gravity.low.level.api.rest.dto;

import com.envision.gravity.common.bo.ObjectField;
import com.envision.gravity.common.vo.category.CategoryReq;
import com.envision.gravity.common.vo.field.FieldReq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/** @Author: qi.jiang2 @Date: 2024/05/30 17:12 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ObjsRelatedInfo {

    private Set<String> systemIds;

    private List<FieldReq> fieldReqList;

    private List<CategoryReq> categoryReqList;

    private Map<String, ObjectField> objectFieldMap;
}
