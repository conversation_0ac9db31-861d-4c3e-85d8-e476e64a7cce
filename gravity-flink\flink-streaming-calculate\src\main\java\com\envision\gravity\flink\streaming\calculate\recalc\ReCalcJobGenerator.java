package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobTypeEnum;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcMetaInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.UUID;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReCalcJobGenerator extends ProcessFunction<RecCalcMetaInfo, TblCalcJobInfo> {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobGenerator.class);
    private static final long serialVersionUID = -5889211678013023089L;
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public void processElement(
            RecCalcMetaInfo recCalcMetaInfo,
            ProcessFunction<RecCalcMetaInfo, TblCalcJobInfo>.Context context,
            Collector<TblCalcJobInfo> collector)
            throws Exception {

        if (recCalcMetaInfo == null || recCalcMetaInfo.getOrgId() == null) {
            logger.warn("Invalid RecCalcMetaInfo: missing required fields");
            return;
        }

        try {
            // 生成作业信息
            TblCalcJobInfo jobInfo = generateJobInfo(recCalcMetaInfo);

            logger.info(
                    "Generated ReCalc job: jobId={}, prefRuleId={}, orgId={}",
                    jobInfo.getJobId(),
                    jobInfo.getPrefRuleId(),
                    jobInfo.getSrcOrgId());

            collector.collect(jobInfo);

        } catch (Exception e) {
            logger.error(
                    "Failed to generate ReCalc job for prefRuleId={}, orgId={}: {}",
                    recCalcMetaInfo.getRuleInfo().getPrefRuleId(),
                    recCalcMetaInfo.getOrgId(),
                    e.getMessage(),
                    e);
        }
    }

    /** 生成重跑计算作业信息 */
    private TblCalcJobInfo generateJobInfo(RecCalcMetaInfo recCalcMetaInfo) throws Exception {
        String jobId = generateJobId();
        long currentTime = System.currentTimeMillis();

        // 计算时间范围
        long calcEndTime = currentTime;
        long calcStartTime = calcEndTime - CalcLionConfig.getReCalcTimeRangeSeconds() * 1000L;

        return TblCalcJobInfo.builder()
                .jobId(jobId)
                .prefRuleId(recCalcMetaInfo.getRuleInfo().getPrefRuleId())
                .ruleInfo(recCalcMetaInfo)
                .calcStartTime(calcStartTime)
                .calcEndTime(calcEndTime)
                .status(ReCalcJobStatusEnum.INIT.getCode())
                .type(ReCalcJobTypeEnum.DEFAULT.getCode())
                .srcOrgId(recCalcMetaInfo.getOrgId())
                .targetOrgId(recCalcMetaInfo.getOrgId())
                .progress(0)
                .startedTime(0L)
                .finishedTime(0L)
                .cancelledTime(0L)
                .createdTime(currentTime)
                .createdUser("gravity")
                .modifiedTime(currentTime)
                .modifiedUser("gravity")
                .sysCreatedTime(0L)
                .sysModifiedTime(0L)
                .build();
    }

    /** 生成唯一的作业ID */
    private String generateJobId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
