package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.streaming.calculate.cdc.*;
import com.envision.gravity.flink.streaming.calculate.dto.*;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcMetaCdcProcessor extends ProcessFunction<ConvertedCdcRecord, Void> {
    private static final long serialVersionUID = -5979818665562137943L;

    private static final Logger logger = LoggerFactory.getLogger(CalcMetaCdcProcessor.class);

    // TODO when close need to remove cache
    @Override
    public void open(Configuration parameters) throws Exception {
        CalcMetaProcessor.getInstance().batchLoad();
    }

    @Override
    public void processElement(
            ConvertedCdcRecord value,
            ProcessFunction<ConvertedCdcRecord, Void>.Context ctx,
            Collector<Void> out)
            throws Exception {
        CalcMetaCdcUtils.process(value);
    }
}
