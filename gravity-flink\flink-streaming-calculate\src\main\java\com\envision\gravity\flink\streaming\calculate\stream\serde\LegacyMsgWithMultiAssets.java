package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.List;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@NoArgsConstructor
@TypeInfo(PojoFactory.LegacyMsgWithMultiAssetsType.class)
public class LegacyMsgWithMultiAssets extends LegacyMsg {

    private List<LegacyPayload> payload;

    public List<LegacyPayload> getPayload() {
        return payload;
    }

    public void setPayload(List<LegacyPayload> payload) {
        this.payload = payload;
    }
}
