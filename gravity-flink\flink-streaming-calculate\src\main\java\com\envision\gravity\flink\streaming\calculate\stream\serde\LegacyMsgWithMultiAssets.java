package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@NoArgsConstructor
@TypeInfo(PojoFactory.LegacyMsgWithMultiAssetsType.class)
public class LegacyMsgWithMultiAssets extends LegacyMsg {

    private List<LegacyPayload> payload;

    /** 消息头信息（用于AspectCalc和ReCalc） */
    private Map<String, String> headers = new HashMap<>();

    public List<LegacyPayload> getPayload() {
        return payload;
    }

    public void setPayload(List<LegacyPayload> payload) {
        this.payload = payload;
    }

    public Map<String, String> getHeaders() {
        if (headers == null) {
            headers = new HashMap<>();
        }
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    /** 添加Header */
    public void addHeader(String key, String value) {
        getHeaders().put(key, value);
    }

    /** 获取Header */
    public String getHeader(String key) {
        return getHeaders().get(key);
    }

    /** 检查是否包含指定Header */
    public boolean hasHeader(String key) {
        return getHeaders().containsKey(key);
    }
}
