package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
@Slf4j
class ScheduledThreadPoolTest {

    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE =
            new ScheduledThreadPoolExecutor(
                    1,
                    new ThreadFactory() {
                        private final AtomicInteger poolNumber = new AtomicInteger(1);

                        @Override
                        public Thread newThread(@NotNull Runnable r) {
                            return new Thread(
                                    r,
                                    String.format(
                                            "Virtual-Attr-Sync-Scheduled-Thread-Pool-%d",
                                            poolNumber.getAndIncrement()));
                        }
                    });

    @Test
    void test() throws InterruptedException {
        SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(
                () -> {
                    try {
                        log.info(">>> Test Scheduled Thread Pool");
                    } catch (Exception e) {
                        log.error("Fetch and emit data error!", e);
                    }
                },
                1,
                1,
                TimeUnit.SECONDS);

        Thread.sleep(10000);
    }
}
