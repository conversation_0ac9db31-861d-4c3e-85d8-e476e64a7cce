// Generated from
// C:/Users/<USER>/IdeaProjects/gravity-all/gravity-rest-api/gravity-low-level-api/src/main/java/com/envision/gravity/low/level/api/rest/antlr/Condition.g4 by ANTLR 4.13.2
package com.envision.gravity.low.level.api.rest.antlr;

import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ErrorNode;
import org.antlr.v4.runtime.tree.TerminalNode;

/**
 * This class provides an empty implementation of {@link ConditionListener}, which can be extended
 * to create a listener which only needs to handle a subset of the available methods.
 */
@SuppressWarnings("CheckReturnValue")
public class ConditionBaseListener implements ConditionListener {
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParse(ConditionParser.ParseContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParse(ConditionParser.ParseContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphAssetInRelatedModelsExpr(
            ConditionParser.GraphAssetInRelatedModelsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphAssetInRelatedModelsExpr(
            ConditionParser.GraphAssetInRelatedModelsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphAndExpr(ConditionParser.GraphAndExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphAndExpr(ConditionParser.GraphAndExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphParenExpr(ConditionParser.GraphParenExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphParenExpr(ConditionParser.GraphParenExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterComparatorExpr(ConditionParser.ComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitComparatorExpr(ConditionParser.ComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinModelExpr(ConditionParser.JoinModelExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinModelExpr(ConditionParser.JoinModelExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterIsExistsExpr(ConditionParser.IsExistsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitIsExistsExpr(ConditionParser.IsExistsExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterInExpr(ConditionParser.InExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitInExpr(ConditionParser.InExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterLikeExpr(ConditionParser.LikeExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitLikeExpr(ConditionParser.LikeExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterBinaryExpr(ConditionParser.BinaryExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitBinaryExpr(ConditionParser.BinaryExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParenExpr(ConditionParser.ParenExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParenExpr(ConditionParser.ParenExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterField(ConditionParser.FieldContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitField(ConditionParser.FieldContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterFields(ConditionParser.FieldsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitFields(ConditionParser.FieldsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParenFields(ConditionParser.ParenFieldsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParenFields(ConditionParser.ParenFieldsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParenValues(ConditionParser.ParenValuesContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParenValues(ConditionParser.ParenValuesContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinGraph(ConditionParser.JoinGraphContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinGraph(ConditionParser.JoinGraphContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParenGraphExpr(ConditionParser.ParenGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParenGraphExpr(ConditionParser.ParenGraphExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterJoinModel(ConditionParser.JoinModelContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitJoinModel(ConditionParser.JoinModelContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterModelParams(ConditionParser.ModelParamsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitModelParams(ConditionParser.ModelParamsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterAssetInModels(ConditionParser.AssetInModelsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitAssetInModels(ConditionParser.AssetInModelsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterValues(ConditionParser.ValuesContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitValues(ConditionParser.ValuesContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterValue(ConditionParser.ValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitValue(ConditionParser.ValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterStringValue(ConditionParser.StringValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitStringValue(ConditionParser.StringValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterLike(ConditionParser.LikeContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitLike(ConditionParser.LikeContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterAnd(ConditionParser.AndContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitAnd(ConditionParser.AndContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterComparator(ConditionParser.ComparatorContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitComparator(ConditionParser.ComparatorContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterBooleanValue(ConditionParser.BooleanValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitBooleanValue(ConditionParser.BooleanValueContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterIsExists(ConditionParser.IsExistsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitIsExists(ConditionParser.IsExistsContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterIsIn(ConditionParser.IsInContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitIsIn(ConditionParser.IsInContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterBinary(ConditionParser.BinaryContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitBinary(ConditionParser.BinaryContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterTimestamp(ConditionParser.TimestampContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitTimestamp(ConditionParser.TimestampContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterLeftParen(ConditionParser.LeftParenContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitLeftParen(ConditionParser.LeftParenContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterRightParen(ConditionParser.RightParenContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitRightParen(ConditionParser.RightParenContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterI18n(ConditionParser.I18nContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitI18n(ConditionParser.I18nContext ctx) {}

    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void enterEveryRule(ParserRuleContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void exitEveryRule(ParserRuleContext ctx) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void visitTerminal(TerminalNode node) {}
    /**
     * {@inheritDoc}
     *
     * <p>The default implementation does nothing.
     */
    @Override
    public void visitErrorNode(ErrorNode node) {}
}
