<?xml version="1.0" encoding="utf-8" ?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <property name="pattern" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level [%thread] [%tid] %c{1}:%L - %m%n"/>

    <property name="digest-pattern" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level - %m%n"/>

    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <!-- 注意将layout class更改为skywalking的类-->
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="fileLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home:-.}/logs/tsdb-adapter.log</file>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home:-.}/logs/history/tsdb-adapter.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- each file should be at most 50MB, keep 30 days worth of history, but at most 5GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="consoleLog"/>
<!--        <appender-ref ref="fileLog"/>-->
    </root>

    <logger name="io.eniot.tsdb.adapter.service" level="debug" />

</configuration>