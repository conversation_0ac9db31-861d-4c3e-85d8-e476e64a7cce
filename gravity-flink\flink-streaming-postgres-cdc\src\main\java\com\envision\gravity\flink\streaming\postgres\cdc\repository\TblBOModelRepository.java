package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblBOModelMapper;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class TblBOModelRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblBOModelRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public int updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> modelIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (modelIdList.isEmpty()) {
                return 0;
            }

            TblBOModelMapper tblBOModelMapper = session.getMapper(TblBOModelMapper.class);
            return tblBOModelMapper.updateSysModifiedTimeByPrimaryKeys(schemaName, modelIdList);
        } catch (Exception e) {
            log.error("Update model sys_modified_time error.", e);
            throw new GravityRuntimeException("Update model sys_modified_time error.", e);
        }
    }

    public List<String> selectAssetIdList(
            String schemaName, Multimap<String, String> modelGroupMap) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");
            if (modelGroupMap.isEmpty()) {
                return Collections.emptyList();
            }

            TblBOModelMapper tblBOModelMapper = session.getMapper(TblBOModelMapper.class);
            return tblBOModelMapper.selectAssetIdList(schemaName, modelGroupMap);
        } catch (Exception e) {
            log.error("Select asset_id list by model_id and group_id error.", e);
            throw new GravityRuntimeException(
                    "Select asset_id list by model_id and group_id error.", e);
        }
    }
}
