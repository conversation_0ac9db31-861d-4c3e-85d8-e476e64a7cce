package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TypeInfo(PojoFactory.LatestPointMapType.class)
public class LatestPointMap {
    private HashMap<String, Map<String, List<LatestMeasurePointEntityLocal>>> measurePointsMapByOu;
    private HashMap<String, List<BinarySchemaIdentity>> toUpdateSchemaHash;
}
