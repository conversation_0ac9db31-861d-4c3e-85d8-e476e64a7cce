package com.envision.gravity.common.pojo;

import javax.validation.constraints.NotBlank;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EdgeProperty {

    @NotBlank(message = "subGraphId can not blank")
    private String subGraphId;

    @NotBlank(message = "edgeType can not blank")
    private String edgeType;

    private String fromVid;

    @NotBlank(message = "edgeType can not blank")
    private String toVid;

    private int order;

    private boolean tree;
}
