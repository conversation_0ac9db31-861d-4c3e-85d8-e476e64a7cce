package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TblObjField {

    private String systemId;

    private String fieldId;

    private String calcFieldExp;

    private String createdUser;

    private String modifiedUser;

    private Timestamp createdTime;

    private Timestamp modifiedTime;
}
