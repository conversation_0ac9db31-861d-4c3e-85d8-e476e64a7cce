package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.ArrayList;
import java.util.List;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
class TblBOModelSqlProviderTest {

    @Test
    void updateSysModifiedTimeByPrimaryKeys() {
        List<String> modelIdList = new ArrayList<>();
        modelIdList.add("model_id1");
        modelIdList.add("model_id2");
        modelIdList.add("model_id3");
        TblBOModelSqlProvider tblBOModelSqlProvider = new TblBOModelSqlProvider();
        String sql =
                tblBOModelSqlProvider.updateSysModifiedTimeByPrimaryKeys("gravity", modelIdList);
        System.out.println(sql);
    }
}
