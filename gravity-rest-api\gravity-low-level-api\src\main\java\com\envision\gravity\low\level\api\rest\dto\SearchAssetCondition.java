package com.envision.gravity.low.level.api.rest.dto;

import com.envision.gravity.common.vo.search.asset.SearchAssetJoinModelParam;
import com.envision.gravity.common.vo.search.asset.SearchAssetWithGraphParam;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/** @Author: qi.jiang2 @Date: 2024/04/23 12:23 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchAssetCondition {

    private String baseCondition;

    private SearchAssetWithGraphParam searchAssetWithGraphParam;

    private boolean isWithGraph;

    private SearchAssetJoinModelParam searchAssetJoinModelParam;

    public boolean isBaseConditionExist() {
        return StringUtils.isNotEmpty(baseCondition);
    }
}
