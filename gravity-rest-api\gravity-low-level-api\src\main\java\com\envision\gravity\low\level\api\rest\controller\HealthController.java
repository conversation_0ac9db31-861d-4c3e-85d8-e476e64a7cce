package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** @Author: qi.jiang2 @Date: 2024/05/23 15:15 @Description: */
@RestController
@RequestMapping(value = "/health")
public class HealthController {

    @GetMapping(value = "")
    public ResponseResult<?> checkHealth() {
        return ResponseResult.createResult(
                ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMessage());
    }
}
