package com.envision.gravity.flink.streaming.calculate.stream.serde;

/** <AUTHOR> 2023/8/14 */
public class PointParseException extends RuntimeException {

    private static final long serialVersionUID = 7598200927789179828L;

    private Object point;

    public PointParseException(Object point) {
        super();
        this.point = point;
    }

    public PointParseException(Object point, String message) {
        super(message);
        this.point = point;
    }

    public PointParseException(Object point, String message, Throwable throwable) {
        super(message, throwable);
        this.point = point;
    }

    public PointParseException(Object point, Throwable throwable) {
        super(throwable);
        this.point = point;
    }

    public void setPoint(String point) {
        this.point = point;
    }
}
