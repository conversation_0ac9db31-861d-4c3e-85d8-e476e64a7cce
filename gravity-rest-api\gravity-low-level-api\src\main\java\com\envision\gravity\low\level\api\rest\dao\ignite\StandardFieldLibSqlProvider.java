package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.low.level.api.rest.enums.Constants;

import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.stream.Collectors;

/** <AUTHOR> 2024/10/21 */
public class StandardFieldLibSqlProvider {

    public String queryByRawFieldIds(List<String> rawFieldIds) {
        SQL sql = new SQL();
        sql.SELECT("raw_field_id");
        sql.SELECT("field_index");
        sql.FROM(Constants.TASK_SCHEMA + Constants.STANDARD_FIELD_LIB);
        String idExpr =
                rawFieldIds.stream()
                        .map(id -> String.format("'%s'", id))
                        .collect(Collectors.joining(", "));
        sql.WHERE(String.format("raw_field_id in (%s)", idExpr));
        return sql.toString();
    }
}
