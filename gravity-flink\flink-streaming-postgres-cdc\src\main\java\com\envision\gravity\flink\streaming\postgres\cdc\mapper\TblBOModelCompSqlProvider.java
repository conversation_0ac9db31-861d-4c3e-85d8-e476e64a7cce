package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public class TblBOModelCompSqlProvider {
    public String selectModelGroupList(
            String schemaName, List<String> modelIdList, List<String> compIdList) {
        String modelIds =
                modelIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model_comp tbmc");
                        INNER_JOIN(
                                schemaName + ".tbl_bo_model tbm on tbmc.model_id = tbm.model_id");
                        WHERE(
                                "tbmc.model_id in ( "
                                        + modelIds
                                        + " ) and tbmc.comp_id in ( "
                                        + compIds
                                        + " )");
                    }
                };

        return sql.toString();
    }

    public String selectModelGroupListByModelId(String schemaName, List<String> modelIdList) {
        String modelIds =
                modelIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        WHERE("tbm.model_id in ( " + modelIds + " )");
                    }
                };

        return sql.toString();
    }
}
