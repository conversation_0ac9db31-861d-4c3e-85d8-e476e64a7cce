package com.envision.gravity.common.service.id.segment.model;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/19
 * @description double buffer
 */
@Data
public class SegmentBuffer {
    private String key;
    // Double buffer
    private final Segment[] segments;
    // The index of the currently used segment
    private volatile int currentPos;
    // Is the next segment in a switchable state
    private volatile boolean nextReady;
    private volatile boolean initOk;
    private final AtomicBoolean threadRunning;
    private final ReadWriteLock lock;

    private volatile int step;
    private volatile int minStep;
    private volatile long updateTimestamp;

    public SegmentBuffer() {
        segments = new Segment[] {new Segment(this), new Segment(this)};
        currentPos = 0;
        nextReady = false;
        initOk = false;
        threadRunning = new AtomicBoolean(false);
        lock = new ReentrantReadWriteLock();
    }

    public Segment getCurrent() {
        return segments[currentPos];
    }

    public int nextPos() {
        return (currentPos + 1) % 2;
    }

    public void switchPos() {
        currentPos = nextPos();
    }

    public Lock rLock() {
        return lock.readLock();
    }

    public Lock wLock() {
        return lock.writeLock();
    }

    @Override
    public String toString() {
        return "SegmentBuffer{"
                + "key='"
                + key
                + '\''
                + ", segments="
                + Arrays.toString(segments)
                + ", currentPos="
                + currentPos
                + ", nextReady="
                + nextReady
                + ", initOk="
                + initOk
                + ", threadRunning="
                + threadRunning
                + ", step="
                + step
                + ", minStep="
                + minStep
                + ", updateTimestamp="
                + updateTimestamp
                + '}';
    }
}
