package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
public class TblPrefExtSqlProvider {
    public String queryVirtualAttrInfo(String schemeName) {
        SQL sql =
                new SQL() {
                    {
                        SELECT(
                                "tbmc.model_id,\n"
                                        + "       tpe.pref_id,\n"
                                        + "       tcp.field_id,\n"
                                        + "       tcp.raw_field_id,\n"
                                        + "       case\n"
                                        + "           when tc.anonymous is true then tp.pref_name\n"
                                        + "           else concat(tc.comp_name, ':', tp.pref_name)\n"
                                        + "           end as attr_name");
                        FROM(schemeName + ".tbl_pref_ext tpe");
                        INNER_JOIN(schemeName + ".tbl_pref tp on tpe.pref_id = tp.pref_id");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_component_pref tcp on tp.pref_id = tcp.pref_id and tpe.comp_id = tcp.comp_id");
                        INNER_JOIN(schemeName + ".tbl_component tc on tcp.comp_id = tc.comp_id");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_bo_model_comp tbmc on tc.comp_id = tbmc.comp_id");
                        WHERE("tpe.metric_type = 3");
                    }
                };

        return sql.toString();
    }
}
