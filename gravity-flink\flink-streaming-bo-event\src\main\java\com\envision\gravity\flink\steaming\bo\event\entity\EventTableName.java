package com.envision.gravity.flink.steaming.bo.event.entity;

import java.util.Optional;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Getter
public enum EventTableName {
    OBJECT_DETAIL_ORIGIN("object_detail_origin"),
    TBL_START_VID("tbl_start_vid"),
    TBL_EDGE("tbl_edge"),
    TBL_SUB_GRAPH("tbl_sub_graph");

    private final String name;

    EventTableName(String name) {
        this.name = name;
    }

    public static Optional<EventTableName> find(String expr) {
        for (EventTableName t : EventTableName.values()) {
            if (expr.equalsIgnoreCase(t.getName())) {
                return Optional.of(t);
            }
        }

        return Optional.empty();
    }
}
