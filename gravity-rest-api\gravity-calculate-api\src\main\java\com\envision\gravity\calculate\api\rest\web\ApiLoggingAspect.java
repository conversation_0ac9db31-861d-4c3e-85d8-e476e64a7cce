package com.envision.gravity.calculate.api.rest.web;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Enumeration;
import java.util.UUID;

/** <AUTHOR> 2024/8/22 */
// @Aspect
// @Component
public class ApiLoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(ApiLoggingAspect.class);

    @Around("execution(public * com.envision.gravity.calculate.api.rest.controller.*.*(..))")
    public Object logApiExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();

        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        ContentCachingRequestWrapper request =
                (ContentCachingRequestWrapper) attributes.getRequest();

        String traceId = generateTraceId();

        String uri = request.getRequestURI();
        String method = request.getMethod();
        String params = getRequestParams(request);
        String accessKey = request.getHeader("apim-accesskey");
        String requestBody = getRequestBody(request);

        Object proceed;
        try {
            proceed = joinPoint.proceed();
        } catch (Throwable ex) {
            // Re throw the exception to ensure that the controller can handle it
            logger.error(
                    "Exception occurred while executing API: {} | TraceId: {} | AccessKey: {} | Method: {} | Params: {} | RequestBody: {} | Exception: {}",
                    uri,
                    traceId,
                    accessKey,
                    method,
                    params,
                    requestBody,
                    ex.getMessage(),
                    ex);
            throw ex;
        } finally {
            long executionTime = System.currentTimeMillis() - start;
            logger.info(
                    "Executed API: {} | TraceId: {} | Time: {} ms | AccessKey: {} | Method: {} | Params: {} | RequestBody: {}",
                    uri,
                    traceId,
                    executionTime,
                    accessKey,
                    method,
                    params,
                    requestBody);
        }

        return proceed;
    }

    private String getRequestParams(HttpServletRequest request) {
        StringBuilder params = new StringBuilder();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            params.append(paramName).append("=").append(paramValue).append("&");
        }
        return params.length() > 0 ? params.substring(0, params.length() - 1) : "";
    }

    private String getRequestBody(ContentCachingRequestWrapper request)
            throws UnsupportedEncodingException {
        byte[] content = request.getContentAsByteArray();
        return new String(content, request.getCharacterEncoding());
    }

    private String generateTraceId() {
        return UUID.randomUUID().toString();
    }

    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder body = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            body.append(line);
        }
        return body.toString();
    }

    private String getRequestHeaders(HttpServletRequest request) {
        StringBuilder headers = new StringBuilder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.append(headerName).append(": ").append(headerValue).append(", ");
        }
        return headers.length() > 0 ? headers.substring(0, headers.length() - 2) : "";
    }
}
