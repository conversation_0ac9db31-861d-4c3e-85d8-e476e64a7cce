package com.envision.gravity.flink.streaming.calculate.utils;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;

import java.util.*;


import com.univers.business.object.calc.dto.ExpressionCalculatorInput;
import com.univers.business.object.calc.request.CalcExpressionRequest;
import com.univers.business.object.calc.response.CalcExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class CalcCommonUtilsTest {

    private static final Logger logger = LoggerFactory.getLogger(CalcCommonUtilsTest.class);

    @Test
    public void thisTest() {
        List<CalcExpressionResponse> calcResult;
        CalcExpressionRequest calcRequest = new CalcExpressionRequest();
        List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);

        Map<String, Object> calcData = new HashMap<>();
        calcData.put("wind_general_001.WGEN.GenSpd", 121.0);
        calcData.put("this.NormalAttr1", 3.0);
        calcInputs.add(
                ExpressionCalculatorInput.builder()
                        .expression("\"wind_general_001\".\"WGEN.GenSpd\"*2+\"NormalAttr1\"")
                        .data(calcData)
                        .build());
        calcRequest.setCalcInputs(calcInputs);
        try {
            calcResult = ExpressionUtil.calcExpression(calcRequest);
        } catch (Exception e) {
            logger.error(
                    "Calculate failed, orgId: {}, param: {}, reason {}",
                    "o17186913277371853",
                    calcRequest,
                    e.getMessage());
            throw new RuntimeException("Expression calculation failed", e);
        }
    }

    @Test
    public void exprParseTest() {
        BaseCalcPropertyMeta baseCalcPropertyMeta1 =
                BaseCalcPropertyMeta.builder()
                        .targetCompId("comp1")
                        .targetPrefId("pref1")
                        .expression("\"model1\".\"point1\"")
                        .srcCategoryId("WindFarm")
                        .build();
        List<CalcPropertyMeta> parsedExprInfos1 =
                CalcCommonService.parseExpr(
                        "model1",
                        "comp1",
                        "pref1",
                        Collections.singletonList(baseCalcPropertyMeta1));

        BaseCalcPropertyMeta baseCalcPropertyMeta2 =
                BaseCalcPropertyMeta.builder()
                        .targetCompId("comp1")
                        .targetPrefId("pref1")
                        .expression("\"model1\".\"point1\" * 2")
                        .srcCategoryId("WindFarm")
                        .build();
        List<CalcPropertyMeta> parsedExprInfos2 =
                CalcCommonService.parseExpr(
                        "model1",
                        "comp1",
                        "pref1",
                        Collections.singletonList(baseCalcPropertyMeta2));

        BaseCalcPropertyMeta baseCalcPropertyMeta3 =
                BaseCalcPropertyMeta.builder()
                        .targetCompId("comp1")
                        .targetPrefId("pref1")
                        .expression("\"model1\".\"point1\" * 2 + \"point2\"")
                        .srcCategoryId("WindFarm")
                        .build();
        List<CalcPropertyMeta> parsedExprInfos3 =
                CalcCommonService.parseExpr(
                        "model1",
                        "comp1",
                        "pref1",
                        Collections.singletonList(baseCalcPropertyMeta3));

        System.out.println("11111");
    }

    @Test
    public void directMappingTest() {
        BaseCalcPropertyMeta baseCalcPropertyMeta =
                BaseCalcPropertyMeta.builder()
                        .targetCompId("dtmi:custom:jennyGravityModel001;1")
                        .targetPrefId("dtmi:custom:jennyGravityModel001;1__measurepoint__b")
                        // .expression("\"dtmi:custom:jennyGravityModel001;1\".\"c\"")
                        // .expression("\"jennyGravityModel001\".\"c\"")
                        .expression("\"c\"")
                        .srcCategoryId("dtmi:custom:jennyGravityModel001;1")
                        .build();
        List<CalcPropertyMeta> parsedExprInfos =
                CalcCommonService.parseExpr(
                        "dtmi:custom:jennyGravityModel001;1",
                        "dtmi:custom:jennyGravityModel001;1",
                        "dtmi:custom:jennyGravityModel001;1__measurepoint__b",
                        Collections.singletonList(baseCalcPropertyMeta));

        System.out.println("11111");
    }

    // Temporarily disabled due to Lombok annotation processing issues
    // @Test
    public void srcPrefItemEqualsTest() {
        // SrcPrefItem p1 = new SrcPrefItem();
        // p1.setModelId("model1");
        // p1.setPrefName("p1");
        // p1.setPrefType(PrefType.MEASUREPOINT);
        //
        // SrcPrefItem p2 = new SrcPrefItem();
        // p2.setModelId("model1");
        // p2.setPrefName("p1");
        // p2.setPrefType(PrefType.MEASUREPOINT);

        // Assertions.assertEquals(p1, p2);
    }
}
