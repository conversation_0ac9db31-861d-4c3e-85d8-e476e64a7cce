package com.envision.gravity.flink.streaming.calculate.dto.meta;

import java.io.Serializable;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切面计算Job元信息（AspectCalcFlow专用）
 *
 * <p>与TblCalcJobInfo的区别： 1. 数据来源：实时元数据 vs PostgreSQL表 2. 使用场景：AspectCalcFlow vs ReCalcBatchJob 3.
 * 生命周期：动态生成 vs 持久化存储
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobMetaInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 组织ID */
    private String orgId;

    /** Job ID（动态生成） */
    private String jobId;

    /** 基础计算属性元数据 */
    private BaseCalcPropertyMeta ruleInfo;

    /** 目标模型ID列表 */
    private List<String> targetModelIds;

    /** 目标属性元数据 */
    private CalcPropertyMeta targetPropertyMeta;

    /** 获取计算规则ID */
    public String getRuleId() {
        return ruleInfo != null ? ruleInfo.getPrefRuleId() : null;
    }

    /** 检查是否为直白映射 */
    public boolean isDirectMapping() {
        return targetPropertyMeta != null && targetPropertyMeta.isDirectMapping();
    }

    /** 获取目标属性类型 */
    public PrefType getTargetPrefType() {
        return targetPropertyMeta != null ? targetPropertyMeta.getPrefType() : null;
    }

    /** 获取表达式 */
    public String getExpression() {
        return targetPropertyMeta != null ? targetPropertyMeta.getExpression() : null;
    }

    /** 检查是否为有效的Job元信息 */
    public boolean isValid() {
        return orgId != null
                && !orgId.isEmpty()
                && jobId != null
                && !jobId.isEmpty()
                && ruleInfo != null
                && targetModelIds != null
                && !targetModelIds.isEmpty()
                && targetPropertyMeta != null;
    }

    /** 获取目标属性名称 */
    public String getTargetPrefName() {
        return targetPropertyMeta != null ? targetPropertyMeta.getPrefName() : null;
    }

    /** 获取源类别ID */
    public String getSrcCategoryId() {
        return ruleInfo != null ? ruleInfo.getSrcCategoryId() : null;
    }

    @Override
    public String toString() {
        return String.format(
                "CalcJobMetaInfo{orgId='%s', jobId='%s', ruleId='%s', "
                        + "targetModels=%d, targetPref='%s', directMapping=%s}",
                orgId,
                jobId,
                getRuleId(),
                targetModelIds != null ? targetModelIds.size() : 0,
                getTargetPrefName(),
                isDirectMapping());
    }
}
