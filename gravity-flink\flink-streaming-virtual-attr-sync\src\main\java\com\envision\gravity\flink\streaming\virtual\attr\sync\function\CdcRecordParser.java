package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.cdc.CdcRecord;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
@Slf4j
public class CdcRecordParser extends ProcessFunction<String, RefreshReq> {

    private static final long serialVersionUID = -2181241750608539544L;
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, RefreshReq>.Context ctx,
            Collector<RefreshReq> out)
            throws Exception {
        try {
            CdcRecord cdcRecord = OBJECT_MAPPER.readValue(value, CdcRecord.class);
            String schemeName = cdcRecord.getSource().getSchema();
            out.collect(RefreshReq.builder().schemaName(schemeName).build());
        } catch (JsonProcessingException e) {
            log.error("Parse cdc record error, value:{}", value, e);
        }
    }
}
