package com.envision.gravity.common.bo;

import javax.validation.constraints.NotBlank;

import java.util.Objects;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description: ObjectField
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ObjectField {

    @NotBlank(message = "fieldId can not be blank")
    private String fieldId;

    private String fieldName;
    private String categoryId;
    private Object value;
    private String calcFieldExp;
    private String fieldDisplayName;
    private String fieldType;
    private String dataType;
    private String unit;
    private String uniqueKey;

    public boolean metaEquals(ObjectField objectField) {
        return Objects.equals(fieldId, objectField.getFieldId())
                && Objects.equals(fieldName, objectField.getFieldName())
                && Objects.equals(categoryId, objectField.getCategoryId())
                && Objects.equals(calcFieldExp, objectField.getCalcFieldExp())
                && Objects.equals(fieldDisplayName, objectField.getFieldDisplayName())
                && Objects.equals(fieldType, objectField.getFieldType())
                && Objects.equals(dataType, objectField.getDataType())
                && Objects.equals(unit, objectField.getUnit())
                && Objects.equals(uniqueKey, objectField.getUniqueKey());
    }
}
