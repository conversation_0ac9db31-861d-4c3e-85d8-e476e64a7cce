package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.HashMap;
import java.util.Map;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Flink Job Submitter for ReCalc Batch Jobs Uses Flink REST API with pre-uploaded JAR files
 *
 * <AUTHOR>
 */
public class FlinkJobSubmitterByRest implements FlinkJobSubmitter.FlinkJobSubmitterInterface {

    private static final Logger logger = LoggerFactory.getLogger(FlinkJobSubmitterByRest.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private final OkHttpClient httpClient;
    private final String flinkRestUrl;
    private final String jobJarId;
    private final int submitTimeoutSeconds;

    public FlinkJobSubmitterByRest() {
        this.submitTimeoutSeconds = CalcLionConfig.getFlinkJobSubmitTimeoutSeconds();
        this.jobJarId = CalcLionConfig.getCalcFlinkJarPrefix();
        this.flinkRestUrl = CalcLionConfig.getFlinkJobManagerRestUrl();

        // Initialize HTTP client
        this.httpClient =
                new OkHttpClient.Builder()
                        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                        .readTimeout(submitTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                        .writeTimeout(submitTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS)
                        .build();

        logger.info(
                "FlinkJobSubmitterByRest initialized with REST URL: {}, JAR ID: {}, timeout: {}s",
                flinkRestUrl,
                jobJarId,
                submitTimeoutSeconds);
    }

    /** Submit ReCalc batch job using Flink REST API */
    public String submitReCalcBatchJob(TblCalcJobInfo jobInfo) throws Exception {
        logger.info(
                "Submitting ReCalc batch job via REST API: jobId={}, prefRuleId={}",
                jobInfo.getJobId(),
                jobInfo.getPrefRuleId());

        return submitJobUsingRestAPI(jobInfo);
    }

    /** Submit job using Flink REST API with pre-uploaded JAR */
    private String submitJobUsingRestAPI(TblCalcJobInfo jobInfo) throws Exception {
        logger.info("Submitting job using REST API: jobId={}", jobInfo.getJobId());

        // 1. Find pre-uploaded JAR
        String jarId = findPreUploadedJar();

        // 2. Submit job
        String flinkJobId = submitJobWithJar(jarId, jobInfo);

        logger.info(
                "Successfully submitted job using REST API: jobId={}, flinkJobId={}",
                jobInfo.getJobId(),
                flinkJobId);

        return flinkJobId;
    }

    /** Find pre-uploaded JAR file on Flink cluster */
    private String findPreUploadedJar() throws Exception {
        String checkUrl = flinkRestUrl + "/jars";
        Request request = new Request.Builder().url(checkUrl).get().build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JsonNode jsonResponse = OBJECT_MAPPER.readTree(responseBody);
                JsonNode files = jsonResponse.get("files");

                if (files != null && files.isArray()) {
                    String targetJarName = jobJarId;

                    // Find JAR by name
                    for (JsonNode file : files) {
                        String fileName = file.get("name").asText();
                        if (fileName.contains(targetJarName)
                                || fileName.contains("recalc-batch-job")
                                || fileName.contains("flink-streaming-calculate")) {
                            String jarId = file.get("id").asText();
                            logger.info(
                                    "Found pre-uploaded JAR on cluster: name={}, id={}",
                                    fileName,
                                    jarId);
                            return jarId;
                        }
                    }

                    // If specific JAR not found, use the first available JAR as fallback
                    if (files.size() > 0) {
                        String jarId = files.get(0).get("id").asText();
                        String fileName = files.get(0).get("name").asText();
                        logger.warn(
                                "Target JAR '{}' not found, using fallback JAR: name={}, id={}",
                                targetJarName,
                                fileName,
                                jarId);
                        return jarId;
                    }
                }
            }
        }

        throw new RuntimeException(
                "No JAR file available on Flink cluster. "
                        + "Please upload the ReCalc batch job JAR to Flink cluster first. "
                        + "Expected JAR name contains: "
                        + jobJarId);
    }

    /** Submit job with uploaded JAR using REST API */
    private String submitJobWithJar(String jarId, TblCalcJobInfo jobInfo) throws Exception {
        String submitUrl = flinkRestUrl + "/jars/" + jarId + "/run";

        // Build request body
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("entryClass", CalcLionConfig.getFlinkJobMainClass());
        requestBody.put("parallelism", CalcLionConfig.getFlinkJobParallelism());

        // Build arguments and log them
        String[] args = buildJobArguments(jobInfo);
        String programArgsString = String.join(" ", args);
        requestBody.put("programArgs", programArgsString);

        String jsonBody = OBJECT_MAPPER.writeValueAsString(requestBody);

        RequestBody requestBodyObj =
                RequestBody.create(MediaType.parse("application/json"), jsonBody);
        Request request = new Request.Builder().url(submitUrl).post(requestBodyObj).build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JsonNode jsonResponse = OBJECT_MAPPER.readTree(responseBody);
                return jsonResponse.get("jobid").asText();
            } else {
                String responseBody = response.body() != null ? response.body().string() : "";
                throw new RuntimeException(
                        "Failed to submit job via REST API. Status: "
                                + response.code()
                                + ", Body: "
                                + responseBody);
            }
        }
    }

    /** Build job arguments from TblCalcJobInfo */
    private String[] buildJobArguments(TblCalcJobInfo jobInfo) {
        Map<String, String> params = buildJobParameters(jobInfo);

        String[] args = new String[params.size() * 2];
        int index = 0;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            args[index++] = "--" + entry.getKey();
            args[index++] = entry.getValue();
        }

        logger.info("Built job arguments: {}", java.util.Arrays.toString(args));
        return args;
    }

    /** Build job parameters map */
    private Map<String, String> buildJobParameters(TblCalcJobInfo jobInfo) {
        Map<String, String> params = new HashMap<>();
        params.put("jobId", jobInfo.getJobId());
        return params;
    }

    /** Check if Flink cluster is available */
    public boolean isClusterAvailable() {
        try {
            String healthUrl = flinkRestUrl + "/overview";
            Request request = new Request.Builder().url(healthUrl).get().build();

            try (Response response = httpClient.newCall(request).execute()) {
                boolean available = response.isSuccessful();
                logger.debug("Flink cluster is available via REST API: {}", available);
                return available;
            }
        } catch (Exception e) {
            logger.warn("Flink cluster health check failed via REST API: {}", e.getMessage());
            return false;
        }
    }

    /** Close resources */
    public void close() {
        try {
            // OkHttpClient doesn't need explicit closing
            logger.info("FlinkJobSubmitterByRest closed");
        } catch (Exception e) {
            logger.warn("Error closing FlinkJobSubmitterByRest: {}", e.getMessage());
        }
    }
}
