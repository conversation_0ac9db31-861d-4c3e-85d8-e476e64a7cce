package com.envision.gravity.common.definition.bo;

import javax.validation.constraints.NotBlank;

import java.util.List;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataObjectDef {
    @NotBlank(message = "DataObject: systemId can not be blank")
    private String systemId;

    private JSONObject systemDisplayName;
    private List<ModelAttributes> models;

    private DataObjectDef before;
}
