package com.envision.gravity.calculate.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownstreamAssetSummary {

    private Set<String> assetIds;

    private Map<String, DownstreamAssetInfo> presentAssetInfo;

    private Set<String> notPresentAssetIds;
}
