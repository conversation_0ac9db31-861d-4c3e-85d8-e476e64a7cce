package com.envision.gravity.flink.streaming.calculate.utils;


import com.envision.arch.lion.client.ConfigCache;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.flink.configuration.ConfigOption;

public class LionUtil {

    private static Log logger = LogFactory.getLog(LionUtil.class);

    public static String getValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            logger.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static Integer getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            logger.error("ERROR: get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getValue(ConfigOption<String> config) {
        if (config == null) {
            return null;
        }
        return getValue(config.key(), config.defaultValue());
    }

    public static Integer getIntValue(ConfigOption<Integer> config) {
        if (config == null) {
            return null;
        }
        return getIntValue(config.key(), config.defaultValue());
    }
}
