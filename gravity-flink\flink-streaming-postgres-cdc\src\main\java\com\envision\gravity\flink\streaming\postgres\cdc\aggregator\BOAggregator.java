package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblBOInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/19
 * @description
 */
public class BOAggregator implements Aggregator {

    public BOAggregator(SqlSessionFactory sqlSessionFactory) {}

    @Override
    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        List<String> assetIdList =
                records.stream()
                        .map(record -> ((TblBOInfo) record.getAfter()).getAssetId())
                        .distinct()
                        .collect(Collectors.toList());

        // 1. refresh object detail
        return AggregatedResults.builder()
                .refreshObjectReq(RefreshObjectReq.builder().boUpsertRefresh(assetIdList).build())
                .build();
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        return null;
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        List<String> assetIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblBOInfo before = (TblBOInfo) record.getBefore();
                                    TblBOInfo after = (TblBOInfo) record.getAfter();
                                    if (!Objects.equals(
                                                    before.getAssetDisplayName(),
                                                    after.getAssetDisplayName())
                                            || !Objects.equals(
                                                    before.getSystemId(), after.getSystemId())
                                            || !Objects.equals(
                                                    before.getCreatedUser(), after.getCreatedUser())
                                            || !Objects.equals(
                                                    before.getModifiedUser(),
                                                    after.getModifiedUser())
                                            || !Objects.equals(
                                                    before.getCreatedTime(), after.getCreatedTime())
                                            || !Objects.equals(
                                                    before.getModifiedTime(),
                                                    after.getModifiedTime())) {
                                        return after.getAssetId();
                                    }
                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        // 1. refresh object detail
        return AggregatedResults.builder()
                .refreshObjectReq(RefreshObjectReq.builder().boUpsertRefresh(assetIdList).build())
                .build();
    }
}
