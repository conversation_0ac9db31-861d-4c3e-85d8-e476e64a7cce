package com.envision.gravity.low.level.api.rest.antlr;

import com.envision.gravity.common.vo.search.asset.JoinModelTagsOpType;
import com.envision.gravity.common.vo.search.asset.SearchAssetJoinModelParam;
import com.envision.gravity.common.vo.search.asset.SearchAssetWithGraphParam;
import com.envision.gravity.low.level.api.rest.enums.*;
import com.envision.gravity.low.level.api.rest.exception.ParseExpressionException;
import com.envision.gravity.low.level.api.rest.exception.UnsupportSearchException;
import com.envision.gravity.low.level.api.rest.util.ConditionParseUtil;
import com.envision.gravity.low.level.api.rest.util.ExpressionUtils;
import com.envision.gravity.low.level.api.rest.util.LionUtil;
import com.envision.gravity.low.level.api.rest.util.SpecialStringProcessor;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;

/** @Author: qi.jiang2 @Date: 2024/03/18 15:36 @Description: */
@Component
public class AssetConditionEvalVisitor extends ConditionBaseVisitor<String> {

    private static final Log logger = LogFactory.getLog(AssetConditionEvalVisitor.class);

    private final Map<String, AssetSearchSupportFieldEnum> supportFieldMap = new HashMap<>(8);

    private String orgId;

    private boolean isWithGraph = false;

    private final int USE_PG_JSONB_ARRAY_ANY_LIMIT =
            LionUtil.getIntValue("gravity-rest.useArrayAnyValueSize", 10);

    private final SearchAssetWithGraphParam searchAssetWithGraphParam =
            SearchAssetWithGraphParam.builder()
                    .graphBaseCondition(new ArrayList<>())
                    .graphFromCondition(new ArrayList<>())
                    .graphToCondition(new ArrayList<>())
                    .graphSubGraphIdCondition(new ArrayList<>())
                    .excludeFromVid(false)
                    .excludeToVid(false)
                    .maxStep(10)
                    .build();

    private SearchAssetJoinModelParam searchAssetJoinModelParam;

    private List<String> localeList = new ArrayList<>();

    // ThreadLocal variable to track fuzzy search expressions
    private final ThreadLocal<Boolean> isContainsFuzzySearch = ThreadLocal.withInitial(() -> false);

    public void setLocaleList(List<String> localeList) {
        this.localeList = localeList;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public boolean isWithGraph() {
        return isWithGraph;
    }

    public SearchAssetWithGraphParam getSearchAssetWithGraphParam() {
        return searchAssetWithGraphParam;
    }

    public SearchAssetJoinModelParam getSearchAssetJoinModelParam() {
        return this.searchAssetJoinModelParam;
    }

    public AssetConditionEvalVisitor() {
        for (AssetSearchSupportFieldEnum supportFieldEnum : AssetSearchSupportFieldEnum.values()) {
            supportFieldMap.put(supportFieldEnum.getSupportField(), supportFieldEnum);
        }
    }

    // --------------------------------------------------------------
    // fuzzy_search(i18n(attributes._name, 'zh_CN'), 'searchWord')
    // fuzzy_search(attributes._name, 'searchWord')
    // --------------------------------------------------------------
    @Override
    public String visitFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx) {
        // Check if fuzzy search expression already exists
        if (isContainsFuzzySearch.get()) {
            throw new ParseExpressionException(
                    "Multiple fuzzy search expressions are not allowed in a single query");
        }
        // Set the flag to true
        isContainsFuzzySearch.set(true);

        String searchFieldExpr = visit(ctx.fuzzySearchField());
        String searchWord = visit(ctx.stringValue());

        if (ExpressionUtils.isEmptyStr(searchWord)) {
            throw new ParseExpressionException("The search word cannot be empty in fuzzy_search");
        }

        // Escape special characters
        searchWord = ExpressionUtils.likeEscape(searchWord);
        searchWord = SpecialStringProcessor.processVarcharWithDollarChar(searchWord);

        String searchField;
        if (ctx.fuzzySearchField().i18n() != null) {
            // Extract field and locale from i18n function
            String[] i18nParams = extractI18nParams(ctx.fuzzySearchField().i18n());
            searchField = i18nParams[0];
            String locale = i18nParams[1];

            // Handle locale specific search
            if ("*".equals(locale)) {
                // Search in all supported locales
                return getFuzzySearchExpression(searchField, searchWord, localeList);
            } else {
                // Search in specific locale
                return getFuzzySearchExpression(
                        searchField, searchWord, Collections.singletonList(locale));
            }
        } else {
            searchField = searchFieldExpr;
            return getFuzzySearchExpression(
                    searchField, searchWord, Collections.singletonList("default"));
        }
    }

    /**
     * Extract parameters from i18n function
     *
     * @param i18nContext The i18n context from ANTLR
     * @return Array containing [field, locale]
     */
    private String[] extractI18nParams(ConditionParser.I18nContext i18nContext) {
        String field = visit(i18nContext.field());
        String locale = visit(i18nContext.stringValue());

        // Remove quotes from locale
        locale = locale.substring(1, locale.length() - 1);

        return new String[] {field, locale};
    }

    /**
     * Generate fuzzy search expression for given locales
     *
     * @param field The field to search in
     * @param searchWord The word to search for
     * @param locales List of locales to search in
     * @return SQL expression for fuzzy search
     */
    private String getFuzzySearchExpression(String field, String searchWord, List<String> locales) {
        AssetFuzzySearchSupportFieldEnum supportFieldEnum =
                AssetFuzzySearchSupportFieldEnum.getByExprField(field);
        StringBuilder expr = new StringBuilder();

        // Build coalesce expression for the field
        expr.append("lower(coalesce(");

        // Add all locales to coalesce
        if (!locales.isEmpty()) {
            for (int i = 0; i < locales.size(); i++) {
                expr.append(supportFieldEnum.getSqlExpr())
                        .append(" ->> '")
                        .append(locales.get(i))
                        .append("'");

                if (i < locales.size() - 1) {
                    expr.append(", ");
                }
            }

            // Add default locale if not already in the list
            if (!locales.contains("default")) {
                expr.append(", ").append(supportFieldEnum.getSqlExpr()).append(" ->> 'default'");
            }
        } else {
            // If locales is empty, only use default locale
            expr.append(supportFieldEnum.getSqlExpr()).append(" ->> 'default'");
        }

        // Add empty string as final fallback
        expr.append(", '')) LIKE ").append(searchWord.toLowerCase());

        return expr.toString();
    }

    @Override
    public String visitFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx) {
        if (ctx.i18n() != null) {
            return visit(ctx.i18n());
        }
        return visit(ctx.field());
    }

    // --------------------------------------------------------------
    // join model(exists(tags.xxx))
    // join model(tags.xxx = xxx)
    // --------------------------------------------------------------
    @Override
    public String visitJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx) {
        String tagKey = joinModelExprCheck(ctx.field());

        String tagVal = ctx.value().accept(this);
        String escapedTagVal = ExpressionUtils.varcharCompareEscape(tagVal);
        // String value format is 'xxxx', need erase
        String erasedTagVal = escapedTagVal;
        if (escapedTagVal.startsWith("'") && escapedTagVal.endsWith("'")) {
            erasedTagVal = escapedTagVal.substring(1, escapedTagVal.length() - 1);
        }

        this.searchAssetJoinModelParam =
                new SearchAssetJoinModelParam(
                        JoinModelTagsOpType.EQ,
                        tagKey,
                        SpecialStringProcessor.processVarcharWithDollarChar(erasedTagVal));

        String expr = String.format("tags.%s=%s", tagKey, tagVal);
        logger.debug("visitJoinModelTagEqExpr: " + expr);
        return expr;
    }

    @Override
    public String visitJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx) {
        String tagKey = joinModelExprCheck(ctx.field());

        this.searchAssetJoinModelParam =
                new SearchAssetJoinModelParam(JoinModelTagsOpType.EXISTS, tagKey, null);

        String expr = String.format("exists(tags.%s)", tagKey);
        logger.debug("visitJoinModelTagExistsExpr: " + expr);
        return expr;
    }

    private String joinModelExprCheck(ConditionParser.FieldContext tagField) {
        if (this.searchAssetJoinModelParam != null) {
            throw new UnsupportSearchException(
                    "Unsupported search with multi join model expressions");
        }

        if (StringUtils.isEmpty(tagField.getText())) {
            throw new UnsupportSearchException("join model expr is empty ...");
        }

        if (!"tags".equals(tagField.start.getText())) {
            throw new UnsupportSearchException("Missing tags prefix in join model");
        }

        return tagField.stop.getText();
    }

    @Override
    public String visitJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx) {
        if (ctx.joinModelTagEqExpr() != null) {
            visit(ctx.joinModelTagEqExpr());
        } else {
            visit(ctx.joinModelTagExistsExpr());
        }
        return "";
    }

    @Override
    public String visitJoinModelExpr(ConditionParser.JoinModelExprContext ctx) {
        visit(ctx.joinModel());
        visit(ctx.parenJoinModelExpr());
        return "";
    }

    @Override
    public String visitJoinModel(ConditionParser.JoinModelContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx) {
        return ctx.LEFT_PAREN() + visit(ctx.joinModelGraphExpr()) + ctx.LEFT_PAREN();
    }

    @Override
    public String visitParse(ConditionParser.ParseContext ctx) {
        return visit(ctx.getChild(0));
    }

    @Override
    public String visitParenExpr(ConditionParser.ParenExprContext ctx) {
        String left = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);

        if (StringUtils.isEmpty(op)) {
            return "";
        }
        return left + op + right;
    }

    @Override
    public String visitLikeExpr(ConditionParser.LikeExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String replaceRight = ExpressionUtils.likeEscape(right);
        String replaceRightDollar =
                SpecialStringProcessor.processVarcharWithDollarChar(replaceRight);
        return getLikeExpression(fieldStr, op, replaceRightDollar, "", false);
    }

    private String getLikeExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left = new StringBuilder();
        String key = "";
        String supportField;
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            supportField = fieldStr.substring(0, dotIndex);
            key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
        } else {
            supportField = fieldStr;
        }
        AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
        if (supportFieldEnum == null) {
            throw new UnsupportSearchException(
                    String.format("Unsupported search by field: %s", supportField));
        }
        if (key.isEmpty()) {
            left = new StringBuilder(supportFieldEnum.getSqlField() + " ");
        } else {
            if (!isI18n) {
                left = new StringBuilder(supportFieldEnum.getSqlField() + "->>'" + key + "' ");
            } else {
                if (locale.isEmpty() || "*".equals(locale)) {
                    left.append("(");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(supportFieldEnum.getSqlField())
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' ")
                                .append(op)
                                .append(" ")
                                .append(right);
                        if (i < localeList.size() - 1) {
                            left.append(" or ");
                        }
                    }
                    left.append(" or (");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(supportFieldEnum.getSqlField())
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' isnull and ");
                    }
                    left.append(supportFieldEnum.getSqlField())
                            .append(" -> '")
                            .append(key)
                            .append("' ->> '")
                            .append(Constants.DEFAULT_LANGUAGE)
                            .append("' ")
                            .append(op)
                            .append(" ")
                            .append(right)
                            .append("))");
                } else {
                    if (!localeList.contains(locale)
                            && !Constants.DEFAULT_LANGUAGE.equals(locale)) {
                        locale = Constants.DEFAULT_LANGUAGE;
                    }
                    String sqlField = supportFieldEnum.getSqlField();
                    left =
                            new StringBuilder(
                                    "("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " or ("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + Constants.DEFAULT_LANGUAGE
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " and "
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' isnull))");
                }
                return left.toString();
            }
        }
        return left + op + " " + right;
    }

    @Override
    public String visitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        String replaceRight = ExpressionUtils.likeEscape(right);
        String replaceRightDollar =
                SpecialStringProcessor.processVarcharWithDollarChar(replaceRight);
        return getLikeExpression(fieldStr, op, replaceRightDollar, locale, true);
    }

    @Override
    public String visitComparatorExpr(ConditionParser.ComparatorExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (fieldStr.equals(AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSupportField())) {
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format("SupportField: %s not support comparator: %s", fieldStr, op));
            }
            return "("
                    + AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSqlField()
                    + " in ("
                    + getModelRelationSql("(" + right + ")")
                    + ") or "
                    + AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSqlField()
                    + " in ("
                    + right
                    + "))";
        }
        return getComparatorExpression(fieldStr, op, right, "", false);
    }

    private String getModelRelationSql(String condition) {
        return "with recursive model_finder as (select m.from_model_id,m.to_model_id, 1 as steps from "
                + orgId
                + ".tbl_model_relation m where m.from_model_id in "
                + condition
                + " union select m.from_model_id,m.to_model_id, f.steps+1 as steps from "
                + orgId
                + ".tbl_model_relation m, model_finder f where m.from_model_id = f.to_model_id and steps < 10) select distinct m.from_model_id as model_id from model_finder m union select distinct m.to_model_id from model_finder m";
    }

    @Override
    public String visitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        return getComparatorExpression(fieldStr, op, right, locale, true);
    }

    private String getComparatorExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left = new StringBuilder();
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            //            if (key.contains(Constants.DOT)) {
            //                int secondDotIndex = key.indexOf(Constants.DOT);
            //                key = key.substring(0, secondDotIndex);
            //            }
            AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", supportField));
            }
            if (!PgDataTypeEnum.JSONB.getName().equals(supportFieldEnum.getDataType())) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s type not jsonb, can not search by field.key",
                                supportField));
            }
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)
                    && !CalcOperatorEnum.NEQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s not support comparator: %s", supportField, op));
            }
            boolean isNEQ = CalcOperatorEnum.NEQ.getSymbol().equals(op);
            String sqlField = supportFieldEnum.getSqlField();
            if (!isI18n) {
                left = new StringBuilder(sqlField + " ");
                op = "@> ";
                String defaultRight;
                String newRight;
                if (right.startsWith(Constants.APOSTROPHE)
                        && right.endsWith(Constants.APOSTROPHE)) {
                    right = right.substring(1, right.length() - 1);
                    right = ExpressionUtils.jsonCompareEscape(right);
                    // newRight = "'{\"" + key + "\":\"" + right + "\"}'";
                    // defaultRight = "'{\"default\":\"" + right + "\"}'";
                    newRight = SpecialStringProcessor.processJsonValueWithDollarChar(key, right);
                    defaultRight =
                            SpecialStringProcessor.processJsonValueWithDollarChar("default", right);
                } else {
                    newRight = "'{\"" + key + "\":" + right + "}'";
                    defaultRight = "'{\"default\":" + right + "}'";
                }
                right = newRight;
                if (supportFieldEnum
                        .getSupportField()
                        .equalsIgnoreCase(
                                AssetSearchSupportFieldEnum.ASSET_DISPLAY_NAME.getSupportField())) {
                    return "(("
                            + left
                            + op
                            + right
                            + ") or (not "
                            + left
                            + op
                            + right
                            + " and "
                            + left
                            + op
                            + defaultRight
                            + "))";
                }
                if (isNEQ) {
                    return "((not " + left + op + right + ") or " + left + " is null)";
                }
            } else {
                right = ExpressionUtils.varcharCompareEscape(right);
                right = SpecialStringProcessor.processVarcharWithDollarChar(right);
                if (locale.isEmpty() || "*".equals(locale)) {
                    left.append("(");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(sqlField)
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' ")
                                .append(op)
                                .append(" ")
                                .append(right);
                        if (i < localeList.size() - 1) {
                            left.append(" or ");
                        }
                    }
                    left.append(" or (");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(sqlField)
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' isnull and ");
                    }
                    left.append(supportFieldEnum.getSqlField())
                            .append(" -> '")
                            .append(key)
                            .append("' ->> '")
                            .append(Constants.DEFAULT_LANGUAGE)
                            .append("' ")
                            .append(op)
                            .append(" ")
                            .append(right)
                            .append("))");
                } else {
                    if (!localeList.contains(locale)
                            && !Constants.DEFAULT_LANGUAGE.equals(locale)) {
                        locale = Constants.DEFAULT_LANGUAGE;
                    }
                    left =
                            new StringBuilder(
                                    "("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " or ("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + Constants.DEFAULT_LANGUAGE
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " and "
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' isnull))");
                }
                return left.toString();
            }
        } else {
            AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            left = new StringBuilder(supportFieldEnum.getSqlField() + " ");
            boolean isNEQ = CalcOperatorEnum.NEQ.getSymbol().equals(op);
            op = op + " ";
            if (supportFieldEnum
                    .getSupportField()
                    .equals(AssetSearchSupportFieldEnum.ASSET_CREATE_TIME.getSupportField())) {
                Timestamp timestamp = new Timestamp(Long.parseLong(right));
                return left + op + "'" + timestamp + "'";
            }
            if (supportFieldEnum
                    .getSupportField()
                    .equals(AssetSearchSupportFieldEnum.SUB_GRAPH_ID.getSupportField())) {
                if (isNEQ) {
                    return String.format(
                            "((not %s @> '[\"%s\"]') or topo_ids is null)",
                            left, right.substring(1, right.length() - 1));
                }
                return left + "@> '[\"" + right.substring(1, right.length() - 1) + "\"]'";
            }
        }
        return left + op + right;
    }

    @Override
    public String visitIsExistsExpr(ConditionParser.IsExistsExprContext ctx) {
        StringBuilder res = new StringBuilder();
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        right = right.substring(1, right.length() - 1);
        String[] fieldStrList = right.split(",");
        for (int i = 0; i < fieldStrList.length; i++) {
            String fieldStr = fieldStrList[i];
            if (!fieldStr.contains(Constants.DOT)) {
                throw new UnsupportSearchException(
                        String.format(
                                "The exists and not exists can only be used with field.key! Field: %s",
                                fieldStr));
            }
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            String sqlField = supportFieldEnum.getSqlField();
            if (op.equals("exists")) {
                res.append("(")
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("')");
            } else {
                res.append("((not ")
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("') or ")
                        .append(sqlField)
                        .append(" is null)");
            }

            if (i < fieldStrList.length - 1) {
                res.append(" and ");
            }
        }
        return res.toString();
    }

    @Override
    public String visitBinaryExpr(ConditionParser.BinaryExprContext ctx) {
        String left = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (left.isEmpty()) {
            return right;
        }
        if (right.isEmpty()) {
            return left;
        }
        return left + " " + op + " " + right;
    }

    @Override
    public String visitInExpr(ConditionParser.InExprContext ctx) {
        String left = "";
        String op = visit(ctx.op);
        boolean isNotIn = op.equalsIgnoreCase(Constants.OPERATOR_NOT_IN);
        op = op + " ";
        String right = visit(ctx.right);
        String fieldStr = visit(ctx.left);

        if (op.equals(Constants.OPERATOR_NOT_IN)
                && !AssetSearchSupportFieldEnum.SUB_GRAPH_ID
                        .getSupportField()
                        .equalsIgnoreCase(fieldStr)) {
            throw new ParseExpressionException(
                    String.format("search asset with field: %s not support not in.", fieldStr));
        }

        if (fieldStr.equals(AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSupportField())) {
            return String.format(
                    "(%s in (%s) or %s in %s)",
                    AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSqlField(),
                    getModelRelationSql(right),
                    AssetSearchSupportFieldEnum.FROM_RELATED_MODEL_ID.getSqlField(),
                    right);
        }

        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", supportField));
            }
            if (!PgDataTypeEnum.JSONB.getName().equals(supportFieldEnum.getDataType())) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s type not jsonb, can not search by field.key",
                                supportField));
            }

            Set<Object> compareElems = ConditionParseUtil.extraParensExpr(right);
            if (compareElems.size() > USE_PG_JSONB_ARRAY_ANY_LIMIT) {
                return String.format(
                        "%s ->> '%s' %s %s",
                        supportFieldEnum.getSqlField(),
                        key,
                        op,
                        buildJsonbElementExpr(compareElems));
            } else {
                return supportFieldEnum.getSqlField()
                        + " @> "
                        + buildJsonbArrayExpr(key, compareElems);
            }
        } else {
            AssetSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            left = supportFieldEnum.getSqlField() + " ";
            op = op + " ";
            if (supportFieldEnum
                    .getSupportField()
                    .equals(AssetSearchSupportFieldEnum.SUB_GRAPH_ID.getSupportField())) {
                String[] subGraphIds = right.substring(1, right.length() - 1).split(",");
                List<String> replaceSql = new ArrayList<>();
                if (isNotIn) {
                    for (String subGraphId : subGraphIds) {
                        replaceSql.add(
                                "(not "
                                        + left
                                        + "@> '[\""
                                        + subGraphId.substring(1, subGraphId.length() - 1)
                                        + "\"]')");
                    }
                    return "(" + String.join(" and ", replaceSql) + ")";
                } else {
                    for (String subGraphId : subGraphIds) {
                        replaceSql.add(
                                left
                                        + "@> '[\""
                                        + subGraphId.substring(1, subGraphId.length() - 1)
                                        + "\"]'");
                    }
                    return "(" + String.join(" or ", replaceSql) + ")";
                }
            }
        }

        List<String> valueList = ConditionParseUtil.replaceEscape(right);
        right = String.format("(%s)", String.join(", ", valueList));
        // TODO IN ('123', 'abc') 需要循环处理
        return left + op + right;
    }

    private String buildJsonbElementExpr(Set<Object> values) {
        StringBuilder sb = new StringBuilder("(");
        int i = 1;
        for (Object v : values) {
            // NOTICE: values has been removed ''
            sb.append("'").append(v).append("'");
            if (i < values.size()) {
                sb.append(",");
            }
            i++;
        }
        sb.append(")");
        return sb.toString();
    }

    //    ANY(ARRAY[
    //            '{"key1": "value1"}'::jsonb,
    //            '{"key2": "value2"}'::jsonb,
    //            '{"key3": "value3"}'::jsonb
    //    ]);
    private String buildJsonbArrayExpr(String key, Set<Object> values) {
        StringBuilder sb = new StringBuilder("ANY(ARRAY[");
        int i = 1;
        for (Object v : values) {
            if (v instanceof String) {
                String jsonVal = ExpressionUtils.jsonCompareEscape(v.toString());
                // sb.append(String.format("'{\"%s\": \"%s\"", key, jsonVal)).append("}'::jsonb");
                sb.append(SpecialStringProcessor.processJsonValueWithDollarChar(key, jsonVal));
            } else {
                // '{"key1": 2}'::jsonb,
                sb.append(String.format("'{\"%s\": ", key)).append(v).append("}'::jsonb");
            }
            if (i < values.size()) {
                sb.append(",");
            }
            i++;
        }
        sb.append("])");
        return sb.toString();
    }

    @Override
    public String visitGraphAndExpr(ConditionParser.GraphAndExprContext ctx) {
        return visit(ctx.left) + " " + visit(ctx.op) + " " + visit(ctx.right);
    }

    @Override
    public String visitGraphAssetInRelatedModelsExpr(
            ConditionParser.GraphAssetInRelatedModelsExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = " in ";
        String modelIds = visit(ctx.right.values());
        String modelEdgeType = visit(ctx.right.modelEdgeType());
        if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphFromCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.FROM_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                                    + op
                                    + "("
                                    + getAssetInRelatedModelsSql(modelIds, modelEdgeType)
                                    + ")");
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphToCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.TO_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                                    + op
                                    + "("
                                    + getAssetInRelatedModelsSql(modelIds, modelEdgeType)
                                    + ")");
        } else {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support assetInRelatedModels.",
                            fieldStr));
        }
        return "";
    }

    private String getAssetInRelatedModelsSql(String modelIds, String modelEdgeType) {
        return "select distinct asset_id from "
                + orgId
                + Constants.TBL_BO_MODEL_TABLE_NAME
                + " b inner join "
                + orgId
                + Constants.TBL_BO_GROUP_RELATION_TABLE_NAME
                + " g on b.group_id = g.group_id where b.model_id in (with recursive model_finder as (select m.from_model_id,m.to_model_id, 1 as steps from "
                + orgId
                + Constants.TBL_MODEL_RELATION_TABLE_NAME
                + " m where m.from_model_id in ("
                + modelIds
                + ") and relation_type = '"
                + modelEdgeType
                + "' union select m.from_model_id,m.to_model_id, f.steps+1 as steps from "
                + orgId
                + Constants.TBL_MODEL_RELATION_TABLE_NAME
                + " m, model_finder f where m.from_model_id = f.to_model_id and steps < 100) select distinct m.from_model_id from model_finder m union select distinct m.to_model_id from model_finder m) or b.model_id in ("
                + modelIds
                + ")";
    }

    @Override
    public String visitGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = " in ";
        String right = visit(ctx.right);
        if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphFromCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.FROM_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                                    + op
                                    + "("
                                    + getAssetInModelsSql(right)
                                    + ")");
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphToCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.TO_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                                    + op
                                    + "("
                                    + getAssetInModelsSql(right)
                                    + ")");
        } else {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support assetInModels.",
                            fieldStr));
        }
        return "";
    }

    private String getAssetInModelsSql(String condition) {
        return "select distinct asset_id from "
                + orgId
                + Constants.TBL_BO_MODEL_TABLE_NAME
                + " b inner join "
                + orgId
                + Constants.TBL_BO_GROUP_RELATION_TABLE_NAME
                + " g on b.group_id = g.group_id where b.model_id in "
                + condition;
    }

    @Override
    public String visitGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (op.equals(Constants.OPERATOR_NOT_IN)) {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support not in.", fieldStr));
        }
        if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphSubGraphIdCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
            searchAssetWithGraphParam
                    .getGraphBaseCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphBaseCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getTable()
                                    + AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphFromCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.FROM_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphToCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.TO_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support in and not in.",
                            fieldStr));
        }
        return "";
    }

    @Override
    public String visitGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        if (!op.equals(CalcOperatorEnum.EQ.getSymbol())
                && !op.equals(CalcOperatorEnum.NEQ.getSymbol())) {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support operator: %s.",
                            fieldStr, op));
        }
        if (op.equals(CalcOperatorEnum.NEQ.getSymbol())) {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support operator: %s.",
                            fieldStr, op));
        }
        if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphSubGraphIdCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
            searchAssetWithGraphParam
                    .getGraphBaseCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.SUB_GRAPH_ID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphBaseCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getTable()
                                    + AssetGraphSearchSupportFieldEnum.EDGE_TYPE.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.FROM_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphFromCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.FROM_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.FROM_VID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.TO_VID.getSupportField())) {
            searchAssetWithGraphParam
                    .getGraphToCondition()
                    .add(
                            AssetGraphSearchSupportFieldEnum.TO_VID.getTable()
                                    + AssetGraphSearchSupportFieldEnum.TO_VID.getSqlField()
                                    + " "
                                    + op
                                    + " "
                                    + right);
        } else if (fieldStr.equals(AssetGraphSearchSupportFieldEnum.MAX_STEP.getSupportField())) {
            searchAssetWithGraphParam.setMaxStep(Integer.parseInt(right));
        } else if (fieldStr.equals(
                AssetGraphSearchSupportFieldEnum.EXCLUDE_FROM_VID.getSupportField())) {
            searchAssetWithGraphParam.setExcludeFromVid(Boolean.parseBoolean(right));
        } else if (fieldStr.equals(
                AssetGraphSearchSupportFieldEnum.EXCLUDE_TO_VID.getSupportField())) {
            searchAssetWithGraphParam.setExcludeToVid(Boolean.parseBoolean(right));
        } else {
            throw new ParseExpressionException(
                    String.format(
                            "search asset with graph by field: %s not support in and not in.",
                            fieldStr));
        }
        return "";
    }

    @Override
    public String visitGraphParenExpr(ConditionParser.GraphParenExprContext ctx) {
        visit(ctx.left);
        visit(ctx.op);
        visit(ctx.right);
        return "";
    }

    @Override
    public String visitJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx) {
        visit(ctx.joinGraph());
        visit(ctx.parenGraphExpr());
        return "";
    }

    @Override
    public String visitParenGraphExpr(ConditionParser.ParenGraphExprContext ctx) {
        return ctx.LEFT_PAREN() + visit(ctx.graphExpr()) + ctx.RIGHT_PAREN();
    }

    @Override
    public String visitJoinGraph(ConditionParser.JoinGraphContext ctx) {
        if (isWithGraph) {
            throw new ParseExpressionException("join graph only can appear once");
        }
        isWithGraph = true;
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitAssetInModels(ConditionParser.AssetInModelsContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitAnd(ConditionParser.AndContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitIsIn(ConditionParser.IsInContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitI18n(ConditionParser.I18nContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLike(ConditionParser.LikeContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitIsExists(ConditionParser.IsExistsContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitBinary(ConditionParser.BinaryContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitComparator(ConditionParser.ComparatorContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitField(ConditionParser.FieldContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitFields(ConditionParser.FieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValues(ConditionParser.ValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValue(ConditionParser.ValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitStringValue(ConditionParser.StringValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitBooleanValue(ConditionParser.BooleanValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenFields(ConditionParser.ParenFieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenValues(ConditionParser.ParenValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitTimestamp(ConditionParser.TimestampContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLeftParen(ConditionParser.LeftParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitRightParen(ConditionParser.RightParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitModelParams(ConditionParser.ModelParamsContext ctx) {
        return ctx.getText();
    }
}
