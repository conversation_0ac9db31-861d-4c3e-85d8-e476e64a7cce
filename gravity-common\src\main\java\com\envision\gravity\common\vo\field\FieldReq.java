package com.envision.gravity.common.vo.field;

import com.envision.gravity.common.vo.category.CategoryReq;

import javax.validation.constraints.NotBlank;

import java.util.Objects;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FieldReq {

    @NotBlank(message = "fieldId can not blank")
    private String fieldId;

    private String fieldName;
    private CategoryReq categoryReq;
    private String fieldDisplayName;
    private String fieldType;
    private String dataType;
    private String unit;
    private String createdUser;
    private String modifiedUser;

    public boolean metaEquals(FieldReq fieldReq) {
        return Objects.equals(fieldId, fieldReq.getFieldId())
                && Objects.equals(fieldName, fieldReq.getFieldName())
                && Objects.equals(
                        categoryReq.getCategoryId(), fieldReq.getCategoryReq().getCategoryId())
                && Objects.equals(fieldDisplayName, fieldReq.getFieldDisplayName())
                && Objects.equals(fieldType, fieldReq.getFieldType())
                && Objects.equals(dataType, fieldReq.getDataType())
                && Objects.equals(unit, fieldReq.getUnit());
    }
}
