package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/26
 * @description
 */
public class TblComponentSqlProvider {
    public String selectModelGroupList(String schemaName, List<String> compIdList) {
        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tbm.model_id = tbmc.model_id");
                        WHERE("tbmc.comp_id in ( " + compIds + " )");
                    }
                };

        return sql.toString();
    }

    public String selectModelGroupByComp(String schemaName, List<String> compIdList) {
        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tbm.model_id = tbmc.model_id");
                        INNER_JOIN(schemaName + ".tbl_component tc on tbmc.comp_id = tc.comp_id");
                        WHERE("tc.comp_id in ( " + compIds + " )");
                    }
                };

        return sql.toString();
    }
}
