package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/25
 * @description
 */
@Data
@Builder
public class TblTask {
    public static final String TABLE_NAME = "TBL_TASK";
    public static final String KEY_TYPE_NAME = "TBL_TASK_KEY";
    public static final String VALUE_TYPE_NAME = "TBL_TASK_VALUE";

    private String taskId;
    private String storageType;
    private String operationName;
    private String databaseName;
    private String tableName;
    private String requestParams;
    private String status;
    private int retryTimes;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
