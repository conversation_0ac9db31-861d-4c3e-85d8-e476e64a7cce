package com.envision.gravity.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
public enum PrefSignalType {
    AI("AI", (byte) 0),
    DI("DI", (byte) 1),
    PI("PI", (byte) 2),
    GENERIC("GENERIC", (byte) 3);

    private static final PrefSignalType[] VALUE_LIST = new PrefSignalType[4];

    static {
        for (PrefSignalType type : PrefSignalType.values()) {
            VALUE_LIST[type.id] = type;
        }
    }

    public static PrefSignalType fromId(byte id) {
        try {
            return VALUE_LIST[id];
        } catch (Exception e) {
            return null;
        }
    }

    public static PrefSignalType fromName(String name) {
        for (PrefSignalType t : PrefSignalType.values()) {
            if (Objects.equals(t.name, name)) {
                return t;
            }
        }
        return null;
    }

    private final String name;
    private final byte id;

    PrefSignalType(String name, byte id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public byte getId() {
        return id;
    }
}
