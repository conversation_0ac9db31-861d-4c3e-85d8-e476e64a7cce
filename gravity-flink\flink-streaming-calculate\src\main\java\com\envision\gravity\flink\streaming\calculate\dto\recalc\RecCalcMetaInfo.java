package com.envision.gravity.flink.streaming.calculate.dto.recalc;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.flink.streaming.calculate.dto.TblPropertyUpstreamRule;

import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RecCalcMetaInfo {

    private String orgId;

    private TblPropertyUpstreamRule ruleInfo;

    private Set<String> targetModelIds;

    private CalcPropertyMeta targetPropertyMeta;
}
