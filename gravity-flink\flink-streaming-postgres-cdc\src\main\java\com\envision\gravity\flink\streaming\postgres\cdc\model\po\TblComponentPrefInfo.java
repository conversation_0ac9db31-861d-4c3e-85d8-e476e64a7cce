package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/19
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblComponentPrefInfo implements CDCTableEntity {
    private static final long serialVersionUID = 6737537231260227293L;
    private String compId;
    private String prefId;
    private String fieldId;
    private String rawFieldId;
    private String metricId;
    private Integer fieldIndex;
    private Boolean horizontal;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
