package com.envision.gravity.flink.streaming.calculate.batch.processor;

import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.message.CalcResultMsg;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;
import com.envision.gravity.flink.streaming.calculate.sqlgateway.CalculationServiceHelper;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * ReCalcJobTaskProcessor 单元测试
 *
 * <p>测试功能： 1. 任务处理逻辑 2. Job状态检查 3. 历史数据查询 4. 计算结果输出 5. 任务完成通知
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@ExtendWith(MockitoExtension.class)
class ReCalcJobTaskProcessorTest {

    @Mock private ProcessFunction.Context mockContext;
    @Mock private Collector<CalcResultMsg> mockCollector;
    @Mock private TaskCompletionNotifier mockNotifier;
    @Mock private TblCalcJobInfoMapper mockJobInfoMapper;
    @Mock private SqlSession mockSqlSession;
    @Mock private SqlSessionFactory mockSqlSessionFactory;
    @Mock private CalculationServiceHelper mockCalculationHelper;

    private ReCalcJobTaskProcessor processor;
    private CalcJobTask testTask;

    private static final String TEST_JOB_ID = "test-job-001";
    private static final String TEST_TASK_ID = "task-001";
    private static final String TEST_ORG_ID = "o17186913277371853";

    @BeforeEach
    void setUp() {
        processor = new ReCalcJobTaskProcessor(TEST_JOB_ID);
        testTask = createTestTask();
    }

    @Test
    void testConstructorWithValidJobId() {
        // ✅ 测试构造函数参数验证
        assertNotNull(processor);

        // 测试无效参数
        assertThrows(
                IllegalArgumentException.class,
                () -> {
                    new ReCalcJobTaskProcessor(null);
                });

        assertThrows(
                IllegalArgumentException.class,
                () -> {
                    new ReCalcJobTaskProcessor("");
                });
    }

    @Test
    void testJobStatusCheck() throws Exception {
        // ✅ 测试Job状态检查
        TblCalcJobInfo mockJobInfo = createMockJobInfo();

        try (MockedStatic<CalcPGSourceConfig> configMock = mockStatic(CalcPGSourceConfig.class)) {
            configMock
                    .when(CalcPGSourceConfig::getSqlSessionFactory)
                    .thenReturn(mockSqlSessionFactory);
            when(mockSqlSessionFactory.openSession()).thenReturn(mockSqlSession);
            when(mockSqlSession.getMapper(TblCalcJobInfoMapper.class))
                    .thenReturn(mockJobInfoMapper);

            // 测试Job未取消的情况
            mockJobInfo.setStatus(ReCalcJobStatusEnum.RUNNING.getCode());
            when(mockJobInfoMapper.findByJobId(TEST_JOB_ID)).thenReturn(mockJobInfo);

            // 验证状态检查逻辑
            assertEquals(ReCalcJobStatusEnum.RUNNING.getCode(), mockJobInfo.getStatus());
            assertNotEquals(ReCalcJobStatusEnum.CANCELLED.getCode(), mockJobInfo.getStatus());

            // 测试Job已取消的情况
            mockJobInfo.setStatus(ReCalcJobStatusEnum.CANCELLED.getCode());
            assertEquals(ReCalcJobStatusEnum.CANCELLED.getCode(), mockJobInfo.getStatus());
        }
    }

    @Test
    void testTaskProcessing() throws Exception {
        // ✅ 测试任务处理逻辑
        try (MockedStatic<TaskCompletionNotifier> notifierMock =
                        mockStatic(TaskCompletionNotifier.class);
                MockedStatic<CalculationServiceHelper> helperMock =
                        mockStatic(CalculationServiceHelper.class)) {

            notifierMock
                    .when(() -> TaskCompletionNotifier.getInstance(TEST_JOB_ID))
                    .thenReturn(mockNotifier);
            helperMock
                    .when(CalculationServiceHelper::getInstance)
                    .thenReturn(mockCalculationHelper);

            // Mock任务处理
            doNothing().when(mockNotifier).notifyTaskCompleted(TEST_TASK_ID);

            // 验证任务处理调用
            mockNotifier.notifyTaskCompleted(TEST_TASK_ID);
            verify(mockNotifier, times(1)).notifyTaskCompleted(TEST_TASK_ID);
        }
    }

    @Test
    void testHistoricalDataQuery() {
        // ✅ 测试历史数据查询
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(3600L);

            // 验证时间分片配置
            assertEquals(3600L, CalcLionConfig.getReCalcJobTaskTimeSplitSeconds());

            // 测试时间范围计算
            long startTime = testTask.getTimeRange().getStartTime();
            long endTime = testTask.getTimeRange().getEndTime();
            long duration = endTime - startTime;

            assertTrue(duration > 0);
            assertTrue(startTime < endTime);
        }
    }

    @Test
    void testCalculationResultOutput() throws Exception {
        // ✅ 测试计算结果输出
        CalcResultMsg testResult =
                CalcResultMsg.builder().targetModel2MsgMap(Collections.emptyMap()).build();

        // 模拟结果收集
        doNothing().when(mockCollector).collect(testResult);

        // 验证结果输出
        mockCollector.collect(testResult);
        verify(mockCollector, times(1)).collect(testResult);
    }

    @Test
    void testTaskCompletionNotification() {
        // ✅ 测试任务完成通知
        try (MockedStatic<TaskCompletionNotifier> notifierMock =
                mockStatic(TaskCompletionNotifier.class)) {
            notifierMock
                    .when(() -> TaskCompletionNotifier.getInstance(TEST_JOB_ID))
                    .thenReturn(mockNotifier);

            // 测试成功完成通知
            doNothing().when(mockNotifier).notifyTaskCompleted(TEST_TASK_ID);
            mockNotifier.notifyTaskCompleted(TEST_TASK_ID);
            verify(mockNotifier, times(1)).notifyTaskCompleted(TEST_TASK_ID);

            // 测试失败通知
            String errorMessage = "Task processing failed";
            doNothing().when(mockNotifier).notifyTaskFailed(TEST_TASK_ID, errorMessage);
            mockNotifier.notifyTaskFailed(TEST_TASK_ID, errorMessage);
            verify(mockNotifier, times(1)).notifyTaskFailed(TEST_TASK_ID, errorMessage);
        }
    }

    @Test
    void testAssetIdProcessing() {
        // ✅ 测试资产ID处理
        List<String> testAssetIds = Arrays.asList("asset-001", "asset-002", "asset-003");
        testTask.setTargetAssetIds(testAssetIds);

        assertNotNull(testTask.getTargetAssetIds());
        assertEquals(3, testTask.getTargetAssetIds().size());
        assertTrue(testTask.getTargetAssetIds().contains("asset-001"));
    }

    @Test
    void testErrorHandling() {
        // ✅ 测试错误处理
        try (MockedStatic<TaskCompletionNotifier> notifierMock =
                mockStatic(TaskCompletionNotifier.class)) {
            notifierMock
                    .when(() -> TaskCompletionNotifier.getInstance(TEST_JOB_ID))
                    .thenReturn(mockNotifier);

            // 模拟处理异常
            RuntimeException testException = new RuntimeException("Test error");
            String errorMessage = testException.getMessage();

            doNothing().when(mockNotifier).notifyTaskFailed(TEST_TASK_ID, errorMessage);

            // 验证错误处理
            mockNotifier.notifyTaskFailed(TEST_TASK_ID, errorMessage);
            verify(mockNotifier, times(1)).notifyTaskFailed(TEST_TASK_ID, errorMessage);
        }
    }

    @Test
    void testJobStatusEnumValidation() {
        // ✅ 测试Job状态枚举验证
        assertTrue(
                ReCalcJobStatusEnum.getByCode(ReCalcJobStatusEnum.PENDING.getCode()).isPresent());
        assertTrue(
                ReCalcJobStatusEnum.getByCode(ReCalcJobStatusEnum.RUNNING.getCode()).isPresent());
        assertTrue(
                ReCalcJobStatusEnum.getByCode(ReCalcJobStatusEnum.COMPLETED.getCode()).isPresent());
        assertTrue(
                ReCalcJobStatusEnum.getByCode(ReCalcJobStatusEnum.CANCELLED.getCode()).isPresent());

        // 测试无效状态码
        assertFalse(ReCalcJobStatusEnum.getByCode(-1).isPresent());
        assertFalse(ReCalcJobStatusEnum.getByCode(999).isPresent());
    }

    private CalcJobTask createTestTask() {
        return CalcJobTask.builder()
                .taskId(TEST_TASK_ID)
                .jobId(TEST_JOB_ID)
                .targetAssetIds(Arrays.asList("asset-001", "asset-002"))
                .timeRange(
                        com.envision.gravity.flink.streaming.calculate.dto.calc.TimeRange.builder()
                                .startTime(System.currentTimeMillis() - 3600000)
                                .endTime(System.currentTimeMillis())
                                .build())
                .build();
    }

    private TblCalcJobInfo createMockJobInfo() {
        TblCalcJobInfo jobInfo = new TblCalcJobInfo();
        jobInfo.setJobId(TEST_JOB_ID);
        jobInfo.setSrcOrgId(TEST_ORG_ID);
        jobInfo.setStatus(ReCalcJobStatusEnum.RUNNING.getCode());
        jobInfo.setStartTime(System.currentTimeMillis() - 3600000);
        jobInfo.setEndTime(System.currentTimeMillis());
        return jobInfo;
    }
}
