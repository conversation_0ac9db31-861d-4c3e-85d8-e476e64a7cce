package com.envision.gravity;

import com.envision.gravity.common.util.IgniteUtil;

import javax.cache.expiry.Duration;
import javax.cache.expiry.ModifiedExpiryPolicy;

import java.util.*;
import java.util.concurrent.TimeUnit;


import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.client.ClientCacheConfiguration;

public class CreateSQL {
    public static void main(String[] args) {

        Map<String, String> config = parseArgs(args);
        int timeout = 60;
        String region = "InMemory_Region";
        if (config.get("timeout") != null) {
            timeout = Integer.parseInt(config.get("timeout"));
        }

        if (config.get("region") != null) {
            region = config.get("region");
        }

        ClientCacheConfiguration cfg = new ClientCacheConfiguration();
        cfg.setExpiryPolicy(new ModifiedExpiryPolicy(new Duration(TimeUnit.SECONDS, timeout)));
        cfg.setName("GRAVITY_SCHEMA_HASHCODE");
        cfg.setSqlSchema("GRAVITY");
        cfg.setDataRegionName(region);

        QueryEntity queryEntity =
                new QueryEntity("GRAVITY_SCHEMA_HASHCODE_KEY", "GRAVITY_SCHEMA_HASHCODE_VAL");
        queryEntity.setTableName("SCHEMA_HASHCODE");

        Set<String> keySet = new TreeSet<>();
        keySet.add("DATABASE");
        keySet.add("SIZE");
        keySet.add("HASH1");
        keySet.add("HASH2");

        queryEntity.setKeyFields(keySet);

        LinkedHashMap<String, String> fields = new LinkedHashMap<>();
        fields.put("DATABASE", "java.lang.String");
        fields.put("SIZE", "java.lang.Integer");
        fields.put("HASH1", "java.lang.Integer");
        fields.put("HASH2", "java.lang.Integer");
        fields.put("HASH3", "java.lang.Integer");

        queryEntity.setFields(fields);
        cfg.setQueryEntities(queryEntity);

        IgniteUtil.getIgniteClient().createCache(cfg);
        IgniteUtil.close();
        System.out.println("create GRAVITY_SCHEMA_HASHCODE suc.");
    }

    private static Map<String, String> parseArgs(String[] args) {
        Map<String, String> result = new HashMap<>();

        for (String arg : args) {
            if (arg.startsWith("--") && arg.contains("=")) {
                int idx = arg.indexOf('=');
                String key = arg.substring(2, idx); // 去掉 "--"
                String value = arg.substring(idx + 1);
                result.put(key, value);
            } else {
                System.err.println("Invalid argument format: " + arg);
            }
        }

        return result;
    }
}
