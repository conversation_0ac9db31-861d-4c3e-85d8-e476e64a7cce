package com.envision.gravity.ignite.core.store.jdbc.dialect;

import java.util.Collection;


import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.internal.util.typedef.C1;
import org.apache.ignite.internal.util.typedef.F;

/**
 * <AUTHOR>
 * @date 2024/6/3
 * @description
 */
public class PostgreSQLDialect extends BasicJdbcDialect {
    private static final long serialVersionUID = 0L;

    @Override
    public boolean hasMerge() {
        return true;
    }

    /** {@inheritDoc} */
    @Override
    public String mergeQuery(
            String fullTblName, Collection<String> keyCols, Collection<String> uniqCols) {
        Collection<String> cols = F.concat(false, keyCols, uniqCols);

        String updPart =
                mkString(
                        uniqCols,
                        (C1<String, String>) col -> String.format("%s = excluded.%s", col, col),
                        "",
                        ", ",
                        "");

        return String.format(
                "INSERT INTO %s (%s) VALUES (%s) ON CONFLICT (%s) DO UPDATE SET %s",
                fullTblName,
                mkString(cols, ", "),
                repeat("?", cols.size(), "", ",", ""),
                mkString(keyCols, ", "),
                updPart);
    }
}
