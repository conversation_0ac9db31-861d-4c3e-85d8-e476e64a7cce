package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.obj.entity.LatestMeasurePointEntity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class LatestMeasurePointEntityLocal {
    private String rowFieldId;
    private Integer fieldIndex;
    private String stringValue;
    private Double doubleValue;
    private Long longValue;
    private Boolean boolValue;
    private Long time;
    private Long quality;
    private Boolean horizontal;
    private Boolean alreadyChanged;
    private Long prePointTime;

    public static LatestMeasurePointEntity toLatestMeasurePointEntity(
            LatestMeasurePointEntityLocal local) {
        Object value = null;
        if (local.getStringValue() != null) {
            value = local.getStringValue();
        } else if (local.getDoubleValue() != null) {
            value = local.getDoubleValue();
        } else if (local.getLongValue() != null) {
            value = local.getLongValue();
        } else if (local.getBoolValue() != null) {
            value = local.getBoolValue();
        }

        return new LatestMeasurePointEntity(
                local.getRowFieldId(),
                local.getFieldIndex(),
                value,
                local.getTime(),
                local.getQuality(),
                local.getHorizontal(),
                local.getAlreadyChanged(),
                local.getPrePointTime());
    }
}
