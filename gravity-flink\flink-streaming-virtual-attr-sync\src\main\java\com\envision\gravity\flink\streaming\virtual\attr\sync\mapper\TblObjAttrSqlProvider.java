package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @description
 */
public class TblObjAttrSqlProvider {
    public String queryAttrValue(String schemeName, String fieldId, List<String> systemIdList) {
        String systemIds =
                systemIdList.stream()
                        .distinct()
                        .map(systemId -> "'" + systemId + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT(
                                "system_id,\n"
                                        + "       field_id,\n"
                                        + "       coalesce(value_bool::text, value_long::text, value_double::text,\n"
                                        + "                value_string::text, value_json::text) as attr_value");
                        FROM(schemeName + ".tbl_obj_attr");
                        WHERE(
                                String.format(
                                        "field_id = '%s' and system_id in (%s)",
                                        fieldId, systemIds));
                    }
                };

        return sql.toString();
    }
}
