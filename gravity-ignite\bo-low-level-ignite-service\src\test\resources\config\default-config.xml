<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation=" http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd ">
    <bean class="org.apache.ignite.configuration.IgniteConfiguration" id="ignite.cfg">
        <property name="consistentId" value="ignite-node-gravity-vm-0001"/>
        <property name="workDirectory" value="/opt/ignite/apache-ignite/work"/>
        <property name="snapshotPath" value="/opt/ignite/apache-ignite/snapshots"/>
        <property name="clientMode" value="false"/>
        <property name="networkTimeout" value="120000"/>
        <!--  失败检测 超时时长  -->
        <property name="failureDetectionTimeout" value="#{60 * 60 * 1000}"/>
        <!--   服务worker 之间交互 timeout 时间，默认 10s   -->
        <property name="systemWorkerBlockedTimeout" value="#{60 * 60 * 1000}"/>
        <!--   服务出现故障自动重启   -->
        <property name="failureHandler">
            <bean class="org.apache.ignite.failure.RestartProcessFailureHandler"/>
        </property>
        <!--   公共线程池负责Ignite的计算网格，所有的计算任务都由这个线程池接收然后处理   -->
        <property name="publicThreadPoolSize" value="128"/>
        <!--   系统线程池处理所有与缓存相关的操作   -->
        <property name="systemThreadPoolSize" value="128"/>
        <!--   查询线程池处理集群内所有的SQL、扫描和SPI查询   -->
        <property name="queryThreadPoolSize" value="128"/>
        <!--   Enable peer class loading.   -->
        <property name="peerClassLoadingEnabled" value="true"/>
        <!--   Set deployment mode.   -->
        <property name="deploymentMode" value="CONTINUOUS"/>
        <!--   禁用丢失资源缓存   -->
        <property name="peerClassLoadingMissedResourcesCacheSize" value="0"/>
        <property name="authenticationEnabled" value="true"/>
        <!--   Set batch size.   -->
        <property name="rebalanceBatchSize" value="#{1 * 1024 * 1024}"/>
        <!--   Set throttle interval.   -->
        <property name="rebalanceThrottle" value="100"/>
        <!--   <property name="rebalanceThreadPoolSize" value="4"/>   -->
        <property name="rebalanceTimeout" value="#{120 * 1000L}"/>
        <!--   SQL Engine   -->
        <property name="sqlConfiguration">
            <bean class="org.apache.ignite.configuration.SqlConfiguration">
                <property name="defaultQueryTimeout" value="120000"/>
                <property name="sqlSchemas">
                    <list>
                        <value>GRAVITY</value>
                    </list>
                </property>
                <property name="queryEnginesConfiguration">
                    <list>
                        <bean class="org.apache.ignite.indexing.IndexingQueryEngineConfiguration">
                            <property name="default" value="true"/>
                        </bean>
                        <bean class="org.apache.ignite.calcite.CalciteQueryEngineConfiguration">
                            <property name="default" value="false"/>
                            <property name="globalMemoryQuota" value="#{ 10 * 1024L * 1024 * 1024 }"/>
                            <property name="queryMemoryQuota" value="#{ 2 * 1024L * 1024 * 1024 }"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>
        <property name="transactionConfiguration">
            <bean class="org.apache.ignite.configuration.TransactionConfiguration">
                <!-- Set the timeout to 5 minutes -->
                <property name="TxTimeoutOnPartitionMapExchange" value="300000"/>
                <property name="defaultTxTimeout" value="300000"/>
            </bean>
        </property>
        <property name="metricExporterSpi">
            <list>
                <bean class="org.apache.ignite.spi.metric.jmx.JmxMetricExporterSpi"/>
            </list>
        </property>
        <property name="dataStorageConfiguration">
            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">
                <property name="defaultWarmUpConfiguration">
                    <bean class="org.apache.ignite.configuration.LoadAllWarmUpConfiguration"/>
                </property>
                <property name="pageSize" value="#{4 * 1024}"/>
                <!--  FSYNC | LOG_ONLY | BACKGROUND | NONE  -->
                <property name="walMode" value="LOG_ONLY"/>
                <!--  disable wal archive  -->
                <property name="walPath" value="/opt/ignite/apache-ignite/wal"/>
                <property name="walArchivePath" value="/opt/ignite/apache-ignite/wal"/>
                <property name="maxWalArchiveSize" value="#{ 20 * 1024 * 1024L * 1024}"/>
                <property name="walSegmentSize" value="#{ 512 * 1024 * 1024}"/>
                <property name="checkpointFrequency" value="#{24 * 60 * 60 * 1000L}"/>
                <property name="checkpointThreads" value="1"/>
                <property name="writeThrottlingEnabled" value="true"/>
                <property name="metricsEnabled" value="true"/>
                <property name="defaultDataRegionConfiguration">
                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                        <property name="name" value="Default_Region"/>
                        <property name="initialSize" value="#{ 10 * 1024L * 1024L * 1024}"/>
                        <property name="maxSize" value="#{ 10 * 1024L * 1024 * 1024}"/>
                        <property name="persistenceEnabled" value="true"/>
                        <property name="checkpointPageBufferSize" value="#{ 512 * 1024L * 1024}"/>
                        <property name="metricsEnabled" value="true"/>
                    </bean>
                </property>
                <property name="dataRegionConfigurations">
                    <list>
                        <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                            <property name="name" value="InMemory_Region"/>
                            <property name="initialSize" value="#{ 2 * 1024L * 1024 * 1024}"/>
                            <property name="maxSize" value="#{ 2 * 1024L * 1024 * 1024}"/>
                            <property name="persistenceEnabled" value="false"/>
                            <property name="metricsEnabled" value="false"/>
                            <property name="pageEvictionMode" value="RANDOM_2_LRU"/>
                        </bean>
                        <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                            <property name="name" value="BO_InMemory_Region"/>
                            <property name="initialSize" value="#{ 6 * 1024L * 1024 * 1024}"/>
                            <property name="maxSize" value="#{ 6 * 1024L * 1024 * 1024}"/>
                            <property name="persistenceEnabled" value="false"/>
                            <property name="metricsEnabled" value="false"/>
                            <property name="pageEvictionMode" value="DISABLED"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>
        <property name="discoverySpi">
            <bean class="org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi">
                <property name="localAddress" value="gravity-vm-0001.eniot.io"/>
                <!--  Initial local port to listen to.  -->
                <property name="localPort" value="47500"/>
                <property name="ipFinder">
                    <bean class="org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder">
                        <property name="addresses">
                            <list>
                                <value>gravity-vm-0001.eniot.io:47500</value>
                                <value>gravity-vm-0002.eniot.io:47500</value>
                                <value>gravity-vm-0003.eniot.io:47500</value>
                            </list>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>
        <property name="communicationSpi">
            <bean class="org.apache.ignite.spi.communication.tcp.TcpCommunicationSpi">
                <property name="messageQueueLimit" value="512"/>
                <property name="slowClientQueueLimit" value="256"/>
                <property name="idleConnectionTimeout" value="3600000"/>
                <property name="sharedMemoryPort" value="-1"/>
            </bean>
        </property>
        <property name="lifecycleBeans">
            <list>
                <bean class="com.envision.gravity.low.level.api.sql.service.impl.BOLowLevelServiceImpl"/>
            </list>
        </property>
        <property name="clientConnectorConfiguration">
            <bean class="org.apache.ignite.configuration.ClientConnectorConfiguration">
                <property name="thinClientEnabled" value="true"/>
                <property name="port" value="10800"/>
                <property name="portRange" value="500"/>
                <property name="sslEnabled" value="false"/>
                <property name="threadPoolSize" value="256"/>
                <property name="maxOpenCursorsPerConnection" value="10240"/>
            </bean>
        </property>
        <property name="serviceConfiguration">
            <list>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="TSDBMetaService"/>
                    <property name="service">
                        <bean class="com.envision.gravity.ignite.tsdb.TSDBMetaServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="BOLowLevelIgniteService"/>
                    <property name="service">
                        <bean class="com.envision.gravity.low.level.api.sql.service.impl.BOLowLevelServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="createCacheService"/>
                    <property name="service">
                        <bean class="com.eniot.tableengine.service.impl.CreateCacheServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="getCacheStoreFactoryService"/>
                    <property name="service">
                        <bean class="com.eniot.metricengine.service.impl.GetCacheStoreFactoryServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="loadCacheService"/>
                    <property name="service">
                        <bean class="com.eniot.metricengine.service.impl.LoadCacheServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="updateTblObjService"/>
                    <property name="service">
                        <bean class="com.envision.gravity.obj.UpdateObjService"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
            </list>
        </property>
        <property name="cacheConfiguration">
            <list>
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="Gravity_UDF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="sqlFunctionClasses" value="com.envision.gravity.ignite.udf.GravitySqlFunctions"/>
                    <property name="sqlSchema" value="GRAVITY"/>
                </bean>
            </list>
        </property>
    </bean>
    <bean class="org.postgresql.ds.PGSimpleDataSource" id="postgresDataSourceGravity">
        <property name="URL" value="*************************************************************************"/>
        <property name="user" value="gravity_user"/>
        <property name="password" value="3mGU4ueRmkEv$%jvfgKn"/>
    </bean>
</beans>