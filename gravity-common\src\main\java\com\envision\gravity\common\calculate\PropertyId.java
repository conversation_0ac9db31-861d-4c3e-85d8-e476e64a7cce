package com.envision.gravity.common.calculate;


import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PropertyId {
    private String modelId;

    private String compId;

    private String prefId;

    public PropertyId(String compId, String prefId) {
        this.compId = compId;
        this.prefId = prefId;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        PropertyId other = (PropertyId) obj;

        // Compare modelId
        if (modelId == null) {
            if (other.modelId != null) {
                return false;
            }
        } else if (!modelId.equals(other.modelId)) {
            return false;
        }

        // Compare compId
        if (compId == null) {
            if (other.compId != null) {
                return false;
            }
        } else if (!compId.equals(other.compId)) {
            return false;
        }

        // Compare prefId
        if (prefId == null) {
            if (other.prefId != null) {
                return false;
            }
        } else if (!prefId.equals(other.prefId)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;

        result = prime * result + ((modelId == null) ? 0 : modelId.hashCode());
        result = prime * result + ((compId == null) ? 0 : compId.hashCode());
        result = prime * result + ((prefId == null) ? 0 : prefId.hashCode());

        return result;
    }
}
