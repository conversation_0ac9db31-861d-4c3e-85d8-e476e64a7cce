package com.envision.gravity.flink.streaming.calculate.dto.query;

import java.io.Serializable;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间序列查询实体
 *
 * <p>用于： 1. ReCalcJobTaskProcessor 的历史数据范围查询 2. CalculationServiceHelper.queryTSValues() 方法参数
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TSQueryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 组织ID */
    private String orgId;

    /** 模型ID */
    private String modelId;

    /** 资产ID列表 */
    private List<String> assetIds;

    /** 属性名称列表 */
    private List<String> propertyNames;

    /** 开始时间（毫秒时间戳） */
    private long startTime;

    /** 结束时间（毫秒时间戳） */
    private long endTime;

    /** 查询限制（可选） */
    private Integer limit;

    /** 检查是否为有效的查询实体 */
    public boolean isValid() {
        return orgId != null
                && !orgId.isEmpty()
                && modelId != null
                && !modelId.isEmpty()
                && assetIds != null
                && !assetIds.isEmpty()
                && propertyNames != null
                && !propertyNames.isEmpty()
                && startTime > 0
                && endTime > startTime;
    }

    /** 获取时间范围（毫秒） */
    public long getTimeRangeMs() {
        return endTime - startTime;
    }

    /** 获取时间范围（秒） */
    public long getTimeRangeSeconds() {
        return getTimeRangeMs() / 1000;
    }

    @Override
    public String toString() {
        return String.format(
                "TSQueryEntity{orgId='%s', modelId='%s', assets=%d, properties=%d, "
                        + "timeRange=%d-%d (%ds)}",
                orgId,
                modelId,
                assetIds != null ? assetIds.size() : 0,
                propertyNames != null ? propertyNames.size() : 0,
                startTime,
                endTime,
                getTimeRangeSeconds());
    }
}
