package com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula;

import com.envision.gravity.common.exception.BadRequestException;
import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.pool.EnvisionSessionWrapper;
import com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.pool.NebulaSessionPool;

import java.util.List;


import com.vesoft.nebula.client.graph.data.ResultSet;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
@Slf4j
public class NebulaGraphExecutor {

    private static final NebulaSessionPool NEBULA_SESSION_POOL = NebulaSessionPool.getInstance();

    public void deleteVertices(String spaceName, List<String> vidList) {
        if (spaceName == null || spaceName.isEmpty()) {
            throw new BadRequestException("SpaceName is null!");
        }
        if (vidList.isEmpty()) {
            return;
        }

        StringBuilder nGql = new StringBuilder("use " + spaceName + "; " + "delete vertex ");
        for (int i = 0; i < vidList.size(); i++) {
            nGql.append("\"").append(vidList.get(i)).append("\"");

            if (i < vidList.size() - 1) {
                nGql.append(", ");
            } else {
                nGql.append(" WITH EDGE;");
            }
        }

        log.debug("Execute ngql:{}", nGql);

        EnvisionSessionWrapper session = null;
        try {
            session = NEBULA_SESSION_POOL.getSession(spaceName);
            ResultSet resultSet = session.execute(nGql.toString());
            if (resultSet.getErrorCode() != 0) {
                log.error(
                        "BatchDeleteVertex failed. nGQL: {}. errorCode: {}. errorMsg: {}",
                        nGql,
                        resultSet.getErrorCode(),
                        resultSet.getErrorMessage());
                throw new GravityRuntimeException("BatchDeleteVertex failed.");
            }
        } catch (Exception e) {
            log.error("Batch delete vertex error.", e);
            throw new GravityRuntimeException("Batch delete vertex error.", e);
        } finally {
            if (session != null) {
                NEBULA_SESSION_POOL.returnSession(session, spaceName);
            }
        }
    }
}
