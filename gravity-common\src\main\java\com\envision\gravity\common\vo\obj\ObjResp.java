package com.envision.gravity.common.vo.obj;

import com.envision.gravity.common.vo.field.FieldResp;

import java.sql.Timestamp;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ObjResp {

    private String systemId;
    private String systemDisplayName;
    private String categoryId;
    private Map<String, FieldResp> fields;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
