package com.envision.gravity.flink.streaming.calculate.batch;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;

import java.util.Arrays;
import java.util.List;


import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReCalc Batch Job - Temporary Word Count Implementation This is a placeholder implementation for
 * integration testing Will be replaced with actual calculation logic later
 *
 * <AUTHOR>
 */
public class SimpleFlinkBatchJob {

    private static final Logger logger = LoggerFactory.getLogger(SimpleFlinkBatchJob.class);

    public static void main(String[] args) throws Exception {
        // Parse parameters
        ParameterTool params = ParameterTool.fromArgs(args);
        String jobId = params.get("jobId");

        // Update job status to RUNNING
        updateJobStatus(jobId, ReCalcJobStatusEnum.RUNNING, "started_time");

        try {
            // Execute the batch job
            executeBatchJob(jobId);

            // Update job status to FINISHED
            updateJobStatus(jobId, ReCalcJobStatusEnum.FINISHED, "finished_time");

            logger.info("ReCalc Batch Job completed successfully: jobId={}", jobId);

        } catch (Exception e) {
            logger.error("ReCalc Batch Job failed: jobId={}, error={}", jobId, e.getMessage(), e);

            // Update job status to FAILED
            updateJobStatus(jobId, ReCalcJobStatusEnum.FAILED, "finished_time");

            throw e;
        }
    }

    /** Execute the actual batch job logic Currently implements a simple word count for testing */
    private static void executeBatchJob(String jobId) throws Exception {
        // Get execution environment
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        // Set parallelism from parameters or config
        env.setParallelism(2);

        logger.info("Batch job parallelism set to: {}", env.getParallelism());

        // Create sample data for word count (simulating calculation input)
        // In real implementation, this would be replaced with actual data source queries
        List<String> sampleData = generateSampleCalculationData();

        logger.info("Processing {} sample data records for word count", sampleData.size());

        // Create DataSet from sample data
        DataSet<String> text = env.fromCollection(sampleData);

        // Word count processing (simulating calculation logic)
        DataSet<Tuple2<String, Integer>> wordCounts =
                text.flatMap(new WordSplitter()).groupBy(0).reduce(new WordCounter());

        // Execute the job with a discarding sink (required for Flink batch jobs)
        // In real implementation, this would write results to database/kafka
        logger.info("Executing word count calculation for job: {}", jobId);

        // Add a discarding sink to satisfy Flink's requirement (works in detached mode)
        // This avoids file system permission issues
        wordCounts.output(
                new org.apache.flink.api.common.io.OutputFormat<Tuple2<String, Integer>>() {
                    @Override
                    public void configure(
                            org.apache.flink.configuration.Configuration parameters) {}

                    @Override
                    public void open(int taskNumber, int numTasks) {}

                    @Override
                    public void writeRecord(Tuple2<String, Integer> record) {
                        // Discard the record - this is just for testing
                        logger.debug("Processed word count: {} = {}", record.f0, record.f1);
                    }

                    @Override
                    public void close() {}
                });

        // Execute the job
        env.execute("ReCalc Batch Job - " + jobId);

        // Simulate calculation processing time
        int processingTimeMs = 2000; // Fixed time for testing
        logger.info("Simulating calculation processing time: {}ms", processingTimeMs);
        Thread.sleep(processingTimeMs);

        logger.info("Batch job execution completed for jobId: {}", jobId);
    }

    /** Generate sample calculation data for word count testing */
    private static List<String> generateSampleCalculationData() {
        return Arrays.asList(
                "ReCalc batch job ",
                "Batch processing calculation results for job",
                "Data transformation and calculation for rule");
    }

    /** Update job status in database */
    private static void updateJobStatus(
            String jobId, ReCalcJobStatusEnum status, String timeField) {
        try {
            SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
            try (SqlSession sqlSession = sqlSessionFactory.openSession(true)) {
                TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);

                long currentTime = System.currentTimeMillis();
                int result =
                        mapper.updateStatusWithTime(
                                jobId, status.getCode(), timeField, currentTime);

                if (result > 0) {
                    logger.info(
                            "Updated job status: jobId={}, status={}, timeField={}",
                            jobId,
                            status,
                            timeField);
                } else {
                    logger.warn("Failed to update job status: jobId={}, status={}", jobId, status);
                }
            }
        } catch (Exception e) {
            logger.error(
                    "Database error while updating job status: jobId={}, status={}",
                    jobId,
                    status,
                    e);
        }
    }

    /**
     * Word splitter function for word count In real implementation, this would be replaced with
     * data parsing and transformation logic
     */
    public static class WordSplitter implements FlatMapFunction<String, Tuple2<String, Integer>> {
        @Override
        public void flatMap(String sentence, Collector<Tuple2<String, Integer>> out) {
            // Split sentence into words and emit each word with count 1
            for (String word : sentence.toLowerCase().split("\\W+")) {
                if (word.length() > 2) { // Filter out very short words
                    out.collect(new Tuple2<>(word, 1));
                }
            }
        }
    }

    /**
     * Word counter function for word count In real implementation, this would be replaced with
     * calculation aggregation logic
     */
    public static class WordCounter implements ReduceFunction<Tuple2<String, Integer>> {
        @Override
        public Tuple2<String, Integer> reduce(
                Tuple2<String, Integer> a, Tuple2<String, Integer> b) {
            // Sum up the counts for the same word
            return new Tuple2<>(a.f0, a.f1 + b.f1);
        }
    }
}
