package com.envision.gravity.calculate.api.rest.exception;

public class CalcRuntimeException extends RuntimeException {
    private static final long serialVersionUID = -881483906148352379L;

    public CalcRuntimeException() {
        super();
    }

    public CalcRuntimeException(String message) {
        super(message);
    }

    public CalcRuntimeException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public CalcRuntimeException(Throwable throwable) {
        super(throwable);
    }
}
