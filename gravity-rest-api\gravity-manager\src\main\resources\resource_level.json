{"tsdbResourceCfg": {"influxdbBucketRetention": "180d"}, "rateLimitCfgList": [{"group": "GRAVITY", "consumerClientId": "cds-adapter", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "model", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "dcm", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "tsdb-adapter", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "gravity", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "tag", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "data-inbound", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "default", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "demo", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}, {"group": "GRAVITY", "consumerClientId": "model-fuzzy-search", "resource": "default", "type": "CONCURRENCY", "threshold": 10, "rejectPolicy": "LOG", "downloadPolicy": "SILENT", "status": true}]}