package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblEdge;
import com.envision.gravity.common.po.TblStartVid;
import com.envision.gravity.common.pojo.EdgeProperty;
import com.envision.gravity.common.vo.topo.EdgeDeleteParam;
import com.envision.gravity.low.level.api.rest.enums.Constants;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/** @Author: qi.jiang2 @Date: 2024/04/11 15:59 @Description: */
public class TblEdgeSqlProvider {

    public String selectNeedDeleteStartVidByEdges(List<TblEdge> needDeleteEdges, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (TblEdge tblEdge : needDeleteEdges) {
            replace.add(
                    "(sub_graph_id = '"
                            + tblEdge.getSubGraphId()
                            + "' and to_vid = '"
                            + tblEdge.getFromVid()
                            + "')");
        }
        sql.WHERE(String.join(" or ", replace));
        return sql.toString();
    }

    public String selectNeedAddStartVid(List<String> subGraphIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            replace.add(
                    "(sub_graph_id = '"
                            + subGraphId
                            + "' and not from_vid in (select to_vid from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " where sub_graph_id='"
                            + subGraphId
                            + "'))");
        }
        sql.WHERE(String.join(" or ", replace));
        return sql.toString();
    }

    public String selectNeedDeleteStartVid(List<TblStartVid> tblStartVids, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (TblStartVid tblStartVid : tblStartVids) {
            replace.add(
                    "(sub_graph_id = '"
                            + tblStartVid.getSubGraphId()
                            + "' and to_vid = '"
                            + tblStartVid.getStartVid()
                            + "')");
        }
        sql.WHERE(String.join(" or ", replace));
        return sql.toString();
    }

    public String selectBySubGraphIds(List<String> subGraphIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            placeHolder.add("'" + subGraphId + "'");
        }
        sql.WHERE("sub_graph_id in (" + String.join(", ", placeHolder) + ")");
        return sql.toString();
    }

    public String selectExistsSubGraphId(List<String> subGraphIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            placeHolder.add("'" + subGraphId + "'");
        }
        sql.WHERE("sub_graph_id in (" + String.join(", ", placeHolder) + ")");
        return sql.toString();
    }

    public String selectExistsEdges(List<EdgeDeleteParam> edgeProperties, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (EdgeDeleteParam edgeDeleteParam : edgeProperties) {
            String subCondition =
                    String.format(
                            "(sub_graph_id = '%s' and edge_type_id = '%s'",
                            edgeDeleteParam.getSubGraphId(), edgeDeleteParam.getEdgeType());
            if (!StringUtils.isEmpty(edgeDeleteParam.getFromVid())) {
                subCondition += String.format(" and from_vid = '%s'", edgeDeleteParam.getFromVid());
            }
            if (edgeDeleteParam.getToVid() != null && !edgeDeleteParam.getToVid().isEmpty()) {
                subCondition += String.format(" and to_vid = '%s'", edgeDeleteParam.getToVid());
            }
            subCondition += ")";
            placeHolder.add(subCondition);
        }
        sql.WHERE(String.join(" or ", placeHolder));
        return sql.toString();
    }

    public String selectInEdgesByToVidList(
            List<String> toVidList, String subGraphId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String toVid : toVidList) {
            placeHolder.add("'" + toVid + "'");
        }
        sql.WHERE(
                "sub_graph_id = '"
                        + subGraphId
                        + "' and to_vid in ("
                        + String.join(", ", placeHolder)
                        + ")");
        return sql.toString();
    }

    public String selectEdgesByToVidAndSubGraphIdExcludeDeleteEdges(
            List<String> toVids, List<EdgeProperty> deleteEdges, String subGraphId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String toVid : toVids) {
            placeHolder.add("'" + toVid + "'");
        }
        String where =
                "(sub_graph_id = '"
                        + subGraphId
                        + "' and to_vid in ("
                        + String.join(", ", placeHolder)
                        + "))";
        if (deleteEdges != null && !deleteEdges.isEmpty()) {
            List<String> deleteCondition = new ArrayList<>();
            for (EdgeProperty edgeProperty : deleteEdges) {
                deleteCondition.add(
                        "(sub_graph_id ='"
                                + edgeProperty.getSubGraphId()
                                + "' and from_vid ='"
                                + edgeProperty.getFromVid()
                                + "' and to_vid ='"
                                + edgeProperty.getToVid()
                                + "' and edge_type_id ='"
                                + edgeProperty.getEdgeType()
                                + "')");
            }
            where += " and not (" + String.join(" or ", deleteCondition) + ")";
        }

        sql.WHERE(where);
        return sql.toString();
    }

    public String selectOutEdgesByToVidList(
            List<String> toVidList, String subGraphId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String toVid : toVidList) {
            placeHolder.add("'" + toVid + "'");
        }
        sql.WHERE(
                "sub_graph_id = '"
                        + subGraphId
                        + "' and from_vid in ("
                        + String.join(", ", placeHolder)
                        + ")");
        return sql.toString();
    }

    public String selectEdgesByToVidAndSubGraphId(
            Map<String, List<String>> subGraphIdToVidMap,
            List<EdgeProperty> deleteEdges,
            String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : subGraphIdToVidMap.entrySet()) {
            String subGraphId = entry.getKey();
            List<String> replaceToVidList = new ArrayList<>();
            for (String toVid : entry.getValue()) {
                replaceToVidList.add("'" + toVid + "'");
            }
            placeHolder.add(
                    "(sub_graph_id = '"
                            + subGraphId
                            + "' and to_vid in ("
                            + String.join(", ", replaceToVidList)
                            + "))");
        }

        String where = String.join(" or ", placeHolder);
        if (deleteEdges != null && !deleteEdges.isEmpty()) {
            List<String> deleteCondition = new ArrayList<>();
            for (EdgeProperty edgeProperty : deleteEdges) {
                deleteCondition.add(
                        "(sub_graph_id ='"
                                + edgeProperty.getSubGraphId()
                                + "' and from_vid ='"
                                + edgeProperty.getFromVid()
                                + "' and to_vid ='"
                                + edgeProperty.getToVid()
                                + "' and edge_type_id ='"
                                + edgeProperty.getEdgeType()
                                + "')");
            }
            where += " and not (" + String.join(" or ", deleteCondition) + ")";
        }

        sql.WHERE(where);
        return sql.toString();
    }

    public String selectEdgesByFromVidAndSubGraphId(
            Map<String, List<String>> subGraphIdFromVidMap,
            List<EdgeProperty> deleteEdges,
            String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : subGraphIdFromVidMap.entrySet()) {
            String subGraphId = entry.getKey();
            List<String> replaceFromVidList = new ArrayList<>();
            for (String fromVid : entry.getValue()) {
                replaceFromVidList.add("'" + fromVid + "'");
            }
            placeHolder.add(
                    "(sub_graph_id = '"
                            + subGraphId
                            + "' and from_vid in ("
                            + String.join(", ", replaceFromVidList)
                            + "))");
        }

        String where = String.join(" or ", placeHolder);
        if (deleteEdges != null && !deleteEdges.isEmpty()) {
            List<String> deleteCondition = new ArrayList<>();
            for (EdgeProperty edgeProperty : deleteEdges) {
                deleteCondition.add(
                        "(sub_graph_id ='"
                                + edgeProperty.getSubGraphId()
                                + "' and from_vid ='"
                                + edgeProperty.getFromVid()
                                + "' and to_vid ='"
                                + edgeProperty.getToVid()
                                + "' and edge_type_id ='"
                                + edgeProperty.getEdgeType()
                                + "')");
            }
            where += " and not (" + String.join(" or ", deleteCondition) + ")";
        }

        sql.WHERE(where);
        return sql.toString();
    }

    public String selectInAndOutEdgesByToVidList(
            Map<String, Map<String, List<EdgeProperty>>> subGraphIdToVidEdgeMap,
            List<EdgeProperty> deleteEdges,
            String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("sub_graph_id");
        sql.SELECT("edge_type_id");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<EdgeProperty>>> entry :
                subGraphIdToVidEdgeMap.entrySet()) {
            String subGraphId = entry.getKey();
            List<String> replaceToVidList = new ArrayList<>();
            for (String toVid : entry.getValue().keySet()) {
                replaceToVidList.add("'" + toVid + "'");
            }
            placeHolder.add(
                    "(sub_graph_id = '"
                            + subGraphId
                            + "' and (from_vid in ("
                            + String.join(", ", replaceToVidList)
                            + ") or to_vid in ("
                            + String.join(", ", replaceToVidList)
                            + ")))");
        }

        String where = String.join(" or ", placeHolder);
        if (deleteEdges != null && !deleteEdges.isEmpty()) {
            List<String> deleteCondition = new ArrayList<>();
            for (EdgeProperty edgeProperty : deleteEdges) {
                deleteCondition.add(
                        "(sub_graph_id ='"
                                + edgeProperty.getSubGraphId()
                                + "' and from_vid ='"
                                + edgeProperty.getFromVid()
                                + "' and to_vid ='"
                                + edgeProperty.getToVid()
                                + "' and edge_type_id ='"
                                + edgeProperty.getEdgeType()
                                + "')");
            }
            where += " and not (" + String.join(" or ", deleteCondition) + ")";
        }

        sql.WHERE(where);
        return sql.toString();
    }

    public String selectEdgesByToVidList(List<String> toVidList, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("from_vid")
                .SELECT("to_vid")
                .SELECT("sub_graph_id")
                .SELECT("edge_type_id")
                .FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);

        String placeholders =
                toVidList.stream()
                        .map(toVid -> "'" + toVid + "'")
                        .collect(Collectors.joining(", "));

        sql.WHERE("to_vid in (" + placeholders + ")");
        return sql.toString();
    }
}
