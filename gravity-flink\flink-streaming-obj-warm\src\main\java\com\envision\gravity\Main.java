package com.envision.gravity;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.envision.gravity.flink.warm.util.LionConfig.TYPE_MAP;

import org.apache.flink.api.java.tuple.Tuple2;

public class Main {
    public static void main(String[] args) {
        String cols =
                "SYSTEM_DISPLAY_NAME:VARCHAR,CATEGORY_ID:VARCHAR,"
                        + "CREATED_TIME:TIMESTAMP,CREATED_USER:VARCHAR,MODIFIED_TIME:TIMESTAMP,"
                        + "POINT_LAST_PROCESS_TIME:TIMESTAMP,MODIFIED_USER:VARCHAR,"
                        + "DCM__DEVICEKEY__VARCHAR:VARCHAR,point_last_process_time:TIMESTAMP,"
                        + "system_display_name:VARCHAR,category_id:VARCHAR,"
                        + "created_time:TIMESTAMP,created_user:VARCHAR,"
                        + "modified_time:TIMESTAMP,modified_user:VARCHAR";

        List<Tuple2<String, Class<?>>> fixedCols =
                Arrays.stream(cols.split(","))
                        .map(String::trim)
                        .map(
                                col -> {
                                    String[] arr = col.split(":");
                                    if (arr.length != 2) {

                                        System.out.println("Invalid fixed column: " + col);
                                        throw new RuntimeException("Invalid fixed column: " + col);
                                    }
                                    Class<?> type = TYPE_MAP.get(arr[1].trim().toUpperCase());
                                    return new Tuple2<String, Class<?>>(arr[0].trim(), type);
                                })
                        .collect(Collectors.toList());
        System.out.println(fixedCols);
    }
}
