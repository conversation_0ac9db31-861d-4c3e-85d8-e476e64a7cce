package com.envision.gravity.flink.streaming.bo.view.operator.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.bo.view.operator.mapper.ModelDetailOriginMapper;
import com.envision.gravity.flink.streaming.bo.view.operator.model.pg.ModelProperties;

import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/3
 * @description
 */
@Slf4j
public class ModelDetailOriginRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public ModelDetailOriginRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public ModelProperties selectModelProperties(String schemeName, String modelId) {
        try (SqlSession session = sqlSessionFactory.openSession()) {

            Objects.requireNonNull(schemeName, "Scheme name cannot be null.");

            ModelDetailOriginMapper modelDetailOriginMapper =
                    session.getMapper(ModelDetailOriginMapper.class);
            return modelDetailOriginMapper.selectModelProperties(schemeName, modelId);
        } catch (Exception e) {
            log.error("Select model properties error.", e);
            throw new GravityRuntimeException("Select model properties error.", e);
        }
    }
}
