package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblPrefExtCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "comp_id", String.class, "comp_id"),
                        new JdbcTypeField(Types.VARCHAR, "category", String.class, "category"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "comp_id", String.class, "comp_id"),
                        new JdbcTypeField(Types.VARCHAR, "category", String.class, "category"),
                        new JdbcTypeField(
                                Types.VARCHAR, "source_type", String.class, "source_type"),
                        new JdbcTypeField(
                                Types.VARCHAR, "source_value", String.class, "source_value"),
                        new JdbcTypeField(Types.VARCHAR, "metric_type", Long.class, "metric_type"),
                        new JdbcTypeField(Types.VARCHAR, "db_name", String.class, "db_name"),
                        new JdbcTypeField(Types.VARCHAR, "table_type", Long.class, "table_type"),
                        new JdbcTypeField(Types.VARCHAR, "table_name", String.class, "table_name"),
                        new JdbcTypeField(
                                Types.VARCHAR, "raw_expression", String.class, "raw_expression"),
                        new JdbcTypeField(
                                Types.VARCHAR, "agg_expression", String.class, "agg_expression"),
                        new JdbcTypeField(Types.VARCHAR, "filter", String.class, "filter"),
                        new JdbcTypeField(
                                Types.VARCHAR, "extra_tables", String.class, "extra_tables"),
                        new JdbcTypeField(
                                Types.VARCHAR,
                                "extra_attributes",
                                String.class,
                                "extra_attributes"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("pref_id");
        QUERY_ENTITY_KEY_FIELDS.add("comp_id");
        QUERY_ENTITY_KEY_FIELDS.add("category");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("pref_id");
        NOT_NULL_FIELDS.add("comp_id");
        NOT_NULL_FIELDS.add("category");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("pref_id"),
                        new QueryIndex("comp_id"),
                        new QueryIndex("category"));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("pref_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("comp_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("category", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("source_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("source_value", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("metric_type", "java.lang.Long");
        QUERY_ENTITY_FIELDS.put("db_name", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("table_type", "java.lang.Long");
        QUERY_ENTITY_FIELDS.put("table_name", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("raw_expression", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("agg_expression", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("filter", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("extra_tables", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("extra_attributes", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
