package com.envision.gravity.flink.streaming.bo.sync.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.record.TimestampType;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Msg {
    private Headers headers;
    private String key;
    private Payload value;
    private long timestamp;
    private TimestampType timestampType;
}
