package com.envision.gravity.flink.streaming.calculate.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FieldMapping<PERSON>ey {
    private String compId;
    private String prefId;
    private String fieldId;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((compId == null) ? 0 : compId.hashCode());
        result = prime * result + ((prefId == null) ? 0 : prefId.hashCode());
        result = prime * result + ((fieldId == null) ? 0 : fieldId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        FieldMappingKey other = (FieldMappingKey) obj;

        if (compId == null) {
            if (other.compId != null) {
                return false;
            }
        } else if (!compId.equals(other.compId)) {
            return false;
        }

        if (prefId == null) {
            if (other.prefId != null) {
                return false;
            }
        } else if (!prefId.equals(other.prefId)) {
            return false;
        }

        if (fieldId == null) {
            if (other.fieldId != null) {
                return false;
            }
        } else if (!fieldId.equals(other.fieldId)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        return "FieldMappingKey{"
                + "compId='"
                + compId
                + '\''
                + ", prefId='"
                + prefId
                + '\''
                + ", fieldId='"
                + fieldId
                + '\''
                + '}';
    }
}
