package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblTagCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "data_id", String.class, "data_id"),
                        new JdbcTypeField(Types.VARCHAR, "tag_id", String.class, "tag_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "data_id", String.class, "data_id"),
                        new JdbcTypeField(Types.VARCHAR, "data_type", String.class, "data_type"),
                        new JdbcTypeField(Types.VARCHAR, "tag_type", String.class, "tag_type"),
                        new JdbcTypeField(Types.VARCHAR, "tag_id", String.class, "tag_id"),
                        new JdbcTypeField(Types.VARCHAR, "tag_group", String.class, "tag_group"),
                        new JdbcTypeField(Types.VARCHAR, "marker", String.class, "marker"),
                        new JdbcTypeField(Types.VARCHAR, "tag_key", String.class, "tag_key"),
                        new JdbcTypeField(Types.VARCHAR, "tag_value", String.class, "tag_value"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"),
                        new JdbcTypeField(
                                Types.VARCHAR, "data_tag_id", String.class, "data_tag_id"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("data_id");
        QUERY_ENTITY_KEY_FIELDS.add("tag_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("data_id");
        NOT_NULL_FIELDS.add("tag_id");
        NOT_NULL_FIELDS.add("tag_type");

        // indexes
        INDEXES = Arrays.asList(new QueryIndex("data_id"), new QueryIndex("data_tag_id"));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("data_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("data_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tag_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tag_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tag_group", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("marker", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tag_key", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tag_value", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("data_tag_id", "java.lang.String");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
