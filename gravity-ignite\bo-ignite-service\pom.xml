<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-ignite</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>bo-ignite-service</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>
    <name>bo-ignite-service</name>
    <description>bo-ignite-service</description>

    <dependencies>

        <!-- gravity -->
        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
        </dependency>
        <!-- gravity -->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- log -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- log -->

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- ignite -->
        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-spring</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-indexing</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- ignite -->

        <!-- InfluxDB/TSDB -->
        <dependency>
            <groupId>io.eniot.tsdb</groupId>
            <artifactId>enos-tsdb-all</artifactId>
        </dependency>

    </dependencies>

</project>
