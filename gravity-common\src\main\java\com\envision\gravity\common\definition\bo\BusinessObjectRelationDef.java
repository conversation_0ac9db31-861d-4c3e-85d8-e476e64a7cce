package com.envision.gravity.common.definition.bo;

import javax.validation.constraints.NotBlank;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessObjectRelationDef {
    @NotBlank(message = "BusinessObjectRelation: assetId can not be blank")
    private String assetId;

    private List<RelationDef> relation;

    private Relation relationUpserted;
    private Relation relationDeleted;
}
