package com.envision.gravity.flink.streaming.bo.view.operator.function;

import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;
import com.envision.gravity.flink.streaming.bo.view.operator.entity.OPEnum;
import com.envision.gravity.flink.streaming.bo.view.operator.model.ModelDetailOrigin;
import com.envision.gravity.flink.streaming.bo.view.operator.side.SideOutputs;

import java.sql.Timestamp;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
@Slf4j
public class CdcRecordRouter extends ProcessFunction<String, ModelDetailOriginCdcRecord> {

    private static final long serialVersionUID = -4934298461513526931L;
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    private static final String MODEL_FOR_MDM_TYPE = "modelForMdmType";

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, ModelDetailOriginCdcRecord>.Context ctx,
            Collector<ModelDetailOriginCdcRecord> out)
            throws Exception {
        try {
            // 1. extract bo model detail info
            ModelDetailOriginCdcRecord modelDetailOriginCdcRecord =
                    OBJECT_MAPPER.readValue(value, ModelDetailOriginCdcRecord.class);

            String schemaName = modelDetailOriginCdcRecord.getSource().getSchema();
            String op = modelDetailOriginCdcRecord.getOp();
            Timestamp tsMs = modelDetailOriginCdcRecord.getTsMs();
            log.info("Received bo view request");

            if (OPEnum.c.name().equalsIgnoreCase(op) || OPEnum.u.name().equalsIgnoreCase(op)) {
                ModelDetailOrigin after = modelDetailOriginCdcRecord.getAfter();

                if (checkMdmType(after)) {
                    log.info(
                            "Received create or replace bo view request, schemaName: [{}], modelId: [{}], op: [{}], tsMs: [{}]",
                            schemaName,
                            after.getModelId(),
                            op,
                            tsMs);
                    ctx.output(SideOutputs.CREATE_OR_REPLACE_VIEW_TAG, modelDetailOriginCdcRecord);
                }

            } else if (OPEnum.d.name().equalsIgnoreCase(op)) {
                ModelDetailOrigin before = modelDetailOriginCdcRecord.getBefore();

                if (checkMdmType(before)) {
                    log.info(
                            "Received delete bo view request, schemaName: [{}], modelId: [{}], op: [{}], tsMs: [{}]",
                            schemaName,
                            before.getModelId(),
                            op,
                            tsMs);
                    ctx.output(SideOutputs.DROP_VIEW_TAG, modelDetailOriginCdcRecord);
                }
            } else {
                log.warn("Unknown operation:{}", op);
            }

        } catch (JsonProcessingException e) {
            log.error("Parse cdc info error, value:{}", value, e);
        } catch (Exception e) {
            log.error("Unknown error, value:{}", value, e);
        }
    }

    private boolean checkMdmType(ModelDetailOrigin modelDetailOrigin)
            throws JsonProcessingException {
        // {"mdmType": "", "modelForMdmType": "true"}
        String modelTags = modelDetailOrigin.getModelTags();
        if (modelTags != null) {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(modelTags);
            return jsonNode.get(MODEL_FOR_MDM_TYPE) != null;
        }

        return false;
    }
}
