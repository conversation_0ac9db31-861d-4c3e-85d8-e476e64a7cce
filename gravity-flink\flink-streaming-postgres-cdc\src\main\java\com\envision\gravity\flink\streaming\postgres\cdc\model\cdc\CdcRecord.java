package com.envision.gravity.flink.streaming.postgres.cdc.model.cdc;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/11
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CdcRecord {
    @JsonIgnore private String before;
    @JsonIgnore private String after;
    private Source source;
    private String op;
    private Long tsMs;
    @JsonIgnore private Object transaction;
}
