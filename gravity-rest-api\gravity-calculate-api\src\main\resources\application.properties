server.port=8081
spring.application.name=gravity-calculate-rest-api
server.servlet.context-path=/gravity-service/v3.0
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER

management.server.port=8082
management.endpoints.web.exposure.include=metrics,prometheus
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.metrics.enable.jvm=true
management.metrics.enable.tomcat=false
management.metrics.enable.system=true
management.metrics.enable.process=true
management.metrics.enable.log4j2=false
management.metrics.enable.logback=true
management.metrics.enable.http=true
management.metrics.enable.kafka=false
management.metrics.tags.application=${spring.application.name}


## logging.level.com.envision.gravity.low.level.api.rest.dao.pg=DEBUG