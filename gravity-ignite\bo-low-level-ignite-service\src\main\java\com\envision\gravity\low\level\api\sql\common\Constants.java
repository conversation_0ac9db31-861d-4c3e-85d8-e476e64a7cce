package com.envision.gravity.low.level.api.sql.common;

/**
 * <AUTHOR>
 * @date 2024/4/28
 * @description
 */
public class Constants {
    public static final String TBL_BO_MODEL_TABLE_NAME = "TBL_BO_MODEL";
    public static final String TBL_BO_MODEL_COMP_TABLE_NAME = "TBL_BO_MODEL_COMP";
    public static final String TBL_COMPONENT_TABLE_NAME = "TBL_COMPONENT";
    public static final String TBL_COMPONENT_PREF_TABLE_NAME = "TBL_COMPONENT_PREF";
    public static final String TBL_PREF_TABLE_NAME = "TBL_PREF";
    public static final String TBL_PREF_EXT_TABLE_NAME = "TBL_PREF_EXT";
    public static final String TBL_MODEL_RELATION_TABLE_NAME = "TBL_MODEL_RELATION";
    public static final String TBL_BO_GROUP_TABLE_NAME = "TBL_BO_GROUP";
    public static final String TBL_BO_GROUP_RELATION_TABLE_NAME = "TBL_BO_GROUP_RELATION";
    public static final String TBL_BO_TABLE_NAME = "TBL_BO";
    public static final String TBL_TAG_TABLE_NAME = "TBL_TAG";
    public static final String TBL_OBJ_ATTR_TABLE_NAME = "TBL_OBJ_ATTR";
    public static final String TBL_SUB_GRAPH_TABLE_NAME = "TBL_SUB_GRAPH";
    public static final String TBL_EDGE_TABLE_NAME = "TBL_EDGE";
    public static final String TBL_EDGE_TYPE_TABLE_NAME = "TBL_EDGE_TYPE";
    public static final String TBL_EDGE_TYPE_PROP_TABLE_NAME = "TBL_EDGE_TYPE_PROP";
    public static final String TBL_START_VID_TABLE_NAME = "TBL_START_VID";

    public static final String CACHE_NAME_PATTERN = "%s_%s";
    public static final String KEY_TYPE_PATTERN = "%s_%s_KEY";
    public static final String VALUE_TYPE_PATTERN = "%s_%s_VALUE";
    public static final String BO_IN_MEMORY_REGION = "BO_InMemory_Region";
    public static final String POSTGRES_DATA_SOURCE_BEAN_NAME = "postgresDataSourceGravity";
    public static final String GRAVITY_UDF = "GRAVITY_UDF";

    public static final String QUERY_OU_ID_SQL_PATTERN = "SELECT OU_ID FROM GRAVITY.TBL_OU_INFO;";
    public static final String QUERY_SCHEMAS_SQL_PATTERN = "SELECT SCHEMA_NAME FROM SYS.SCHEMAS;";
    public static final String REGISTER_OU_SQL_PATTERN =
            "MERGE INTO GRAVITY.TBL_OU_INFO\n"
                    + "(OU_ID, RESOURCE_LEVEL, CREATED_USER, MODIFIED_USER, CREATED_TIME, MODIFIED_TIME)\n"
                    + "VALUES(?, ?, ?, ?, ?, ?);";
    public static final String TBL_OU_INFO_CACHE_NAME = "GRAVITY_TBL_OU_INFO";
    public static final String QUERY_CACHE_TABLE_PATTERN =
            "SELECT COUNT(*) FROM SYS.TABLES\n"
                    + "WHERE SCHEMA_NAME = ? \n"
                    + "AND TABLE_NAME = ?;";

    public static final String QUERY_LOST_PARTITIONS =
            "SELECT DISTINCT sc.cache_name FROM SYS.partition_states AS sps\n"
                    + "JOIN sys.caches AS sc ON SPS.cache_group_id = sc.cache_group_id\n"
                    + "WHERE sps.state = 'LOST';";
}
