package com.envision.gravity.common.vo.sync;

import com.envision.gravity.common.definition.graph.BORelationDataDefinition;


import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncBORelationsReq extends BaseSyncReq {
    private BORelationDataDefinition boRelationDataDefinition;
    private boolean forceUpdate;
}
