package com.envision.gravity.calculate.api.rest.dto;

import com.envision.gravity.common.enums.PrefType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownstreamRule {

    private String targetPrefName;

    private String targetCompId;

    private String targetPrefId;

    private String targetCategory;

    private String srcCategory;

    private String srcCompId;

    private String srcPrefId;

    private String expression;

    private PrefType prefType;

    private long sysCreatedTime;
    private long sysModifiedTime;

    private boolean isValidExpr = true;

    public DownstreamRule(List<?> objects) {}
}
