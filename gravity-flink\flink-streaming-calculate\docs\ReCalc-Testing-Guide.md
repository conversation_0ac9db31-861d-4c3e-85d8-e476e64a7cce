# ReCalc 测试指南

## 概述

本文档描述了 ReCalc（重跑计算流）组件的测试策略和实现。包括单元测试、集成测试和端到端测试的完整测试套件。

## 测试架构

### 1. 单元测试
每个核心算子都有对应的单元测试：

- **ReCalcCdcProcessorTest**: 测试 CDC 事件处理逻辑
- **ReCalcJobGeneratorTest**: 测试作业信息生成逻辑
- **ReCalcJobPgWriterTest**: 测试数据库写入逻辑
- **ReCalcJobTriggerTest**: 测试作业提交触发逻辑
- **FlinkJobSubmitterTest**: 测试 Flink 作业提交逻辑

### 2. 集成测试
- **ReCalcIntegrationTest**: 测试完整的数据流处理管道

### 3. 端到端测试
- **ReCalcBatchJob**: 使用简化的 word count 实现进行集成验证

## 测试组件说明

### ReCalcBatchJob (Word Count 版本)

为了验证整体链路，我们实现了一个简化版本的 `ReCalcBatchJob`：

```java
public class ReCalcBatchJob {
    public static void main(String[] args) throws Exception {
        // 1. 解析作业参数
        // 2. 更新作业状态为 RUNNING
        // 3. 执行 word count 处理
        // 4. 更新作业状态为 FINISHED/FAILED
    }
}
```

**特性**:
- 接受标准的作业参数（jobId, prefRuleId, ruleInfo 等）
- 自动更新数据库中的作业状态
- 执行简单的 word count 算法验证 Flink 批处理功能
- 提供完整的日志记录和错误处理

## 运行测试

### 1. 运行单元测试

#### 使用脚本运行所有测试
```bash
chmod +x scripts/run-unit-tests.sh
./scripts/run-unit-tests.sh
```

#### 运行单个测试类
```bash
mvn test -Dtest=ReCalcCdcProcessorTest
mvn test -Dtest=ReCalcJobGeneratorTest
mvn test -Dtest=ReCalcJobPgWriterTest
mvn test -Dtest=ReCalcJobTriggerTest
mvn test -Dtest=FlinkJobSubmitterTest
```

#### 运行所有 ReCalc 测试
```bash
mvn test -Dtest="com.envision.gravity.flink.streaming.calculate.recalc.**"
```

### 2. 运行集成测试
```bash
mvn test -Dtest=ReCalcIntegrationTest
```

### 3. 生成测试报告
```bash
mvn surefire-report:report
mvn jacoco:report
```

## 测试覆盖范围

### ReCalcCdcProcessorTest
- ✅ 有效的更新操作处理
- ✅ 创建操作被忽略
- ✅ 删除操作被忽略
- ✅ ADHOC 类型规则被忽略
- ✅ 不支持的表被忽略
- ✅ 目标属性元数据未找到的处理
- ✅ 异常处理

### ReCalcJobGeneratorTest
- ✅ 有效输入的作业生成
- ✅ 时间范围计算正确性
- ✅ 规则信息序列化
- ✅ 作业 ID 唯一性
- ✅ 默认值设置
- ✅ 空值和异常处理

### ReCalcJobPgWriterTest
- ✅ 成功插入数据库
- ✅ 插入失败处理
- ✅ 数据库异常处理
- ✅ 空值处理
- ✅ 完整字段验证
- ✅ 多条记录处理

### ReCalcJobTriggerTest
- ✅ 成功的作业提交
- ✅ 集群不可用处理
- ✅ 无效时间范围处理
- ✅ 空规则信息处理
- ✅ 冲突作业取消
- ✅ 作业提交失败处理
- ✅ 数据库错误处理

### FlinkJobSubmitterTest
- ✅ 成功的作业提交
- ✅ JAR 文件未找到处理
- ✅ 作业提交失败处理
- ✅ JAR 查找逻辑
- ✅ 集群健康检查
- ✅ 网络异常处理

### ReCalcIntegrationTest
- ✅ 端到端数据流处理
- ✅ 无效记录过滤
- ✅ 多组件协作验证

## 测试数据和 Mock

### 测试数据创建
```java
private TblCalcJobInfo createTblCalcJobInfo(String jobId, String prefRuleId) {
    return TblCalcJobInfo.builder()
        .jobId(jobId)
        .prefRuleId(prefRuleId)
        .ruleInfo("test rule info")
        .calcStartTime(1000L)
        .calcEndTime(2000L)
        .status(ReCalcJobStatusEnum.INIT.getCode())
        .type(ReCalcJobTypeEnum.DEFAULT.getCode())
        .srcOrgId("o12345")
        .targetOrgId("o12345")
        .build();
}
```

### Mock 配置
```java
@Mock
private CalcMetaProcessor calcMetaProcessor;

@Mock
private FlinkJobSubmitter jobSubmitter;

@Mock
private TblCalcJobInfoMapper mapper;
```

## 测试配置

### 测试属性文件
`src/test/resources/test-application.properties` 包含测试专用的配置：

```properties
# 测试环境的 Flink 配置
gravity-flink.cluster.jobmanager.host=localhost
gravity-flink.cluster.jobmanager.port=8081
gravity-flink.recalc.job-submit-retry-limit=2
gravity-flink.recalc.time-range-days=30

# 测试数据库配置
gravity-common.postgresql.jdbc-url=*********************************************
```

## 故障排查

### 常见测试问题

1. **Mock 初始化失败**
   ```bash
   # 确保使用正确的 Mockito 版本
   mvn dependency:tree | grep mockito
   ```

2. **数据库连接测试失败**
   ```bash
   # 检查测试配置
   cat src/test/resources/test-application.properties
   ```

3. **Flink 测试环境问题**
   ```bash
   # 使用本地测试环境
   mvn test -Dtest=ReCalcIntegrationTest -Dflink.test.mode=local
   ```

### 调试测试
```bash
# 运行带详细输出的测试
mvn test -Dtest=ReCalcCdcProcessorTest -X

# 运行特定测试方法
mvn test -Dtest=ReCalcCdcProcessorTest#testProcessElement_ValidUpdateOperation_Success
```

## 持续集成

### CI/CD 集成
```yaml
# .github/workflows/test.yml
- name: Run ReCalc Unit Tests
  run: |
    cd gravity-flink/flink-streaming-calculate
    ./scripts/run-unit-tests.sh

- name: Generate Test Reports
  run: |
    mvn surefire-report:report
    mvn jacoco:report
```

### 测试覆盖率目标
- 单元测试覆盖率: > 80%
- 集成测试覆盖率: > 70%
- 关键路径覆盖率: 100%

## 下一步计划

1. **完善 ReCalcBatchJob**: 将 word count 实现替换为真正的计算逻辑
2. **性能测试**: 添加负载测试和性能基准测试
3. **端到端测试**: 添加完整的系统级测试
4. **测试自动化**: 集成到 CI/CD 流水线

## 贡献指南

### 添加新测试
1. 在对应的测试类中添加测试方法
2. 使用描述性的测试方法名
3. 遵循 AAA 模式（Arrange, Act, Assert）
4. 添加必要的注释和文档

### 测试命名规范
```java
@Test
void testMethodName_Scenario_ExpectedResult() {
    // Arrange
    // Act  
    // Assert
}
```

这个测试套件为 ReCalc 功能提供了全面的测试覆盖，确保了代码质量和系统可靠性。
