package com.envision.gravity.flink.streaming.bo.sync.entity;

import com.envision.gravity.common.definition.DefinitionEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Payload {
    private long tsMs;
    private String op;
    private boolean forceUpdate;
    private String source;
    private String orgId;
    private boolean syncModelFirst;
    private DefinitionEntity payload;
    private String version;
    private long retryCount;
}
