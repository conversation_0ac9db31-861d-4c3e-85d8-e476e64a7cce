package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaCdcUtils;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsg;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgParser;
import com.envision.gravity.flink.streaming.calculate.stream.serde.MsgParser;


import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.CoProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Co-process function that parses Kafka messages and updates cache with CDC records. Handles
 * message parsing from Kafka stream and metadata updates from CDC stream.
 *
 * <AUTHOR>
 */
public class ParseMsgAndUpdateCache
        extends CoProcessFunction<
                Tuple2<String, OffsetInfo>, ConvertedCdcRecord, Tuple2<LegacyMsg, OffsetInfo>> {

    private static final Logger logger = LoggerFactory.getLogger(ParseMsgAndUpdateCache.class);

    private static MsgParser parser = new LegacyMsgParser();

    private final String identity;

    public ParseMsgAndUpdateCache(String identity) {
        this.identity = identity;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        CalcMetaProcessor.getInstance().batchLoad();
    }

    @Override
    public void processElement1(
            Tuple2<String, OffsetInfo> value,
            CoProcessFunction<
                                    Tuple2<String, OffsetInfo>,
                                    ConvertedCdcRecord,
                                    Tuple2<LegacyMsg, OffsetInfo>>
                            .Context
                    context,
            Collector<Tuple2<LegacyMsg, OffsetInfo>> out)
            throws Exception {
        logger.debug("Received Kafka message: {}", value.f0);

        LegacyMsg msg = (LegacyMsg) parser.parse(value.f0);
        if (msg == null) {
            logger.warn("Message parsing failed: {}", value.f0);
        } else {
            logger.debug(
                    "Message parsing successful, orgId: {}, modelId: {}",
                    msg.getOrgId(),
                    msg.getModelId());
            out.collect(new Tuple2<>(msg, value.f1));
        }
    }

    @Override
    public void processElement2(
            ConvertedCdcRecord value,
            CoProcessFunction<
                                    Tuple2<String, OffsetInfo>,
                                    ConvertedCdcRecord,
                                    Tuple2<LegacyMsg, OffsetInfo>>
                            .Context
                    context,
            Collector<Tuple2<LegacyMsg, OffsetInfo>> collector)
            throws Exception {
        CalcMetaCdcUtils.process(value);
    }

    @Override
    public void close() {
        IgniteUtil.close();
    }
}
