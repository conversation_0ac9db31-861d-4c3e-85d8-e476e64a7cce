package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import com.google.common.collect.Multimap;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/12
 * @description
 */
public class TblBOModelSqlProvider {
    public String updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> modelIdList) {
        String modelIds =
                modelIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.UPDATE(schemaName + ".tbl_bo_model");
        sql.SET("sys_modified_time = CURRENT_TIMESTAMP");
        sql.WHERE("model_id in ( " + modelIds + " )");
        return sql.toString();
    }

    public String selectAssetIdList(String schemaName, Multimap<String, String> modelGroupMap) {
        String modelIds =
                modelGroupMap.keySet().stream()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String groupIds =
                modelGroupMap.values().stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tb.asset_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_group_relation tbgr ON tbm.group_id = tbgr.group_id");
                        INNER_JOIN(schemaName + ".tbl_bo tb on tbgr.asset_id = tb.asset_id");
                        WHERE(
                                "tbm.model_id in ( "
                                        + modelIds
                                        + " ) and tbm.group_id in ( "
                                        + groupIds
                                        + " )");
                    }
                };

        return sql.toString();
    }
}
