package com.envision.gravity.flink.streaming.virtual.attr.sync.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblBOModelMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ModelAsset;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
@Slf4j
public class TblBOModelRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblBOModelRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public int selectAssetTotalCount(String schemeName, String modelId) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemeName, "Scheme name cannot be null.");

            TblBOModelMapper tblBOModelMapper = session.getMapper(TblBOModelMapper.class);
            return tblBOModelMapper.selectAssetTotalCount(schemeName, modelId);
        } catch (Exception e) {
            log.error("Select asset total count error.", e);
            throw new GravityRuntimeException("Select asset total count error.", e);
        }
    }

    public List<ModelAsset> queryAsset(String schemeName, String modelId, int limit, int offset) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemeName, "Scheme name cannot be null.");

            if (limit < 0) {
                log.error("limit must not be negative ,but was {}", limit);
                return Collections.emptyList();
            }

            if (offset < 0) {
                log.error("offset must not be negative ,but was {}", offset);
                return Collections.emptyList();
            }

            TblBOModelMapper tblBOModelMapper = session.getMapper(TblBOModelMapper.class);
            return tblBOModelMapper.queryAsset(schemeName, modelId, limit, offset);
        } catch (Exception e) {
            log.error("Query asset error.", e);
            throw new GravityRuntimeException("Query asset error.", e);
        }
    }
}
