package com.envision.gravity.calculate.api.rest.web;

import com.envision.gravity.common.util.LionUtil;

public class CalcRestConfig {

    public static int getCalcDownstreamRuleRefreshIntervalSeconds() {
        return LionUtil.getIntValue(
                "gravity-calc-rest.downstream.rule-refresh-interval-seconds", 30);
    }

    public static int getCalcDownstreamBatchSizeLimit() {
        return LionUtil.getIntValue("gravity-calc-rest.downstream.batch-size-limit", 200);
    }

    public static int getCalcDownstreamRuleLoadPageSize() {
        return LionUtil.getIntValue("gravity-calc-rest.downstream.rule-load-page-size", 500);
    }

    public static int getCalcDownstreamRuleCacheMaxSize() {
        return LionUtil.getIntValue("gravity-calc-rest.downstream.rule-cache-max-size", 10000);
    }

    public static int getCalcDownstreamRuleCacheExpireTimeSeconds() {
        return LionUtil.getIntValue(
                "gravity-calc-rest.downstream.rule-cache-expire-time-seconds", 300);
    }

    public static int getCalcDownstreamBoCacheMaxSize() {
        return LionUtil.getIntValue("gravity-calc-rest.downstream.bo-cache-max-size", 10000);
    }

    public static int getCalcDownstreamBoCacheExpireTimeSeconds() {
        return LionUtil.getIntValue(
                "gravity-calc-rest.downstream.bo-cache-expire-time-seconds", 300);
    }
}
