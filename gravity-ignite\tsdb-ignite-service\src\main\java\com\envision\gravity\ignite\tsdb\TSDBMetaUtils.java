package com.envision.gravity.ignite.tsdb;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


import io.eniot.tsdb.common.sharding.TSDBNode;
import org.apache.commons.lang3.SerializationUtils;

/** <AUTHOR> 2024/8/15 */
public class TSDBMetaUtils {

    public static List<TSDBNode> parseNodes(String url) {
        List<TSDBNode> nodes = new ArrayList<>(1);
        int startIndex = url.indexOf("//") + 2;
        int endIndex = url.indexOf("?");
        String[] subUrls = url.substring(startIndex, endIndex).split(",");
        for (String address : subUrls) {
            String httpAddress = String.join("", "http://", address);
            TSDBNode tsdbNode = new TSDBNode(httpAddress);
            nodes.add(tsdbNode);
        }
        return nodes;
    }

    public static List<TSDBNode> deepCopy(List<TSDBNode> original) {
        return original.stream().map(SerializationUtils::clone).collect(Collectors.toList());
    }
}
