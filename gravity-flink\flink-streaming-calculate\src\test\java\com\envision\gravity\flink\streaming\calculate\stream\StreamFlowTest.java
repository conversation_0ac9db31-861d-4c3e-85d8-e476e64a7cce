package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.flink.streaming.calculate.StreamFlow;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;


import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class StreamFlowTest {

    private static final Logger logger = LoggerFactory.getLogger(StreamFlowTest.class);

    @Test
    public void startFlow() {
        try {
            StreamFlow.main(new String[] {"--local", "true"});
            System.out.println("✅ StreamFlow execution completed.");
            System.exit(1);
        } catch (Exception e) {
            System.err.println("❌ StreamFlow execution failed: " + e.getMessage());
        }
    }

    @Test
    public void testStreamFlowInitializationOnly() {
        System.out.println("=== Testing StreamFlow Initialization Only ===");

        try {
            // 测试所有 StreamFlow 需要的配置和组件初始化
            System.out.println("=== Step 1: Testing Configuration Loading ===");
            testConfigurationLoading();
            System.out.println("✅ Configuration loading successful");

            System.out.println("=== Step 2: Testing CalcMetaProcessor Initialization ===");
            testCalcMetaProcessorInitialization();
            System.out.println("✅ CalcMetaProcessor initialization successful");

            System.out.println("=== Step 3: Testing Flink Environment Setup ===");
            testFlinkEnvironmentSetup();
            System.out.println("✅ Flink environment setup successful");

            System.out.println("=== 🎉 All StreamFlow initialization tests passed! ===");
            System.out.println(
                    "=== StreamFlow is ready to start, but not starting actual stream processing ===");

        } catch (Exception e) {
            System.out.println("=== ❌ StreamFlow initialization test failed ===");
            System.out.println("Exception: " + e.getClass().getSimpleName());
            System.out.println("Message: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    private void testConfigurationLoading() {
        // 测试所有 StreamFlow 需要的配置项
        System.out.println("Kafka Servers: " + CalcLionConfig.getCalcCommonKafkaServers());
        System.out.println(
                "Kafka Consumer Group: " + CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup());
        System.out.println(
                "Kafka Topic Pattern: " + CalcLionConfig.getCalcStreamKafkaSourceTopicPattern());
        System.out.println(
                "Kafka Batch Size: " + CalcLionConfig.getCalcStreamKafkaSourceBatchSize());
        System.out.println(
                "Kafka Timeout Seconds: "
                        + CalcLionConfig.getCalcStreamKafkaSourceBatchTimeoutSeconds());

        System.out.println("PG Hostname: " + CalcLionConfig.getPgHostname());
        System.out.println("PG Port: " + CalcLionConfig.getPgPort());
        System.out.println("PG Database: " + CalcLionConfig.getPgDatabase());
        System.out.println("PG Username: " + CalcLionConfig.getPgUsername());

        System.out.println("CDC Schema List: " + CalcLionConfig.getCalcMetaPgCdcSchemaList());
        System.out.println("CDC Table List: " + CalcLionConfig.getCalcMetaPgCdcTableList());
        System.out.println("CDC Slot Name: " + CalcLionConfig.getCalcStreamPgCdcSlotName());
    }

    private void testCalcMetaProcessorInitialization() {
        // 这会触发 CalcMetaProcessor 的完整初始化，包括 batchLoad()
        CalcMetaProcessor processor = CalcMetaProcessor.getInstance();
        processor.batchLoad();
        System.out.println("CalcMetaProcessor initialized and loaded successfully");
    }

    private void testFlinkEnvironmentSetup() {
        // 测试 Flink 环境设置（但不启动流处理）
        org.apache.flink.streaming.api.environment.StreamExecutionEnvironment env =
                org.apache.flink.streaming.api.environment.StreamExecutionEnvironment
                        .getExecutionEnvironment();

        // 模拟 StreamFlow 中的环境配置
        com.envision.gravity.flink.common.utils.FlinkCommonUtils.tryRunAsLocalModel(
                new String[] {"--local", "true"}, env);
        env.setParallelism(2);

        int parallelism = env.getParallelism();
        System.out.println("Flink environment parallelism: " + parallelism);

        // 测试 KeySelector 创建
        int[] keySelectorArray = KeySelectUtil.createRebalanceKeys(parallelism);
        System.out.println("KeySelector array created with length: " + keySelectorArray.length);

        System.out.println("Flink environment configured successfully (not started)");
    }
}
