package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public interface Aggregator {
    AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records);

    AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records);

    AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records);
}
