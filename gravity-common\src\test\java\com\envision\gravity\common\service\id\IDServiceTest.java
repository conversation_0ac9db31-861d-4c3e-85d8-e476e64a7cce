package com.envision.gravity.common.service.id;

import com.envision.gravity.common.enums.IDType;

import java.util.HashSet;
import java.util.Set;


import org.apache.ignite.Ignite;
import org.apache.ignite.Ignition;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description
 */
class IDServiceTest {

    @Test
    void getInstanceByIgnite() {
        Ignition.setClientMode(true);
        IgniteConfiguration cfg = getIgniteConfiguration();
        Ignite ignite = Ignition.getOrStart(cfg);
        IDService idService = IDService.getInstance(ignite);
        System.out.println(idService.getIds(1, IDType.COMMON_ID));
    }

    private static IgniteConfiguration getIgniteConfiguration() {
        IgniteConfiguration cfg = new IgniteConfiguration();
        cfg.setPeerClassLoadingEnabled(true);
        cfg.setDeploymentMode(DeploymentMode.CONTINUOUS);
        cfg.setAuthenticationEnabled(true);
        cfg.setClientMode(true);
        TcpDiscoverySpi spi = new TcpDiscoverySpi();
        TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();
        Set<String> addresses = new HashSet<>(4);
        addresses.add("************:47500");
        ipFinder.setAddresses(addresses);
        spi.setIpFinder(ipFinder);
        cfg.setDiscoverySpi(spi);
        cfg.setNetworkTimeout(120000);
        cfg.setAuthenticationEnabled(true);
        return cfg;
    }

    @Test
    void getInstanceByIgniteClient() {
        ClientConfiguration cfg =
                new ClientConfiguration()
                        .setAddresses("*************:10800")
                        .setPartitionAwarenessEnabled(true)
                        .setUserName("ignite")
                        .setUserPassword("ignite");
        IgniteClient igniteClient = Ignition.startClient(cfg);
        IDService idService = IDService.getInstance(igniteClient);
        System.out.println(idService.getIds(100, IDType.COMMON_ID));
    }
}
