package com.envisioniot.dp.sql.router.parser.gsql.mapper;

import com.envisioniot.dp.sql.router.utils.ResultSetUtils;

import com.envision.gravity.common.po.TblPropertyDownstreamRule;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblPropertyDownstreamRuleRowMapper
        implements ResultSetUtils.RowMapper<TblPropertyDownstreamRule> {
    @Override
    public TblPropertyDownstreamRule mapRow(ResultSet rs) throws SQLException {
        return TblPropertyDownstreamRule.builder()
                .prefRuleId(rs.getString("pref_rule_id"))
                .targetCategory(rs.getString("target_category"))
                .targetCompId(rs.getString("target_comp_id"))
                .targetPrefId(rs.getString("target_pref_id"))
                .srcCategory(rs.getString("src_category"))
                .srcCompId(rs.getString("src_comp_id"))
                .srcPrefId(rs.getString("src_pref_id"))
                .expression(rs.getString("expression"))
                .createdTime(rs.getTimestamp("created_time"))
                .createdUser(rs.getString("created_user"))
                .modifiedTime(rs.getTimestamp("modified_time"))
                .modifiedUser(rs.getString("modified_user"))
                .build();
    }
}
