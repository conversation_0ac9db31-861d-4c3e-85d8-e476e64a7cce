package com.envision.gravity.common.service.id.segment.model;

import java.util.concurrent.atomic.AtomicLong;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/19
 * @description
 */
@Data
public class Segment {
    private AtomicLong value = new AtomicLong(0);
    private volatile long max;
    private volatile int step;
    private SegmentBuffer buffer;

    public Segment(SegmentBuffer buffer) {
        this.buffer = buffer;
    }

    public long getIdle() {
        return this.getMax() - getValue().get();
    }

    @Override
    public String toString() {
        return "Segment(" + "value:" + value + ",max:" + max + ",step:" + step + ")";
    }
}
