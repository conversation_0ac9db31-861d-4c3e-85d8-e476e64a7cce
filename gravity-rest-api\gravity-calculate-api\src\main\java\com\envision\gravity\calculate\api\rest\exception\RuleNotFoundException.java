package com.envision.gravity.calculate.api.rest.exception;

public class RuleNotFoundException extends CalcRuntimeException {
    private static final long serialVersionUID = 714917889915924530L;

    public RuleNotFoundException() {
        super();
    }

    public RuleNotFoundException(String message) {
        super(message);
    }

    public RuleNotFoundException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public RuleNotFoundException(Throwable throwable) {
        super(throwable);
    }
}
