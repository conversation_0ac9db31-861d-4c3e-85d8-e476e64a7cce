package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingKey;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingRecord;
import com.envision.gravity.flink.streaming.calculate.dto.TblComponentPref;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;
import com.envision.gravity.flink.streaming.calculate.meta.DirectMappingProcessor;

import java.util.*;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblComponentPrefCdcProcessor {

    private static final Logger logger =
            LoggerFactory.getLogger(TblComponentPrefCdcProcessor.class);

    private static volatile TblComponentPrefCdcProcessor uniqueInstance;

    private final CalcPrefCache calcPrefCache;

    private final DirectMappingProcessor directMappingProcessor;

    private final CalcMetaProcessor calcMetaProcessor;

    public static TblComponentPrefCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblComponentPrefCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblComponentPrefCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private TblComponentPrefCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        this.directMappingProcessor = DirectMappingProcessor.getInstance();
        this.calcMetaProcessor = CalcMetaProcessor.getInstance();
    }

    public void process(String orgId, TblComponentPref before, TblComponentPref after, OPEnum op) {
        if (op == OPEnum.c) {
            logger.info("New created component pref, skip it...");
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error(
                        "Component pref cdc params invalid when update, before or after is null");
                return;
            }

            // No need to update cache
            // Query source property based on srcCompId + srcPrefId, then query and update the
            // associated target property
            Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap =
                    this.calcMetaProcessor.getDirectMappingTargetPropertyBySrc(orgId, before);
            if (GTCommonUtils.nonEmptyMap(fieldMappingRecordMap)) {
                this.directMappingProcessor.updateCompPrefFieldMapping(
                        orgId, fieldMappingRecordMap);
            }
        } else if (op == OPEnum.d) {
            if (before == null) {
                logger.error("Component pref cdc params invalid when delete, before is null");
                return;
            }

            Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap =
                    this.calcMetaProcessor.getDirectMappingTargetPropertyBySrc(orgId, before);
            if (GTCommonUtils.nonEmptyMap(fieldMappingRecordMap)) {
                this.directMappingProcessor.deleteCompPrefFieldMapping(
                        orgId, fieldMappingRecordMap.keySet());
            }
        }
    }
}
