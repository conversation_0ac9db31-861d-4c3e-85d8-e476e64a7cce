package com.envision.gravity.common.vo.sync;

import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncBOsResp {
    private String syncId;
    private String orgId;
    private Set<String> successfulBOs;
    private Set<String> failedBOs;
    private Set<String> successfulDOs;
    private Set<String> failedDOs;
    private Set<String> successfulRelations;
    private Set<String> failedRelations;
}
