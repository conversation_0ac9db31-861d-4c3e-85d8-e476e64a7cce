package com.envision.gravity.calculate.api.rest.exception;

public class RuleInvalidException extends CalcRuntimeException {
    private static final long serialVersionUID = -2841257194249688658L;

    public RuleInvalidException() {
        super();
    }

    public RuleInvalidException(String message) {
        super(message);
    }

    public RuleInvalidException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public RuleInvalidException(Throwable throwable) {
        super(throwable);
    }
}
