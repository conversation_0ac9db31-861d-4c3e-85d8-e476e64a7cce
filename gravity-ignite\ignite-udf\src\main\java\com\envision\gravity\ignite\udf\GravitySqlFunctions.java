package com.envision.gravity.ignite.udf;

import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ignite.cache.query.annotations.QuerySqlFunction;

/**
 * <AUTHOR>
 * @date 2024/1/17
 * @description
 */
public class GravitySqlFunctions {

    @QuerySqlFunction(alias = "I18N")
    public static String i18n(String i18nName, String locale) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(i18nName);
            JsonNode localeNode = jsonNode.path(locale);
            if (localeNode.isTextual()) {
                return localeNode.asText();
            }

            JsonNode defaultNode = jsonNode.path("default");
            if (defaultNode.isTextual()) {
                return defaultNode.asText();
            }

            throw new RuntimeException("No translation found for locale: " + locale);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error reading i18nName!", e);
        }
    }

    @QuerySqlFunction(alias = "TO_OFFSET_DATETIME")
    public static String toOffsetDateTime(Timestamp timestamp, String timeZone) {
        ZoneId zoneId = null;
        try {
            zoneId = ZoneId.of(timeZone);
        } catch (Exception ignore) {
        }
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        if (zoneId != null) {
            formatter = formatter.withZone(zoneId);
        } else {
            formatter = formatter.withZone(ZoneOffset.UTC);
        }
        return formatter.format(timestamp.toInstant());
    }
}
