package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.PropFieldMeta;
import com.envision.gravity.common.po.TblComponentPref;
import com.envision.gravity.low.level.api.rest.model.AssetModel;
import com.envision.gravity.low.level.api.rest.model.InnerComponentPref;

import com.google.common.collect.Multimap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
@Mapper
public interface PGTblComponentPrefMapper {
    /**
     * select asset_id model_id map by comp_id and pref_id
     *
     * @param schemeName schemeName
     * @param compPrefMap key: comp_id, value: pref_id list
     * @return {@link AssetModel}
     */
    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "selectAssetModelMap")
    @Results({
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR)
    })
    List<AssetModel> selectAssetModelMap(String schemeName, Multimap<String, String> compPrefMap);

    @SelectProvider(
            type = TblComponentPrefSqlProvider.class,
            method = "selectFieldIdsByPropertyUniqueKey")
    List<String> selectFieldIdsByPropertyUniqueKey(String uniqueKey, String orgId);

    @UpdateProvider(type = TblComponentPrefSqlProvider.class, method = "batchReplaceRawFieldId")
    int batchReplaceRawFieldId(List<InnerComponentPref> innerComponentPrefList, String orgId);

    @SelectProvider(
            type = TblComponentPrefSqlProvider.class,
            method = "selectFieldIdsByPrefNameAndCompName")
    List<String> selectFieldIdsByPrefNameAndCompName(
            String prefName, String compName, String orgId);

    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "findPrefByFieldIds")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_id", property = "prefId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_name", property = "prefName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_type", property = "prefType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_data_type", property = "prefDataType", jdbcType = JdbcType.VARCHAR)
    })
    List<PropFieldMeta> findPrefByFieldIds(
            @Param("fieldIds") Set<String> fieldIds, @Param("orgId") String orgId);

    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "findCompPrefByFieldIds")
    @Results({
        @Result(column = "comp_id", property = "compId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_id", property = "prefId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "raw_field_id", property = "rawFieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.INTEGER),
        @Result(column = "horizontal", property = "horizontal", jdbcType = JdbcType.BOOLEAN),
    })
    List<TblComponentPref> findCompPrefByFieldIds(
            @Param("fieldIds") Set<String> fieldIds, @Param("orgId") String orgId);

    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "findCompPrefByPrefName")
    List<Map<String, Object>> findCompPrefByPrefName(
            @Param("compName") String compName,
            @Param("prefName") String prefName,
            @Param("orgId") String orgId);
}
