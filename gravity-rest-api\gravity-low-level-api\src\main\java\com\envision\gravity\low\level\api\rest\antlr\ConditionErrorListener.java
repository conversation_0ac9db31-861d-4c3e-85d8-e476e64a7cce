package com.envision.gravity.low.level.api.rest.antlr;

import org.antlr.v4.runtime.*;

/** @Author: qi.jiang2 @Date: 2024/03/13 17:32 @Description: */
public class ConditionErrorListener extends ConsoleErrorListener {
    @Override
    public void syntaxError(
            Recognizer<?, ?> recognizer,
            Object offendingSymbol,
            int line,
            int charPositionInLine,
            String msg,
            RecognitionException e) {
        if (e != null) {
            throw new RuntimeException("line " + line + ":" + charPositionInLine + " " + msg);
        }
    }
}
