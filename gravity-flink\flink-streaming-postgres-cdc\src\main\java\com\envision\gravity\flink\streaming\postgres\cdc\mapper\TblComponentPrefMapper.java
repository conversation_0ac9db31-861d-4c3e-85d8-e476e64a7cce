package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public interface TblComponentPrefMapper {
    /**
     * @param schemaName schemaName
     * @param compIdList cmpIdList
     * @param prefIdList prefIdList
     * @return {@link ModelGroup} list
     */
    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "selectModelGroupList")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList);

    /**
     * @param schemaName schemaName
     * @param compIdList cmpIdList
     * @param prefIdList prefIdList
     * @return {@link ModelGroup} list
     */
    @SelectProvider(type = TblComponentPrefSqlProvider.class, method = "selectAttrModelGroupList")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectAttrModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList);

    /**
     * @param schemaName schemaName
     * @param compIdList cmpIdList
     * @return {@link ModelGroup} list
     */
    @SelectProvider(
            type = TblComponentPrefSqlProvider.class,
            method = "selectModelGroupListByCompId")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupListByCompId(String schemaName, List<String> compIdList);
}
