package com.envision.gravity.flink.streaming.calculate.aspect;

import com.envision.gravity.flink.streaming.calculate.aspect.processor.AspectCalcJobTaskProcessor;
import com.envision.gravity.flink.streaming.calculate.aspect.sink.AspectCalcKafkaRecordSerializer;
import com.envision.gravity.flink.streaming.calculate.aspect.source.GetCalcJobInfoTimer;
import com.envision.gravity.flink.streaming.calculate.aspect.source.StreamingCalcJobTaskSource;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import com.envision.gravity.flink.streaming.calculate.dto.meta.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.concurrent.TimeUnit;


import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AspectCalc流式计算作业主类
 *
 * <p>功能： 1. 持续运行的流式计算作业 2. 处理多个Job的切面计算 3. 基于最新值进行实时计算 4. 通过KafkaSink输出结果
 *
 * <p>使用方式： flink run -c com.envision.gravity.flink.streaming.calculate.aspect.AspectCalcFlow \ -p 8
 * flink-streaming-calculate-1.0.jar
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class AspectCalcFlow {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcFlow.class);

    public static void main(String[] args) throws Exception {
        logger.info("Starting AspectCalcFlow");

        // 创建执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment();

        // 构建数据流
        buildDataStream(env);

        // 执行作业
        String jobName = "AspectCalcFlow";
        logger.info("Executing AspectCalcFlow: {}", jobName);
        env.execute(jobName);

        logger.info("AspectCalcFlow completed");
    }

    /** 创建执行环境 */
    private static StreamExecutionEnvironment createExecutionEnvironment() {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 基础配置
        env.setParallelism(CalcLionConfig.getAspectCalcDefaultParallelism());

        // Checkpoint配置
        env.enableCheckpointing(
                CalcLionConfig.getAspectCalcCheckpointIntervalMs(), CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig()
                .setCheckpointTimeout(CalcLionConfig.getAspectCalcCheckpointTimeoutMs());
        env.getCheckpointConfig()
                .setMaxConcurrentCheckpoints(CalcLionConfig.getAspectCalcCheckpointMaxConcurrent());

        // 重启策略
        env.setRestartStrategy(
                RestartStrategies.fixedDelayRestart(
                        CalcLionConfig.getAspectCalcRestartAttempts(),
                        Time.of(
                                CalcLionConfig.getAspectCalcRestartDelayMs(),
                                TimeUnit.MILLISECONDS)));

        logger.info(
                "Created execution environment with parallelism: {}",
                CalcLionConfig.getAspectCalcDefaultParallelism());

        return env;
    }

    /** 构建数据流 */
    private static void buildDataStream(StreamExecutionEnvironment env) {
        // ✅ 1. 定时获取多个Job信息（从实时元数据）
        DataStream<CalcJobMetaInfo> jobInfoStream =
                env.addSource(new GetCalcJobInfoTimer(CalcLionConfig.getJobInfoCheckIntervalMs()))
                        .name("GetCalcJobInfoTimer")
                        .uid("get-calc-job-info-timer")
                        .setParallelism(1);

        // ✅ 2. 流式任务源（支持多Job并发）
        DataStream<CalcJobTask> taskStream =
                env.fromSource(
                                StreamingCalcJobTaskSource.builder()
                                        .setTaskCompletionCheckInterval(
                                                CalcLionConfig.getTaskCompletionCheckIntervalMs())
                                        .setMaxJobsPerReader(CalcLionConfig.getMaxJobsPerReader())
                                        .setTaskBatchSize(CalcLionConfig.getTaskBatchSize())
                                        .build(),
                                org.apache.flink.api.common.eventtime.WatermarkStrategy
                                        .noWatermarks(),
                                "StreamingCalcJobTaskSource")
                        .uid("streaming-calc-job-task-source")
                        .setParallelism(CalcLionConfig.getCalcJobSourceReaderParallelism());

        // ✅ 3. 切面计算处理器（高并发处理）
        DataStream<LegacyMsgWithMultiAssets> resultStream =
                taskStream
                        .process(new AspectCalcJobTaskProcessor())
                        .name("AspectCalcJobTaskProcessor")
                        .uid("aspect-calc-job-task-processor")
                        .setParallelism(CalcLionConfig.getAspectCalcJobTaskProcessorParallelism());

        // ✅ 4. KafkaSink输出（并行写入）
        KafkaSink<LegacyMsgWithMultiAssets> kafkaSink =
                KafkaSink.<LegacyMsgWithMultiAssets>builder()
                        .setBootstrapServers(CalcLionConfig.getAspectCalcKafkaBootstrapServers())
                        .setRecordSerializer(new AspectCalcKafkaRecordSerializer())
                        .build();

        resultStream
                .sinkTo(kafkaSink)
                .name("AspectCalcKafkaSink")
                .uid("aspect-calc-kafka-sink")
                .setParallelism(CalcLionConfig.getKafkaSinkParallelism());

        logger.info(
                "Built AspectCalcFlow data stream with parallelism - "
                        + "Timer: 1, TaskSource: {}, Processor: {}, KafkaSink: {}",
                CalcLionConfig.getCalcJobSourceReaderParallelism(),
                CalcLionConfig.getAspectCalcJobTaskProcessorParallelism(),
                CalcLionConfig.getKafkaSinkParallelism());
    }
}
