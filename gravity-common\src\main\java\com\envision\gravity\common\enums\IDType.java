package com.envision.gravity.common.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/7
 * @description
 */
@Getter
public enum IDType {
    OBJECT_ID("OBJECT_ID", 4, 8, 14776336L),
    COMMON_ID("COMMON_ID", 4, 12, 14776336L);
    private final String name;
    private final int generatedLength;
    private final int totalLength;
    private final long maxId;

    IDType(String name, int generatedLength, int totalLength, long maxId) {
        this.name = name;
        this.generatedLength = generatedLength;
        this.totalLength = totalLength;
        this.maxId = maxId;
    }
}
