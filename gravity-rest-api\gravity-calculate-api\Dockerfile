FROM harbor.eniot.io/envisioniot/java8-env:release

ENV APP_NAME gravity-calculate-api
ENV APP_HOME /home/<USER>/gravity-calculate-api

RUN mkdir -p /data/apps/logs $APP_HOME &&\
    chmod 777 /data/apps/logs $APP_HOME

COPY --chown=envuser:envuser ./gravity-rest-api/gravity-calculate-api/target/gravity-calculate-api-*.jar $APP_HOME/gravity-calculate-api.jar
COPY --chown=envuser:envuser ./gravity-rest-api/gravity-calculate-api/startup.sh $APP_HOME/

WORKDIR $APP_HOME

RUN chmod 755 startup.sh

USER envuser

ENTRYPOINT ["bash","./startup.sh"]