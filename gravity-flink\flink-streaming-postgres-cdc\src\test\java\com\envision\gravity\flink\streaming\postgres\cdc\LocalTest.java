package com.envision.gravity.flink.streaming.postgres.cdc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/22
 * @description
 */
class LocalTest {

    @Test
    void contactTest() {
        List<String> list1 = Arrays.asList("apple", "banana", "cherry");
        List<String> list2 = new ArrayList<>();

        System.out.println(
                Stream.concat(list1.stream(), list2.stream())
                        .distinct()
                        .collect(Collectors.toList()));
    }
}
