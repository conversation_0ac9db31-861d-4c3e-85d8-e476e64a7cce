# CalculateProcessor 算子完整设计方案

## 1. 概述

本文档描述了 `CalculateProcessor` 算子的完整技术设计方案，包括直白映射（Direct Mapping）和非直白映射（Non-Direct Mapping）两种计算模式的实现设计。

## 2. 背景介绍

### 2.1 业务场景

`CalculateProcessor` 是 Flink 流式计算中的核心算子，负责处理设备测点数据的实时计算和转换。支持两种主要的计算模式：

- **直白映射（Direct Mapping）**：简单的一对一映射，目标属性直接复制源测点的值
- **非直白映射（Non-Direct Mapping）**：复杂的表达式计算，目标属性通过数学表达式计算得出

### 2.2 技术架构

```
输入数据流 -> CalculateProcessor -> 输出数据流
    ↓              ↓                    ↓
LegacyMsgList -> 计算处理逻辑 -> CalcResultMsg
```

## 3. 整体架构设计

### 3.1 核心组件架构

```mermaid
graph TB
    A[CalculateProcessor] --> B[消息预处理模块]
    A --> C[资产映射查询模块]
    A --> D[计算规则处理模块]
    
    D --> E[直白映射处理器]
    D --> F[非直白映射处理器]
    
    F --> G[数据查询模块]
    F --> H[表达式计算模块]
    
    G --> I[SQL Gateway 查询]
    H --> J[ExpressionUtil 计算引擎]
    
    E --> K[结果构建模块]
    F --> K
    
    K --> L[输出消息生成]
```

### 3.2 数据流处理流程

```mermaid
flowchart TD
    A[接收 LegacyMsgList] --> B[消息类型转换]
    B --> C[提取源资产信息]
    C --> D[查询目标资产映射]
    D --> E[遍历源测点数据]
    
    E --> F[查找关联目标属性]
    F --> G{判断映射类型}
    
    G -->|直白映射| H[直接值复制]
    G -->|非直白映射| I[数据源分析]
    
    I --> J[构建查询请求]
    J --> K[SQL Gateway 查询]
    K --> L[表达式数据准备]
    L --> M[表达式计算]
    
    H --> N[构建输出消息]
    M --> N
    N --> O[返回计算结果]
```

## 4. 直白映射（Direct Mapping）设计

### 4.1 实现原理

直白映射是最简单的计算模式，实现一对一的数据复制：

```java
// 核心逻辑：直接值复制
targetPayload.getPoints().put(targetProp.getPrefName(), srcPointEntry.getValue());
```

### 4.2 处理流程

1. **规则匹配**：根据源测点名称和模型ID查找关联的目标属性
2. **资产过滤**：根据目标资产信息过滤适用的计算规则
3. **直接映射**：将源测点值直接复制到目标属性
4. **消息构建**：构建包含计算结果的输出消息

### 4.3 数据结构

```java
// 输入数据结构
LegacyPayload srcPayload = {
    assetId: "asset001",
    time: 1640995200000,
    points: {
        "temperature": 25.5,
        "pressure": 101.3
    }
}

// 输出数据结构  
LegacyPayload targetPayload = {
    assetId: "target_asset001",
    time: 1640995200000,
    points: {
        "temp_calc": 25.5  // 直接复制 temperature 的值
    }
}
```

### 4.4 性能特点

- **高性能**：无需额外数据查询和复杂计算
- **低延迟**：直接内存操作，延迟极低
- **高可靠性**：逻辑简单，故障率低

## 5. 非直白映射（Non-Direct Mapping）设计

### 5.1 实现原理

非直白映射支持复杂的表达式计算，可以引用多个数据源：

```java
// 表达式示例
"model1.temperature * 1.8 + 32"  // 温度单位转换
"model1.voltage * model2.current"  // 功率计算
"this.speed * 3.6"  // 当前模型的速度转换
```

### 5.2 数据源分类

| 数据源类型 | 判断条件 | 获取方式 | 示例 |
|-----------|---------|---------|------|
| 当前模型测点 | `modelId == current && prefType == MEASUREPOINT` | LegacyPayload 直接获取 | 当前消息中的温度值 |
| 当前模型属性 | `modelId == current && prefType == ATTRIBUTE` | SQL Gateway 查询 | 设备的额定功率属性 |
| 其他模型数据 | `modelId != current` | SQL Gateway 查询 | 关联设备的状态信息 |

### 5.3 SQL Gateway 查询设计

#### 5.3.1 查询接口设计

```java
/**
 * 从 SQL Gateway 查询资产的最新值
 * @param orgId 组织ID
 * @param modelId 模型ID
 * @param assetPropertyInfoMap 资产属性信息映射 Map<assetId, List<PropertyInfo>>
 * @return 查询结果 Map<assetId, LegacyPayload>
 */
public Map<String, LegacyPayload> queryLatestValues(
    String orgId, 
    String modelId, 
    Map<String, List<PropertyInfo>> assetPropertyInfoMap
);
```

#### 5.3.2 动态 SQL 模板设计

使用 Velocity 模板引擎构建动态 SQL，直接遍历 `assetPropertyInfoMap` 的值：

**Velocity 模板文件 (query-latest-values.vm):**
```sql
SELECT /*+ ORG('$orgId') */
#foreach($property in $allProperties)
    /*+ SET(`$property.prefName`=TS(`$property.prefName`)) */
#end
    asset_id#if($allProperties.size() > 0),#end
#foreach($property in $allProperties)
    #if($property.prefType.name() == "ATTRIBUTE")
    `$property.prefName` AS `$property.prefName`#if($foreach.hasNext),#end
    #elseif($property.prefType.name() == "MEASUREPOINT")
    `${property.prefName}.time` AS `${property.prefName}_time`,
    `${property.prefName}.value` AS `${property.prefName}_value`#if($foreach.hasNext),#end
    #end
#end
FROM `$modelId`
WHERE asset_id IN (#foreach($assetId in $assetIds)'$assetId'#if($foreach.hasNext), #end#end)
```

**Java 模板处理逻辑:**
```java
/**
 * 构建动态 SQL 查询语句
 * @param orgId 组织ID
 * @param modelId 模型ID
 * @param assetPropertyInfoMap 资产属性信息映射
 * @return 动态生成的 SQL 语句
 */
private String buildDynamicSql(String orgId, String modelId,
                              Map<String, List<PropertyInfo>> assetPropertyInfoMap) {

    if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
        logger.warn("assetPropertyInfoMap 为空，跳过查询");
        return null;
    }

    // 收集所有唯一的 PropertyInfo，遍历 assetPropertyInfoMap 的 values
    Set<PropertyInfo> allProperties = new HashSet<>();
    for (List<PropertyInfo> propertyList : assetPropertyInfoMap.values()) {
        if (propertyList != null) {
            allProperties.addAll(propertyList);
        }
    }

    if (allProperties.isEmpty()) {
        logger.warn("assetPropertyInfoMap 中未找到属性信息，跳过查询");
        return null;
    }

    // 准备 Velocity 上下文
    VelocityContext context = new VelocityContext();
    context.put("orgId", orgId);
    context.put("modelId", modelId);
    context.put("allProperties", allProperties);
    context.put("assetIds", assetPropertyInfoMap.keySet());

    // 渲染模板
    StringWriter writer = new StringWriter();
    Template template = velocityEngine.getTemplate("query-latest-values.vm");
    template.merge(context, writer);

    return writer.toString();
}
```

**核心改进点:**
1. **直接遍历处理**: 遍历 `assetPropertyInfoMap.values()` 中的每个 `PropertyInfo`
2. **根据 prefType 判断**: 根据 `PropertyInfo.prefType` 判断是测点还是属性
3. **正确的 SQL 拼接**:
   - 属性: `attrName` AS `attrName`
   - 测点: `pointName.time` AS `pointName_time`, `pointName.value` AS `pointName_value`
4. **完善的空值检查**: 对 `assetPropertyInfoMap` 进行完整的空值判断

#### 5.3.3 完整查询方法实现

```java
/**
 * 从 SQL Gateway 查询资产的最新值
 * @param orgId 组织ID
 * @param modelId 模型ID
 * @param assetPropertyInfoMap 资产属性信息映射
 * @return 查询结果
 */
public Map<String, LegacyPayload> queryLatestValues(
        String orgId,
        String modelId,
        Map<String, List<PropertyInfo>> assetPropertyInfoMap) {

    if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
        logger.debug("assetPropertyInfoMap 为空，返回空结果");
        return new HashMap<>();
    }

    // 构建动态 SQL
    String sql = buildDynamicSql(orgId, modelId, assetPropertyInfoMap);
    if (sql == null) {
        logger.warn("SQL 构建失败，返回空结果");
        return new HashMap<>();
    }

    logger.debug("执行 SQL Gateway 查询: {}", sql);

    Map<String, LegacyPayload> result = new HashMap<>();

    try (Connection connection = sqlGatewayDataSource.getConnection();
         PreparedStatement statement = connection.prepareStatement(sql);
         ResultSet resultSet = statement.executeQuery()) {

        result = parseResultSetToLegacyPayload(resultSet, assetPropertyInfoMap);

    } catch (SQLException e) {
        logger.error("SQL Gateway 查询失败: {}", e.getMessage(), e);
        // 返回空结果而不是抛异常，允许部分处理
        return new HashMap<>();
    }

    logger.debug("SQL Gateway 查询完成，返回 {} 个资产", result.size());
    return result;
}

/**
 * 解析 ResultSet 为 LegacyPayload 映射
 * @param resultSet SQL 查询结果集
 * @param assetPropertyInfoMap 原始属性信息映射，用于参考
 * @return Map<assetId, LegacyPayload>
 */
private Map<String, LegacyPayload> parseResultSetToLegacyPayload(
        ResultSet resultSet,
        Map<String, List<PropertyInfo>> assetPropertyInfoMap) throws SQLException {

    Map<String, LegacyPayload> result = new HashMap<>();

    while (resultSet.next()) {
        String assetId = resultSet.getString("asset_id");

        LegacyPayload payload = result.computeIfAbsent(assetId, k -> {
            LegacyPayload newPayload = new LegacyPayload();
            newPayload.setAssetId(assetId);
            newPayload.setPoints(new HashMap<>());
            newPayload.setTime(System.currentTimeMillis()); // 默认时间戳
            return newPayload;
        });

        // 获取该资产的属性列表
        List<PropertyInfo> propertyList = assetPropertyInfoMap.get(assetId);
        if (propertyList == null) {
            continue;
        }

        long maxTimestamp = payload.getTime();

        // 处理每个属性
        for (PropertyInfo property : propertyList) {
            String prefName = property.getPrefName();
            PrefType prefType = property.getPrefType();

            try {
                if (prefType == PrefType.ATTRIBUTE) {
                    // 属性：直接获取值
                    Object value = resultSet.getObject(prefName);
                    if (value != null) {
                        payload.getPoints().put(prefName, value);
                    }

                } else if (prefType == PrefType.MEASUREPOINT) {
                    // 测点：获取时间和值
                    String timeColumn = prefName + "_time";
                    String valueColumn = prefName + "_value";

                    Object value = resultSet.getObject(valueColumn);
                    Object timeObj = resultSet.getObject(timeColumn);

                    if (value != null) {
                        payload.getPoints().put(prefName, value);

                        // 更新时间戳
                        if (timeObj != null) {
                            long timestamp = convertToTimestamp(timeObj);
                            maxTimestamp = Math.max(maxTimestamp, timestamp);
                        }
                    }
                }
            } catch (SQLException e) {
                logger.warn("解析属性 {} 失败: {}", prefName, e.getMessage());
                // 继续处理其他属性
            }
        }

        // 更新 payload 时间戳为找到的最大时间戳
        payload.setTime(maxTimestamp);
    }

    return result;
}

/**
 * 将各种时间对象转换为时间戳
 */
private long convertToTimestamp(Object timeObj) {
    if (timeObj instanceof Long) {
        return (Long) timeObj;
    } else if (timeObj instanceof Timestamp) {
        return ((Timestamp) timeObj).getTime();
    } else if (timeObj instanceof java.util.Date) {
        return ((java.util.Date) timeObj).getTime();
    } else {
        // 尝试解析为字符串
        try {
            return Long.parseLong(timeObj.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法解析时间戳: {}", timeObj);
            return System.currentTimeMillis();
        }
    }
}
```

#### 5.3.4 SQL 生成示例

**输入数据示例:**
```java
assetPropertyInfoMap = {
    "asset001": [
        PropertyInfo{prefName="temperature", prefType=MEASUREPOINT},
        PropertyInfo{prefName="rated_power", prefType=ATTRIBUTE}
    ],
    "asset002": [
        PropertyInfo{prefName="temperature", prefType=MEASUREPOINT},
        PropertyInfo{prefName="pressure", prefType=MEASUREPOINT}
    ]
}
```

**生成的 SQL:**
```sql
SELECT /*+ ORG('o123456') */
    /*+ SET(`temperature`=TS(`temperature`)) */
    /*+ SET(`rated_power`=TS(`rated_power`)) */
    /*+ SET(`pressure`=TS(`pressure`)) */
    asset_id,
    `rated_power` AS `rated_power`,
    `temperature.time` AS `temperature_time`,
    `temperature.value` AS `temperature_value`,
    `pressure.time` AS `pressure_time`,
    `pressure.value` AS `pressure_value`
FROM `modelId`
WHERE asset_id IN ('asset001', 'asset002')
```

**关键改进说明:**
1. **简化 SET Hint 逻辑**: 所有属性和测点都使用相同的 `/*+ SET(\`prefName\`=TS(\`prefName\`)) */` 语法
2. **保留 SELECT 字段区分**: 在 SELECT 部分仍然根据 `prefType` 区分属性和测点的字段格式

**查询结果解析:**
```java
// 返回结果示例
Map<String, LegacyPayload> result = {
    "asset001": LegacyPayload{
        assetId: "asset001",
        time: 1640995200000,  // 取最大时间戳
        points: {
            "temperature": 25.5,
            "rated_power": 1000.0
        }
    },
    "asset002": LegacyPayload{
        assetId: "asset002",
        time: 1640995200000,
        points: {
            "temperature": 23.8,
            "pressure": 101.3
        }
    }
}
```

### 5.4 表达式计算流程

#### 5.4.1 数据准备策略

```java
Map<String, Object> calcData = new HashMap<>();

// 策略1：当前模型测点数据
// Key: LegacyMsg.modelId + "." + prefName
// Value: srcPayload.points.get(prefName)

// 策略2：查询获取的数据
// Key: SrcPrefItem.modelId + "." + prefName  
// Value: queryResult.points.get(prefName)

// 策略3：当前模型表达式（isExprUseCurrentModel = true）
// Key: "this." + prefName
// Value: 对应的测点值
```

#### 5.4.2 表达式计算调用

```java
CalcExpressionRequest calcRequest = new CalcExpressionRequest();
List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);

calcInputs.add(
    ExpressionCalculatorInput.builder()
        .expression(targetProp.getExpression())
        .data(calcData)
        .build());
calcRequest.setCalcInputs(calcInputs);

List<CalcExpressionResponse> calcResult = ExpressionUtil.calcExpression(calcRequest);
ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();
```

## 6. 资源管理设计

### 6.1 连接池管理

```java
// SQL Gateway 连接池配置
private transient HikariDataSource sqlGatewayDataSource;

@Override
public void open(Configuration parameters) throws Exception {
    super.open(parameters);
    initSqlGatewayDataSource();
}

@Override
public void close() throws Exception {
    if (sqlGatewayDataSource != null) {
        sqlGatewayDataSource.close();
    }
    super.close();
}
```

### 6.2 缓存管理

```java
// 计算规则缓存
private CalcPrefCache calcPrefCache;

@Override
public void open(Configuration parameters) throws Exception {
    this.calcPrefCache = CacheFactory.getCalcPrefCache();
}
```

## 7. 错误处理策略

### 7.1 错误分类处理

| 错误类型 | 处理策略 | 日志级别 | 影响范围 |
|---------|---------|---------|---------|
| SQL Gateway 连接失败 | 跳过计算，记录错误 | ERROR | 单个计算任务 |
| SQL 执行失败 | 跳过计算，记录错误 | ERROR | 单个查询 |
| 表达式计算失败 | 抛出异常 | ERROR | 整个消息处理 |
| 数据类型转换失败 | 使用默认值，记录警告 | WARN | 单个数据点 |

### 7.2 容错机制

```java
try {
    // 表达式计算
    calcResult = ExpressionUtil.calcExpression(calcRequest);
} catch (Exception e) {
    logger.error(
        "Calculate failed, orgId: {}, param: {}, reason {}",
        orgId, calcRequest, e.getMessage());
    throw new CalcFailedException(e);
}
```

## 8. 性能优化策略

### 8.1 批量处理优化

- **数据分组**：按 modelId 分组减少查询次数
- **批量查询**：单次查询支持多个 assetId
- **连接复用**：使用连接池避免频繁建立连接

### 8.2 内存优化

- **及时释放**：处理完成后及时释放大对象引用
- **流式处理**：避免大量数据在内存中堆积
- **合理配置**：设置合适的连接池大小

### 8.3 缓存策略

- **规则缓存**：`calcPrefCache` 由 CDC 模块负责实时更新维护，无需额外配置
- **模板缓存**：缓存编译后的 Velocity SQL 模板，提高模板渲染性能
- **结果缓存**：考虑短期缓存频繁查询的属性值（可选优化）

## 9. 监控和运维

### 9.1 关键指标

- **处理延迟**：消息处理的端到端延迟
- **吞吐量**：每秒处理的消息数量
- **错误率**：计算失败的比例
- **连接池状态**：活跃连接数、等待连接数

### 9.2 告警机制

- **高延迟告警**：处理延迟超过阈值
- **高错误率告警**：错误率超过阈值
- **连接池告警**：连接池资源不足
- **SQL Gateway 不可用告警**：查询服务异常

## 10. 配置管理

### 10.1 必需配置项

```properties
# SQL Gateway 连接配置
gravity-common.sql-gateway.jdbc-url=*************************
gravity-common.sql-gateway.calculate.username=username
gravity-common.sql-gateway.calculate.password=password

# 连接池配置
gravity-flink.calc-stream.sql-gateway.pool.max-size=10
gravity-flink.calc-stream.sql-gateway.pool.timeout=30000
```

### 10.2 可选配置项

```properties
# 性能调优配置
gravity-flink.calc-stream.batch.size=1000
gravity-flink.calc-stream.query.timeout=5000
# 注意：calcPrefCache 由 CDC 模块负责更新维护，无需配置 TTL
```

## 11. 实施计划

### 11.1 开发阶段

#### Phase 1: 基础设施完善（已完成）
- ✅ 直白映射功能实现
- ✅ 基础算子框架搭建
- ✅ 缓存集成和资源管理

#### Phase 2: 非直白映射核心功能（进行中）
- 🚧 SQL Gateway 连接池管理
- 🚧 数据查询模块实现
- 🚧 表达式计算集成

#### Phase 3: 功能完善和优化
- ⏳ 错误处理机制完善
- ⏳ 性能优化和调优
- ⏳ 监控指标集成

#### Phase 4: 测试和部署
- ⏳ 单元测试和集成测试
- ⏳ 性能基准测试
- ⏳ 生产环境部署

### 11.2 测试策略

#### 11.2.1 单元测试

```java
// 直白映射测试
@Test
public void testDirectMapping() {
    // 测试简单的一对一映射
}

// 非直白映射测试
@Test
public void testNonDirectMapping() {
    // 测试复杂表达式计算
}

// SQL Gateway 查询测试
@Test
public void testQueryLatestValues() {
    // 测试数据查询功能
}
```

#### 11.2.2 集成测试

```java
// 端到端测试
@Test
public void testEndToEndProcessing() {
    // 测试完整的消息处理流程
}

// 性能测试
@Test
public void testPerformanceBenchmark() {
    // 测试处理性能和资源使用
}
```

#### 11.2.3 测试数据准备

- **模拟数据源**：准备各种类型的测试消息
- **规则配置**：配置不同复杂度的计算规则
- **环境准备**：搭建测试环境的 SQL Gateway

## 12. 技术依赖

### 12.1 核心依赖

```xml
<!-- Flink 流处理框架 -->
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-streaming-java_2.12</artifactId>
</dependency>

<!-- 表达式计算引擎 -->
<dependency>
    <groupId>com.univers.business.object</groupId>
    <artifactId>calc-util</artifactId>
</dependency>

<!-- Velocity 模板引擎 -->
<dependency>
    <groupId>org.apache.velocity</groupId>
    <artifactId>velocity-engine-core</artifactId>
</dependency>

<!-- HikariCP 连接池 -->
<dependency>
    <groupId>com.zaxxer</groupId>
    <artifactId>HikariCP</artifactId>
</dependency>

<!-- MySQL JDBC 驱动 -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>
```

### 12.2 内部依赖

- `gravity-common`: 通用工具类和配置管理
- `gravity-cache`: 计算规则缓存
- `flink-common`: Flink 通用组件

## 13. 部署和运维

### 13.1 部署要求

#### 13.1.1 环境要求

- **Flink 版本**: 1.14+
- **Java 版本**: JDK 8+
- **内存要求**: 最小 2GB，推荐 4GB+
- **网络要求**: 能够访问 SQL Gateway 服务

#### 13.1.2 配置检查清单

- [ ] SQL Gateway 连接配置正确
- [ ] 计算规则缓存可用
- [ ] 日志级别配置合理
- [ ] 监控指标配置完整

### 13.2 运维监控

#### 13.2.1 关键监控指标

```java
// 处理性能指标
- 消息处理延迟 (ms)
- 消息处理吞吐量 (msg/s)
- 计算成功率 (%)

// 资源使用指标
- CPU 使用率 (%)
- 内存使用率 (%)
- 连接池活跃连接数

// 错误监控指标
- SQL Gateway 查询失败率 (%)
- 表达式计算失败率 (%)
- 系统异常次数
```

#### 13.2.2 告警规则

```yaml
# 性能告警
- 消息处理延迟 > 5000ms
- 消息处理吞吐量 < 100 msg/s
- 计算成功率 < 95%

# 资源告警
- CPU 使用率 > 80%
- 内存使用率 > 85%
- 连接池使用率 > 90%

# 错误告警
- SQL Gateway 查询失败率 > 5%
- 表达式计算失败率 > 1%
- 系统异常次数 > 10/min
```

## 14. 常见问题和解决方案

### 14.1 性能问题

#### Q1: 处理延迟过高
**原因**: SQL Gateway 查询慢、表达式计算复杂
**解决方案**:
- 优化 SQL 查询语句
- 增加连接池大小
- 简化复杂表达式

#### Q2: 内存使用过高
**原因**: 大量数据缓存、连接池配置不当
**解决方案**:
- 及时释放大对象
- 调整连接池配置
- 增加 JVM 堆内存

### 14.2 功能问题

#### Q1: 计算结果不正确
**原因**: 表达式错误、数据类型不匹配
**解决方案**:
- 验证表达式语法
- 检查数据类型转换
- 增加数据校验逻辑

#### Q2: SQL Gateway 连接失败
**原因**: 网络问题、配置错误、服务不可用
**解决方案**:
- 检查网络连通性
- 验证连接配置
- 确认服务状态

## 15. 最佳实践

### 15.1 开发最佳实践

1. **表达式设计**: 保持表达式简洁，避免过度复杂的计算
2. **错误处理**: 对所有外部调用添加异常处理
3. **日志记录**: 记录关键处理步骤和错误信息
4. **资源管理**: 确保资源的正确打开和关闭

### 15.2 运维最佳实践

1. **监控覆盖**: 建立完整的监控体系
2. **容量规划**: 根据业务量合理配置资源
3. **故障演练**: 定期进行故障恢复演练
4. **版本管理**: 建立规范的版本发布流程

---

**文档版本**: 1.0
**最后更新**: 2024-12-19
**作者**: 系统架构团队
