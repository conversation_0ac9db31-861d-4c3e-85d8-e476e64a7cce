package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;


import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.UpdateProvider;

public interface TblPrefUpstreamMapper {
    @InsertProvider(type = TblPrefUpstreamSqlProvider.class, method = "insert")
    int insert(String orgId, String ruleId, BaseCalcPropertyMeta calcPrefMeta);

    @DeleteProvider(type = TblPrefUpstreamSqlProvider.class, method = "deleteByRuleId")
    int deleteByRuleId(String orgId, String ruleId);

    @UpdateProvider(type = TblPrefUpstreamSqlProvider.class, method = "updateExprByRuleId")
    int updateExprByRuleId(String orgId, String ruleId, String expr);
}
