package com.envision.gravity.ignite.tsdb.loader;

import java.util.Map;
import java.util.Set;


import io.eniot.tsdb.adapter.interpolator.InterpolatorType;
import io.eniot.tsdb.adapter.query.Query;
import io.eniot.tsdb.common.Retention;
import io.eniot.tsdb.common.TimeRange;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> 2024/3/19 */
@Getter
@Setter
public class TSDBSdkParams {

    private String orgId;

    private Map<String, Set<Query.AggregationType>> fieldIdWithAggrs;

    private TimeRange timeRange;

    private Set<String> systemIds;

    private Retention timeGroup;

    private Integer slimit;

    private Map<String, Set<String>> prefName2fieldId;

    private Map<String, String> asset2systemId;

    private Boolean useLocalTime;

    // timezone => assetId set
    private Map<String, Set<String>> timeZone2assetId;

    private TSDBQueryType queryType;

    // Interpolation interval is timeGroup
    private InterpolatorType interpolatorType;

    public TSDBSdkParams() {}

    @Override
    public String toString() {
        return "TSDBSdkParams{"
                + "orgId='"
                + orgId
                + '\''
                + ", fieldIdWithAggrs="
                + fieldIdWithAggrs
                + ", timeRange="
                + timeRange
                + ", systemIds="
                + systemIds
                + ", timeGroup="
                + timeGroup
                + ", slimit="
                + slimit
                + ", prefName2fieldId="
                + prefName2fieldId
                + ", asset2systemId="
                + asset2systemId
                + ", useLocalTime="
                + useLocalTime
                + ", timeZone2assetId="
                + timeZone2assetId
                + ", queryType="
                + queryType
                + ", interpolatorType="
                + interpolatorType
                + '}';
    }
}
