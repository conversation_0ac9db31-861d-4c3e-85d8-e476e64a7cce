package com.envision.gravity.low.level.api.sql.table;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


import org.apache.ignite.cache.QueryIndex;

/**
 * <AUTHOR>
 * @date 2024/7/31
 * @description
 */
public interface CacheCfgTableInfo {
    Set<String> getQueryEntityKeyFields();

    Set<String> getNotNullFields();

    List<QueryIndex> getIndexes();

    LinkedHashMap<String, String> getQueryEntityFields();

    Map<String, Object> getDefaultFieldValues();

    String getAffKeyFieldName();
}
