package com.envision.gravity.flink.steaming.bo.event.generator;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.definition.bo.*;
import com.envision.gravity.common.event.AssetEventType;
import com.envision.gravity.flink.steaming.bo.event.entity.Constants;
import com.envision.gravity.flink.steaming.bo.event.entity.EventMsg;
import com.envision.gravity.flink.steaming.bo.event.entity.EventTableName;
import com.envision.gravity.flink.steaming.bo.event.entity.table.ObjectDetailOrigin;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblEdge;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblStartVid;

import java.sql.Timestamp;
import java.util.*;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Slf4j
public class AssetEventGenerator implements Generator {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public EventMsg generateByCreate(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                if (EventTableName.TBL_START_VID.getName().equalsIgnoreCase(record.getTable())) {
                    TblStartVid after = (TblStartVid) record.getAfter();
                    if (after == null) {
                        log.error(
                                "Generate event due to root node create: the cdc record has a null 'after' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = after.getModifiedTime();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObjectRelation(
                                            BusinessObjectRelationDef.builder()
                                                    .assetId(after.getStartVid())
                                                    .relationUpserted(
                                                            Relation.builder()
                                                                    .graphId(after.getSubGraphId())
                                                                    .toVid(after.getStartVid())
                                                                    .build())
                                                    .build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(after.getStartVid())
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.TOPO_CHANGED)
                                    .payload(payload)
                                    .build());
                } else if (EventTableName.TBL_EDGE.getName().equalsIgnoreCase(record.getTable())) {
                    TblEdge after = (TblEdge) record.getAfter();
                    if (after == null) {
                        log.error(
                                "Generate event due to edge create: the cdc record has a null 'after' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = after.getModifiedTime();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObjectRelation(buildEdgeUpsertedBuilder(after).build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(after.getToVid())
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.TOPO_CHANGED)
                                    .payload(payload)
                                    .build());
                } else if (EventTableName.OBJECT_DETAIL_ORIGIN
                        .getName()
                        .equalsIgnoreCase(record.getTable())) {
                    ObjectDetailOrigin after = (ObjectDetailOrigin) record.getAfter();
                    if (after == null) {
                        log.error(
                                "Generate event due to asset create: the cdc record has a null 'after' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = after.getModifiedTime();
                    AssetModelMap assetModelMap = getAssetModelMap(after);
                    String assetId = assetModelMap.getAssetId();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObject(
                                            buildBusinessObjectBuilder(after, assetModelMap)
                                                    .build())
                                    .dataObject(
                                            buildDataObjectBuilder(after, assetModelMap).build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(assetId)
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.CREATE)
                                    .payload(payload)
                                    .build());
                } else {
                    log.error(
                            "Generate event due to asset create: unknown table name: {}",
                            record.getTable());
                }
            } catch (JsonProcessingException e) {
                log.error(
                        "Generate event due to asset create: parse the cdc record error, record: {}",
                        record,
                        e);
            } catch (Exception e) {
                log.error(
                        "Generate event due to asset create: unknown exception, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    @Override
    public EventMsg generateByDelete(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                if (EventTableName.TBL_START_VID.getName().equalsIgnoreCase(record.getTable())) {
                    TblStartVid before = (TblStartVid) record.getBefore();
                    if (before == null) {
                        log.error(
                                "Generate event due to root node delete: the cdc record has a null 'before' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = before.getModifiedTime();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObjectRelation(
                                            BusinessObjectRelationDef.builder()
                                                    .assetId(before.getStartVid())
                                                    .relationDeleted(
                                                            Relation.builder()
                                                                    .graphId(before.getSubGraphId())
                                                                    .toVid(before.getStartVid())
                                                                    .build())
                                                    .build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(before.getStartVid())
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.TOPO_CHANGED)
                                    .payload(payload)
                                    .build());
                } else if (EventTableName.TBL_EDGE.getName().equalsIgnoreCase(record.getTable())) {
                    TblEdge before = (TblEdge) record.getBefore();
                    if (before == null) {
                        log.error(
                                "Generate event due to edge delete: the cdc record has a null 'before' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = before.getModifiedTime();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObjectRelation(
                                            BusinessObjectRelationDef.builder()
                                                    .assetId(before.getToVid())
                                                    .relationDeleted(
                                                            Relation.builder()
                                                                    .graphId(before.getSubGraphId())
                                                                    .fromVid(before.getFromVid())
                                                                    .toVid(before.getToVid())
                                                                    .edgeType(
                                                                            before.getEdgeTypeId())
                                                                    .build())
                                                    .build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(before.getToVid())
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.TOPO_CHANGED)
                                    .payload(payload)
                                    .build());
                } else if (EventTableName.OBJECT_DETAIL_ORIGIN
                        .getName()
                        .equalsIgnoreCase(record.getTable())) {
                    ObjectDetailOrigin before = (ObjectDetailOrigin) record.getBefore();
                    if (before == null) {
                        log.error(
                                "Generate event due to asset delete: the cdc record has a null 'before' field, record: {}",
                                record);
                        return null;
                    }

                    long eventTime = before.getModifiedTime();
                    AssetModelMap assetModelMap = getAssetModelMap(before);
                    String assetId = assetModelMap.getAssetId();
                    String systemId = assetModelMap.getSystemId();

                    BODefinition payload =
                            BODefinition.builder()
                                    .businessObject(
                                            BusinessObjectDef.builder()
                                                    .assetId(assetId)
                                                    .before(
                                                            buildBusinessObjectBuilder(
                                                                            before, assetModelMap)
                                                                    .build())
                                                    .build())
                                    .dataObject(
                                            DataObjectDef.builder()
                                                    .systemId(systemId)
                                                    .before(
                                                            buildDataObjectBuilder(
                                                                            before, assetModelMap)
                                                                    .build())
                                                    .build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(assetId)
                                    .eventTime(eventTime)
                                    .eventType(AssetEventType.DELETE)
                                    .payload(payload)
                                    .build());
                } else {
                    log.error(
                            "Generate event due to asset delete: unknown table name: {}",
                            record.getTable());
                }
            } catch (JsonProcessingException e) {
                log.error(
                        "Generate event due to asset delete: parse the cdc record error, record: {}",
                        record,
                        e);
            } catch (Exception e) {
                log.error(
                        "Generate event due to asset delete: unknown exception, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    @Override
    public EventMsg generateByUpdate(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                if (EventTableName.TBL_START_VID.getName().equalsIgnoreCase(record.getTable())) {
                    return null;
                } else if (EventTableName.TBL_EDGE.getName().equalsIgnoreCase(record.getTable())) {
                    TblEdge before = (TblEdge) record.getBefore();
                    TblEdge after = (TblEdge) record.getAfter();
                    if (after == null) {
                        log.error(
                                "Generate event due to edge update: the cdc record has a null 'after' field, record: {}",
                                record);
                        return null;
                    }

                    if (!Objects.equals(before.getPropValue(), after.getPropValue())) {
                        long eventTime = after.getModifiedTime();

                        BODefinition payload =
                                BODefinition.builder()
                                        .businessObjectRelation(
                                                buildEdgeUpsertedBuilder(after).build())
                                        .build();

                        return buildEventMsg(
                                BuildEventMsgReq.builder()
                                        .record(record)
                                        .key(after.getToVid())
                                        .eventTime(eventTime)
                                        .eventType(AssetEventType.TOPO_CHANGED)
                                        .payload(payload)
                                        .build());
                    }
                } else if (EventTableName.OBJECT_DETAIL_ORIGIN
                        .getName()
                        .equalsIgnoreCase(record.getTable())) {
                    ObjectDetailOrigin before = (ObjectDetailOrigin) record.getBefore();
                    ObjectDetailOrigin after = (ObjectDetailOrigin) record.getAfter();
                    if (before == null || after == null) {
                        log.error(
                                "Generate event due to asset update: the cdc record has a null 'before' field or 'after' field, record: {}",
                                record);
                        return null;
                    }

                    if (!Objects.equals(before.getAssetDisplayName(), after.getAssetDisplayName())
                            || !Objects.equals(before.getAssetTags(), after.getAssetTags())
                            || !Objects.equals(before.getAttributes(), after.getAttributes())
                            || !Objects.equals(
                                    before.getAssetCreatedUser(), after.getAssetCreatedUser())
                            || !Objects.equals(
                                    before.getAssetModifiedUser(), after.getAssetModifiedUser())
                            || !Objects.equals(
                                    before.getAssetCreatedTime(), after.getAssetCreatedTime())) {

                        long eventTime = after.getModifiedTime();

                        AssetModelMap assetModelMap = getAssetModelMap(after);
                        String assetId = assetModelMap.getAssetId();

                        BODefinition payload =
                                BODefinition.builder()
                                        .businessObject(
                                                buildBusinessObjectBuilder(after, assetModelMap)
                                                        .before(
                                                                buildBusinessObjectBuilder(
                                                                                before,
                                                                                assetModelMap)
                                                                        .build())
                                                        .build())
                                        .dataObject(
                                                buildDataObjectBuilder(after, assetModelMap)
                                                        .before(
                                                                buildDataObjectBuilder(
                                                                                before,
                                                                                assetModelMap)
                                                                        .build())
                                                        .build())
                                        .build();

                        return buildEventMsg(
                                BuildEventMsgReq.builder()
                                        .record(record)
                                        .key(assetId)
                                        .eventTime(eventTime)
                                        .eventType(AssetEventType.METADATA_CHANGED)
                                        .payload(payload)
                                        .build());
                    }
                } else {
                    log.error(
                            "Generate event due to asset update: unknown table name: {}",
                            record.getTable());
                }
            } catch (JsonProcessingException e) {
                log.error(
                        "Generate event due to asset update: parse the cdc record error, record: {}",
                        record,
                        e);
            } catch (Exception e) {
                log.error(
                        "Generate event due to asset update: unknown exception, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    private BusinessObjectDef.BusinessObjectDefBuilder buildBusinessObjectBuilder(
            ObjectDetailOrigin objectDetailOrigin, AssetModelMap assetModelMap)
            throws JsonProcessingException {
        String assetId = assetModelMap.getAssetId();
        List<String> modelIds = Collections.singletonList(assetModelMap.getModelId());
        String systemId = assetModelMap.getSystemId();

        return BusinessObjectDef.builder()
                .assetId(assetId)
                .modelIds(modelIds)
                .name(
                        objectDetailOrigin.getAssetDisplayName() != null
                                ? JSONObject.parseObject(objectDetailOrigin.getAssetDisplayName())
                                : null)
                .tags(
                        objectDetailOrigin.getAssetTags() != null
                                ? OBJECT_MAPPER.readValue(
                                        objectDetailOrigin.getAssetTags(),
                                        new TypeReference<Map<String, String>>() {})
                                : null)
                .systemId(systemId)
                .createdUser(objectDetailOrigin.getCreatedUser())
                .modifiedUser(objectDetailOrigin.getModifiedUser())
                .createdTime(new Timestamp(objectDetailOrigin.getCreatedTime()))
                .modifiedTime(new Timestamp(objectDetailOrigin.getModifiedTime()));
    }

    private DataObjectDef.DataObjectDefBuilder buildDataObjectBuilder(
            ObjectDetailOrigin objectDetailOrigin, AssetModelMap assetModelMap)
            throws JsonProcessingException {
        String modelId = assetModelMap.getModelId();
        String systemId = assetModelMap.getSystemId();

        return DataObjectDef.builder()
                .systemId(systemId)
                .models(
                        Collections.singletonList(
                                ModelAttributes.builder()
                                        .modelId(modelId)
                                        .attributes(
                                                objectDetailOrigin.getAttributes() != null
                                                        ? OBJECT_MAPPER.readValue(
                                                                objectDetailOrigin.getAttributes(),
                                                                new TypeReference<
                                                                        Map<String, Object>>() {})
                                                        : null)
                                        .build()));
    }

    private BusinessObjectRelationDef.BusinessObjectRelationDefBuilder buildEdgeUpsertedBuilder(
            TblEdge edge) {
        return BusinessObjectRelationDef.builder()
                .assetId(edge.getToVid())
                .relationUpserted(
                        Relation.builder()
                                .graphId(edge.getSubGraphId())
                                .fromVid(edge.getFromVid())
                                .toVid(edge.getToVid())
                                .edgeType(edge.getEdgeTypeId())
                                .build());
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AssetModelMap {
        private String assetId;
        private String modelId;
        private String systemId;
    }

    private AssetModelMap getAssetModelMap(ObjectDetailOrigin objectDetailOrigin) {
        String assetId = objectDetailOrigin.getAssetId();
        // TODO need to get modelIds from assetId?
        String modelId = objectDetailOrigin.getModelId();
        // TODO need to get systemId from assetId?

        return new AssetModelMap(assetId, modelId, assetId);
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BuildEventMsgReq {
        private ConvertedCdcRecord record;
        private String key;
        private Long eventTime;
        private AssetEventType eventType;
        private BODefinition payload;
    }

    private EventMsg buildEventMsg(BuildEventMsgReq req) {
        EventMsg event = new EventMsg();
        event.setTsMs(System.currentTimeMillis());
        event.setEventTime(req.getEventTime());
        event.setEventSource(Constants.DFLT_ASSET_EVENT_SOURCE);
        event.setEventType(req.getEventType().name().toUpperCase());
        event.setOrgId(req.getRecord().getDb());
        event.setKey(req.getKey());
        event.setPayload(req.getPayload());
        event.setVersion(Constants.DFLT_VERSION);
        return event;
    }
}
