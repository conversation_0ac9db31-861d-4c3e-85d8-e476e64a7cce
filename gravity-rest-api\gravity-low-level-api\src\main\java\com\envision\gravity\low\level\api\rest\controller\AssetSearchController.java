package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.search.asset.SearchAssetReq;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.exception.ParamInvalidException;
import com.envision.gravity.low.level.api.rest.service.SearchAssetsByModelAPIService;
import com.envision.gravity.low.level.api.rest.service.SearchAssetsService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

import java.util.Collections;
import java.util.List;

import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.*;

/** @Author: qi.jiang2 @Date: 2024/03/05 15:33 @Description: */
@Api(tags = "Objects search")
@RestController
@Validated
@RequestMapping("/objects")
public class AssetSearchController {

    @Resource private SearchAssetsService searchAssetsService;
    @Resource private SearchAssetsByModelAPIService searchAssetsByModelAPIService;

    @GravityLog
    @PostMapping("/search")
    public ResponseResult<?> searchAssets(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody SearchAssetReq searchAssetReq,
            HttpServletRequest request) {
        if (searchAssetReq.getPagination() != null && searchAssetReq.getScroll() != null) {
            throw new ParamInvalidException("only choose one between pagination and scroll");
        }
        checkAssetPagination(searchAssetReq.getPagination());
        checkScroll(searchAssetReq.getScroll());
        checkAssetGroupBy(searchAssetReq.getGroupBy());
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        return searchAssetsService.searchAssets(searchAssetReq, language, orgId);
    }

    @GravityLog
    @PostMapping("/search-authed-objects")
    public ResponseResult<?> searchAssetsByModelAPI(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody JSONObject payload,
            HttpServletRequest request) {
        String authedResources = request.getHeader(Constants.HTTP_HEAD_AUTHED_RESOURCES);
        if (authedResources == null || authedResources.isEmpty()) {
            return searchAssetsByModelAPIService.searchAuthedAssets(
                    orgId, true, Collections.emptyList(), payload);
        } else {
            Pair<Boolean, List<String>> authRes =
                    JsonUtil.parseAuthedResourcesHeader(authedResources);
            return searchAssetsByModelAPIService.searchAuthedAssets(
                    orgId, authRes.getLeft(), authRes.getRight(), payload);
        }
    }
}
