package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.dto.CalcType;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;


import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class CalcMetaProcessorTest {

    private final Logger logger = LoggerFactory.getLogger(CalcMetaProcessorTest.class);

    @Test
    public void loaderTest() {
        CalcMetaProcessor.getInstance().batchLoad();
        logger.info("1111");
    }

    @Test
    public void deleteUpstreamRule() {
        String orgId = "o17186913277371853";
        String ruleId = "202505141134";

        String igniteSql =
                String.format(
                        "DELETE FROM %s.TBL_PROPERTY_UPSTREAM_RULE WHERE PREF_RULE_ID = '%s'",
                        orgId, ruleId);
        IgniteUtil.query(orgId, igniteSql);

        // pg
        SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblPrefUpstreamMapper prefUpstreamMapper =
                    session.getMapper(TblPrefUpstreamMapper.class);
            prefUpstreamMapper.deleteByRuleId(orgId, ruleId);
        } catch (Exception e) {
            logger.error("Delete pref upstream rule [{}] failed: ", ruleId, e);
        }

        logger.info("Upstream rule [{}] has been deleted", ruleId);
    }

    @Test
    public void updateUpstreamRuleExpr() {
        String orgId = "o17186913277371853";
        String ruleId = "202505141134";
        String expression = "\"wind_general_001\".\"WGEN.GenSpd\"*2";

        String igniteSql =
                String.format(
                        "UPDATE %s.TBL_PROPERTY_UPSTREAM_RULE SET EXPRESSION = '%s', MODIFIED_TIME=CURRENT_TIMESTAMP(), MODIFIED_USER='gravity' WHERE PREF_RULE_ID = '%s'",
                        orgId, expression, ruleId);
        IgniteUtil.query(orgId, igniteSql);

        // pg
        SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblPrefUpstreamMapper prefUpstreamMapper =
                    session.getMapper(TblPrefUpstreamMapper.class);
            prefUpstreamMapper.updateExprByRuleId(orgId, ruleId, expression);
        } catch (Exception e) {
            logger.error("Delete pref upstream rule [{}] failed: ", ruleId, e);
        }

        logger.info("Upstream rule [{}] has been deleted", ruleId);
    }

    @Test
    public void addDirectMappingPref() {
        String orgId = "o17186913277371853";
        BaseCalcPropertyMeta meta0 =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest")
                        .targetCompId("gravityMultiModelTest")
                        .targetPrefId("gravityMultiModelTest__measurepoint__WGEN.GenSpd")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"WGEN.GenSpd\"")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        BaseCalcPropertyMeta meta01 =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest02")
                        .targetCompId("gravityMultiModelTest02")
                        .targetPrefId("gravityMultiModelTest02__measurepoint__WGEN.GenSpd")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"WGEN.GenSpd\"")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        BaseCalcPropertyMeta meta02 =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest02")
                        .targetCompId("gravityMultiModelTest02")
                        .targetPrefId("gravityMultiModelTest02__measurepoint__directMapPoint01")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"directMapPoint01\"")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        BaseCalcPropertyMeta meta03 =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest")
                        .targetCompId("gravityMultiModelTest")
                        .targetPrefId("gravityMultiModelTest__measurepoint__directMapPoint01")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"directMapPoint01\"")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        BaseCalcPropertyMeta meta04 =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest")
                        .targetCompId("gravityMultiModelTest")
                        .targetPrefId("gravityMultiModelTest__measurepoint__mmPoint02")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"mmPoint02\"*2")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        BaseCalcPropertyMeta meta =
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId("gravityMultiModelTest")
                        .targetCompId("gravityMultiModelTest")
                        .targetPrefId("gravityMultiModelTest__measurepoint__WGEN.GenSpd2")
                        .srcCategoryId("wind_general_001")
                        .expression("\"wind_general_001\".\"WGEN.GenSpd\"")
                        .calcType(CalcType.PREF_CALC.getType())
                        .build();

        String ruleId = "202505141134";

        String igniteSql =
                String.format(
                        "INSERT INTO %s.TBL_PROPERTY_UPSTREAM_RULE(PREF_RULE_ID, TARGET_CATEGORY,"
                                + "TARGET_COMP_ID, TARGET_PREF_ID, SRC_CATEGORY, EXPRESSION, CALC_TYPE, CREATED_TIME) "
                                + "VALUES('%s', '%s', '%s', '%s', '%s', '%s', %d, CURRENT_TIMESTAMP())",
                        orgId,
                        ruleId,
                        meta.getTargetCategoryId(),
                        meta.getTargetCompId(),
                        meta.getTargetPrefId(),
                        meta.getSrcCategoryId(),
                        meta.getExpression(),
                        meta.getCalcType());
        IgniteUtil.query(orgId, igniteSql);

        // pg
        SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblPrefUpstreamMapper prefUpstreamMapper =
                    session.getMapper(TblPrefUpstreamMapper.class);
            prefUpstreamMapper.insert(orgId, ruleId, meta);
        } catch (Exception e) {
            logger.error("Create upstream rule failed: ", e);
        }

        logger.info("Create upstream rule success");
    }
}
