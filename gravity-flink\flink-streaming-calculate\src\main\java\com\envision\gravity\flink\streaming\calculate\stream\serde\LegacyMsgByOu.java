package com.envision.gravity.flink.streaming.calculate.stream.serde;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LegacyMsgByOu {

    // ouid -> msg list
    private final Map<String, List<LegacyMsg>> data = new HashMap<>();

    public void add(LegacyMsg msg) {
        List<LegacyMsg> msgs = data.get(msg.getOrgId());
        if (msgs == null) {
            msgs = new ArrayList<>();
            msgs.add(msg);
            data.put(msg.getOrgId(), msgs);
        } else {
            msgs.add(msg);
        }
    }

    public List<LegacyMsg> getMsgsListByOu(String ou) {
        return data.get(ou);
    }

    public Map<String, List<LegacyMsg>> getData() {
        return data;
    }
}
