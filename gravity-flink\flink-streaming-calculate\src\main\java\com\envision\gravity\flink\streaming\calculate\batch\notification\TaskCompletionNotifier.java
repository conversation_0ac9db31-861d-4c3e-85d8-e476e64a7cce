package com.envision.gravity.flink.streaming.calculate.batch.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 任务完成通知器
 * 
 * 功能：
 * 1. 支持Job级别状态隔离
 * 2. 支持全局状态管理（流式场景）
 * 3. 线程安全的任务状态管理
 * 4. 内存状态通知机制
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class TaskCompletionNotifier {
    
    private static final Logger logger = LoggerFactory.getLogger(TaskCompletionNotifier.class);
    
    // ✅ 多Job状态隔离：使用jobId作为命名空间
    private static final Map<String, TaskCompletionNotifier> INSTANCES = new ConcurrentHashMap<>();
    
    private final String jobId;
    private final Set<String> completedTasks;
    private final Set<String> failedTasks;
    private final AtomicLong lastUpdateTime;
    
    private TaskCompletionNotifier(String jobId) {
        this.jobId = jobId;
        this.completedTasks = ConcurrentHashMap.newKeySet();
        this.failedTasks = ConcurrentHashMap.newKeySet();
        this.lastUpdateTime = new AtomicLong(System.currentTimeMillis());
    }
    
    /**
     * ✅ 获取Job级别的实例（完全隔离）
     */
    public static TaskCompletionNotifier getInstance(String jobId) {
        return INSTANCES.computeIfAbsent(jobId, TaskCompletionNotifier::new);
    }
    
    /**
     * ✅ 获取全局实例（用于流式场景）
     */
    public static TaskCompletionNotifier getGlobalInstance() {
        return getInstance("global");
    }
    
    /**
     * 标记任务完成（线程安全，Job级别隔离）
     */
    public void markTaskCompleted(String taskId) {
        completedTasks.add(taskId);
        failedTasks.remove(taskId); // 从失败集合中移除
        lastUpdateTime.set(System.currentTimeMillis());
        logger.debug("Task {} marked as completed for job {}", taskId, jobId);
    }
    
    /**
     * 标记任务完成（带jobId参数，用于流式场景）
     */
    public void markTaskCompleted(String jobId, String taskId) {
        TaskCompletionNotifier instance = getInstance(jobId);
        instance.markTaskCompleted(taskId);
    }
    
    /**
     * 标记任务失败（线程安全，Job级别隔离）
     */
    public void markTaskFailed(String taskId, String errorMessage) {
        failedTasks.add(taskId);
        lastUpdateTime.set(System.currentTimeMillis());
        logger.warn("Task {} marked as failed for job {}: {}", taskId, jobId, errorMessage);
    }
    
    /**
     * 标记任务失败（带jobId参数，用于流式场景）
     */
    public void markTaskFailed(String jobId, String taskId, String errorMessage) {
        TaskCompletionNotifier instance = getInstance(jobId);
        instance.markTaskFailed(taskId, errorMessage);
    }
    
    /**
     * 获取已完成的任务（返回副本，避免并发修改）
     */
    public Set<String> getCompletedTasks() {
        return new HashSet<>(completedTasks);
    }
    
    /**
     * 获取失败的任务（返回副本，避免并发修改）
     */
    public Set<String> getFailedTasks() {
        return new HashSet<>(failedTasks);
    }
    
    /**
     * 检查任务是否已完成
     */
    public boolean isTaskCompleted(String taskId) {
        return completedTasks.contains(taskId);
    }
    
    /**
     * 检查任务是否已完成（带jobId参数，用于流式场景）
     */
    public boolean isTaskCompleted(String jobId, String taskId) {
        TaskCompletionNotifier instance = getInstance(jobId);
        return instance.isTaskCompleted(taskId);
    }
    
    /**
     * 检查任务是否失败
     */
    public boolean isTaskFailed(String taskId) {
        return failedTasks.contains(taskId);
    }
    
    /**
     * 获取任务统计信息
     */
    public TaskStatistics getStatistics() {
        return new TaskStatistics(
            completedTasks.size(),
            failedTasks.size(),
            lastUpdateTime.get()
        );
    }
    
    /**
     * ✅ 清理特定Job的状态（状态隔离）
     */
    public void cleanup() {
        completedTasks.clear();
        failedTasks.clear();
        INSTANCES.remove(jobId);
        logger.info("Cleaned up task completion state for job {}", jobId);
    }
    
    /**
     * ✅ 静态方法：清理特定Job的状态
     */
    public static void cleanup(String jobId) {
        TaskCompletionNotifier instance = INSTANCES.remove(jobId);
        if (instance != null) {
            instance.cleanup();
        }
    }
    
    /**
     * ✅ 获取所有活跃的Job数量（监控用）
     */
    public static int getActiveJobCount() {
        return INSTANCES.size();
    }
    
    /**
     * ✅ 获取所有活跃的JobID（监控用）
     */
    public static Set<String> getActiveJobIds() {
        return new HashSet<>(INSTANCES.keySet());
    }
    
    /**
     * 任务统计信息
     */
    public static class TaskStatistics {
        private final int completedCount;
        private final int failedCount;
        private final long lastUpdateTime;
        
        public TaskStatistics(int completedCount, int failedCount, long lastUpdateTime) {
            this.completedCount = completedCount;
            this.failedCount = failedCount;
            this.lastUpdateTime = lastUpdateTime;
        }
        
        public int getCompletedCount() {
            return completedCount;
        }
        
        public int getFailedCount() {
            return failedCount;
        }
        
        public long getLastUpdateTime() {
            return lastUpdateTime;
        }
        
        public int getTotalCount() {
            return completedCount + failedCount;
        }
        
        @Override
        public String toString() {
            return String.format("TaskStatistics{completed=%d, failed=%d, total=%d, lastUpdate=%d}", 
                               completedCount, failedCount, getTotalCount(), lastUpdateTime);
        }
    }
}
