package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;


import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算作业信息管理器 - 流批兼容的广播流管理
 *
 * <AUTHOR>
 */
public class CalcJobInfoManager {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobInfoManager.class);

    // 广播状态描述符
    public static final MapStateDescriptor<String, TblCalcJobInfo> CALC_JOB_INFO_DESCRIPTOR =
            new MapStateDescriptor<>("calc-job-info-broadcast", String.class, TblCalcJobInfo.class);

    /** 创建计算作业信息广播流（流批兼容） */
    public static BroadcastStream<TblCalcJobInfo> createCalcJobInfoBroadcast(
            StreamExecutionEnvironment env, TblCalcJobInfo jobInfo) {

        return env.fromElements(jobInfo)
                .name("CalcJobInfoSource")
                .uid("calc-job-info-source")
                .broadcast(CALC_JOB_INFO_DESCRIPTOR);
    }

    /** 从广播状态获取计算作业信息 */
    public static TblCalcJobInfo getCalcJobInfo(
            ReadOnlyBroadcastState<String, TblCalcJobInfo> broadcastState, String jobId)
            throws Exception {

        TblCalcJobInfo jobInfo = broadcastState.get(jobId);
        if (jobInfo == null) {
            throw new RuntimeException("Calc job info not found for jobId: " + jobId);
        }
        return jobInfo;
    }

    /** 更新广播状态中的作业信息 */
    public static void updateCalcJobInfo(
            BroadcastState<String, TblCalcJobInfo> broadcastState, TblCalcJobInfo jobInfo)
            throws Exception {

        broadcastState.put(jobInfo.getJobId(), jobInfo);
        logger.info("Updated calc job info in broadcast state: {}", jobInfo.getJobId());
    }
}
