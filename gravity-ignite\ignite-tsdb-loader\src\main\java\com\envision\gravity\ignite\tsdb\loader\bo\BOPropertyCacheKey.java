package com.envision.gravity.ignite.tsdb.loader.bo;

import java.util.Objects;


import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> 2024/9/4 */
@Getter
@Setter
public class BOPropertyCacheKey {

    private String orgId;

    private String assetId;

    private String prefName;

    public BOPropertyCacheKey(String orgId, String assetId, String prefName) {
        this.orgId = orgId;
        this.assetId = assetId;
        this.prefName = prefName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BOPropertyCacheKey that = (BOPropertyCacheKey) o;
        return Objects.equals(orgId, that.orgId)
                && Objects.equals(assetId, that.assetId)
                && Objects.equals(prefName, that.prefName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orgId, assetId, prefName);
    }
}
