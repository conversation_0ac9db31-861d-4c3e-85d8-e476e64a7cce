package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblBO;
import com.envision.gravity.low.level.api.rest.dto.ObjAttrValueSet;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;

import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/12
 * @description
 */
public interface PGTblBOMapper {
    @UpdateProvider(type = TblBOSqlProvider.class, method = "updateAttrModifiedTimeByPrimaryKey")
    int updateAttrModifiedTimeByPrimaryKey(String orgId, String assetId);

    @UpdateProvider(
            type = TblBOSqlProvider.class,
            method = "batchUpdateAttrModifiedTimeByPrimaryKey")
    int batchUpdateAttrModifiedTimeByPrimaryKey(String orgId, Set<String> assetIdList);

    @UpdateProvider(
            type = TblBOSqlProvider.class,
            method = "batchUpdateAttrModifiedTimeBySystemIds")
    int batchUpdateAttrModifiedTimeBySystemIds(String orgId, List<String> systemIds);

    @SelectProvider(type = TblBOSqlProvider.class, method = "selectAssetIdList")
    Set<String> selectAssetIdList(String orgId, List<String> groupIdList);

    @SelectProvider(type = TblBOSqlProvider.class, method = "batchRefreshObjectDetailBySystemIds")
    String batchRefreshObjectDetailBySystemIds(
            String orgId,
            Set<String> systemIdList,
            AuditHeader auditHeader,
            ObjAttrValueSet objAttrValueSet);

    @UpdateProvider(type = TblBOSqlProvider.class, method = "incRefreshObjectDetailWithAttrValue")
    void incRefreshObjectDetailWithAttrValue(
            String orgId, AuditHeader auditHeader, ObjAttrValueSet objAttrValueSet);

    @UpdateProvider(type = TblBOSqlProvider.class, method = "unbindSystemIds")
    void unbindSystemIds(@Param("orgId") String orgId, @Param("systemIds") Set<String> systemIds);

    @SelectProvider(type = TblBOSqlProvider.class, method = "queryByaAssetId")
    @Results({
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "asset_display_name",
                property = "assetDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblBO> queryByaAssetId(String orgId, List<String> assetIdList);
}
