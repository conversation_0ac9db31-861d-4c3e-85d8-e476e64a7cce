package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import com.envision.gravity.common.po.TblObjAttr;

import java.util.List;


import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
public interface TblBOMapper {
    @UpdateProvider(type = TblBOSqlProvider.class, method = "updateSysModifiedTimeByPrimaryKeys")
    int updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> assetIdList);

    @SelectProvider(type = TblBOSqlProvider.class, method = "batchRefreshObjectDetailByAssetIds")
    String batchRefreshObjectDetailByAssetIds(String schemaName, List<String> assetIdList);

    @SelectProvider(type = TblBOSqlProvider.class, method = "fullRefreshObjectDetailWithAttrValue")
    String fullRefreshObjectDetailWithAttrValue(
            String schemaName, List<String> assetIdList, List<TblObjAttr> tblObjAttrList);
}
