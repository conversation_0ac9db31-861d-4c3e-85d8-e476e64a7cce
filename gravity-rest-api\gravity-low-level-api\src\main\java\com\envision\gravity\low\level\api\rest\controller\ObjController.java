package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.obj.*;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.service.ObjService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.*;

import static com.envision.gravity.low.level.api.rest.common.Constants.MAX_REQUEST_SIZE;
import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.*;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description obj controller
 */
@Slf4j
@Api(tags = "Obj")
@Validated
@GravityLog
@RestController
@RequestMapping("/obj")
public class ObjController {

    @Resource private ObjService objService;

    @GravityLog
    @PostMapping(value = "/batch-add-or-update")
    public ResponseResult<?> batchCreateOrUpdateObjs(
            @RequestBody List<@Valid ObjReq> objs,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (objs == null || objs.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (objs.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of create or update objs exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    objs.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of create or update objs must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, objs.size()))
                    .build();
        }

        checkObjReqDefine(objs);
        return objService.batchCreateOrUpdateObjs(objs, orgId, auditHeader, false);
    }

    @GravityLog
    @PatchMapping(value = "/field/batch-patch")
    public ResponseResult<?> batchPatchObjFields(
            @RequestBody List<@Valid PatchObjFieldReq> objFields,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestParam(required = false, defaultValue = "false") boolean disableReqSizeLimit,
            HttpServletRequest request) {
        log.debug(String.format("Start patch obj fields, param: %s.", objFields));

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (objFields == null || objFields.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (!disableReqSizeLimit && objFields.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of patch obj fields exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    objFields.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of patch obj fields must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, objFields.size()))
                    .build();
        }

        checkPatchObjFieldReqIsValid(objFields);
        return objService.batchPatchObjFields(objFields, orgId, auditHeader);
    }

    @GravityLog
    @PostMapping(value = "/attr/batch-upsert")
    public ResponseResult<?> batchUpsertObjAttrValue(
            @RequestBody List<UpsertObjAttrValueReq> objAttrs,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        log.debug(String.format("Start upsert obj attrs value, param: %s.", objAttrs));

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (objAttrs == null || objAttrs.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (objAttrs.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of upsert objs attr exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    objAttrs.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of upsert objs attr must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, objAttrs.size()))
                    .build();
        }

        return objService.batchUpsertObjAttrValue(objAttrs, orgId, auditHeader);
    }

    @GravityLog
    @DeleteMapping(value = "/batch-delete")
    public ResponseResult<?> batchDeleteObjs(
            @RequestBody List<DeleteObjReq> objs,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (StringUtils.isNotEmpty(auditInfo)) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (objs == null || objs.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (objs.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of delete objs exceeds the limit of {}, current delete size: {}",
                    MAX_REQUEST_SIZE,
                    objs.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of delete objs must be less than or equal to %d, current delete size: %d",
                                    MAX_REQUEST_SIZE, objs.size()))
                    .build();
        }

        return objService.batchDeleteObjs(objs, orgId, auditHeader);
    }

    @GravityLog
    @PostMapping
    public ResponseResult<?> queryObj(
            @RequestBody QueryObjReq query,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        List<ObjResp> objs = objService.queryObj(query, language, orgId);
        log.info("Query obj success.");
        return ResponseResult.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .message(ResponseCodeEnum.SUCCESS.getMessage())
                .data(objs)
                .build();
    }

    @GravityLog
    @PostMapping(value = "/list")
    public ResponseResult<?> queryObjList(
            @Valid @RequestBody QueryObjListReq queryObjListReq,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        QueryObjListResp objs = objService.queryObjList(queryObjListReq, language, orgId);
        log.info("Query obj list success.");
        return ResponseResult.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .message(ResponseCodeEnum.SUCCESS.getMessage())
                .data(objs)
                .build();
    }
}
