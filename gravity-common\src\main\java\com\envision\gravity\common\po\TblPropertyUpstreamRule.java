package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TblPropertyUpstreamRule {

    @KeyColumn(name = "pref_rule_id")
    @ColumnName("pref_rule_id")
    @RequiredField(message = "pref_rule_id field is required")
    private String prefRuleId;

    @ValueColumn(name = "target_category")
    @ColumnName("target_category")
    private String targetCategory;

    @ValueColumn(name = "target_comp_id")
    @ColumnName("target_comp_id")
    private String targetCompId;

    @ValueColumn(name = "target_pref_id")
    @ColumnName("target_pref_id")
    private String targetPrefId;

    @ValueColumn(name = "src_category")
    @ColumnName("src_category")
    private String srcCategory;

    @ValueColumn(name = "expression")
    @ColumnName("expression")
    private String expression;

    @ValueColumn(name = "calc_type", type = Integer.class)
    @ColumnName("calc_type")
    private Integer calcType;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
