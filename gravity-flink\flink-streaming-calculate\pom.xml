<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-flink</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>flink-streaming-calculate</artifactId>
    <packaging>jar</packaging>
    <version>${release.version}</version>
    <name>flink-streaming-calculate</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--<dep.scope>compile</dep.scope>-->
        <!--<dep.scope>provided</dep.scope>-->
        <dep.scope>provided</dep.scope>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.univers</groupId>
            <artifactId>business-object-calc-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>flink-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-local-cache</artifactId>
            <version>${release.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- flink -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-runtime-web_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-kafka_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <!-- other -->
        <dependency>
            <groupId>com.ververica</groupId>
            <artifactId>flink-sql-connector-postgres-cdc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.envisioniot</groupId>
            <artifactId>apim-poseidon</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ignite-core</artifactId>
                    <groupId>org.apache.ignite</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>perf4j</artifactId>
                    <groupId>org.perf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>ignite-core</artifactId>
            <groupId>org.apache.ignite</groupId>
        </dependency>

        <dependency>
            <groupId>com.envision.arch.lion</groupId>
            <artifactId>lion-client</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>
        <!-- pg -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <!-- cache -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>tbl-obj-ignite-service</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <!-- Kafka clients for testing -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.8.1</version>
            <scope>provided</scope>
        </dependency>

        <!-- Velocity template engine for dynamic SQL generation -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
        </dependency>

        <!-- MySQL JDBC driver for SQL Gateway -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <!-- CSV processing for integration tests -->
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.7.1</version>
            <scope>test</scope>
        </dependency>

        <!-- OkHttp for HTTP client (Java 8 compatible) -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>gravity-calculate-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <!-- JVM 参数，包括断言和内存设置 -->
                    <argLine>-ea -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8</argLine>
                    <workingDirectory>${project.basedir}</workingDirectory>
                    <!-- 系统属性，使用相对路径以提高可移植性 -->
                    <systemPropertyVariables>
                        <arch.path>./deploy/apps</arch.path>
                        <java.security.auth.login.config>./deploy/zk_client_jaas.conf</java.security.auth.login.config>
                        <LOG_LEVEL>DEBUG</LOG_LEVEL>
                        <logback.configurationFile>logback-test.xml</logback.configurationFile>
                        <org.apache.logging.log4j.level>ERROR</org.apache.logging.log4j.level>
                        <log4j.configuration>ERROR</log4j.configuration>
                    </systemPropertyVariables>
                    <!-- 确保支持 JUnit 5 -->
                    <useModulePath>false</useModulePath>
                    <!-- 强制重新编译测试代码 -->
                    <reuseForks>false</reuseForks>
                    <forkCount>1</forkCount>
                    <!-- 确保类路径正确 -->
                    <useSystemClassLoader>true</useSystemClassLoader>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>