package com.envision.gravity.low.level.api.sql.ignite.client;

import com.envision.gravity.common.enums.IDType;
import com.envision.gravity.common.exception.InternalException;
import com.envision.gravity.common.ignite.service.BOLowLevelService;
import com.envision.gravity.common.ignite.service.GSqlService;
import com.envision.gravity.common.ignite.service.IDService;
import com.envision.gravity.common.ignite.service.LowLevelIgniteService;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.low.level.api.sql.common.BOCoreCacheCfgTableEnum;
import com.envision.gravity.low.level.api.sql.common.BOCoreCacheTableEnum;
import com.envision.gravity.low.level.api.sql.common.BOCorePersistentTableEnum;
import com.envision.gravity.low.level.api.sql.table.CacheCfgTableInfo;
import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;
import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import javax.cache.CacheException;

import java.sql.Timestamp;
import java.util.*;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.cache.*;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/3/8
 * @description
 */
@Slf4j
public class ThinIgniteClientTest {
    public static final String CACHE_NAME_PATTERN = "%s_%s";
    public static final String KEY_TYPE_PATTERN = "%s_%s_KEY";
    public static final String VALUE_TYPE_PATTERN = "%s_%s_VALUE";
    public static final String BO_IN_MEMORY_REGION = "BO_InMemory_Region";
    public static final String DEFAULT_REGION = "Default_Region";
    public static final String POSTGRES_DATA_SOURCE_BEAN_NAME = "postgresDataSourceGravity";

    private static IgniteClient IGNITE_CLIENT;
    private static Ignite ignite;

    @BeforeAll
    public static void init() {
        // thin ignite client
        //        ClientConfiguration cfg =
        //                new ClientConfiguration()
        //                        .setAddresses("***********:10800")
        //                        .setPartitionAwarenessEnabled(true)
        //                        .setUserName("ignite")
        //                        .setUserPassword("ignite");
        //        IGNITE_CLIENT = Ignition.startClient(cfg);

        // fat ignite client
        //        Ignition.setClientMode(true);
        //        IgniteConfiguration config = getIgniteConfiguration();
        //        ignite = Ignition.getOrStart(config);
    }

    @Test
    void updateTSDBLastOnlineTime() {
        IGNITE_CLIENT
                .query(
                        new SqlFieldsQuery(
                                        "UPDATE TSDB_CLUSTER_NODE_INFO SET LAST_ONLINE_TIME = ?, NODE_STATUS = ?\n"
                                                + "WHERE NODE_HASH_ID = '3292602849';")
                                .setArgs(new Timestamp(1731772800000L), "OFFLINE")
                                .setSchema("GRAVITY"))
                .getAll();
    }

    @NotNull
    private static IgniteConfiguration getIgniteConfiguration() {
        IgniteConfiguration cfg = new IgniteConfiguration();
        cfg.setPeerClassLoadingEnabled(true);
        cfg.setDeploymentMode(DeploymentMode.CONTINUOUS);
        cfg.setAuthenticationEnabled(true);
        cfg.setClientMode(true);
        TcpDiscoverySpi spi = new TcpDiscoverySpi();
        TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();
        Set<String> addresses = new HashSet<>(4);
        addresses.add("*************:47500");
        ipFinder.setAddresses(addresses);
        spi.setIpFinder(ipFinder);
        cfg.setDiscoverySpi(spi);
        cfg.setNetworkTimeout(120000);
        cfg.setAuthenticationEnabled(true);
        return cfg;
    }

    @Test
    void destroyCacheTest() {
        IGNITE_CLIENT.destroyCache("o17186913277371853_TBL_OBJ_POINT_PART");
    }

    @Test
    void localQueryTest() {
        List<List<?>> queryResult =
                IGNITE_CLIENT
                        .query(
                                new SqlFieldsQuery(
                                                "SELECT\n" + "COUNT(*) \n" + "FROM  TBL_OBJ_part;")
                                        .setSchema("O17320979624031160")
                                        .setLocal(true))
                        .getAll();
        System.out.println(queryResult);
    }

    @Test
    void queryOuIdList() {
        List<List<?>> queryResult =
                IGNITE_CLIENT
                        .query(
                                new SqlFieldsQuery("SELECT OU_ID FROM TBL_OU_INFO;")
                                        .setSchema("GRAVITY"))
                        .getAll();
        System.out.println(queryResult);

        List<List<?>> queryResultUpd =
                IGNITE_CLIENT
                        .query(
                                new SqlFieldsQuery("SELECT OU_ID FROM TBL_OU_INFO;")
                                        .setSchema("GRAVITY"))
                        .getAll();
        System.out.println(queryResultUpd);
    }

    @Test
    void initOu() {
        IGNITE_CLIENT
                .services()
                .serviceProxy(BOLowLevelService.SERVICE_NAME, BOLowLevelService.class)
                .initOU("o123", "1");
    }

    @Test
    void test() {
        List<List<?>> all =
                IGNITE_CLIENT
                        .query(
                                new SqlFieldsQuery(
                                                "select _VAL, _KEY from o17231990549791976.TBL_OBJ WHERE SYSTEM_ID = 'demo15demoU1Kyjqku';")
                                        .setSchema("O17231990549791976"))
                        .getAll();

        all.get(1).get(8).toString();
        all.get(2).get(8).toString();
        System.out.println(all);
    }

    @Test
    void queryCacheTest() {
        ClientCache<BinaryObject, BinaryObject> cache =
                IGNITE_CLIENT.cache("o17231990549791976_TBL_OBJ").withKeepBinary();
        BinaryObjectBuilder keyBuilder =
                IGNITE_CLIENT.binary().builder("o17231990549791976_TBL_OBJ_KEY");
        keyBuilder.setField("system_id", "demo15demoU1Kyjqku");
        BinaryObject value = cache.get(keyBuilder.build());
        Object field = value.field("LATITUDE__DOUBLE");
        System.out.println(field);
        System.out.println(value);
    }

    @Test
    void keyBuilderTest() {
        BinaryObjectBuilder binaryObjectBuilder =
                IGNITE_CLIENT.binary().builder("o17186913277371853_TBL_BO_MODEL_COMP_KEY");
        binaryObjectBuilder.setField("comp_id", "xxx");
        binaryObjectBuilder.setField("model_id", "yyy");
        BinaryObject binaryObject = binaryObjectBuilder.build();
        System.out.println(binaryObject);

        BinaryObjectBuilder binaryObjectBuilder2 =
                IGNITE_CLIENT.binary().builder("o17186913277371853_TBL_BO_MODEL_COMP_KEY");
        binaryObjectBuilder2.setField("model_id", "qqq");
        binaryObjectBuilder2.setField("comp_id", "zzz");
        BinaryObject binaryObject2 = binaryObjectBuilder2.build();
        System.out.println(binaryObject2);
    }

    @Test
    void getValueBuilderTest() {
        ClientCache<BinaryObject, BinaryObject> cache =
                IGNITE_CLIENT.cache("o17186913277371853_TBL_BO_MODEL_COMP").withKeepBinary();

        BinaryObjectBuilder keyBuilder =
                IGNITE_CLIENT.binary().builder("o17186913277371853_TBL_BO_MODEL_COMP_KEY");
        keyBuilder.setField("model_id", "UT_model_createAutoBase_modelId_Err387");
        keyBuilder.setField("comp_id", "UT_model_createAutoBase_modelId_Err387__DCM");
        BinaryObject keyBinaryObject = keyBuilder.build();

        BinaryObject valueBinaryObject = cache.get(keyBinaryObject);
        BinaryObjectBuilder valueBinaryObjectBuilder = valueBinaryObject.toBuilder();
        valueBinaryObjectBuilder.setField("created_user", "gravity", String.class);
        valueBinaryObjectBuilder.build();

        BinaryObject valueBinaryObject1 = cache.get(keyBinaryObject);
        BinaryObjectBuilder valueBinaryObjectBuilder1 = valueBinaryObject1.toBuilder();
        valueBinaryObjectBuilder1.setField("modified_user", "gravity", String.class);
        valueBinaryObjectBuilder1.build();
    }

    @Test
    void sqlTest() {
        ClientCache<Object, Object> cache =
                IGNITE_CLIENT.cache("SQL_O16227961710541858_TBL_OBJ").withKeepBinary();
        BinaryObjectBuilder tblOBJValue =
                IGNITE_CLIENT
                        .binary()
                        .builder(
                                "SQL_O16227961710541858_TBL_OBJ_1c6baf19_7cd3_41d0_8b2f_044290e7ecce");
        Map<String, BinaryObject> tblOBJCacheMap = new HashMap<>(2);
        tblOBJValue.setField("dtmi_test0605001", 1998L, Long.class);
        tblOBJCacheMap.put("0wfV0Wb7_1yP", tblOBJValue.build());
        cache.putAll(tblOBJCacheMap);

        List<List<?>> all =
                IGNITE_CLIENT
                        .query(
                                new SqlFieldsQuery(
                                                "select _key, _val, \"dtmi_test0605001\", \"dtmi:custom:Tsdb_Api_Regression_Test_Model;1__measurepoint__Ai.DoubleTypePoint_2.1_VCKhnpdX_value\"  \n"
                                                        + "from O16227961710541858.tbl_obj \n"
                                                        + "where system_id = '0wfV0Wb7_1yP';")
                                        .setSchema("O16227961710541858"))
                        .getAll();
        System.out.println(all);
    }

    @Test
    void externalStorageTest() {}

    @Test
    void atomicSequenceTest() {
        System.out.println(
                IGNITE_CLIENT.atomicLong("TestAtomicSequence", 1, true).getAndIncrement());
    }

    @Test
    void cacheTest() {
        System.out.println(IGNITE_CLIENT.cache("xxx"));
        System.out.println(IGNITE_CLIENT.cacheNames());
    }

    @Test
    void queryTest() {
        FieldsQueryCursor<List<?>> result =
                IGNITE_CLIENT.query(
                        new SqlFieldsQuery("SELECT * FROM METRIC_ENGINE_CUSTOM_DB;")
                                .setSchema("PUBLIC"));
        List<List<?>> dataResult = result.getAll();
        System.out.println(">>> Columns Count: " + result.getColumnsCount());
        System.out.println(">>> Columns Name: " + result.getFieldName(0));
        System.out.println(">>> Data Size: " + dataResult.size());
        System.out.println(">>> Columns Size: " + dataResult.get(0).size());
    }

    @Test
    void cacheOperator() {
        System.out.println(">>> " + IGNITE_CLIENT.cacheNames());
    }

    @Test
    void schemaOperator() {}

    @Test
    void idServiceTest() {
        System.out.println(IGNITE_CLIENT.services().serviceDescriptors());
        ResponseResult<?> responseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(IDService.SERVICE_NAME, IDService.class)
                        .get(2, IDType.COMMON_ID);
        System.out.println(responseResult.getData());
    }

    @Test
    void selectModelTest() {
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(GSqlService.SERVICE_NAME, GSqlService.class)
                        .execute(
                                "",
                                "O16227961710541858",
                                "SELECT MODEL_ID, DESCRIPTION, MODIFIED_TIME FROM _BO_MODEL;",
                                null);
        System.out.println(">>> " + gSqlServiceResponseResult.getData());
    }

    @Test
    void insertModelTest() {
        String noModelId =
                "REPLACE INTO _BO_MODEL(model_id, model_display_Name, created_user, modified_user) "
                        + "VALUES ('EnOS_Solar_Inverter', '{\"default\": \"逆变器\",\"en_US\": \"Inverter\",\"zh_CN\": \"逆变器\"}', 'Gravity', 'Gravity');";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(GSqlService.SERVICE_NAME, GSqlService.class)
                        .execute("", "O16227961710541858", noModelId, null);
        System.out.println(gSqlServiceResponseResult.getData());
    }

    @Test
    void insertBOGroupTest() {
        String noGroupId =
                "replace     \n"
                        + "/*+ ENGINE('bo_low_level') */   \n"
                        + "/*+ ORG('o16227961710541858') */       \n"
                        + "  into  o16227961710541858._BO_GROUP(group_id, group_name, group_display_name, description,    created_user, modified_user)  \n"
                        + "values         \n"
                        + "(    'a',    'a',    '{\"default\": \"Solar_Inverter_Group1\"}',    '{}',    'a',    'a');";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(GSqlService.SERVICE_NAME, GSqlService.class)
                        .execute("", "O16227961710541858", noGroupId, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertBOTest() {
        String insertWithValues =
                "INSERT INTO _BO (asset_display_name, system_id, created_user, modified_user) "
                        + "VALUES ('{\"default\": \"山东LCLL5.9MWp分布式项目_INV_1\"}','xxx', 'Gravity', 'Gravity'), "
                        + "('{\"default\": \"光伏场站\"}', 'xxx', 'Gravity', 'Gravity');";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME, LowLevelIgniteService.class)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertPropertyTest() {
        String insertWithValues =
                "Replace INTO _PROPERTY (pref_id, pref_name, pref_display_name, pref_type, writable, required, has_quality, pref_data_type, pref_signal_type, unit, created_user, modified_user) "
                        + "VALUES ('a', 'b', 'c', 'MEASUREPOINT', false, false, false, 'string', 'f', 'g', 'Test', 'Test'), "
                        + "('A', 'B', 'C', 'command', false, false, false, null, 'F', 'G', 'Test', 'Test');";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME, LowLevelIgniteService.class)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertTagTest() {
        String insertWithValues =
                "replace INTO _TAG (data_id, data_type, tag_type, tag_id, created_user, modified_user) "
                        + "VALUES ('a', null, 'model', 'a', 'Test', 'Test'), "
                        + "('A', null, 'property', 'A', 'Test', 'Test');";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME, LowLevelIgniteService.class)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertJsonTest() {
        String insertWithValues =
                "replace /*+ ENGINE('bo_low_level') */ /*+ ORG('o16227961710541858') */ into _COMPONENT\n"
                        + "(comp_id, comp_name, comp_display_name, description, anonymous, template, created_time, created_user, modified_time, modified_user) values \n"
                        + "( 'ceshi0091__ceshi001', 'ceshi001', '{\"default\": \"\\\"1\"}', null, false, 'ceshi001', '2024-05-17 02:37:50.923646', 'changjie.zhou', '2024-05-17 02:37:50.923646', 'changjie.zhou' ) ;";
        ResponseResult<?> gSqlServiceResponseResult =
                IGNITE_CLIENT
                        .services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME, LowLevelIgniteService.class)
                        .execute("", "GRAVITY", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void getOrCreateCacheTest() {
        String ouId = "123456789";
        String tableName = "TBL_BO_MODEL";
        CacheConfiguration<BinaryObject, BinaryObject> cfg = new CacheConfiguration<>();
        cfg.setName(String.format(CACHE_NAME_PATTERN, ouId, tableName));
        cfg.setCacheMode(CacheMode.REPLICATED);
        cfg.setAtomicityMode(CacheAtomicityMode.TRANSACTIONAL);
        cfg.setDataRegionName(BO_IN_MEMORY_REGION);
        cfg.setSqlSchema(ouId);

        // cacheStoreFactory
        CacheJdbcPojoStoreFactory<BinaryObject, BinaryObject> storeFactory =
                new CacheJdbcPojoStoreFactory<>();
        storeFactory.setDataSourceBean(POSTGRES_DATA_SOURCE_BEAN_NAME);
        storeFactory.setDialect(new BasicJdbcDialect());
        // types
        JdbcType jdbcType = new JdbcType();
        jdbcType.setCacheName(String.format(CACHE_NAME_PATTERN, ouId, tableName));
        jdbcType.setKeyType(String.format(KEY_TYPE_PATTERN, ouId, tableName));
        jdbcType.setValueType(String.format(VALUE_TYPE_PATTERN, ouId, tableName));
        jdbcType.setDatabaseSchema(ouId);
        jdbcType.setDatabaseTable(tableName);

        // key fields
        // jdbcType.setKeyFields(keyFields);
        // value fields
        // jdbcType.setValueFields(valFields);
        storeFactory.setTypes(jdbcType);

        cfg.setCacheStoreFactory(storeFactory);

        cfg.setReadThrough(false);
        cfg.setWriteThrough(false);

        // create QueryEntity
        QueryEntity qryEntity = new QueryEntity();
        qryEntity.setTableName(tableName);
        qryEntity.setKeyType(String.format(KEY_TYPE_PATTERN, ouId, tableName));
        qryEntity.setValueType(String.format(VALUE_TYPE_PATTERN, ouId, tableName));
        // qryEntity.setKeyFields();
        // qryEntity.setNotNullFields();
        // index
        // qryEntity.setIndexes(indexes);
        // qryEntity.setFields();
        Map<String, Object> defaultFieldValues = new HashMap<>();
        qryEntity.setDefaultFieldValues(defaultFieldValues);
        cfg.setQueryEntities(Collections.singleton(qryEntity));
    }

    @Test
    void createCacheTable() {
        String ouId = "o17234452345781374";
        // create cache table
        Arrays.stream(BOCoreCacheTableEnum.values())
                .filter(BOCoreCacheTableEnum.TBL_COMPONENT::equals)
                .forEach(
                        table -> {
                            String dataSourceTableName = table.getDataSourceTableName();
                            String queryEntityTableName = table.getQueryEntityTableName();
                            CacheMode cacheMode = table.getCacheMode();
                            CacheAtomicityMode atomicityMode = table.getAtomicityMode();
                            boolean readThrough = table.isReadThrough();
                            boolean writeThrough = table.isWriteThrough();
                            CacheTableInfo cacheTableInfo = table.getCacheTableInfo();
                            try {
                                CacheConfiguration<BinaryObject, BinaryObject> cfg =
                                        new CacheConfiguration<>();
                                cfg.setName(
                                        String.format(
                                                CACHE_NAME_PATTERN, ouId, queryEntityTableName));
                                cfg.setCacheMode(cacheMode);
                                cfg.setAtomicityMode(atomicityMode);
                                cfg.setDataRegionName(BO_IN_MEMORY_REGION);
                                cfg.setSqlSchema(ouId);

                                // cacheStoreFactory
                                CacheJdbcPojoStoreFactory<BinaryObject, BinaryObject> storeFactory =
                                        new CacheJdbcPojoStoreFactory<>();
                                storeFactory.setDataSourceBean(POSTGRES_DATA_SOURCE_BEAN_NAME);
                                storeFactory.setDialect(new BasicJdbcDialect());
                                // types
                                JdbcType jdbcType = new JdbcType();
                                jdbcType.setCacheName(
                                        String.format(
                                                CACHE_NAME_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setKeyType(
                                        String.format(
                                                KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setValueType(
                                        String.format(
                                                VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setDatabaseSchema(ouId);
                                jdbcType.setDatabaseTable(dataSourceTableName);

                                // key fields
                                List<JdbcTypeField> keyFields = cacheTableInfo.getKeyFields();
                                jdbcType.setKeyFields(keyFields.toArray(new JdbcTypeField[0]));
                                // value fields
                                List<JdbcTypeField> valFields = cacheTableInfo.getValueFields();
                                jdbcType.setValueFields(valFields.toArray(new JdbcTypeField[0]));
                                storeFactory.setTypes(jdbcType);
                                cfg.setCacheStoreFactory(storeFactory);

                                cfg.setReadThrough(readThrough);
                                cfg.setWriteThrough(writeThrough);

                                // create QueryEntity
                                QueryEntity qryEntity = new QueryEntity();
                                qryEntity.setTableName(queryEntityTableName);
                                qryEntity.setKeyType(
                                        String.format(
                                                KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setValueType(
                                        String.format(
                                                VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setKeyFields(cacheTableInfo.getQueryEntityKeyFields());
                                qryEntity.setNotNullFields(cacheTableInfo.getNotNullFields());
                                // index
                                qryEntity.setIndexes(cacheTableInfo.getIndexes());
                                qryEntity.setFields(cacheTableInfo.getQueryEntityFields());
                                if (cacheTableInfo.getDefaultFieldValues() != null) {
                                    qryEntity.setDefaultFieldValues(
                                            cacheTableInfo.getDefaultFieldValues());
                                }
                                cfg.setQueryEntities(Collections.singleton(qryEntity));

                                if (cacheTableInfo.getAffKeyFieldName() != null
                                        && !cacheTableInfo.getAffKeyFieldName().isEmpty()) {
                                    CacheKeyConfiguration cacheKeyCfg = new CacheKeyConfiguration();
                                    cacheKeyCfg.setTypeName(
                                            String.format(
                                                    KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                    cacheKeyCfg.setAffinityKeyFieldName(
                                            cacheTableInfo.getAffKeyFieldName());
                                    cfg.setKeyConfiguration(cacheKeyCfg);
                                }

                                ignite.getOrCreateCache(cfg);
                            } catch (CacheException e) {
                                log.error(
                                        String.format(
                                                "Create cache table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                                throw new InternalException(
                                        String.format(
                                                "Create cache table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                            }
                        });
    }

    @Test
    void createPersistentTable() {
        String ouId = "o17340806139801708";
        // create persistent table
        Arrays.stream(BOCorePersistentTableEnum.values())
                .forEach(
                        table -> {
                            String tableName = table.getTableName();
                            String cacheMode = table.getCacheMode();
                            String atomicityMode = table.getAtomicityMode();
                            PersistentTableInfo persistentTableInfo =
                                    table.getPersistentTableInfo();

                            String keyType = String.format(KEY_TYPE_PATTERN, ouId, tableName);
                            String valueType = String.format(VALUE_TYPE_PATTERN, ouId, tableName);
                            String cacheName = String.format(CACHE_NAME_PATTERN, ouId, tableName);

                            // create table
                            String createTableSql =
                                    String.format(
                                            persistentTableInfo.getCreateTableSQLPattern(),
                                            ouId,
                                            cacheMode,
                                            atomicityMode,
                                            keyType,
                                            valueType,
                                            cacheName);
                            System.out.println(createTableSql);
                            //                            IGNITE_CLIENT.query(new
                            // SqlFieldsQuery(createTableSql)).getAll();

                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                            // create indexes
                            List<String> createIndexSQLPatternList =
                                    persistentTableInfo.getCreateIndexSQLPatternList();
                            createIndexSQLPatternList.forEach(
                                    createIndexSQLPattern -> {
                                        String createIndexSql =
                                                String.format(createIndexSQLPattern, ouId);
                                        System.out.println(createIndexSql);
                                        //                                        IGNITE_CLIENT
                                        //                                                .query(new
                                        // SqlFieldsQuery(createIndexSql))
                                        //                                                .getAll();
                                    });
                        });
    }

    @Test
    void createTableTest() {
        String tableName = "PUBLIC";
        String keyType = "PUBLIC_TBL_OBJ_KEY";
        String valueType = "PUBLIC_TBL_OBJ_VALUE";
        String cacheName = "PUBLIC_TBL_OBJ";

        String createTableSql =
                String.format(
                        "CREATE TABLE IF NOT EXISTS %s.TBL_OBJ "
                                + "(\n"
                                + "    SYSTEM_ID            VARCHAR,\n"
                                + "    SYSTEM_DISPLAY_NAME  VARCHAR,\n"
                                + "    CATEGORY_ID          VARCHAR,\n"
                                + "    CREATED_TIME         TIMESTAMP,\n"
                                + "    CREATED_USER         VARCHAR,\n"
                                + "    MODIFIED_TIME        TIMESTAMP,\n"
                                + "    MODIFIED_USER        VARCHAR,\n"
                                + "    PRIMARY KEY (SYSTEM_ID)\n"
                                + "    )WITH \"template=REPLICATED,ATOMICITY=TRANSACTIONAL,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";",
                        tableName, keyType, valueType, cacheName);

        List<List<?>> all = IGNITE_CLIENT.query(new SqlFieldsQuery(createTableSql)).getAll();
    }

    @Test
    void createPersistentTableWithApi() {
        String ouId = "o20241230";

        String queryEntityTableName = "TBL_OBJ_POINT_PART2";
        CacheMode cacheMode = CacheMode.PARTITIONED;
        CacheAtomicityMode atomicityMode = CacheAtomicityMode.ATOMIC;
        try {
            CacheConfiguration<BinaryObject, BinaryObject> cfg = new CacheConfiguration<>();
            cfg.setName(String.format(CACHE_NAME_PATTERN, ouId, queryEntityTableName));
            cfg.setCacheMode(cacheMode);
            cfg.setAtomicityMode(atomicityMode);
            cfg.setDataRegionName("Default_Region");
            cfg.setSqlSchema(ouId);
            cfg.setQueryParallelism(8);

            Set<String> queryEntityKeyFields = new LinkedHashSet<>();
            queryEntityKeyFields.add("SYSTEM_ID");
            queryEntityKeyFields.add("FIELD_INDEX");

            Set<String> notNullFields = new LinkedHashSet<>();
            notNullFields.add("SERIES_ID");
            notNullFields.add("SYSTEM_ID");
            notNullFields.add("FIELD_INDEX");

            //            List<QueryIndex> indexes = Collections.singletonList(
            //                    new QueryIndex(
            //                            Arrays.asList("SYSTEM_ID", "FIELD_INDEX"),
            // QueryIndexType.SORTED).setInlineSize(30));

            LinkedHashMap<String, String> queryEntityFields = new LinkedHashMap<>();
            queryEntityFields.put("SERIES_ID", "java.lang.String");
            queryEntityFields.put("SYSTEM_ID", "java.lang.String");
            queryEntityFields.put("FIELD_INDEX", "java.lang.Integer");
            queryEntityFields.put("VALUE_BOOL", "java.lang.Boolean");
            queryEntityFields.put("VALUE_STRING", "java.lang.String");
            queryEntityFields.put("VALUE_LONG", "java.lang.Long");
            queryEntityFields.put("VALUE_DOUBLE", "java.lang.Double");
            queryEntityFields.put("QUALITY", "java.lang.Long");
            queryEntityFields.put("TIME", "java.sql.Timestamp");
            queryEntityFields.put("LAST_CHANGED_TIME", "java.sql.Timestamp");
            queryEntityFields.put("LAST_UPDATE_TIME", "java.sql.Timestamp");
            queryEntityFields.put("CREATED_USER", "java.lang.String");
            queryEntityFields.put("MODIFIED_USER", "java.lang.String");
            queryEntityFields.put("CREATED_TIME", "java.sql.Timestamp");
            queryEntityFields.put("MODIFIED_TIME", "java.sql.Timestamp");

            // create QueryEntity
            QueryEntity qryEntity = new QueryEntity();
            qryEntity.setTableName(queryEntityTableName);
            qryEntity.setKeyType(String.format(KEY_TYPE_PATTERN, ouId, queryEntityTableName));
            qryEntity.setValueType(String.format(VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
            qryEntity.setKeyFields(queryEntityKeyFields);
            qryEntity.setNotNullFields(notNullFields);
            // index
            //            qryEntity.setIndexes(indexes);
            qryEntity.setFields(queryEntityFields);
            cfg.setQueryEntities(Collections.singleton(qryEntity));

            ignite.getOrCreateCache(cfg);
        } catch (CacheException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void createCacheCfgTable() {
        String ouId = "o17186913277371853";
        // create cache cfg table
        Arrays.stream(BOCoreCacheCfgTableEnum.values())
                .forEach(
                        table -> {
                            String queryEntityTableName = table.getQueryEntityTableName();
                            CacheMode cacheMode = table.getCacheMode();
                            CacheAtomicityMode atomicityMode = table.getAtomicityMode();
                            CacheCfgTableInfo cacheCfgTableInfo = table.getCacheCfgTableInfo();
                            int queryParallelism = table.getQueryParallelism();
                            try {
                                CacheConfiguration<BinaryObject, BinaryObject> cfg =
                                        new CacheConfiguration<>();
                                cfg.setName(
                                        String.format(
                                                CACHE_NAME_PATTERN, ouId, queryEntityTableName));
                                cfg.setCacheMode(cacheMode);
                                cfg.setAtomicityMode(atomicityMode);
                                cfg.setDataRegionName(DEFAULT_REGION);
                                cfg.setSqlSchema(ouId);
                                cfg.setQueryParallelism(queryParallelism);

                                // create QueryEntity
                                QueryEntity qryEntity = new QueryEntity();
                                qryEntity.setTableName(queryEntityTableName);
                                qryEntity.setKeyType(
                                        String.format(
                                                KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setValueType(
                                        String.format(
                                                VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setKeyFields(cacheCfgTableInfo.getQueryEntityKeyFields());
                                qryEntity.setNotNullFields(cacheCfgTableInfo.getNotNullFields());
                                // index
                                qryEntity.setIndexes(cacheCfgTableInfo.getIndexes());
                                qryEntity.setFields(cacheCfgTableInfo.getQueryEntityFields());
                                if (cacheCfgTableInfo.getDefaultFieldValues() != null) {
                                    qryEntity.setDefaultFieldValues(
                                            cacheCfgTableInfo.getDefaultFieldValues());
                                }
                                cfg.setQueryEntities(Collections.singleton(qryEntity));

                                if (cacheCfgTableInfo.getAffKeyFieldName() != null
                                        && !cacheCfgTableInfo.getAffKeyFieldName().isEmpty()) {
                                    CacheKeyConfiguration cacheKeyCfg = new CacheKeyConfiguration();
                                    cacheKeyCfg.setTypeName(
                                            String.format(
                                                    KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                    cacheKeyCfg.setAffinityKeyFieldName(
                                            cacheCfgTableInfo.getAffKeyFieldName());
                                    cfg.setKeyConfiguration(cacheKeyCfg);
                                }

                                ignite.getOrCreateCache(cfg);
                            } catch (CacheException e) {
                                log.error(
                                        String.format(
                                                "Create cache cfg table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                                throw new InternalException(
                                        String.format(
                                                "Create cache cfg table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                            }
                        });
    }
}
