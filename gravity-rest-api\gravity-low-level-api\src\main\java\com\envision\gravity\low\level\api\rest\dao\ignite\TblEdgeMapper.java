package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblEdge;
import com.envision.gravity.common.po.TblStartVid;
import com.envision.gravity.common.pojo.EdgeProperty;
import com.envision.gravity.common.vo.topo.EdgeDeleteParam;

import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

/** @Author: qi.jiang2 @Date: 2024/04/11 15:59 @Description: */
public interface TblEdgeMapper {

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectBySubGraphIds")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectBySubGraphIds(List<String> subGraphIds, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectExistsEdges")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectExistsEdges(List<EdgeDeleteParam> edgeProperties, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectExistsSubGraphId")
    List<String> selectExistsSubGraphId(List<String> subGraphIds, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectInEdgesByToVidList")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectInEdgesByToVidList(List<String> toVidList, String subGraphId, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectOutEdgesByToVidList")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectOutEdgesByToVidList(
            List<String> toVidList, String subGraphId, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectEdgesByToVidAndSubGraphId")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectEdgesByToVidAndSubGraphId(
            Map<String, List<String>> subGraphIdToVidMap,
            List<EdgeProperty> deleteEdges,
            String orgId);

    @SelectProvider(
            type = TblEdgeSqlProvider.class,
            method = "selectEdgesByToVidAndSubGraphIdExcludeDeleteEdges")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectEdgesByToVidAndSubGraphIdExcludeDeleteEdges(
            List<String> toVids, List<EdgeProperty> deleteEdges, String subGraphId, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectEdgesByFromVidAndSubGraphId")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectEdgesByFromVidAndSubGraphId(
            Map<String, List<String>> subGraphIdFromVidMap,
            List<EdgeProperty> deleteEdges,
            String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectInAndOutEdgesByToVidList")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectInAndOutEdgesByToVidList(
            Map<String, Map<String, List<EdgeProperty>>> subGraphIdToVidEdgeMap,
            List<EdgeProperty> deleteEdges,
            String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectNeedDeleteStartVid")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectNeedDeleteStartVid(List<TblStartVid> tblStartVids, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectNeedDeleteStartVidByEdges")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectNeedDeleteStartVidByEdges(List<TblEdge> needDeleteEdges, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectNeedAddStartVid")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectNeedAddStartVid(List<String> subGraphIds, String orgId);

    @SelectProvider(type = TblEdgeSqlProvider.class, method = "selectEdgesByToVidList")
    @Results({
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> selectEdgesByToVidList(List<String> toVidList, String orgId);
}
