package com.envision.gravity.common.calculate;

import com.envision.gravity.common.enums.PrefType;


import org.apache.commons.lang3.StringUtils;

public class CalcCommonUtils {

    public static String extractCategoryId(String modelPath) {
        if (StringUtils.isEmpty(modelPath)) {
            throw new IllegalArgumentException("Model path cannot be empty");
        }

        String[] parts = modelPath.split("/");
        String categoryId = "";
        for (String part : parts) {
            if (!part.isEmpty()) {
                categoryId = part;
                break;
            }
        }
        return categoryId;
    }

    public static boolean isMeasurePoint(PrefType prefType) {
        return prefType == PrefType.MEASUREPOINT;
    }
}
