// Generated from
// C:/Users/<USER>/IdeaProjects/gravity-all/gravity-rest-api/gravity-low-level-api/src/main/java/com/envision/gravity/low/level/api/rest/antlr/Condition.g4 by ANTLR 4.13.2
package com.envision.gravity.low.level.api.rest.antlr;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;
import org.antlr.v4.runtime.tree.*;

import java.util.List;

@SuppressWarnings({
    "all",
    "warnings",
    "unchecked",
    "unused",
    "cast",
    "CheckReturnValue",
    "this-escape"
})
public class ConditionParser extends Parser {
    static {
        RuntimeMetaData.checkVersion("4.13.2", RuntimeMetaData.VERSION);
    }

    protected static final DFA[] _decisionToDFA;
    protected static final PredictionContextCache _sharedContextCache =
            new PredictionContextCache();
    public static final int T__0 = 1,
            SIGNED_NUMBER = 2,
            DECIMAL_DIGITS = 3,
            DIGIT = 4,
            TRUE = 5,
            FALSE = 6,
            INTEGER = 7,
            LIKE = 8,
            NOT_EXISTS = 9,
            EXISTS = 10,
            AND = 11,
            OR = 12,
            IN = 13,
            NOT_IN = 14,
            GT = 15,
            GE = 16,
            LT = 17,
            LE = 18,
            EQ = 19,
            NEQ = 20,
            LEFT_PAREN = 21,
            RIGHT_PAREN = 22,
            ASSET_IN_MODELS = 23,
            ASSET_IN_RELATED_MODELS = 24,
            JOIN_GRAPH = 25,
            JOIN_MODEL = 26,
            FUZZY_SEARCH = 27,
            FIELD = 28,
            ENGLISH_CHAR = 29,
            CHINESE_CHAR = 30,
            GERMAN_FRENCH_CHAR = 31,
            JAPANESE_CHAR = 32,
            OTHER_CHAR = 33,
            DOT = 34,
            COMMA = 35,
            STRING = 36,
            WS = 37;
    public static final int RULE_parse = 0,
            RULE_graphExpr = 1,
            RULE_joinModelGraphExpr = 2,
            RULE_joinModelTagEqExpr = 3,
            RULE_joinModelTagExistsExpr = 4,
            RULE_expr = 5,
            RULE_fuzzySearchField = 6,
            RULE_field = 7,
            RULE_fields = 8,
            RULE_parenFields = 9,
            RULE_parenValues = 10,
            RULE_joinGraph = 11,
            RULE_parenGraphExpr = 12,
            RULE_joinModel = 13,
            RULE_parenJoinModelExpr = 14,
            RULE_modelEdgeType = 15,
            RULE_modelParams = 16,
            RULE_assetInModels = 17,
            RULE_assetInRelatedModels = 18,
            RULE_values = 19,
            RULE_value = 20,
            RULE_stringValue = 21,
            RULE_like = 22,
            RULE_and = 23,
            RULE_comparator = 24,
            RULE_booleanValue = 25,
            RULE_isExists = 26,
            RULE_isIn = 27,
            RULE_binary = 28,
            RULE_timestamp = 29,
            RULE_leftParen = 30,
            RULE_rightParen = 31,
            RULE_i18n = 32;

    private static String[] makeRuleNames() {
        return new String[] {
            "parse",
            "graphExpr",
            "joinModelGraphExpr",
            "joinModelTagEqExpr",
            "joinModelTagExistsExpr",
            "expr",
            "fuzzySearchField",
            "field",
            "fields",
            "parenFields",
            "parenValues",
            "joinGraph",
            "parenGraphExpr",
            "joinModel",
            "parenJoinModelExpr",
            "modelEdgeType",
            "modelParams",
            "assetInModels",
            "assetInRelatedModels",
            "values",
            "value",
            "stringValue",
            "like",
            "and",
            "comparator",
            "booleanValue",
            "isExists",
            "isIn",
            "binary",
            "timestamp",
            "leftParen",
            "rightParen",
            "i18n"
        };
    }

    public static final String[] ruleNames = makeRuleNames();

    private static String[] makeLiteralNames() {
        return new String[] {
            null,
            "'i18n'",
            null,
            null,
            null,
            "'true'",
            "'false'",
            null,
            "'like'",
            "'not exists'",
            "'exists'",
            "'and'",
            "'or'",
            "'in'",
            "'not in'",
            "'>'",
            "'>='",
            "'<'",
            "'<='",
            "'='",
            "'!='",
            "'('",
            "')'",
            "'in assetInModels'",
            "'in assetInRelatedModels'",
            "'join graph'",
            "'join model'",
            "'fuzzy_search'",
            null,
            null,
            null,
            null,
            null,
            null,
            "'.'",
            "','"
        };
    }

    private static final String[] _LITERAL_NAMES = makeLiteralNames();

    private static String[] makeSymbolicNames() {
        return new String[] {
            null,
            null,
            "SIGNED_NUMBER",
            "DECIMAL_DIGITS",
            "DIGIT",
            "TRUE",
            "FALSE",
            "INTEGER",
            "LIKE",
            "NOT_EXISTS",
            "EXISTS",
            "AND",
            "OR",
            "IN",
            "NOT_IN",
            "GT",
            "GE",
            "LT",
            "LE",
            "EQ",
            "NEQ",
            "LEFT_PAREN",
            "RIGHT_PAREN",
            "ASSET_IN_MODELS",
            "ASSET_IN_RELATED_MODELS",
            "JOIN_GRAPH",
            "JOIN_MODEL",
            "FUZZY_SEARCH",
            "FIELD",
            "ENGLISH_CHAR",
            "CHINESE_CHAR",
            "GERMAN_FRENCH_CHAR",
            "JAPANESE_CHAR",
            "OTHER_CHAR",
            "DOT",
            "COMMA",
            "STRING",
            "WS"
        };
    }

    private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
    public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

    /** @deprecated Use {@link #VOCABULARY} instead. */
    @Deprecated public static final String[] tokenNames;

    static {
        tokenNames = new String[_SYMBOLIC_NAMES.length];
        for (int i = 0; i < tokenNames.length; i++) {
            tokenNames[i] = VOCABULARY.getLiteralName(i);
            if (tokenNames[i] == null) {
                tokenNames[i] = VOCABULARY.getSymbolicName(i);
            }

            if (tokenNames[i] == null) {
                tokenNames[i] = "<INVALID>";
            }
        }
    }

    @Override
    @Deprecated
    public String[] getTokenNames() {
        return tokenNames;
    }

    @Override
    public Vocabulary getVocabulary() {
        return VOCABULARY;
    }

    @Override
    public String getGrammarFileName() {
        return "Condition.g4";
    }

    @Override
    public String[] getRuleNames() {
        return ruleNames;
    }

    @Override
    public String getSerializedATN() {
        return _serializedATN;
    }

    @Override
    public ATN getATN() {
        return _ATN;
    }

    public ConditionParser(TokenStream input) {
        super(input);
        _interp = new ParserATNSimulator(this, _ATN, _decisionToDFA, _sharedContextCache);
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParseContext extends ParserRuleContext {
        public ExprContext expr() {
            return getRuleContext(ExprContext.class, 0);
        }

        public TerminalNode EOF() {
            return getToken(ConditionParser.EOF, 0);
        }

        public ParseContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_parse;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParse(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParse(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParse(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ParseContext parse() throws RecognitionException {
        ParseContext _localctx = new ParseContext(_ctx, getState());
        enterRule(_localctx, 0, RULE_parse);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(66);
                expr(0);
                setState(67);
                match(EOF);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphExprContext extends ParserRuleContext {
        public GraphExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_graphExpr;
        }

        public GraphExprContext() {}

        public void copyFrom(GraphExprContext ctx) {
            super.copyFrom(ctx);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphAssetInRelatedModelsExprContext extends GraphExprContext {
        public FieldContext left;
        public AssetInRelatedModelsContext op;
        public ModelParamsContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public AssetInRelatedModelsContext assetInRelatedModels() {
            return getRuleContext(AssetInRelatedModelsContext.class, 0);
        }

        public ModelParamsContext modelParams() {
            return getRuleContext(ModelParamsContext.class, 0);
        }

        public GraphAssetInRelatedModelsExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphAssetInRelatedModelsExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphAssetInRelatedModelsExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor)
                        .visitGraphAssetInRelatedModelsExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphAndExprContext extends GraphExprContext {
        public GraphExprContext left;
        public AndContext op;
        public GraphExprContext right;

        public List<GraphExprContext> graphExpr() {
            return getRuleContexts(GraphExprContext.class);
        }

        public GraphExprContext graphExpr(int i) {
            return getRuleContext(GraphExprContext.class, i);
        }

        public AndContext and() {
            return getRuleContext(AndContext.class, 0);
        }

        public GraphAndExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphAndExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphAndExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitGraphAndExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphAssetInModelsExprContext extends GraphExprContext {
        public FieldContext left;
        public AssetInModelsContext op;
        public ParenValuesContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public AssetInModelsContext assetInModels() {
            return getRuleContext(AssetInModelsContext.class, 0);
        }

        public ParenValuesContext parenValues() {
            return getRuleContext(ParenValuesContext.class, 0);
        }

        public GraphAssetInModelsExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphAssetInModelsExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphAssetInModelsExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitGraphAssetInModelsExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphParenExprContext extends GraphExprContext {
        public LeftParenContext left;
        public GraphExprContext op;
        public RightParenContext right;

        public LeftParenContext leftParen() {
            return getRuleContext(LeftParenContext.class, 0);
        }

        public GraphExprContext graphExpr() {
            return getRuleContext(GraphExprContext.class, 0);
        }

        public RightParenContext rightParen() {
            return getRuleContext(RightParenContext.class, 0);
        }

        public GraphParenExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphParenExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphParenExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitGraphParenExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphIsInExprContext extends GraphExprContext {
        public FieldContext left;
        public IsInContext op;
        public ParenValuesContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public IsInContext isIn() {
            return getRuleContext(IsInContext.class, 0);
        }

        public ParenValuesContext parenValues() {
            return getRuleContext(ParenValuesContext.class, 0);
        }

        public GraphIsInExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphIsInExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphIsInExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitGraphIsInExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class GraphComparatorExprContext extends GraphExprContext {
        public FieldContext left;
        public ComparatorContext op;
        public ValueContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public ComparatorContext comparator() {
            return getRuleContext(ComparatorContext.class, 0);
        }

        public ValueContext value() {
            return getRuleContext(ValueContext.class, 0);
        }

        public GraphComparatorExprContext(GraphExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterGraphComparatorExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitGraphComparatorExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitGraphComparatorExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final GraphExprContext graphExpr() throws RecognitionException {
        return graphExpr(0);
    }

    private GraphExprContext graphExpr(int _p) throws RecognitionException {
        ParserRuleContext _parentctx = _ctx;
        int _parentState = getState();
        GraphExprContext _localctx = new GraphExprContext(_ctx, _parentState);
        GraphExprContext _prevctx = _localctx;
        int _startState = 2;
        enterRecursionRule(_localctx, 2, RULE_graphExpr, _p);
        try {
            int _alt;
            enterOuterAlt(_localctx, 1);
            {
                setState(90);
                _errHandler.sync(this);
                switch (getInterpreter().adaptivePredict(_input, 0, _ctx)) {
                    case 1:
                        {
                            _localctx = new GraphParenExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;

                            setState(70);
                            ((GraphParenExprContext) _localctx).left = leftParen();
                            setState(71);
                            ((GraphParenExprContext) _localctx).op = graphExpr(0);
                            setState(72);
                            ((GraphParenExprContext) _localctx).right = rightParen();
                        }
                        break;
                    case 2:
                        {
                            _localctx = new GraphIsInExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(74);
                            ((GraphIsInExprContext) _localctx).left = field();
                            setState(75);
                            ((GraphIsInExprContext) _localctx).op = isIn();
                            setState(76);
                            ((GraphIsInExprContext) _localctx).right = parenValues();
                        }
                        break;
                    case 3:
                        {
                            _localctx = new GraphComparatorExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(78);
                            ((GraphComparatorExprContext) _localctx).left = field();
                            setState(79);
                            ((GraphComparatorExprContext) _localctx).op = comparator();
                            setState(80);
                            ((GraphComparatorExprContext) _localctx).right = value();
                        }
                        break;
                    case 4:
                        {
                            _localctx = new GraphAssetInModelsExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(82);
                            ((GraphAssetInModelsExprContext) _localctx).left = field();
                            setState(83);
                            ((GraphAssetInModelsExprContext) _localctx).op = assetInModels();
                            setState(84);
                            ((GraphAssetInModelsExprContext) _localctx).right = parenValues();
                        }
                        break;
                    case 5:
                        {
                            _localctx = new GraphAssetInRelatedModelsExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(86);
                            ((GraphAssetInRelatedModelsExprContext) _localctx).left = field();
                            setState(87);
                            ((GraphAssetInRelatedModelsExprContext) _localctx).op =
                                    assetInRelatedModels();
                            setState(88);
                            ((GraphAssetInRelatedModelsExprContext) _localctx).right =
                                    modelParams();
                        }
                        break;
                }
                _ctx.stop = _input.LT(-1);
                setState(98);
                _errHandler.sync(this);
                _alt = getInterpreter().adaptivePredict(_input, 1, _ctx);
                while (_alt != 2 && _alt != ATN.INVALID_ALT_NUMBER) {
                    if (_alt == 1) {
                        if (_parseListeners != null) triggerExitRuleEvent();
                        _prevctx = _localctx;
                        {
                            {
                                _localctx =
                                        new GraphAndExprContext(
                                                new GraphExprContext(_parentctx, _parentState));
                                ((GraphAndExprContext) _localctx).left = _prevctx;
                                pushNewRecursionContext(_localctx, _startState, RULE_graphExpr);
                                setState(92);
                                if (!(precpred(_ctx, 6)))
                                    throw new FailedPredicateException(this, "precpred(_ctx, 6)");
                                setState(93);
                                ((GraphAndExprContext) _localctx).op = and();
                                setState(94);
                                ((GraphAndExprContext) _localctx).right = graphExpr(7);
                            }
                        }
                    }
                    setState(100);
                    _errHandler.sync(this);
                    _alt = getInterpreter().adaptivePredict(_input, 1, _ctx);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            unrollRecursionContexts(_parentctx);
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinModelGraphExprContext extends ParserRuleContext {
        public JoinModelTagEqExprContext joinModelTagEqExpr() {
            return getRuleContext(JoinModelTagEqExprContext.class, 0);
        }

        public JoinModelTagExistsExprContext joinModelTagExistsExpr() {
            return getRuleContext(JoinModelTagExistsExprContext.class, 0);
        }

        public JoinModelGraphExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_joinModelGraphExpr;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinModelGraphExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinModelGraphExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinModelGraphExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final JoinModelGraphExprContext joinModelGraphExpr() throws RecognitionException {
        JoinModelGraphExprContext _localctx = new JoinModelGraphExprContext(_ctx, getState());
        enterRule(_localctx, 4, RULE_joinModelGraphExpr);
        try {
            setState(103);
            _errHandler.sync(this);
            switch (_input.LA(1)) {
                case FIELD:
                    enterOuterAlt(_localctx, 1);
                    {
                        setState(101);
                        joinModelTagEqExpr();
                    }
                    break;
                case EXISTS:
                    enterOuterAlt(_localctx, 2);
                    {
                        setState(102);
                        joinModelTagExistsExpr();
                    }
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinModelTagEqExprContext extends ParserRuleContext {
        public FieldContext left;
        public Token op;
        public ValueContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public TerminalNode EQ() {
            return getToken(ConditionParser.EQ, 0);
        }

        public ValueContext value() {
            return getRuleContext(ValueContext.class, 0);
        }

        public JoinModelTagEqExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_joinModelTagEqExpr;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinModelTagEqExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinModelTagEqExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinModelTagEqExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final JoinModelTagEqExprContext joinModelTagEqExpr() throws RecognitionException {
        JoinModelTagEqExprContext _localctx = new JoinModelTagEqExprContext(_ctx, getState());
        enterRule(_localctx, 6, RULE_joinModelTagEqExpr);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(105);
                ((JoinModelTagEqExprContext) _localctx).left = field();
                setState(106);
                ((JoinModelTagEqExprContext) _localctx).op = match(EQ);
                setState(107);
                ((JoinModelTagEqExprContext) _localctx).right = value();
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinModelTagExistsExprContext extends ParserRuleContext {
        public TerminalNode EXISTS() {
            return getToken(ConditionParser.EXISTS, 0);
        }

        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public JoinModelTagExistsExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_joinModelTagExistsExpr;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinModelTagExistsExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinModelTagExistsExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinModelTagExistsExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final JoinModelTagExistsExprContext joinModelTagExistsExpr()
            throws RecognitionException {
        JoinModelTagExistsExprContext _localctx =
                new JoinModelTagExistsExprContext(_ctx, getState());
        enterRule(_localctx, 8, RULE_joinModelTagExistsExpr);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(109);
                match(EXISTS);
                setState(110);
                match(LEFT_PAREN);
                setState(111);
                field();
                setState(112);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ExprContext extends ParserRuleContext {
        public ExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_expr;
        }

        public ExprContext() {}

        public void copyFrom(ExprContext ctx) {
            super.copyFrom(ctx);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class I18nLikeExprContext extends ExprContext {
        public I18nContext left;
        public LikeContext op;
        public StringValueContext right;

        public I18nContext i18n() {
            return getRuleContext(I18nContext.class, 0);
        }

        public LikeContext like() {
            return getRuleContext(LikeContext.class, 0);
        }

        public StringValueContext stringValue() {
            return getRuleContext(StringValueContext.class, 0);
        }

        public I18nLikeExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterI18nLikeExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitI18nLikeExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitI18nLikeExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ComparatorExprContext extends ExprContext {
        public FieldContext left;
        public ComparatorContext op;
        public ValueContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public ComparatorContext comparator() {
            return getRuleContext(ComparatorContext.class, 0);
        }

        public ValueContext value() {
            return getRuleContext(ValueContext.class, 0);
        }

        public ComparatorExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterComparatorExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitComparatorExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitComparatorExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinModelExprContext extends ExprContext {
        public JoinModelContext op;
        public ParenJoinModelExprContext right;

        public JoinModelContext joinModel() {
            return getRuleContext(JoinModelContext.class, 0);
        }

        public ParenJoinModelExprContext parenJoinModelExpr() {
            return getRuleContext(ParenJoinModelExprContext.class, 0);
        }

        public JoinModelExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinModelExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinModelExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinModelExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class IsExistsExprContext extends ExprContext {
        public IsExistsContext op;
        public ParenFieldsContext right;

        public IsExistsContext isExists() {
            return getRuleContext(IsExistsContext.class, 0);
        }

        public ParenFieldsContext parenFields() {
            return getRuleContext(ParenFieldsContext.class, 0);
        }

        public IsExistsExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterIsExistsExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitIsExistsExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitIsExistsExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinGraphExprContext extends ExprContext {
        public JoinGraphContext op;
        public ParenGraphExprContext right;

        public JoinGraphContext joinGraph() {
            return getRuleContext(JoinGraphContext.class, 0);
        }

        public ParenGraphExprContext parenGraphExpr() {
            return getRuleContext(ParenGraphExprContext.class, 0);
        }

        public JoinGraphExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinGraphExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinGraphExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinGraphExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class I18nComparatorExprContext extends ExprContext {
        public I18nContext left;
        public ComparatorContext op;
        public ValueContext right;

        public I18nContext i18n() {
            return getRuleContext(I18nContext.class, 0);
        }

        public ComparatorContext comparator() {
            return getRuleContext(ComparatorContext.class, 0);
        }

        public ValueContext value() {
            return getRuleContext(ValueContext.class, 0);
        }

        public I18nComparatorExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterI18nComparatorExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitI18nComparatorExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitI18nComparatorExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class InExprContext extends ExprContext {
        public FieldContext left;
        public IsInContext op;
        public ParenValuesContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public IsInContext isIn() {
            return getRuleContext(IsInContext.class, 0);
        }

        public ParenValuesContext parenValues() {
            return getRuleContext(ParenValuesContext.class, 0);
        }

        public InExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterInExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitInExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitInExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class LikeExprContext extends ExprContext {
        public FieldContext left;
        public LikeContext op;
        public StringValueContext right;

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public LikeContext like() {
            return getRuleContext(LikeContext.class, 0);
        }

        public StringValueContext stringValue() {
            return getRuleContext(StringValueContext.class, 0);
        }

        public LikeExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterLikeExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitLikeExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitLikeExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class FuzzySearchExprContext extends ExprContext {
        public TerminalNode FUZZY_SEARCH() {
            return getToken(ConditionParser.FUZZY_SEARCH, 0);
        }

        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public FuzzySearchFieldContext fuzzySearchField() {
            return getRuleContext(FuzzySearchFieldContext.class, 0);
        }

        public TerminalNode COMMA() {
            return getToken(ConditionParser.COMMA, 0);
        }

        public StringValueContext stringValue() {
            return getRuleContext(StringValueContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public FuzzySearchExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterFuzzySearchExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitFuzzySearchExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitFuzzySearchExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class BinaryExprContext extends ExprContext {
        public ExprContext left;
        public BinaryContext op;
        public ExprContext right;

        public List<ExprContext> expr() {
            return getRuleContexts(ExprContext.class);
        }

        public ExprContext expr(int i) {
            return getRuleContext(ExprContext.class, i);
        }

        public BinaryContext binary() {
            return getRuleContext(BinaryContext.class, 0);
        }

        public BinaryExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterBinaryExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitBinaryExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitBinaryExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParenExprContext extends ExprContext {
        public LeftParenContext left;
        public ExprContext op;
        public RightParenContext right;

        public LeftParenContext leftParen() {
            return getRuleContext(LeftParenContext.class, 0);
        }

        public ExprContext expr() {
            return getRuleContext(ExprContext.class, 0);
        }

        public RightParenContext rightParen() {
            return getRuleContext(RightParenContext.class, 0);
        }

        public ParenExprContext(ExprContext ctx) {
            copyFrom(ctx);
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParenExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParenExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParenExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ExprContext expr() throws RecognitionException {
        return expr(0);
    }

    private ExprContext expr(int _p) throws RecognitionException {
        ParserRuleContext _parentctx = _ctx;
        int _parentState = getState();
        ExprContext _localctx = new ExprContext(_ctx, _parentState);
        ExprContext _prevctx = _localctx;
        int _startState = 10;
        enterRecursionRule(_localctx, 10, RULE_expr, _p);
        try {
            int _alt;
            enterOuterAlt(_localctx, 1);
            {
                setState(155);
                _errHandler.sync(this);
                switch (getInterpreter().adaptivePredict(_input, 3, _ctx)) {
                    case 1:
                        {
                            _localctx = new ParenExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;

                            setState(115);
                            ((ParenExprContext) _localctx).left = leftParen();
                            setState(116);
                            ((ParenExprContext) _localctx).op = expr(0);
                            setState(117);
                            ((ParenExprContext) _localctx).right = rightParen();
                        }
                        break;
                    case 2:
                        {
                            _localctx = new InExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(119);
                            ((InExprContext) _localctx).left = field();
                            setState(120);
                            ((InExprContext) _localctx).op = isIn();
                            setState(121);
                            ((InExprContext) _localctx).right = parenValues();
                        }
                        break;
                    case 3:
                        {
                            _localctx = new ComparatorExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(123);
                            ((ComparatorExprContext) _localctx).left = field();
                            setState(124);
                            ((ComparatorExprContext) _localctx).op = comparator();
                            setState(125);
                            ((ComparatorExprContext) _localctx).right = value();
                        }
                        break;
                    case 4:
                        {
                            _localctx = new LikeExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(127);
                            ((LikeExprContext) _localctx).left = field();
                            setState(128);
                            ((LikeExprContext) _localctx).op = like();
                            setState(129);
                            ((LikeExprContext) _localctx).right = stringValue();
                        }
                        break;
                    case 5:
                        {
                            _localctx = new IsExistsExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(131);
                            ((IsExistsExprContext) _localctx).op = isExists();
                            setState(132);
                            ((IsExistsExprContext) _localctx).right = parenFields();
                        }
                        break;
                    case 6:
                        {
                            _localctx = new JoinGraphExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(134);
                            ((JoinGraphExprContext) _localctx).op = joinGraph();
                            setState(135);
                            ((JoinGraphExprContext) _localctx).right = parenGraphExpr();
                        }
                        break;
                    case 7:
                        {
                            _localctx = new JoinModelExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(137);
                            ((JoinModelExprContext) _localctx).op = joinModel();
                            setState(138);
                            ((JoinModelExprContext) _localctx).right = parenJoinModelExpr();
                        }
                        break;
                    case 8:
                        {
                            _localctx = new I18nComparatorExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(140);
                            ((I18nComparatorExprContext) _localctx).left = i18n();
                            setState(141);
                            ((I18nComparatorExprContext) _localctx).op = comparator();
                            setState(142);
                            ((I18nComparatorExprContext) _localctx).right = value();
                        }
                        break;
                    case 9:
                        {
                            _localctx = new I18nLikeExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(144);
                            ((I18nLikeExprContext) _localctx).left = i18n();
                            setState(145);
                            ((I18nLikeExprContext) _localctx).op = like();
                            setState(146);
                            ((I18nLikeExprContext) _localctx).right = stringValue();
                        }
                        break;
                    case 10:
                        {
                            _localctx = new FuzzySearchExprContext(_localctx);
                            _ctx = _localctx;
                            _prevctx = _localctx;
                            setState(148);
                            match(FUZZY_SEARCH);
                            setState(149);
                            match(LEFT_PAREN);
                            setState(150);
                            fuzzySearchField();
                            setState(151);
                            match(COMMA);
                            setState(152);
                            stringValue();
                            setState(153);
                            match(RIGHT_PAREN);
                        }
                        break;
                }
                _ctx.stop = _input.LT(-1);
                setState(163);
                _errHandler.sync(this);
                _alt = getInterpreter().adaptivePredict(_input, 4, _ctx);
                while (_alt != 2 && _alt != ATN.INVALID_ALT_NUMBER) {
                    if (_alt == 1) {
                        if (_parseListeners != null) triggerExitRuleEvent();
                        _prevctx = _localctx;
                        {
                            {
                                _localctx =
                                        new BinaryExprContext(
                                                new ExprContext(_parentctx, _parentState));
                                ((BinaryExprContext) _localctx).left = _prevctx;
                                pushNewRecursionContext(_localctx, _startState, RULE_expr);
                                setState(157);
                                if (!(precpred(_ctx, 11)))
                                    throw new FailedPredicateException(this, "precpred(_ctx, 11)");
                                setState(158);
                                ((BinaryExprContext) _localctx).op = binary();
                                setState(159);
                                ((BinaryExprContext) _localctx).right = expr(12);
                            }
                        }
                    }
                    setState(165);
                    _errHandler.sync(this);
                    _alt = getInterpreter().adaptivePredict(_input, 4, _ctx);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            unrollRecursionContexts(_parentctx);
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class FuzzySearchFieldContext extends ParserRuleContext {
        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public I18nContext i18n() {
            return getRuleContext(I18nContext.class, 0);
        }

        public FuzzySearchFieldContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_fuzzySearchField;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterFuzzySearchField(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitFuzzySearchField(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitFuzzySearchField(this);
            else return visitor.visitChildren(this);
        }
    }

    public final FuzzySearchFieldContext fuzzySearchField() throws RecognitionException {
        FuzzySearchFieldContext _localctx = new FuzzySearchFieldContext(_ctx, getState());
        enterRule(_localctx, 12, RULE_fuzzySearchField);
        try {
            setState(168);
            _errHandler.sync(this);
            switch (_input.LA(1)) {
                case FIELD:
                    enterOuterAlt(_localctx, 1);
                    {
                        setState(166);
                        field();
                    }
                    break;
                case T__0:
                    enterOuterAlt(_localctx, 2);
                    {
                        setState(167);
                        i18n();
                    }
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class FieldContext extends ParserRuleContext {
        public List<TerminalNode> FIELD() {
            return getTokens(ConditionParser.FIELD);
        }

        public TerminalNode FIELD(int i) {
            return getToken(ConditionParser.FIELD, i);
        }

        public List<TerminalNode> DOT() {
            return getTokens(ConditionParser.DOT);
        }

        public TerminalNode DOT(int i) {
            return getToken(ConditionParser.DOT, i);
        }

        public List<TerminalNode> SIGNED_NUMBER() {
            return getTokens(ConditionParser.SIGNED_NUMBER);
        }

        public TerminalNode SIGNED_NUMBER(int i) {
            return getToken(ConditionParser.SIGNED_NUMBER, i);
        }

        public FieldContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_field;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterField(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitField(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitField(this);
            else return visitor.visitChildren(this);
        }
    }

    public final FieldContext field() throws RecognitionException {
        FieldContext _localctx = new FieldContext(_ctx, getState());
        enterRule(_localctx, 14, RULE_field);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(171);
                _errHandler.sync(this);
                _la = _input.LA(1);
                do {
                    {
                        {
                            setState(170);
                            match(FIELD);
                        }
                    }
                    setState(173);
                    _errHandler.sync(this);
                    _la = _input.LA(1);
                } while (_la == FIELD);
                setState(179);
                _errHandler.sync(this);
                _la = _input.LA(1);
                while (_la == DOT) {
                    {
                        {
                            setState(175);
                            match(DOT);
                            setState(176);
                            _la = _input.LA(1);
                            if (!(_la == SIGNED_NUMBER || _la == FIELD)) {
                                _errHandler.recoverInline(this);
                            } else {
                                if (_input.LA(1) == Token.EOF) matchedEOF = true;
                                _errHandler.reportMatch(this);
                                consume();
                            }
                        }
                    }
                    setState(181);
                    _errHandler.sync(this);
                    _la = _input.LA(1);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class FieldsContext extends ParserRuleContext {
        public List<FieldContext> field() {
            return getRuleContexts(FieldContext.class);
        }

        public FieldContext field(int i) {
            return getRuleContext(FieldContext.class, i);
        }

        public List<TerminalNode> COMMA() {
            return getTokens(ConditionParser.COMMA);
        }

        public TerminalNode COMMA(int i) {
            return getToken(ConditionParser.COMMA, i);
        }

        public FieldsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_fields;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterFields(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitFields(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitFields(this);
            else return visitor.visitChildren(this);
        }
    }

    public final FieldsContext fields() throws RecognitionException {
        FieldsContext _localctx = new FieldsContext(_ctx, getState());
        enterRule(_localctx, 16, RULE_fields);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(182);
                field();
                setState(187);
                _errHandler.sync(this);
                _la = _input.LA(1);
                while (_la == COMMA) {
                    {
                        {
                            setState(183);
                            match(COMMA);
                            setState(184);
                            field();
                        }
                    }
                    setState(189);
                    _errHandler.sync(this);
                    _la = _input.LA(1);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParenFieldsContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public FieldsContext fields() {
            return getRuleContext(FieldsContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public ParenFieldsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_parenFields;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParenFields(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParenFields(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParenFields(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ParenFieldsContext parenFields() throws RecognitionException {
        ParenFieldsContext _localctx = new ParenFieldsContext(_ctx, getState());
        enterRule(_localctx, 18, RULE_parenFields);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(190);
                match(LEFT_PAREN);
                setState(191);
                fields();
                setState(192);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParenValuesContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public ValuesContext values() {
            return getRuleContext(ValuesContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public ParenValuesContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_parenValues;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParenValues(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParenValues(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParenValues(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ParenValuesContext parenValues() throws RecognitionException {
        ParenValuesContext _localctx = new ParenValuesContext(_ctx, getState());
        enterRule(_localctx, 20, RULE_parenValues);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(194);
                match(LEFT_PAREN);
                setState(195);
                values();
                setState(196);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinGraphContext extends ParserRuleContext {
        public TerminalNode JOIN_GRAPH() {
            return getToken(ConditionParser.JOIN_GRAPH, 0);
        }

        public JoinGraphContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_joinGraph;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinGraph(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinGraph(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinGraph(this);
            else return visitor.visitChildren(this);
        }
    }

    public final JoinGraphContext joinGraph() throws RecognitionException {
        JoinGraphContext _localctx = new JoinGraphContext(_ctx, getState());
        enterRule(_localctx, 22, RULE_joinGraph);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(198);
                match(JOIN_GRAPH);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParenGraphExprContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public GraphExprContext graphExpr() {
            return getRuleContext(GraphExprContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public ParenGraphExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_parenGraphExpr;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParenGraphExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParenGraphExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParenGraphExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ParenGraphExprContext parenGraphExpr() throws RecognitionException {
        ParenGraphExprContext _localctx = new ParenGraphExprContext(_ctx, getState());
        enterRule(_localctx, 24, RULE_parenGraphExpr);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(200);
                match(LEFT_PAREN);
                setState(201);
                graphExpr(0);
                setState(202);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class JoinModelContext extends ParserRuleContext {
        public TerminalNode JOIN_MODEL() {
            return getToken(ConditionParser.JOIN_MODEL, 0);
        }

        public JoinModelContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_joinModel;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterJoinModel(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitJoinModel(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitJoinModel(this);
            else return visitor.visitChildren(this);
        }
    }

    public final JoinModelContext joinModel() throws RecognitionException {
        JoinModelContext _localctx = new JoinModelContext(_ctx, getState());
        enterRule(_localctx, 26, RULE_joinModel);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(204);
                match(JOIN_MODEL);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ParenJoinModelExprContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public JoinModelGraphExprContext joinModelGraphExpr() {
            return getRuleContext(JoinModelGraphExprContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public ParenJoinModelExprContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_parenJoinModelExpr;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterParenJoinModelExpr(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitParenJoinModelExpr(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitParenJoinModelExpr(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ParenJoinModelExprContext parenJoinModelExpr() throws RecognitionException {
        ParenJoinModelExprContext _localctx = new ParenJoinModelExprContext(_ctx, getState());
        enterRule(_localctx, 28, RULE_parenJoinModelExpr);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(206);
                match(LEFT_PAREN);
                setState(207);
                joinModelGraphExpr();
                setState(208);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ModelEdgeTypeContext extends ParserRuleContext {
        public TerminalNode FIELD() {
            return getToken(ConditionParser.FIELD, 0);
        }

        public ModelEdgeTypeContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_modelEdgeType;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterModelEdgeType(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitModelEdgeType(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitModelEdgeType(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ModelEdgeTypeContext modelEdgeType() throws RecognitionException {
        ModelEdgeTypeContext _localctx = new ModelEdgeTypeContext(_ctx, getState());
        enterRule(_localctx, 30, RULE_modelEdgeType);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(210);
                match(FIELD);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ModelParamsContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public ModelEdgeTypeContext modelEdgeType() {
            return getRuleContext(ModelEdgeTypeContext.class, 0);
        }

        public TerminalNode COMMA() {
            return getToken(ConditionParser.COMMA, 0);
        }

        public ValuesContext values() {
            return getRuleContext(ValuesContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public ModelParamsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_modelParams;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterModelParams(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitModelParams(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitModelParams(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ModelParamsContext modelParams() throws RecognitionException {
        ModelParamsContext _localctx = new ModelParamsContext(_ctx, getState());
        enterRule(_localctx, 32, RULE_modelParams);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(212);
                match(LEFT_PAREN);
                setState(213);
                modelEdgeType();
                setState(214);
                match(COMMA);
                setState(215);
                values();
                setState(216);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class AssetInModelsContext extends ParserRuleContext {
        public TerminalNode ASSET_IN_MODELS() {
            return getToken(ConditionParser.ASSET_IN_MODELS, 0);
        }

        public AssetInModelsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_assetInModels;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterAssetInModels(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitAssetInModels(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitAssetInModels(this);
            else return visitor.visitChildren(this);
        }
    }

    public final AssetInModelsContext assetInModels() throws RecognitionException {
        AssetInModelsContext _localctx = new AssetInModelsContext(_ctx, getState());
        enterRule(_localctx, 34, RULE_assetInModels);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(218);
                match(ASSET_IN_MODELS);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class AssetInRelatedModelsContext extends ParserRuleContext {
        public TerminalNode ASSET_IN_RELATED_MODELS() {
            return getToken(ConditionParser.ASSET_IN_RELATED_MODELS, 0);
        }

        public AssetInRelatedModelsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_assetInRelatedModels;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterAssetInRelatedModels(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitAssetInRelatedModels(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitAssetInRelatedModels(this);
            else return visitor.visitChildren(this);
        }
    }

    public final AssetInRelatedModelsContext assetInRelatedModels() throws RecognitionException {
        AssetInRelatedModelsContext _localctx = new AssetInRelatedModelsContext(_ctx, getState());
        enterRule(_localctx, 36, RULE_assetInRelatedModels);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(220);
                match(ASSET_IN_RELATED_MODELS);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ValuesContext extends ParserRuleContext {
        public List<ValueContext> value() {
            return getRuleContexts(ValueContext.class);
        }

        public ValueContext value(int i) {
            return getRuleContext(ValueContext.class, i);
        }

        public List<TerminalNode> COMMA() {
            return getTokens(ConditionParser.COMMA);
        }

        public TerminalNode COMMA(int i) {
            return getToken(ConditionParser.COMMA, i);
        }

        public ValuesContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_values;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterValues(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitValues(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitValues(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ValuesContext values() throws RecognitionException {
        ValuesContext _localctx = new ValuesContext(_ctx, getState());
        enterRule(_localctx, 38, RULE_values);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(222);
                value();
                setState(227);
                _errHandler.sync(this);
                _la = _input.LA(1);
                while (_la == COMMA) {
                    {
                        {
                            setState(223);
                            match(COMMA);
                            setState(224);
                            value();
                        }
                    }
                    setState(229);
                    _errHandler.sync(this);
                    _la = _input.LA(1);
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ValueContext extends ParserRuleContext {
        public TerminalNode STRING() {
            return getToken(ConditionParser.STRING, 0);
        }

        public TerminalNode SIGNED_NUMBER() {
            return getToken(ConditionParser.SIGNED_NUMBER, 0);
        }

        public TimestampContext timestamp() {
            return getRuleContext(TimestampContext.class, 0);
        }

        public BooleanValueContext booleanValue() {
            return getRuleContext(BooleanValueContext.class, 0);
        }

        public ValueContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_value;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterValue(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitValue(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitValue(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ValueContext value() throws RecognitionException {
        ValueContext _localctx = new ValueContext(_ctx, getState());
        enterRule(_localctx, 40, RULE_value);
        try {
            setState(234);
            _errHandler.sync(this);
            switch (_input.LA(1)) {
                case STRING:
                    enterOuterAlt(_localctx, 1);
                    {
                        setState(230);
                        match(STRING);
                    }
                    break;
                case SIGNED_NUMBER:
                    enterOuterAlt(_localctx, 2);
                    {
                        setState(231);
                        match(SIGNED_NUMBER);
                    }
                    break;
                case INTEGER:
                    enterOuterAlt(_localctx, 3);
                    {
                        setState(232);
                        timestamp();
                    }
                    break;
                case TRUE:
                case FALSE:
                    enterOuterAlt(_localctx, 4);
                    {
                        setState(233);
                        booleanValue();
                    }
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class StringValueContext extends ParserRuleContext {
        public TerminalNode STRING() {
            return getToken(ConditionParser.STRING, 0);
        }

        public StringValueContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_stringValue;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterStringValue(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitStringValue(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitStringValue(this);
            else return visitor.visitChildren(this);
        }
    }

    public final StringValueContext stringValue() throws RecognitionException {
        StringValueContext _localctx = new StringValueContext(_ctx, getState());
        enterRule(_localctx, 42, RULE_stringValue);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(236);
                match(STRING);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class LikeContext extends ParserRuleContext {
        public TerminalNode LIKE() {
            return getToken(ConditionParser.LIKE, 0);
        }

        public LikeContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_like;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterLike(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitLike(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitLike(this);
            else return visitor.visitChildren(this);
        }
    }

    public final LikeContext like() throws RecognitionException {
        LikeContext _localctx = new LikeContext(_ctx, getState());
        enterRule(_localctx, 44, RULE_like);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(238);
                match(LIKE);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class AndContext extends ParserRuleContext {
        public TerminalNode AND() {
            return getToken(ConditionParser.AND, 0);
        }

        public AndContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_and;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterAnd(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener) ((ConditionListener) listener).exitAnd(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitAnd(this);
            else return visitor.visitChildren(this);
        }
    }

    public final AndContext and() throws RecognitionException {
        AndContext _localctx = new AndContext(_ctx, getState());
        enterRule(_localctx, 46, RULE_and);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(240);
                match(AND);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class ComparatorContext extends ParserRuleContext {
        public TerminalNode GT() {
            return getToken(ConditionParser.GT, 0);
        }

        public TerminalNode GE() {
            return getToken(ConditionParser.GE, 0);
        }

        public TerminalNode LT() {
            return getToken(ConditionParser.LT, 0);
        }

        public TerminalNode LE() {
            return getToken(ConditionParser.LE, 0);
        }

        public TerminalNode EQ() {
            return getToken(ConditionParser.EQ, 0);
        }

        public TerminalNode NEQ() {
            return getToken(ConditionParser.NEQ, 0);
        }

        public ComparatorContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_comparator;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterComparator(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitComparator(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitComparator(this);
            else return visitor.visitChildren(this);
        }
    }

    public final ComparatorContext comparator() throws RecognitionException {
        ComparatorContext _localctx = new ComparatorContext(_ctx, getState());
        enterRule(_localctx, 48, RULE_comparator);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(242);
                _la = _input.LA(1);
                if (!((((_la) & ~0x3f) == 0 && ((1L << _la) & 2064384L) != 0))) {
                    _errHandler.recoverInline(this);
                } else {
                    if (_input.LA(1) == Token.EOF) matchedEOF = true;
                    _errHandler.reportMatch(this);
                    consume();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class BooleanValueContext extends ParserRuleContext {
        public TerminalNode TRUE() {
            return getToken(ConditionParser.TRUE, 0);
        }

        public TerminalNode FALSE() {
            return getToken(ConditionParser.FALSE, 0);
        }

        public BooleanValueContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_booleanValue;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterBooleanValue(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitBooleanValue(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitBooleanValue(this);
            else return visitor.visitChildren(this);
        }
    }

    public final BooleanValueContext booleanValue() throws RecognitionException {
        BooleanValueContext _localctx = new BooleanValueContext(_ctx, getState());
        enterRule(_localctx, 50, RULE_booleanValue);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(244);
                _la = _input.LA(1);
                if (!(_la == TRUE || _la == FALSE)) {
                    _errHandler.recoverInline(this);
                } else {
                    if (_input.LA(1) == Token.EOF) matchedEOF = true;
                    _errHandler.reportMatch(this);
                    consume();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class IsExistsContext extends ParserRuleContext {
        public TerminalNode NOT_EXISTS() {
            return getToken(ConditionParser.NOT_EXISTS, 0);
        }

        public TerminalNode EXISTS() {
            return getToken(ConditionParser.EXISTS, 0);
        }

        public IsExistsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_isExists;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterIsExists(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitIsExists(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitIsExists(this);
            else return visitor.visitChildren(this);
        }
    }

    public final IsExistsContext isExists() throws RecognitionException {
        IsExistsContext _localctx = new IsExistsContext(_ctx, getState());
        enterRule(_localctx, 52, RULE_isExists);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(246);
                _la = _input.LA(1);
                if (!(_la == NOT_EXISTS || _la == EXISTS)) {
                    _errHandler.recoverInline(this);
                } else {
                    if (_input.LA(1) == Token.EOF) matchedEOF = true;
                    _errHandler.reportMatch(this);
                    consume();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class IsInContext extends ParserRuleContext {
        public TerminalNode IN() {
            return getToken(ConditionParser.IN, 0);
        }

        public TerminalNode NOT_IN() {
            return getToken(ConditionParser.NOT_IN, 0);
        }

        public IsInContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_isIn;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterIsIn(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitIsIn(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitIsIn(this);
            else return visitor.visitChildren(this);
        }
    }

    public final IsInContext isIn() throws RecognitionException {
        IsInContext _localctx = new IsInContext(_ctx, getState());
        enterRule(_localctx, 54, RULE_isIn);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(248);
                _la = _input.LA(1);
                if (!(_la == IN || _la == NOT_IN)) {
                    _errHandler.recoverInline(this);
                } else {
                    if (_input.LA(1) == Token.EOF) matchedEOF = true;
                    _errHandler.reportMatch(this);
                    consume();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class BinaryContext extends ParserRuleContext {
        public TerminalNode AND() {
            return getToken(ConditionParser.AND, 0);
        }

        public TerminalNode OR() {
            return getToken(ConditionParser.OR, 0);
        }

        public BinaryContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_binary;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterBinary(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitBinary(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitBinary(this);
            else return visitor.visitChildren(this);
        }
    }

    public final BinaryContext binary() throws RecognitionException {
        BinaryContext _localctx = new BinaryContext(_ctx, getState());
        enterRule(_localctx, 56, RULE_binary);
        int _la;
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(250);
                _la = _input.LA(1);
                if (!(_la == AND || _la == OR)) {
                    _errHandler.recoverInline(this);
                } else {
                    if (_input.LA(1) == Token.EOF) matchedEOF = true;
                    _errHandler.reportMatch(this);
                    consume();
                }
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class TimestampContext extends ParserRuleContext {
        public TerminalNode INTEGER() {
            return getToken(ConditionParser.INTEGER, 0);
        }

        public TimestampContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_timestamp;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterTimestamp(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitTimestamp(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitTimestamp(this);
            else return visitor.visitChildren(this);
        }
    }

    public final TimestampContext timestamp() throws RecognitionException {
        TimestampContext _localctx = new TimestampContext(_ctx, getState());
        enterRule(_localctx, 58, RULE_timestamp);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(252);
                match(INTEGER);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class LeftParenContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public LeftParenContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_leftParen;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterLeftParen(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitLeftParen(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitLeftParen(this);
            else return visitor.visitChildren(this);
        }
    }

    public final LeftParenContext leftParen() throws RecognitionException {
        LeftParenContext _localctx = new LeftParenContext(_ctx, getState());
        enterRule(_localctx, 60, RULE_leftParen);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(254);
                match(LEFT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class RightParenContext extends ParserRuleContext {
        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public RightParenContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_rightParen;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterRightParen(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitRightParen(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitRightParen(this);
            else return visitor.visitChildren(this);
        }
    }

    public final RightParenContext rightParen() throws RecognitionException {
        RightParenContext _localctx = new RightParenContext(_ctx, getState());
        enterRule(_localctx, 62, RULE_rightParen);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(256);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    @SuppressWarnings("CheckReturnValue")
    public static class I18nContext extends ParserRuleContext {
        public TerminalNode LEFT_PAREN() {
            return getToken(ConditionParser.LEFT_PAREN, 0);
        }

        public FieldContext field() {
            return getRuleContext(FieldContext.class, 0);
        }

        public TerminalNode COMMA() {
            return getToken(ConditionParser.COMMA, 0);
        }

        public StringValueContext stringValue() {
            return getRuleContext(StringValueContext.class, 0);
        }

        public TerminalNode RIGHT_PAREN() {
            return getToken(ConditionParser.RIGHT_PAREN, 0);
        }

        public I18nContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        @Override
        public int getRuleIndex() {
            return RULE_i18n;
        }

        @Override
        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).enterI18n(this);
        }

        @Override
        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof ConditionListener)
                ((ConditionListener) listener).exitI18n(this);
        }

        @Override
        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            if (visitor instanceof ConditionVisitor)
                return ((ConditionVisitor<? extends T>) visitor).visitI18n(this);
            else return visitor.visitChildren(this);
        }
    }

    public final I18nContext i18n() throws RecognitionException {
        I18nContext _localctx = new I18nContext(_ctx, getState());
        enterRule(_localctx, 64, RULE_i18n);
        try {
            enterOuterAlt(_localctx, 1);
            {
                setState(258);
                match(T__0);
                setState(259);
                match(LEFT_PAREN);
                setState(260);
                field();
                setState(261);
                match(COMMA);
                setState(262);
                stringValue();
                setState(263);
                match(RIGHT_PAREN);
            }
        } catch (RecognitionException re) {
            _localctx.exception = re;
            _errHandler.reportError(this, re);
            _errHandler.recover(this, re);
        } finally {
            exitRule();
        }
        return _localctx;
    }

    public boolean sempred(RuleContext _localctx, int ruleIndex, int predIndex) {
        switch (ruleIndex) {
            case 1:
                return graphExpr_sempred((GraphExprContext) _localctx, predIndex);
            case 5:
                return expr_sempred((ExprContext) _localctx, predIndex);
        }
        return true;
    }

    private boolean graphExpr_sempred(GraphExprContext _localctx, int predIndex) {
        switch (predIndex) {
            case 0:
                return precpred(_ctx, 6);
        }
        return true;
    }

    private boolean expr_sempred(ExprContext _localctx, int predIndex) {
        switch (predIndex) {
            case 1:
                return precpred(_ctx, 11);
        }
        return true;
    }

    public static final String _serializedATN =
            "\u0004\u0001%\u010a\u0002\u0000\u0007\u0000\u0002\u0001\u0007\u0001\u0002"
                    + "\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004\u0007\u0004\u0002"
                    + "\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007\u0007\u0007\u0002"
                    + "\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b\u0007\u000b\u0002"
                    + "\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002\u000f\u0007\u000f"
                    + "\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002\u0012\u0007\u0012"
                    + "\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002\u0015\u0007\u0015"
                    + "\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002\u0018\u0007\u0018"
                    + "\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002\u001b\u0007\u001b"
                    + "\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002\u001e\u0007\u001e"
                    + "\u0002\u001f\u0007\u001f\u0002 \u0007 \u0001\u0000\u0001\u0000\u0001\u0000"
                    + "\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"
                    + "\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"
                    + "\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"
                    + "\u0001\u0001\u0001\u0001\u0001\u0001\u0003\u0001[\b\u0001\u0001\u0001"
                    + "\u0001\u0001\u0001\u0001\u0001\u0001\u0005\u0001a\b\u0001\n\u0001\f\u0001"
                    + "d\t\u0001\u0001\u0002\u0001\u0002\u0003\u0002h\b\u0002\u0001\u0003\u0001"
                    + "\u0003\u0001\u0003\u0001\u0003\u0001\u0004\u0001\u0004\u0001\u0004\u0001"
                    + "\u0004\u0001\u0004\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0003\u0005\u009c\b\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0001"
                    + "\u0005\u0005\u0005\u00a2\b\u0005\n\u0005\f\u0005\u00a5\t\u0005\u0001\u0006"
                    + "\u0001\u0006\u0003\u0006\u00a9\b\u0006\u0001\u0007\u0004\u0007\u00ac\b"
                    + "\u0007\u000b\u0007\f\u0007\u00ad\u0001\u0007\u0001\u0007\u0005\u0007\u00b2"
                    + "\b\u0007\n\u0007\f\u0007\u00b5\t\u0007\u0001\b\u0001\b\u0001\b\u0005\b"
                    + "\u00ba\b\b\n\b\f\b\u00bd\t\b\u0001\t\u0001\t\u0001\t\u0001\t\u0001\n\u0001"
                    + "\n\u0001\n\u0001\n\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001\f\u0001"
                    + "\f\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000e\u0001\u000e\u0001"
                    + "\u000f\u0001\u000f\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0010\u0001"
                    + "\u0010\u0001\u0010\u0001\u0011\u0001\u0011\u0001\u0012\u0001\u0012\u0001"
                    + "\u0013\u0001\u0013\u0001\u0013\u0005\u0013\u00e2\b\u0013\n\u0013\f\u0013"
                    + "\u00e5\t\u0013\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014\u0003\u0014"
                    + "\u00eb\b\u0014\u0001\u0015\u0001\u0015\u0001\u0016\u0001\u0016\u0001\u0017"
                    + "\u0001\u0017\u0001\u0018\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u001a"
                    + "\u0001\u001a\u0001\u001b\u0001\u001b\u0001\u001c\u0001\u001c\u0001\u001d"
                    + "\u0001\u001d\u0001\u001e\u0001\u001e\u0001\u001f\u0001\u001f\u0001 \u0001"
                    + " \u0001 \u0001 \u0001 \u0001 \u0001 \u0001 \u0000\u0002\u0002\n!\u0000"
                    + "\u0002\u0004\u0006\b\n\f\u000e\u0010\u0012\u0014\u0016\u0018\u001a\u001c"
                    + "\u001e \"$&(*,.02468:<>@\u0000\u0006\u0002\u0000\u0002\u0002\u001c\u001c"
                    + "\u0001\u0000\u000f\u0014\u0001\u0000\u0005\u0006\u0001\u0000\t\n\u0001"
                    + "\u0000\r\u000e\u0001\u0000\u000b\f\u0100\u0000B\u0001\u0000\u0000\u0000"
                    + "\u0002Z\u0001\u0000\u0000\u0000\u0004g\u0001\u0000\u0000\u0000\u0006i"
                    + "\u0001\u0000\u0000\u0000\bm\u0001\u0000\u0000\u0000\n\u009b\u0001\u0000"
                    + "\u0000\u0000\f\u00a8\u0001\u0000\u0000\u0000\u000e\u00ab\u0001\u0000\u0000"
                    + "\u0000\u0010\u00b6\u0001\u0000\u0000\u0000\u0012\u00be\u0001\u0000\u0000"
                    + "\u0000\u0014\u00c2\u0001\u0000\u0000\u0000\u0016\u00c6\u0001\u0000\u0000"
                    + "\u0000\u0018\u00c8\u0001\u0000\u0000\u0000\u001a\u00cc\u0001\u0000\u0000"
                    + "\u0000\u001c\u00ce\u0001\u0000\u0000\u0000\u001e\u00d2\u0001\u0000\u0000"
                    + "\u0000 \u00d4\u0001\u0000\u0000\u0000\"\u00da\u0001\u0000\u0000\u0000"
                    + "$\u00dc\u0001\u0000\u0000\u0000&\u00de\u0001\u0000\u0000\u0000(\u00ea"
                    + "\u0001\u0000\u0000\u0000*\u00ec\u0001\u0000\u0000\u0000,\u00ee\u0001\u0000"
                    + "\u0000\u0000.\u00f0\u0001\u0000\u0000\u00000\u00f2\u0001\u0000\u0000\u0000"
                    + "2\u00f4\u0001\u0000\u0000\u00004\u00f6\u0001\u0000\u0000\u00006\u00f8"
                    + "\u0001\u0000\u0000\u00008\u00fa\u0001\u0000\u0000\u0000:\u00fc\u0001\u0000"
                    + "\u0000\u0000<\u00fe\u0001\u0000\u0000\u0000>\u0100\u0001\u0000\u0000\u0000"
                    + "@\u0102\u0001\u0000\u0000\u0000BC\u0003\n\u0005\u0000CD\u0005\u0000\u0000"
                    + "\u0001D\u0001\u0001\u0000\u0000\u0000EF\u0006\u0001\uffff\uffff\u0000"
                    + "FG\u0003<\u001e\u0000GH\u0003\u0002\u0001\u0000HI\u0003>\u001f\u0000I"
                    + "[\u0001\u0000\u0000\u0000JK\u0003\u000e\u0007\u0000KL\u00036\u001b\u0000"
                    + "LM\u0003\u0014\n\u0000M[\u0001\u0000\u0000\u0000NO\u0003\u000e\u0007\u0000"
                    + "OP\u00030\u0018\u0000PQ\u0003(\u0014\u0000Q[\u0001\u0000\u0000\u0000R"
                    + "S\u0003\u000e\u0007\u0000ST\u0003\"\u0011\u0000TU\u0003\u0014\n\u0000"
                    + "U[\u0001\u0000\u0000\u0000VW\u0003\u000e\u0007\u0000WX\u0003$\u0012\u0000"
                    + "XY\u0003 \u0010\u0000Y[\u0001\u0000\u0000\u0000ZE\u0001\u0000\u0000\u0000"
                    + "ZJ\u0001\u0000\u0000\u0000ZN\u0001\u0000\u0000\u0000ZR\u0001\u0000\u0000"
                    + "\u0000ZV\u0001\u0000\u0000\u0000[b\u0001\u0000\u0000\u0000\\]\n\u0006"
                    + "\u0000\u0000]^\u0003.\u0017\u0000^_\u0003\u0002\u0001\u0007_a\u0001\u0000"
                    + "\u0000\u0000`\\\u0001\u0000\u0000\u0000ad\u0001\u0000\u0000\u0000b`\u0001"
                    + "\u0000\u0000\u0000bc\u0001\u0000\u0000\u0000c\u0003\u0001\u0000\u0000"
                    + "\u0000db\u0001\u0000\u0000\u0000eh\u0003\u0006\u0003\u0000fh\u0003\b\u0004"
                    + "\u0000ge\u0001\u0000\u0000\u0000gf\u0001\u0000\u0000\u0000h\u0005\u0001"
                    + "\u0000\u0000\u0000ij\u0003\u000e\u0007\u0000jk\u0005\u0013\u0000\u0000"
                    + "kl\u0003(\u0014\u0000l\u0007\u0001\u0000\u0000\u0000mn\u0005\n\u0000\u0000"
                    + "no\u0005\u0015\u0000\u0000op\u0003\u000e\u0007\u0000pq\u0005\u0016\u0000"
                    + "\u0000q\t\u0001\u0000\u0000\u0000rs\u0006\u0005\uffff\uffff\u0000st\u0003"
                    + "<\u001e\u0000tu\u0003\n\u0005\u0000uv\u0003>\u001f\u0000v\u009c\u0001"
                    + "\u0000\u0000\u0000wx\u0003\u000e\u0007\u0000xy\u00036\u001b\u0000yz\u0003"
                    + "\u0014\n\u0000z\u009c\u0001\u0000\u0000\u0000{|\u0003\u000e\u0007\u0000"
                    + "|}\u00030\u0018\u0000}~\u0003(\u0014\u0000~\u009c\u0001\u0000\u0000\u0000"
                    + "\u007f\u0080\u0003\u000e\u0007\u0000\u0080\u0081\u0003,\u0016\u0000\u0081"
                    + "\u0082\u0003*\u0015\u0000\u0082\u009c\u0001\u0000\u0000\u0000\u0083\u0084"
                    + "\u00034\u001a\u0000\u0084\u0085\u0003\u0012\t\u0000\u0085\u009c\u0001"
                    + "\u0000\u0000\u0000\u0086\u0087\u0003\u0016\u000b\u0000\u0087\u0088\u0003"
                    + "\u0018\f\u0000\u0088\u009c\u0001\u0000\u0000\u0000\u0089\u008a\u0003\u001a"
                    + "\r\u0000\u008a\u008b\u0003\u001c\u000e\u0000\u008b\u009c\u0001\u0000\u0000"
                    + "\u0000\u008c\u008d\u0003@ \u0000\u008d\u008e\u00030\u0018\u0000\u008e"
                    + "\u008f\u0003(\u0014\u0000\u008f\u009c\u0001\u0000\u0000\u0000\u0090\u0091"
                    + "\u0003@ \u0000\u0091\u0092\u0003,\u0016\u0000\u0092\u0093\u0003*\u0015"
                    + "\u0000\u0093\u009c\u0001\u0000\u0000\u0000\u0094\u0095\u0005\u001b\u0000"
                    + "\u0000\u0095\u0096\u0005\u0015\u0000\u0000\u0096\u0097\u0003\f\u0006\u0000"
                    + "\u0097\u0098\u0005#\u0000\u0000\u0098\u0099\u0003*\u0015\u0000\u0099\u009a"
                    + "\u0005\u0016\u0000\u0000\u009a\u009c\u0001\u0000\u0000\u0000\u009br\u0001"
                    + "\u0000\u0000\u0000\u009bw\u0001\u0000\u0000\u0000\u009b{\u0001\u0000\u0000"
                    + "\u0000\u009b\u007f\u0001\u0000\u0000\u0000\u009b\u0083\u0001\u0000\u0000"
                    + "\u0000\u009b\u0086\u0001\u0000\u0000\u0000\u009b\u0089\u0001\u0000\u0000"
                    + "\u0000\u009b\u008c\u0001\u0000\u0000\u0000\u009b\u0090\u0001\u0000\u0000"
                    + "\u0000\u009b\u0094\u0001\u0000\u0000\u0000\u009c\u00a3\u0001\u0000\u0000"
                    + "\u0000\u009d\u009e\n\u000b\u0000\u0000\u009e\u009f\u00038\u001c\u0000"
                    + "\u009f\u00a0\u0003\n\u0005\f\u00a0\u00a2\u0001\u0000\u0000\u0000\u00a1"
                    + "\u009d\u0001\u0000\u0000\u0000\u00a2\u00a5\u0001\u0000\u0000\u0000\u00a3"
                    + "\u00a1\u0001\u0000\u0000\u0000\u00a3\u00a4\u0001\u0000\u0000\u0000\u00a4"
                    + "\u000b\u0001\u0000\u0000\u0000\u00a5\u00a3\u0001\u0000\u0000\u0000\u00a6"
                    + "\u00a9\u0003\u000e\u0007\u0000\u00a7\u00a9\u0003@ \u0000\u00a8\u00a6\u0001"
                    + "\u0000\u0000\u0000\u00a8\u00a7\u0001\u0000\u0000\u0000\u00a9\r\u0001\u0000"
                    + "\u0000\u0000\u00aa\u00ac\u0005\u001c\u0000\u0000\u00ab\u00aa\u0001\u0000"
                    + "\u0000\u0000\u00ac\u00ad\u0001\u0000\u0000\u0000\u00ad\u00ab\u0001\u0000"
                    + "\u0000\u0000\u00ad\u00ae\u0001\u0000\u0000\u0000\u00ae\u00b3\u0001\u0000"
                    + "\u0000\u0000\u00af\u00b0\u0005\"\u0000\u0000\u00b0\u00b2\u0007\u0000\u0000"
                    + "\u0000\u00b1\u00af\u0001\u0000\u0000\u0000\u00b2\u00b5\u0001\u0000\u0000"
                    + "\u0000\u00b3\u00b1\u0001\u0000\u0000\u0000\u00b3\u00b4\u0001\u0000\u0000"
                    + "\u0000\u00b4\u000f\u0001\u0000\u0000\u0000\u00b5\u00b3\u0001\u0000\u0000"
                    + "\u0000\u00b6\u00bb\u0003\u000e\u0007\u0000\u00b7\u00b8\u0005#\u0000\u0000"
                    + "\u00b8\u00ba\u0003\u000e\u0007\u0000\u00b9\u00b7\u0001\u0000\u0000\u0000"
                    + "\u00ba\u00bd\u0001\u0000\u0000\u0000\u00bb\u00b9\u0001\u0000\u0000\u0000"
                    + "\u00bb\u00bc\u0001\u0000\u0000\u0000\u00bc\u0011\u0001\u0000\u0000\u0000"
                    + "\u00bd\u00bb\u0001\u0000\u0000\u0000\u00be\u00bf\u0005\u0015\u0000\u0000"
                    + "\u00bf\u00c0\u0003\u0010\b\u0000\u00c0\u00c1\u0005\u0016\u0000\u0000\u00c1"
                    + "\u0013\u0001\u0000\u0000\u0000\u00c2\u00c3\u0005\u0015\u0000\u0000\u00c3"
                    + "\u00c4\u0003&\u0013\u0000\u00c4\u00c5\u0005\u0016\u0000\u0000\u00c5\u0015"
                    + "\u0001\u0000\u0000\u0000\u00c6\u00c7\u0005\u0019\u0000\u0000\u00c7\u0017"
                    + "\u0001\u0000\u0000\u0000\u00c8\u00c9\u0005\u0015\u0000\u0000\u00c9\u00ca"
                    + "\u0003\u0002\u0001\u0000\u00ca\u00cb\u0005\u0016\u0000\u0000\u00cb\u0019"
                    + "\u0001\u0000\u0000\u0000\u00cc\u00cd\u0005\u001a\u0000\u0000\u00cd\u001b"
                    + "\u0001\u0000\u0000\u0000\u00ce\u00cf\u0005\u0015\u0000\u0000\u00cf\u00d0"
                    + "\u0003\u0004\u0002\u0000\u00d0\u00d1\u0005\u0016\u0000\u0000\u00d1\u001d"
                    + "\u0001\u0000\u0000\u0000\u00d2\u00d3\u0005\u001c\u0000\u0000\u00d3\u001f"
                    + "\u0001\u0000\u0000\u0000\u00d4\u00d5\u0005\u0015\u0000\u0000\u00d5\u00d6"
                    + "\u0003\u001e\u000f\u0000\u00d6\u00d7\u0005#\u0000\u0000\u00d7\u00d8\u0003"
                    + "&\u0013\u0000\u00d8\u00d9\u0005\u0016\u0000\u0000\u00d9!\u0001\u0000\u0000"
                    + "\u0000\u00da\u00db\u0005\u0017\u0000\u0000\u00db#\u0001\u0000\u0000\u0000"
                    + "\u00dc\u00dd\u0005\u0018\u0000\u0000\u00dd%\u0001\u0000\u0000\u0000\u00de"
                    + "\u00e3\u0003(\u0014\u0000\u00df\u00e0\u0005#\u0000\u0000\u00e0\u00e2\u0003"
                    + "(\u0014\u0000\u00e1\u00df\u0001\u0000\u0000\u0000\u00e2\u00e5\u0001\u0000"
                    + "\u0000\u0000\u00e3\u00e1\u0001\u0000\u0000\u0000\u00e3\u00e4\u0001\u0000"
                    + "\u0000\u0000\u00e4\'\u0001\u0000\u0000\u0000\u00e5\u00e3\u0001\u0000\u0000"
                    + "\u0000\u00e6\u00eb\u0005$\u0000\u0000\u00e7\u00eb\u0005\u0002\u0000\u0000"
                    + "\u00e8\u00eb\u0003:\u001d\u0000\u00e9\u00eb\u00032\u0019\u0000\u00ea\u00e6"
                    + "\u0001\u0000\u0000\u0000\u00ea\u00e7\u0001\u0000\u0000\u0000\u00ea\u00e8"
                    + "\u0001\u0000\u0000\u0000\u00ea\u00e9\u0001\u0000\u0000\u0000\u00eb)\u0001"
                    + "\u0000\u0000\u0000\u00ec\u00ed\u0005$\u0000\u0000\u00ed+\u0001\u0000\u0000"
                    + "\u0000\u00ee\u00ef\u0005\b\u0000\u0000\u00ef-\u0001\u0000\u0000\u0000"
                    + "\u00f0\u00f1\u0005\u000b\u0000\u0000\u00f1/\u0001\u0000\u0000\u0000\u00f2"
                    + "\u00f3\u0007\u0001\u0000\u0000\u00f31\u0001\u0000\u0000\u0000\u00f4\u00f5"
                    + "\u0007\u0002\u0000\u0000\u00f53\u0001\u0000\u0000\u0000\u00f6\u00f7\u0007"
                    + "\u0003\u0000\u0000\u00f75\u0001\u0000\u0000\u0000\u00f8\u00f9\u0007\u0004"
                    + "\u0000\u0000\u00f97\u0001\u0000\u0000\u0000\u00fa\u00fb\u0007\u0005\u0000"
                    + "\u0000\u00fb9\u0001\u0000\u0000\u0000\u00fc\u00fd\u0005\u0007\u0000\u0000"
                    + "\u00fd;\u0001\u0000\u0000\u0000\u00fe\u00ff\u0005\u0015\u0000\u0000\u00ff"
                    + "=\u0001\u0000\u0000\u0000\u0100\u0101\u0005\u0016\u0000\u0000\u0101?\u0001"
                    + "\u0000\u0000\u0000\u0102\u0103\u0005\u0001\u0000\u0000\u0103\u0104\u0005"
                    + "\u0015\u0000\u0000\u0104\u0105\u0003\u000e\u0007\u0000\u0105\u0106\u0005"
                    + "#\u0000\u0000\u0106\u0107\u0003*\u0015\u0000\u0107\u0108\u0005\u0016\u0000"
                    + "\u0000\u0108A\u0001\u0000\u0000\u0000\u000bZbg\u009b\u00a3\u00a8\u00ad"
                    + "\u00b3\u00bb\u00e3\u00ea";
    public static final ATN _ATN = new ATNDeserializer().deserialize(_serializedATN.toCharArray());

    static {
        _decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
        for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
            _decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
        }
    }
}
