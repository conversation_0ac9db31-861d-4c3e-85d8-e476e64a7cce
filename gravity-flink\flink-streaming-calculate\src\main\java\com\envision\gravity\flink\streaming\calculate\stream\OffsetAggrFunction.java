package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;


import org.apache.flink.api.common.functions.AggregateFunction;

public class OffsetAggrFunction implements AggregateFunction<OffsetInfo, OffsetInfo, OffsetInfo> {
    @Override
    public OffsetInfo createAccumulator() {
        return new OffsetInfo();
    }

    @Override
    public OffsetInfo add(OffsetInfo value, OffsetInfo accumulator) {
        if (value.getCollector() != null) {
            value.getCollector()
                    .forEach(
                            (topicAndPartition, offset) -> {
                                Long cur = accumulator.getCollector().get(topicAndPartition);
                                if (cur == null || cur < offset) {
                                    accumulator.getCollector().put(topicAndPartition, offset);
                                }
                            });
        } else {
            accumulator.collect(value);
        }
        return accumulator;
    }

    @Override
    public OffsetInfo getResult(OffsetInfo accumulator) {
        return accumulator;
    }

    @Override
    public OffsetInfo merge(OffsetInfo a, OffsetInfo b) {
        return add(a, b);
    }
}
