package com.envision.gravity.common.enums;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Getter
public enum PrefDataType {
    BOOLEAN("BOOLEAN", (byte) 0) {
        @Override
        public Boolean parseFromTsdbToMySQL(Object value) {
            return value instanceof Boolean ? (Boolean) value : Boolean.valueOf(value.toString());
        }
    },
    INTEGER("INTEGER", (byte) 1) {
        @Override
        public Long parseFromTsdbToMySQL(Object value) {
            // Int always cast to long
            return value instanceof Number
                    ? ((Number) value).longValue()
                    : Long.parseLong(value.toString());
        }
    },
    LONG("LONG", (byte) 2) {
        @Override
        public Long parseFromTsdbToMySQL(Object value) {
            return value instanceof Number
                    ? ((Number) value).longValue()
                    : Long.parseLong(value.toString());
        }
    },
    FLOAT("FLOAT", (byte) 3) {
        @Override
        public Float parseFromTsdbToMySQL(Object value) {
            return value instanceof Number
                    ? ((Number) value).floatValue()
                    : Float.parseFloat(value.toString());
        }
    },
    DOUBLE("DOUBLE", (byte) 4) {
        @Override
        public Double parseFromTsdbToMySQL(Object value) {
            return value instanceof Number
                    ? ((Number) value).doubleValue()
                    : Double.parseDouble(value.toString());
        }
    },
    STRING("STRING", (byte) 5) {
        @Override
        public String parseFromTsdbToMySQL(Object value) {
            return value.toString();
        }
    },
    DATE("DATE", (byte) 6) {
        @Override
        public Date parseFromTsdbToMySQL(Object value) {
            return value instanceof Date ? (Date) value : Date.valueOf(value.toString());
        }
    },
    TIME("TIME", (byte) 7) {
        @Override
        public Time parseFromTsdbToMySQL(Object value) {
            return value instanceof Time ? (Time) value : Time.valueOf(value.toString());
        }
    },
    DATETIME("DATETIME", (byte) 8) {
        @Override
        public Timestamp parseFromTsdbToMySQL(Object value) {
            if (value instanceof Timestamp) {
                return (Timestamp) value;
            } else if (value instanceof Long) {
                return new Timestamp((Long) value);
            } else if (value instanceof Instant) {
                long time = ((Instant) value).toEpochMilli();
                return new Timestamp(time);
            } else {
                return Timestamp.valueOf(value.toString());
            }
        }
    },
    DURATION("DURATION", (byte) 9) {
        @Override
        public Double parseFromTsdbToMySQL(Object value) {
            return value instanceof Double ? (Double) value : Double.valueOf(value.toString());
        }
    },
    ENUM("ENUM", (byte) 10) {
        @Override
        public String parseFromTsdbToMySQL(Object value) {
            return value.toString();
        }
    },
    ARRAY("ARRAY", (byte) 11) {
        @Override
        public String parseFromTsdbToMySQL(Object value) {
            return value.toString();
        }
    },
    MAP("MAP", (byte) 12) {
        @Override
        public String parseFromTsdbToMySQL(Object value) {
            return value.toString();
        }
    },
    OBJECT("OBJECT", (byte) 13) {
        @Override
        public String parseFromTsdbToMySQL(Object value) {
            return value.toString();
        }
    };

    private static final PrefDataType[] VALUE_LIST = new PrefDataType[14];

    static {
        for (PrefDataType dataType : PrefDataType.values()) {
            VALUE_LIST[dataType.id] = dataType;
        }
    }

    public static PrefDataType fromId(byte id) {
        try {
            return VALUE_LIST[id];
        } catch (Exception e) {
            return null;
        }
    }

    public static PrefDataType fromName(String name) {
        for (PrefDataType t : PrefDataType.values()) {
            if (Objects.equals(t.name, name)) {
                return t;
            }
        }
        return null;
    }

    private final String name;
    private final byte id;

    PrefDataType(String name, byte id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public byte getId() {
        return id;
    }

    public Object parseFromTsdbToMySQL(Object value) {
        throw new UnsupportedOperationException();
    }
}
