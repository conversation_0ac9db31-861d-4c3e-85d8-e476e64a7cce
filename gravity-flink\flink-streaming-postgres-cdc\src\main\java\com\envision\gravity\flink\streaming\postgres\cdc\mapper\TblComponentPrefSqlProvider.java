package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public class TblComponentPrefSqlProvider {

    public String selectAttrModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList) {
        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String prefIds =
                prefIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_pref tp");
                        INNER_JOIN(
                                schemaName + ".tbl_component_pref tcp on tp.pref_id = tcp.pref_id");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tcp.comp_id = tbmc.comp_id");
                        INNER_JOIN(
                                schemaName + ".tbl_bo_model tbm on tbmc.model_id = tbm.model_id");
                        WHERE(
                                "tp.pref_type = 'ATTRIBUTE' and tcp.comp_id in ( "
                                        + compIds
                                        + " ) and tcp.pref_id in ( "
                                        + prefIds
                                        + " )");
                    }
                };

        return sql.toString();
    }

    public String selectModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList) {
        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String prefIds =
                prefIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_pref tp");
                        INNER_JOIN(
                                schemaName + ".tbl_component_pref tcp on tp.pref_id = tcp.pref_id");
                        INNER_JOIN(schemaName + ".tbl_component tc on tcp.comp_id = tc.comp_id");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tc.comp_id = tbmc.comp_id");
                        INNER_JOIN(
                                schemaName + ".tbl_bo_model tbm on tbmc.model_id = tbm.model_id");
                        WHERE(
                                "tcp.comp_id in ( "
                                        + compIds
                                        + " ) and tcp.pref_id in ( "
                                        + prefIds
                                        + " )");
                    }
                };

        return sql.toString();
    }

    public String selectModelGroupListByCompId(String schemaName, List<String> compIdList) {
        String compIds =
                compIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tbm.model_id = tbmc.model_id");
                        WHERE("tbmc.comp_id in ( " + compIds + " )");
                    }
                };

        return sql.toString();
    }
}
