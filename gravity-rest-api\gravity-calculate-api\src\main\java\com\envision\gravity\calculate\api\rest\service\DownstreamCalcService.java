package com.envision.gravity.calculate.api.rest.service;

import com.envision.gravity.calculate.api.rest.dto.*;
import com.envision.gravity.calculate.api.rest.exception.CalcFailedException;
import com.envision.gravity.calculate.api.rest.exception.PrefTypeNotSupportException;
import com.envision.gravity.calculate.api.rest.exception.RuleInvalidException;
import com.envision.gravity.calculate.api.rest.web.CalcRestConfig;
import com.envision.gravity.common.calculate.AssetInfo;
import com.envision.gravity.common.calculate.CalcCommonUtils;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.common.util.GTCommonUtils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.univers.business.object.calc.dto.ExpressionCalculatorInput;
import com.univers.business.object.calc.dto.ExpressionCalculatorOutput;
import com.univers.business.object.calc.request.CalcExpressionRequest;
import com.univers.business.object.calc.response.CalcExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;

@Component
public class DownstreamCalcService {
    private static final Logger logger = LoggerFactory.getLogger(DownstreamCalcService.class);

    // Request source assetId => SrcModelId, SrcCategoryId, TargetAssetId(SrcSystemId),
    // TargetCategoryId
    private Map<String, Cache<String, DownstreamAssetInfo>> orgId2PresentAssetInfoMap;

    private Map<String, Cache<String, Integer>> orgId2NotPresentAssetInfoMap;

    // Cache<String, Map<String, DownstreamAssetInfo>> srcAssetInfoMap;

    private ModelMetaQueryHandler modelMetaQueryHandler;

    private DownstreamRuleManager downstreamRuleManager;

    public DownstreamCalcService(DownstreamRuleManager downstreamRuleManager) {
        this.orgId2PresentAssetInfoMap = new ConcurrentHashMap<>(1);
        this.orgId2NotPresentAssetInfoMap = new ConcurrentHashMap<>(1);
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
        this.downstreamRuleManager =
                requireNonNull(downstreamRuleManager, "downstreamRuleManager is null");
    }

    private DownstreamAssetSummary getAssetInfo(String orgId, Set<String> assetIds) {
        Cache<String, DownstreamAssetInfo> ouPresentAssetInfoCache =
                orgId2PresentAssetInfoMap.computeIfAbsent(
                        orgId,
                        k ->
                                Caffeine.newBuilder()
                                        .maximumSize(
                                                CalcRestConfig.getCalcDownstreamBoCacheMaxSize())
                                        .expireAfterWrite(
                                                CalcRestConfig
                                                        .getCalcDownstreamBoCacheExpireTimeSeconds(),
                                                TimeUnit.SECONDS)
                                        .build());

        Cache<String, Integer> ouNotPresentAssetInfoCache =
                orgId2NotPresentAssetInfoMap.computeIfAbsent(
                        orgId,
                        k ->
                                Caffeine.newBuilder()
                                        .maximumSize(
                                                CalcRestConfig.getCalcDownstreamBoCacheMaxSize())
                                        .expireAfterWrite(
                                                CalcRestConfig
                                                        .getCalcDownstreamBoCacheExpireTimeSeconds(),
                                                TimeUnit.SECONDS)
                                        .build());

        // Both present and not present cache not found, need to query ignite
        Set<String> cacheMissedAssetIds =
                assetIds.stream()
                        .filter(
                                assetId ->
                                        (ouPresentAssetInfoCache.getIfPresent(assetId) == null)
                                                && (ouNotPresentAssetInfoCache.getIfPresent(assetId)
                                                        == null))
                        .collect(Collectors.toSet());

        if (!GTCommonUtils.emptyCollection(cacheMissedAssetIds)) {
            Map<String, AssetInfo> dbAssetInfoMap =
                    this.modelMetaQueryHandler.getAssetInfos(orgId, cacheMissedAssetIds);

            if (GTCommonUtils.isEmpty(dbAssetInfoMap)) {
                logger.warn(
                        "Cache missed assetIds not exist in ignite, orgId: {}, assetIds: {}",
                        orgId,
                        cacheMissedAssetIds);
                Map<String, Integer> notExistAssetIds = new HashMap<>();
                for (String assetId : cacheMissedAssetIds) {
                    notExistAssetIds.put(assetId, 1);
                }
                ouNotPresentAssetInfoCache.putAll(notExistAssetIds);
            }

            Map<String, DownstreamAssetInfo> presentAssetInfoMap = new HashMap<>();
            Map<String, String> onboardAssetId2SystemId = new HashMap<>();
            for (Map.Entry<String, AssetInfo> assetInfoEntry : dbAssetInfoMap.entrySet()) {
                String assetId = assetInfoEntry.getKey();
                AssetInfo assetInfo = assetInfoEntry.getValue();

                presentAssetInfoMap.put(
                        assetId,
                        DownstreamAssetInfo.builder()
                                .srcAssetId(assetId)
                                .srcModelId(assetInfo.getModelId())
                                .srcCategory(assetInfo.getCategoryId())
                                .targetAssetId(assetInfo.getSystemId())
                                .build());

                onboardAssetId2SystemId.put(assetId, assetInfo.getSystemId());
            }

            // According systemId query onboard asset info
            Map<String, AssetInfo> onboardAssetInfoMap =
                    this.modelMetaQueryHandler.getAssetInfos(
                            orgId, new HashSet<>(onboardAssetId2SystemId.values()));
            for (Map.Entry<String, DownstreamAssetInfo> assetInfoEntry :
                    presentAssetInfoMap.entrySet()) {
                String onboardSystemId = onboardAssetId2SystemId.get(assetInfoEntry.getKey());
                AssetInfo onboardAssetInfo = onboardAssetInfoMap.get(onboardSystemId);
                assetInfoEntry.getValue().setTargetCategory(onboardAssetInfo.getCategoryId());
            }

            ouPresentAssetInfoCache.putAll(presentAssetInfoMap);
            Map<String, Integer> notPresentAssetInfoMap = new HashMap<>();
            for (String assetId : cacheMissedAssetIds) {
                if (!presentAssetInfoMap.containsKey(assetId)) {
                    notPresentAssetInfoMap.put(assetId, 1);
                }
            }
            ouNotPresentAssetInfoCache.putAll(notPresentAssetInfoMap);
        }

        Map<String, DownstreamAssetInfo> presentAssetInfoMap =
                ouPresentAssetInfoCache.getAllPresent(assetIds);
        Set<String> notPresentAssetIds =
                ouNotPresentAssetInfoCache.getAllPresent(assetIds).keySet();
        return DownstreamAssetSummary.builder()
                .assetIds(assetIds)
                .presentAssetInfo(presentAssetInfoMap)
                .notPresentAssetIds(notPresentAssetIds)
                .build();
    }

    private void calcPreCheck(String orgId, List<DownstreamRequest> downstreamRequest) {
        if (StringUtils.isEmpty(orgId)) {
            throw new IllegalArgumentException("orgId can't be blank");
        }

        if (GTCommonUtils.emptyCollection(downstreamRequest)) {
            throw new IllegalArgumentException("downstreamRequest can't be empty");
        }

        downstreamRequest.forEach(
                request -> {
                    if (StringUtils.isEmpty(request.getAssetId())) {
                        throw new IllegalArgumentException("assetId can't be blank");
                    }
                    if (StringUtils.isEmpty(request.getPointId())) {
                        throw new IllegalArgumentException("pointId can't be blank");
                    }
                    if (request.getValue() == null) {
                        throw new IllegalArgumentException("value can't be null");
                    }
                });
    }

    public List<DownstreamResponse> calculate(
            String orgId, List<DownstreamRequest> downstreamRequest) {
        calcPreCheck(orgId, downstreamRequest);

        if (downstreamRequest.size() > CalcRestConfig.getCalcDownstreamBatchSizeLimit()) {
            throw new IllegalArgumentException(
                    "DownstreamRequest size exceed limit: "
                            + CalcRestConfig.getCalcDownstreamBatchSizeLimit());
        }

        Set<String> assetIds =
                downstreamRequest.stream()
                        .map(DownstreamRequest::getAssetId)
                        .collect(Collectors.toSet());
        DownstreamAssetSummary assetSummary = getAssetInfo(orgId, assetIds);

        List<DownstreamResponse> result = new ArrayList<>(downstreamRequest.size());
        for (DownstreamRequest request : downstreamRequest) {
            String assetId = request.getAssetId();
            DownstreamAssetInfo assetInfo = assetSummary.getPresentAssetInfo().get(assetId);
            if (assetInfo == null) {
                logger.warn("orgId: {}, assetId: {} not present", orgId, assetId);
                result.add(buildEchoResponse(request));
                continue;
            }

            Optional<DownstreamRule> ruleInfo =
                    downstreamRuleManager.getDownstreamRule(orgId, request.getPointId(), assetInfo);
            if (!ruleInfo.isPresent()) {
                logger.warn(
                        "orgId: {}, srcPrefName: {}, assetInfo: {} downstream rule not found",
                        orgId,
                        assetId,
                        assetInfo);
                result.add(buildEchoResponse(request));
                continue;
            }

            DownstreamRule rule = ruleInfo.get();
            if (!rule.isValidExpr()) {
                logger.warn(
                        "orgId: {}, srcPrefName: {}, expression: {} invalid",
                        orgId,
                        assetId,
                        rule.getExpression());
                throw new RuleInvalidException();
            }

            if (!CalcCommonUtils.isMeasurePoint(rule.getPrefType())) {
                logger.warn(
                        "orgId: {}, srcPrefName: {}, prefType: {} not support",
                        orgId,
                        assetId,
                        rule.getPrefType());
                throw new PrefTypeNotSupportException();
            }

            CalcExpressionRequest calcRequest = new CalcExpressionRequest();
            List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);
            Map<String, Object> calcData = new HashMap<>(1);
            calcData.put(
                    assetInfo.getSrcModelId() + "." + request.getPointId(), request.getValue());
            calcInputs.add(
                    ExpressionCalculatorInput.builder()
                            .expression(rule.getExpression())
                            .data(calcData)
                            .build());
            calcRequest.setCalcInputs(calcInputs);

            List<CalcExpressionResponse> calcResult;
            try {
                calcResult = ExpressionUtil.calcExpression(calcRequest);
            } catch (Exception e) {
                logger.error(
                        "Calculate failed, orgId: {}, param: {}, reason {}",
                        orgId,
                        calcRequest,
                        e.getMessage());
                throw new CalcFailedException(e);
            }

            ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();

            result.add(
                    DownstreamResponse.builder()
                            .assetId(assetInfo.getTargetAssetId())
                            .pointId(rule.getTargetPrefName())
                            .srcAssetId(request.getAssetId())
                            .srcPointId(request.getPointId())
                            .value(calcOutput.getValue())
                            .build());
        }

        return result;
    }

    private DownstreamResponse buildEchoResponse(DownstreamRequest request) {
        return DownstreamResponse.builder()
                .assetId(request.getAssetId())
                .pointId(request.getPointId())
                .srcAssetId(request.getAssetId())
                .srcPointId(request.getPointId())
                .value(request.getValue())
                .build();
    }
}
