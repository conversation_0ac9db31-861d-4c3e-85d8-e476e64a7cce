package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TypeInfo(PojoFactory.LegacyMsgListType.class)
public class LegacyMsgList {
    private List<LegacyMsg> legacyMsgList;
}
