package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public class ObjectDetailOriginSqlProvider {
    public String batchDeleteByPrimaryKey(
            String schemaName, List<String> assetIdList, List<String> modelIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));
        String modelIds =
                modelIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.DELETE_FROM(schemaName + ".object_detail_origin");
        sql.WHERE("asset_id in ( " + assetIds + " ) and model_id in ( " + modelIds + " )");

        return sql.toString();
    }

    public String batchDeleteByAssetIdList(String schemaName, List<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.DELETE_FROM(schemaName + ".object_detail_origin");
        sql.WHERE("asset_id in ( " + assetIds + " ) ");

        return sql.toString();
    }
}
