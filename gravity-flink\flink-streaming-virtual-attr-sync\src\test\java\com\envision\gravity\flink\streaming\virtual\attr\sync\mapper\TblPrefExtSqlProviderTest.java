package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @description
 */
class TblPrefExtSqlProviderTest {

    @Test
    void queryVirtualAttrInfo() {
        TblPrefExtSqlProvider tblPrefExtSqlProvider = new TblPrefExtSqlProvider();
        System.out.println(tblPrefExtSqlProvider.queryVirtualAttrInfo("o17231990549791976"));
    }
}
