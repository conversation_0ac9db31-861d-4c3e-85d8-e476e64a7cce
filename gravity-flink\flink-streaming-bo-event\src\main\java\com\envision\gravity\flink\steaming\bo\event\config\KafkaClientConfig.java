package com.envision.gravity.flink.steaming.bo.event.config;

import java.util.Properties;


import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Slf4j
public class KafkaClientConfig {
    // kafka configurations
    private static final String KAFKA_BOOTSTRAP_SERVERS = LionConfig.getKafkaServers();
    private static volatile KafkaProducer<String, String> KAFKA_PRODUCER;

    private KafkaClientConfig() {}

    private static Properties initProducerProperties() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        props.put(
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        return props;
    }

    public static KafkaProducer<String, String> getKafkaProducerInstance() {
        if (KAFKA_PRODUCER == null) {
            synchronized (KafkaClientConfig.class) {
                if (KAFKA_PRODUCER == null) {
                    log.info(
                            "start init kafka producer with bootstrap servers: {}",
                            KAFKA_BOOTSTRAP_SERVERS);
                    Properties props = initProducerProperties();
                    KAFKA_PRODUCER = new KafkaProducer<>(props);
                    log.info("init kafka producer success.");
                }
            }
        }
        return KAFKA_PRODUCER;
    }

    public static void closeKafkaProducer() {
        if (KAFKA_PRODUCER != null) {
            synchronized (KafkaClientConfig.class) {
                try {
                    if (KAFKA_PRODUCER != null) {
                        KAFKA_PRODUCER.flush();
                        KAFKA_PRODUCER.close();
                        log.info("close kafka producer success.");
                    }
                } catch (Exception e) {
                    log.error("close kafka producer error.", e);
                } finally {
                    KAFKA_PRODUCER = null;
                }
            }
        }
    }
}
