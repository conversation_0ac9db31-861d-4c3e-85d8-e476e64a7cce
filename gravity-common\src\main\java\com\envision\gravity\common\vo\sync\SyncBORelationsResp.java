package com.envision.gravity.common.vo.sync;

import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncBORelationsResp {
    private String syncId;
    private String orgId;
    private Set<String> successfulGraphIds;
    private Set<String> failedGraphIds;
}
