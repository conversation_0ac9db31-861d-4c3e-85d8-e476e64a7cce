package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroupUpdateParam;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblBOModelInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public class ModelAggregator implements Aggregator {

    public ModelAggregator(SqlSessionFactory sqlSessionFactory) {}

    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // 1.refresh object detail
        List<ModelGroup> insertRefresh =
                records.stream()
                        .map(
                                record -> {
                                    TblBOModelInfo after = (TblBOModelInfo) record.getAfter();
                                    if (after.getGroupId() == null) {
                                        return null;
                                    }

                                    return ModelGroup.builder()
                                            .modelId(after.getModelId())
                                            .groupId(after.getGroupId())
                                            .build();
                                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        if (insertRefresh.isEmpty()) {
            return null;
        }

        return AggregatedResults.builder()
                .refreshObjectReq(
                        RefreshObjectReq.builder().modelInsertRefresh(insertRefresh).build())
                .build();
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        List<String> modelIdList =
                records.stream()
                        .map(record -> ((TblBOModelInfo) record.getBefore()).getModelId())
                        .distinct()
                        .collect(Collectors.toList());

        // 1. delete model vertex
        NebulaGraphReq nebulaGraphReq =
                NebulaGraphReq.builder().deletedVertexVidList(modelIdList).build();
        return AggregatedResults.builder().nebulaGraphReq(nebulaGraphReq).build();
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // 1.refresh object detail
        List<ModelGroupUpdateParam> groupChangedRefresh =
                records.stream()
                        .map(
                                record -> {
                                    TblBOModelInfo before = (TblBOModelInfo) record.getBefore();
                                    TblBOModelInfo after = (TblBOModelInfo) record.getAfter();
                                    if (Objects.equals(before.getGroupId(), after.getGroupId())) {
                                        return null;
                                    }

                                    return ModelGroupUpdateParam.builder()
                                            .modelId(after.getModelId())
                                            .newGroupId(after.getGroupId())
                                            .oldGroupId(before.getGroupId())
                                            .build();
                                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        if (groupChangedRefresh.isEmpty()) {
            return null;
        }

        return AggregatedResults.builder()
                .refreshObjectReq(
                        RefreshObjectReq.builder()
                                .modelGroupChangedRefresh(groupChangedRefresh)
                                .build())
                .build();
    }
}
