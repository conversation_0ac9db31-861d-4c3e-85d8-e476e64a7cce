package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;

import java.util.stream.Collectors;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

public class OffsetSink extends RichSinkFunction<OffsetInfo> {

    private static final String offsetDatabase = "GRAVITY";

    public static final String INSERT_OFFSET_PATTERN =
            "MERGE INTO TBL_OFFSET_V2 (CONSUMER, TOPIC, PARTITION_ID, OFFSET_ID) VALUES %s ;";

    private final String consumer;
    private transient Counter recordOffsetFailedTimes;

    public OffsetSink(String consumer) {
        this.consumer = consumer;
    }

    @Override
    public void open(Configuration parameters) {
        this.recordOffsetFailedTimes =
                getRuntimeContext()
                        .getMetricGroup()
                        .addGroup("offsetSink", consumer)
                        .counter("record_offset_failed_times");
    }

    @Override
    public void invoke(OffsetInfo value, Context context) throws Exception {
        recordOffset(value);
    }

    @Override
    public void finish() throws Exception {
        super.finish();
    }

    private void recordOffset(OffsetInfo offsetInfo) {
        String values =
                offsetInfo.getCollector().entrySet().stream()
                        .map(
                                entry -> {
                                    return "('"
                                            + consumer
                                            + "', '"
                                            + entry.getKey().f0
                                            + "', "
                                            + entry.getKey().f1
                                            + ", "
                                            + entry.getValue()
                                            + ")";
                                })
                        .collect(Collectors.joining(","));

        try {
            String sql = String.format(INSERT_OFFSET_PATTERN, values);
            IgniteUtil.query(offsetDatabase, sql);
            recordOffsetFailedTimes.dec(recordOffsetFailedTimes.getCount());
        } catch (Throwable t) {
            recordOffsetFailedTimes.inc();
        }
    }
}
