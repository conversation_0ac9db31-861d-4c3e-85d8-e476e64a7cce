package com.envision.gravity.flink.streaming.calculate.aspect.source.split;

import java.io.Serializable;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ✅ Job分片实体
 *
 * <p>用于StreamingCalcJobTaskSource的分片管理 每个分片代表一个Job的处理单元
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobSplit implements Serializable {

    private static final long serialVersionUID = 1L;

    /** Job ID */
    private String jobId;

    /** 获取分片ID（用于标识） */
    public String splitId() {
        return "job-split-" + jobId;
    }

    @Override
    public String toString() {
        return String.format("CalcJobSplit{jobId='%s'}", jobId);
    }
}
