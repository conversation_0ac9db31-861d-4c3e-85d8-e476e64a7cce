package com.envision.gravity.flink.streaming.calculate.recalc.events;


import org.apache.flink.api.connector.source.SourceEvent;

/**
 * 任务完成事件
 *
 * <AUTHOR>
 */
public class TaskCompletedEvent implements SourceEvent {

    private static final long serialVersionUID = 1L;

    private final String taskId;
    private final long completedTime;

    public TaskCompletedEvent(String taskId) {
        this.taskId = taskId;
        this.completedTime = System.currentTimeMillis();
    }

    public String getTaskId() {
        return taskId;
    }

    public long getCompletedTime() {
        return completedTime;
    }

    @Override
    public String toString() {
        return "TaskCompletedEvent{"
                + "taskId='"
                + taskId
                + '\''
                + ", completedTime="
                + completedTime
                + '}';
    }
}
