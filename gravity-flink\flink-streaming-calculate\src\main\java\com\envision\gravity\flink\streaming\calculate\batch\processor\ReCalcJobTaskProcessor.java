package com.envision.gravity.flink.streaming.calculate.batch.processor;

import com.envision.gravity.common.enums.PrefType;
import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.calc.TimeRange;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.message.CalcResultMsg;
import com.envision.gravity.flink.streaming.calculate.dto.query.TSQueryEntity;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyPayload;
import com.envision.gravity.flink.streaming.calculate.utils.SqlGatewayQueryService;

import java.nio.charset.StandardCharsets;
import java.util.*;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReCalc批处理任务处理器
 *
 * <p>功能： 1. 处理CalcJobTask，执行历史数据重跑 2. 使用时间范围查询历史数据 3. 直接写入Kafka 4. 支持Job状态检查和任务完成通知
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class ReCalcJobTaskProcessor extends ProcessFunction<CalcJobTask, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobTaskProcessor.class);

    private final String jobId;

    // ✅ 批处理专用：直接写入Kafka的Producer
    private transient KafkaProducer<String, LegacyMsgWithMultiAssets> kafkaProducer;
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient TblCalcJobInfoMapper jobInfoMapper;
    private transient SqlGatewayQueryService sqlGatewayQueryService;

    public ReCalcJobTaskProcessor(String jobId) {
        this.jobId = jobId;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化Kafka Producer
        this.kafkaProducer = createKafkaProducer();

        // 初始化其他组件
        this.taskCompletionNotifier = TaskCompletionNotifier.getInstance(jobId);
        // jobInfoMapper 需要通过SqlSession获取，这里先设为null，在使用时再获取
        this.jobInfoMapper = null;
        this.sqlGatewayQueryService = SqlGatewayQueryService.getInstance();

        logger.info("ReCalcJobTaskProcessor opened for job: {}", jobId);
    }

    @Override
    public void processElement(CalcJobTask calcJobTask, Context ctx, Collector<Void> out)
            throws Exception {

        String taskId = calcJobTask.getTaskId();
        String currentJobId = calcJobTask.getJobId();

        logger.info(
                "Processing historical data reprocessing task: {} for job: {}",
                taskId,
                currentJobId);

        try {
            // ✅ 关键检查1：Job状态检查（CANCELLED检查）
            if (!checkJobStatusNotCancelledWithEnum(currentJobId)) {
                logger.warn("Job {} is CANCELLED, terminating ReCalcBatchJob", currentJobId);
                // ✅ 批处理任务终止退出
                throw new JobCancelledException(
                        "Job " + currentJobId + " has been cancelled, terminating batch job");
            }

            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, currentJobId);
                return;
            }

            // 获取作业信息
            TblCalcJobInfo jobInfo = jobInfoMapper.findByJobId(currentJobId);
            if (jobInfo == null) {
                logger.error("Job info not found for job: {}", currentJobId);
                return;
            }

            // ✅ 批处理核心：历史数据重跑处理
            executeHistoricalDataReprocessing(jobInfo, calcJobTask);

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(taskId);

            logger.info(
                    "Historical reprocessing task {} completed for job {}", taskId, currentJobId);

        } catch (JobCancelledException e) {
            logger.error(
                    "Job {} cancelled, stopping ReCalcBatchJob: {}", currentJobId, e.getMessage());
            throw e; // 重新抛出，让作业终止
        } catch (Exception e) {
            logger.error(
                    "Historical reprocessing task {} failed for job {}: {}",
                    taskId,
                    currentJobId,
                    e.getMessage(),
                    e);
            taskCompletionNotifier.markTaskFailed(taskId, e.getMessage());
        }
    }

    /** ✅ 使用枚举进行状态检查的推荐方式 */
    private boolean checkJobStatusNotCancelledWithEnum(String jobId) throws Exception {
        try (org.apache.ibatis.session.SqlSession session =
                com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig
                        .getSqlSessionFactory()
                        .openSession()) {

            TblCalcJobInfoMapper mapper = session.getMapper(TblCalcJobInfoMapper.class);
            TblCalcJobInfo currentJobInfo = mapper.findByJobId(jobId);

            if (currentJobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return false;
            }

            int jobStatus = currentJobInfo.getStatus();
            Optional<ReCalcJobStatusEnum> statusEnum = ReCalcJobStatusEnum.getByCode(jobStatus);

            if (statusEnum.isPresent()) {
                ReCalcJobStatusEnum status = statusEnum.get();
                logger.debug(
                        "Job {} current status: {} ({})", jobId, status.name(), status.getCode());

                // ✅ 使用枚举进行状态检查
                if (status == ReCalcJobStatusEnum.CANCELLED) {
                    logger.warn("Job {} status is CANCELLED", jobId);
                    return false;
                }

                return true;
            } else {
                logger.error("Unknown job status code: {} for job: {}", jobStatus, jobId);
                return false;
            }
        }
    }

    /** ✅ 批处理核心方法：历史数据重跑处理 */
    private void executeHistoricalDataReprocessing(TblCalcJobInfo jobInfo, CalcJobTask calcJobTask)
            throws Exception {

        String orgId = jobInfo.getSrcOrgId();

        // ✅ 关键差异1：需要时间拆分处理
        List<TimeRange> timeRanges =
                splitTimeRange(calcJobTask.getStartTime(), calcJobTask.getEndTime());

        logger.info(
                "Historical reprocessing task {} split into {} time ranges",
                calcJobTask.getTaskId(),
                timeRanges.size());

        // ✅ 按时间段处理并立即写入Kafka
        for (TimeRange timeRange : timeRanges) {
            CalcResultMsg calcResults =
                    queryHistoricalDataAndCalculate(orgId, jobInfo, calcJobTask, timeRange);

            // ✅ 关键差异2：立即写入Kafka
            writeToKafkaImmediately(orgId, jobInfo, calcJobTask, calcResults);
        }
    }

    /** ✅ 时间范围拆分 */
    private List<TimeRange> splitTimeRange(long startTime, long endTime) {
        List<TimeRange> timeRanges = new ArrayList<>();
        long splitSeconds = CalcLionConfig.getReCalcJobTaskTimeSplitSeconds();
        long splitMillis = splitSeconds * 1000;

        long currentStart = startTime;
        while (currentStart < endTime) {
            long currentEnd = Math.min(currentStart + splitMillis, endTime);
            timeRanges.add(new TimeRange(currentStart, currentEnd));
            currentStart = currentEnd;
        }

        return timeRanges;
    }

    /** ✅ 关键差异：查询历史数据并计算 */
    private CalcResultMsg queryHistoricalDataAndCalculate(
            String orgId, TblCalcJobInfo jobInfo, CalcJobTask calcJobTask, TimeRange timeRange)
            throws Exception {

        // 1. 构建时间范围查询实体
        Map<String, TSQueryEntity> tsQueryEntities =
                buildTimeRangeQueryEntities(jobInfo, calcJobTask, timeRange);

        // 2. ✅ 关键差异：使用时间序列范围查询方法
        Map<String, List<LegacyPayload>> allTSValues =
                queryAllHistoricalRangeValues(tsQueryEntities);

        // 3. 构建目标资产数据（基于时间范围）
        Map<String, List<LegacyPayload>> targetAssetValues =
                buildTargetAssetValuesFromTimeRange(calcJobTask, allTSValues);

        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
                execTargetPropExprCalc(
                        orgId, jobInfo, calcJobTask, targetAssetValues, modelPathMap);

        return CalcResultMsg.builder().targetModel2MsgMap(targetModel2MsgMap).build();
    }

    /** ✅ 关键差异：查询历史范围值（使用 queryTSValues 方法） */
    private Map<String, List<LegacyPayload>> queryAllHistoricalRangeValues(
            Map<String, TSQueryEntity> tsQueryEntities) throws Exception {

        Map<String, List<LegacyPayload>> allTSValues = new HashMap<>();

        for (Map.Entry<String, TSQueryEntity> entry : tsQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            TSQueryEntity queryEntity = entry.getValue();

            // ✅ 使用时间序列范围查询方法
            Map<String, List<LegacyPayload>> modelTSValues =
                    sqlGatewayQueryService.queryTSValues(
                            queryEntity.getOrgId(), modelId, queryEntity);

            allTSValues.putAll(modelTSValues);
        }

        return allTSValues;
    }

    /** ✅ 关键差异：立即写入Kafka */
    private void writeToKafkaImmediately(
            String orgId,
            TblCalcJobInfo jobInfo,
            CalcJobTask calcJobTask,
            CalcResultMsg calcResults)
            throws Exception {

        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
                calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // ✅ 前置校验1：检查是否应该写入Kafka
            if (!shouldWriteToKafka(jobInfo, message)) {
                continue;
            }

            // ✅ 立即写入Kafka
            ProducerRecord<String, LegacyMsgWithMultiAssets> record =
                    new ProducerRecord<>(buildTopicName(orgId), message.getModelId(), message);

            // ✅ 前置检查2：检查是否为直白映射，添加 direct_mapping header
            if (isDirectMapping(jobInfo)) {
                record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
                logger.debug("Added direct_mapping header for model: {}", message.getModelId());
            }

            kafkaProducer.send(record);

            logger.debug(
                    "Immediately sent historical data to Kafka for model: {}",
                    message.getModelId());
        }
    }

    /** ✅ 前置检查：判断是否应该写入Kafka */
    private boolean shouldWriteToKafka(TblCalcJobInfo jobInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不写入Kafka）
        if (jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip writing attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /** ✅ 前置检查：判断是否为直白映射 */
    private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
        try {
            boolean directMapping = jobInfo.getRuleInfo().getTargetPropertyMeta().isDirectMapping();

            logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
            return directMapping;

        } catch (Exception e) {
            logger.warn(
                    "Failed to check direct mapping for job {}: {}",
                    jobInfo.getJobId(),
                    e.getMessage());
            return false;
        }
    }

    /** 构建Topic名称 */
    private String buildTopicName(String orgId) {
        return CalcLionConfig.getReCalcKafkaSinkTopicPattern() + orgId;
    }

    private KafkaProducer<String, LegacyMsgWithMultiAssets> createKafkaProducer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", CalcLionConfig.getReCalcKafkaBootstrapServers());
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put(
                "value.serializer",
                "com.envision.gravity.flink.streaming.calculate.serializer.LegacyMsgSerializer");

        return new KafkaProducer<>(props);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (kafkaProducer != null) {
            kafkaProducer.close();
        }
        logger.info("ReCalcJobTaskProcessor closed for job: {}", jobId);
    }

    // TODO: 实现其他辅助方法
    private Map<String, TSQueryEntity> buildTimeRangeQueryEntities(
            TblCalcJobInfo jobInfo, CalcJobTask calcJobTask, TimeRange timeRange) {
        // 实现时间范围查询实体构建逻辑
        return new HashMap<>();
    }

    private Map<String, List<LegacyPayload>> buildTargetAssetValuesFromTimeRange(
            CalcJobTask calcJobTask, Map<String, List<LegacyPayload>> allTSValues) {
        // 实现目标资产数据构建逻辑
        return new HashMap<>();
    }

    private Map<String, String> getModelPathMap(String orgId, CalcJobTask calcJobTask) {
        // 实现模型路径映射逻辑
        return new HashMap<>();
    }

    private Map<String, LegacyMsgWithMultiAssets> execTargetPropExprCalc(
            String orgId,
            TblCalcJobInfo jobInfo,
            CalcJobTask calcJobTask,
            Map<String, List<LegacyPayload>> targetAssetValues,
            Map<String, String> modelPathMap) {
        // 实现表达式计算逻辑
        return new HashMap<>();
    }
}

/** ✅ 自定义异常：Job取消异常 */
class JobCancelledException extends Exception {
    public JobCancelledException(String message) {
        super(message);
    }

    public JobCancelledException(String message, Throwable cause) {
        super(message, cause);
    }
}
