package com.envision.gravity.common.vo.search;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/28 11:24 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchPaginationResp {

    private int pageNo;

    private int pageSize;

    private List<Sorter> sorters;

    private long totalSize;
}
