package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ObjAttrValue;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @description
 */
public interface TblObjAttrMapper {
    @SelectProvider(type = TblObjAttrSqlProvider.class, method = "queryAttrValue")
    @Results({
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "attr_value", property = "attrValue", jdbcType = JdbcType.VARCHAR)
    })
    List<ObjAttrValue> queryAttrValue(String schemeName, String fieldId, List<String> systemIdList);
}
