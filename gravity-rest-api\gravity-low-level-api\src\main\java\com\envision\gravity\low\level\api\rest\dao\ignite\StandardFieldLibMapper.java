package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.StandardFieldLib;

import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/** <AUTHOR> */
public interface StandardFieldLibMapper {

    @SelectProvider(type = StandardFieldLibSqlProvider.class, method = "queryByRawFieldIds")
    @Results({
        @Result(column = "raw_field_id", property = "rawFieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<StandardFieldLib> queryByRawFieldIds(List<String> rawFieldIds);
}
