package com.envision.gravity.flink.streaming.bo.view.operator.config;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class LionConfig {

    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getIgniteHostname() {
        return getStringValue("gravity-common.ignite.hostname", null);
    }

    public static int getIgniteClientPort() {
        return getIntValue("gravity-common.ignite.client.port", 10800);
    }

    public static String getIgniteUsername() {
        return getStringValue("gravity-common.ignite.username", "ignite");
    }

    public static String getIgnitePassword() {
        return getStringValue("gravity-common.ignite.password", "ignite");
    }

    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static int getPgMaxPoolSize() {
        return getIntValue("gravity-flink.bo-view-operator.pg.datasource.max.pool.size", 4);
    }

    public static String getPgJdbcUrl() {
        return getStringValue("gravity-common.postgresql.jdbc-url", null);
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.pg.cdc.database", "gravity");
    }

    public static String getPgSchemaList() {
        return getStringValue("gravity-flink.bo-view-operator.pg.cdc.schema.list", "^o.*");
    }

    public static String getPgTableList() {
        return getStringValue(
                "gravity-flink.bo-view-operator.pg.cdc.table.list", "^o.*\\.model_detail_origin$");
    }

    public static String getSlotName() {
        return getStringValue(
                "gravity-flink.bo-view-operator.pg.cdc.slot.name", "dim_bo_view_operator");
    }

    public static int getTimeWindowIntervalInMs() {
        return getIntValue("gravity-flink.bo-view-operator.time.window.interval.in.ms", 60000);
    }

    public static int getTableTTLInSeconds() {
        return getIntValue("gravity-flink.bo-view-operator.table.ttl.in.seconds", 900);
    }

    public static String getMaxBatchSize() {
        return getStringValue("gravity-flink.bo-view-operator.source.max.batch.size", "5");
    }

    public static String getMaxQueueSize() {
        return getStringValue("gravity-flink.bo-view-operator.source.max.queue.size", "10");
    }
}
