package com.envision.gravity.ignite.tsdb.loader;

import java.util.Map;
import java.util.Set;


import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

/** <AUTHOR> 2024/3/19 */
@Getter
public class TSDBLoaderRequest {

    private String requestId;

    private String orgId;

    private Map<String, String> pointWithAggrs;

    // Query by asset localtime
    private String startTime;

    private String endTime;

    // Query by UTC timezone
    private Long startTs;

    private Long endTs;

    private Set<String> assetIds;

    private String timeGroup; // cds time group

    private Integer slimit;

    private Boolean autoInterpolate = false;

    // -----------------------------------------------------------------------------------------
    // prefName => rawFieldIds
    private Map<String, Set<String>> fieldMapping;

    // prefName => dataType
    private Map<String, String> prefDataTypes;

    // assetId => <systemId, timeZone>
    private Map<String, Pair<String, String>> idTimezoneMapping;

    public TSDBLoaderRequest() {}

    // ------------------------------------------------------------------------------------------
    public TSDBLoaderRequest requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public TSDBLoaderRequest orgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    public TSDBLoaderRequest pointWithAggrs(Map<String, String> pointWithAggrs) {
        this.pointWithAggrs = pointWithAggrs;
        return this;
    }

    public TSDBLoaderRequest startTime(String startTime) {
        this.startTime = startTime;
        return this;
    }

    public TSDBLoaderRequest endTime(String endTime) {
        this.endTime = endTime;
        return this;
    }

    public TSDBLoaderRequest startTs(Long startTs) {
        this.startTs = startTs;
        return this;
    }

    public TSDBLoaderRequest endTs(Long endTs) {
        this.endTs = endTs;
        return this;
    }

    public TSDBLoaderRequest assetIds(Set<String> assetIds) {
        this.assetIds = assetIds;
        return this;
    }

    public TSDBLoaderRequest timeGroup(String timeGroup) {
        this.timeGroup = timeGroup;
        return this;
    }

    public TSDBLoaderRequest slimit(Integer slimit) {
        this.slimit = slimit;
        return this;
    }

    public TSDBLoaderRequest autoInterpolate(Boolean autoInterpolate) {
        this.autoInterpolate = autoInterpolate;
        return this;
    }

    public TSDBLoaderRequest fieldMapping(Map<String, Set<String>> fieldMapping) {
        this.fieldMapping = fieldMapping;
        return this;
    }

    public TSDBLoaderRequest prefDataTypes(Map<String, String> prefDataTypes) {
        this.prefDataTypes = prefDataTypes;
        return this;
    }

    public TSDBLoaderRequest idTimezoneMapping(
            Map<String, Pair<String, String>> idTimezoneMapping) {
        this.idTimezoneMapping = idTimezoneMapping;
        return this;
    }

    @Override
    public String toString() {
        return "TSDBLoaderRequest{"
                + "requestId='"
                + requestId
                + '\''
                + ", orgId='"
                + orgId
                + '\''
                + ", pointWithAggrs="
                + pointWithAggrs
                + ", startTime='"
                + startTime
                + '\''
                + ", endTime='"
                + endTime
                + '\''
                + ", startTs="
                + startTs
                + ", endTs="
                + endTs
                + ", assetIds="
                + assetIds
                + ", timeGroup='"
                + timeGroup
                + '\''
                + ", slimit="
                + slimit
                + ", autoInterpolate="
                + autoInterpolate
                + '}';
    }
}
