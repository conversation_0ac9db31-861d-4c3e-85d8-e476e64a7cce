package com.envision.gravity.common.definition.bo;

import com.envision.gravity.common.definition.DefinitionEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BODefinition implements DefinitionEntity {
    private static final long serialVersionUID = 6773972553340984380L;
    private BusinessObjectDef businessObject;
    private DataObjectDef dataObject;
    private BusinessObjectRelationDef businessObjectRelation;
}
