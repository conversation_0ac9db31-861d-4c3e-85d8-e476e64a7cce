package com.envision.gravity.common.vo.obj;

import com.envision.gravity.common.bo.ObjectField;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PatchObjFieldReq {

    @NotBlank(message = "systemId can not be blank")
    private String systemId;

    @Valid private List<ObjectField> addOrUpdateFields;
    private List<String> deletedFields;
}
