package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.cache.write.Pref;
import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TypeInfo(PojoFactory.LegacyPointType.class)
public class LegacyPoint {
    private Pref pref;
    private String stringValue;
    private Double doubleValue;
    private Long longValue;
    private Boolean boolValue;
    private Long quality;

    public Object getValue() {
        if (stringValue != null) {
            return stringValue;
        }
        if (doubleValue != null) {
            return doubleValue;
        }
        if (longValue != null) {
            return longValue;
        }
        return boolValue;
    }

    public LegacyPoint(Pref otherPerf, Object value, Long quality) {
        this.pref = otherPerf;
        this.quality = quality;
        if (value instanceof String) {
            this.stringValue = (String) value;
        } else if (value instanceof Double) {
            this.doubleValue = (Double) value;
        } else if (value instanceof Long) {
            this.longValue = (Long) value;
        } else if (value instanceof Boolean) {
            this.boolValue = (Boolean) value;
        } else {
            throw new IllegalArgumentException("value type error: " + value.getClass());
        }
    }
}
