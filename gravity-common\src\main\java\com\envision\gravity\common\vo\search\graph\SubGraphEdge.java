package com.envision.gravity.common.vo.search.graph;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/** @Author: qi.jiang2 @Date: 2024/04/08 14:57 @Description: */
@Data
@Builder
public class SubGraphEdge {

    private String subGraphId;

    private String fromVid;

    private String toVid;

    private String edgeType;

    private int order;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
