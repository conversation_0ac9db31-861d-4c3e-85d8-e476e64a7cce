package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2025/04/29
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblComponentPrefFieldMappingCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "comp_id", String.class, "comp_id"),
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "field_id", String.class, "field_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "comp_id", String.class, "comp_id"),
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "field_id", String.class, "field_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "raw_field_id", String.class, "raw_field_id"),
                        new JdbcTypeField(
                                Types.INTEGER, "field_index", Integer.class, "field_index"),
                        new JdbcTypeField(Types.BOOLEAN, "horizontal", Boolean.class, "horizontal"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("comp_id");
        QUERY_ENTITY_KEY_FIELDS.add("pref_id");
        QUERY_ENTITY_KEY_FIELDS.add("field_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("comp_id");
        NOT_NULL_FIELDS.add("pref_id");
        NOT_NULL_FIELDS.add("field_id");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("comp_id"),
                        new QueryIndex("pref_id"),
                        new QueryIndex("raw_field_id"),
                        new QueryIndex("field_index"),
                        new QueryIndex("field_id"));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("comp_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("pref_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("field_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("raw_field_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("field_index", "java.lang.Integer");
        QUERY_ENTITY_FIELDS.put("horizontal", "java.lang.Boolean");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
