<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-flink</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>flink-streaming-obj-warm</artifactId>
    <packaging>jar</packaging>
    <version>${release.version}</version>
    <name>flink-streaming-obj-warm</name>
    <properties>
        <provide_scope>provided</provide_scope>
        <!-- <provide_scope>provided</provide_scope> -->
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ververica</groupId>
            <artifactId>flink-sql-connector-postgres-cdc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ignite-core</artifactId>
                    <groupId>org.apache.ignite</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>perf4j</artifactId>
                    <groupId>org.perf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>ignite-core</artifactId>
            <groupId>org.apache.ignite</groupId>
        </dependency>

        <dependency>
            <groupId>com.envision.arch.lion</groupId>
            <artifactId>lion-client</artifactId>
            <scope>${provide_scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <scope>${provide_scope}</scope>
        </dependency>
        <!-- pg -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>${provide_scope}</scope>
        </dependency>



        <!-- cache -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <!-- flink -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.version}</artifactId>
            <scope>${provide_scope}</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients_${scala.version}</artifactId>
            <scope>${provide_scope}</scope>
        </dependency>
    </dependencies>

</project>