<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <bean class="org.apache.ignite.configuration.IgniteConfiguration" id="ignite.cfg">
<!--        <property name="clientMode" value="true"/>-->
<!--        &lt;!&ndash; Enable peer class loading. &ndash;&gt;-->
<!--        <property name="peerClassLoadingEnabled" value="true"/>-->
<!--        &lt;!&ndash; Set deployment mode. &ndash;&gt;-->
<!--        <property name="deploymentMode" value="CONTINUOUS"/>-->
<!--        <property name="authenticationEnabled" value="true"/>-->
<!--        <property name="networkTimeout" value="120000"/>-->

        <!--   SQL Engine   -->
        <property name="sqlConfiguration">
            <bean class="org.apache.ignite.configuration.SqlConfiguration">
                <property name="sqlSchemas">
                    <list>
                        <value>o18729267101629999</value>
                    </list>
                </property>

                <property name="queryEnginesConfiguration">
                    <list>
                        <bean class="org.apache.ignite.indexing.IndexingQueryEngineConfiguration">
                            <property name="default" value="true"/>
                        </bean>
<!--                        <bean class="org.apache.ignite.calcite.CalciteQueryEngineConfiguration">-->
<!--                            <property name="default" value="false"/>-->
<!--                        </bean>-->
                    </list>
                </property>
            </bean>
        </property>

        <property name="dataStorageConfiguration">
            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">
                <property name="defaultDataRegionConfiguration">
                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                        <property name="name" value="Default_Region"/>
                        <property name="initialSize" value="#{ 128 * 1024L * 1024}"/>
                        <property name="maxSize" value="#{ 1 * 1024L * 1024 * 1024}"/>
                        <property name="checkpointPageBufferSize" value="#{ 64 * 1024L * 1024}"/>

                        <property name="persistenceEnabled" value="false"/>
                        <property name="metricsEnabled" value="true"/>
                    </bean>
                </property>

                <property name="dataRegionConfigurations">
                    <list>
                        <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                            <property name="name" value="InMemory_Region"/>
                            <property name="initialSize" value="#{ 128 * 1024L * 1024}"/>
                            <property name="maxSize" value="#{1 * 1024L * 1024 * 1024}"/>

                            <property name="persistenceEnabled" value="false"/>
                            <property name="metricsEnabled" value="false"/>
                            <property name="pageEvictionMode" value="RANDOM_2_LRU"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>

        <property name="discoverySpi">
            <bean class="org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi">
                <property name="ipFinder">
                    <bean class="org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder">
                        <property name="addresses">
                            <list>
                                <value>127.0.0.1:47500..47509</value>
                            </list>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>

        <property name="serviceConfiguration">
            <list>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="TSDBMetaService"/>
                    <property name="service">
                        <bean class="com.envision.gravity.ignite.tsdb.TSDBMetaServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
            </list>
        </property>
        <property name="cacheConfiguration">
            <list>
                <!-- Configuration for TSDB_SHARDING_META -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_TSDB_META_PART"/>
                    <property name="cacheMode" value="PARTITIONED"/>
                    <property name="atomicityMode" value="ATOMIC"/>
<!--                    <property name="dataRegionName" value="InMemory_Region"/>-->
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="storeKeepBinary" value="true"/>
                    <!-- property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TSDB_SHARDING_META"/>
                                        <property name="keyType" value="#{igniteSchema}_TSDB_SHARDING_META_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TSDB_SHARDING_META_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TSDB_SHARDING_META"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="database"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="database"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="table_name"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="table_name"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="system_id"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="system_id"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="measurement"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="measurement"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="database"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="database"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="table_name"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="table_name"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="system_id"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="system_id"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="measurement"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </property>
                                                    <property name="javaFieldName" value="measurement"/>
                                                    <property name="javaFieldType" value="java.lang.String"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="owners"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.OTHER"/>
                                                    </property>
                                                    <property name="javaFieldName" value="owners"/>
                                                    <property name="javaFieldType" value="java.util.List"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="create_time"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </property>
                                                    <property name="javaFieldName" value="create_time"/>
                                                    <property name="javaFieldType" value="java.sql.Timestamp"/>
                                                </bean>

                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <property name="databaseFieldName" value="update_time"/>
                                                    <property name="databaseFieldType">
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </property>
                                                    <property name="javaFieldName" value="update_time"/>
                                                    <property name="javaFieldType" value="java.sql.Timestamp"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property -->
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="false"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_TSDB_META_PART_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_TSDB_META_PART_VALUE"/>
                                <property name="tableName" value="TBL_TSDB_META_PART"/>
                                <property name="keyFields">
                                    <list>
                                        <value>series_id</value>
                                    </list>
                                </property>
                                <!-- property name="notNullFields">
                                    <list>
                                        <value>database</value>
                                        <value>table_name</value>
                                        <value>system_id</value>
                                        <value>measurement</value>
                                        <value>owners</value>
                                        <value>create_time</value>
                                        <value>update_time</value>
                                    </list>
                                </property -->
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="series_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="series_id" value="java.lang.String"/>
                                        <entry key="allocated_node1" value="java.lang.Long"/>
                                        <entry key="allocated_node2" value="java.lang.Long"/>
                                        <entry key="create_time" value="java.sql.Timestamp"/>
                                        <entry key="update_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>

    <bean id="igniteSchema" class="java.lang.String">
        <constructor-arg value="o18729267101629999"/>
    </bean>
</beans>