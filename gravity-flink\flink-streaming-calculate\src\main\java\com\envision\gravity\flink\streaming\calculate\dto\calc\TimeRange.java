package com.envision.gravity.flink.streaming.calculate.dto.calc;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间范围实体
 *
 * <p>用于： 1. ReCalcJobTaskProcessor 的时间拆分 2. 历史数据查询的时间范围定义
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeRange implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 开始时间（毫秒时间戳） */
    private long startTime;

    /** 结束时间（毫秒时间戳） */
    private long endTime;

    /** 获取时间范围长度（毫秒） */
    public long getDurationMs() {
        return endTime - startTime;
    }

    /** 获取时间范围长度（秒） */
    public long getDurationSeconds() {
        return getDurationMs() / 1000;
    }

    /** 检查是否为有效的时间范围 */
    public boolean isValid() {
        return startTime > 0 && endTime > startTime;
    }

    /** 检查是否包含指定时间点 */
    public boolean contains(long timestamp) {
        return timestamp >= startTime && timestamp < endTime;
    }

    /** 检查是否与另一个时间范围重叠 */
    public boolean overlaps(TimeRange other) {
        return startTime < other.endTime && endTime > other.startTime;
    }

    /** 格式化开始时间 */
    public String getFormattedStartTime() {
        return formatTimestamp(startTime);
    }

    /** 格式化结束时间 */
    public String getFormattedEndTime() {
        return formatTimestamp(endTime);
    }

    private String formatTimestamp(long timestamp) {
        LocalDateTime dateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public String toString() {
        return String.format(
                "TimeRange{%s - %s, duration=%ds}",
                getFormattedStartTime(), getFormattedEndTime(), getDurationSeconds());
    }
}
