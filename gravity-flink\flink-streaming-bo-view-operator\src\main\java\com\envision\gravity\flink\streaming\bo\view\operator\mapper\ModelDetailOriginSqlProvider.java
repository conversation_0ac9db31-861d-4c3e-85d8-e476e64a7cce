package com.envision.gravity.flink.streaming.bo.view.operator.mapper;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/7/3
 * @description
 */
public class ModelDetailOriginSqlProvider {
    public String selectModelProperties(String schemeName, String modelId) {
        SQL sql =
                new SQL() {
                    {
                        SELECT("model_id, properties");
                        FROM(schemeName + ".model_detail_origin mdo");
                        WHERE("mdo.model_id = '" + modelId + "'");
                    }
                };

        return sql.toString();
    }
}
