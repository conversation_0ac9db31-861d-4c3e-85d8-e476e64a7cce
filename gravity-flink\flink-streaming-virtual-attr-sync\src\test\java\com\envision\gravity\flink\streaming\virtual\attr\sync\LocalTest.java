package com.envision.gravity.flink.streaming.virtual.attr.sync;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/12
 * @description
 */
class LocalTest {
    @Test
    void objectEqualTest() {
        Object object1 = 1;
        Object object2 = 1;
        System.out.println(Objects.equals(null, null));
        System.out.println(Objects.equals(1, null));
        System.out.println(Objects.equals(1.1, 1.1));
        System.out.println(Objects.equals("s", "s"));
        System.out.println(Objects.equals("1", 1));
        System.out.println(Objects.equals(true, false));
        System.out.println(Objects.equals(123456, 123456));
        System.out.println(Objects.equals(object1, object2));
    }

    @Test
    void refreshAssetIds() throws IOException {
        String filePath =
                "C:\\envision\\gravity-all\\gravity-flink\\flink-streaming-virtual-attr-sync\\src\\test\\resources\\asset_ids.txt";
        List<String> assetIDList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String currentLine;
            while ((currentLine = reader.readLine()) != null) {
                assetIDList.add(currentLine);
            }
        }

        System.out.println("Number of asset IDs: " + assetIDList.size());

        int batchSize = 1000;
        int totalBatches = (assetIDList.size() + batchSize - 1) / batchSize;

        for (int batch = 0; batch < totalBatches; batch++) {
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("INSERT VERTEX Object (asset_id) VALUES ");

            for (int i = batch * batchSize;
                    i < (batch + 1) * batchSize && i < assetIDList.size();
                    i++) {
                queryBuilder
                        .append("\"")
                        .append(assetIDList.get(i))
                        .append("\":(\"")
                        .append(assetIDList.get(i))
                        .append("\")");
                if (i < assetIDList.size() - 1) {
                    queryBuilder.append(", ");
                }
            }

            String query = queryBuilder.toString();
            System.out.println("Batch " + (batch + 1) + " NGQL Query:");
            System.out.println(query);
        }
    }

    @Test
    void refreshSubgraphIds() throws IOException {
        String filePath =
                "C:\\envision\\gravity-all\\gravity-flink\\flink-streaming-virtual-attr-sync\\src\\test\\resources\\sub_graph_ids.txt";
        List<String> subgraphIDList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String currentLine;
            while ((currentLine = reader.readLine()) != null) {
                subgraphIDList.add(currentLine);
            }
        }

        System.out.println("Number of subgraph IDs: " + subgraphIDList.size());

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder
                .append("INSERT VERTEX ")
                .append("Gravity_Sub_Graph")
                .append("(")
                .append("sub_graph_id")
                .append(") VALUES ");

        for (int i = 0; i < subgraphIDList.size(); i++) {
            String value = subgraphIDList.get(i);
            queryBuilder.append("\"").append(value).append("\":(\"").append(value).append("\")");
            if (i < subgraphIDList.size() - 1) {
                queryBuilder.append(",");
            } else if (i == subgraphIDList.size() - 1) {
                queryBuilder.append(";");
            }
        }

        String query = queryBuilder.toString();
        System.out.println(query);
    }

    @Test
    void genNGQL() {
        String systemIds =
                "12tJ174E_0zS\n"
                        + "12tJ174E_0zT\n"
                        + "12tJ19Fw_00i\n"
                        + "12tJ174E_0zG\n"
                        + "12tJ18Za_007\n"
                        + "12tJ174E_0zX\n"
                        + "12tJ174E_0zJ\n"
                        + "12tJ17q4_0Ds\n"
                        + "12tJ174E_0zL\n"
                        + "12tJ174E_0zw\n"
                        + "12tJ174E_0zx\n"
                        + "12tJ17q4_0Dw\n"
                        + "12tJ174E_0zP\n"
                        + "12tJ174E_0zQ\n"
                        + "12tJ19Hq_00o\n"
                        + "12tJ174G_0Ab\n"
                        + "12tJ174G_0A9\n"
                        + "12tJ174E_0zU\n"
                        + "12tJ174E_0zF\n"
                        + "12tJ174E_0zW\n"
                        + "12tJ174E_0zH\n"
                        + "12tJ174E_0zY\n"
                        + "12tJ17q3_0B0\n"
                        + "12tJ19Fv_00h\n"
                        + "12tJ12vR_0gB\n"
                        + "12tJ174E_0zz\n"
                        + "12tJ174G_0A3\n"
                        + "12tJ174G_0A4\n"
                        + "12tJ17q3_0AU\n"
                        + "12tJ17q3_0AV\n"
                        + "12tJ174E_0zR\n"
                        + "12tJ17q3_0AX\n"
                        + "12tJ17q2_0AO\n"
                        + "12tJ17q3_0BL\n"
                        + "12tJ174E_0zu\n"
                        + "12tJ174E_0zv\n"
                        + "12tJ17q2_0AK\n"
                        + "12tJ174E_0zy\n"
                        + "12tJ17q2_0AM\n"
                        + "12tJ174E_0zK\n"
                        + "12tJ174G_0A1\n"
                        + "12tJ174G_0A2\n"
                        + "12tJ174G_0Ad\n"
                        + "12tJ17q3_0AT\n"
                        + "12tJ174G_0A7\n"
                        + "12tJ17q3_0AW\n"
                        + "12tJ17q2_0AN\n"
                        + "12tJ174E_0zV\n"
                        + "12tJ174D_0zs\n"
                        + "12tJ174D_0zt\n"
                        + "12tJ174G_0A0\n"
                        + "12tJ17q2_0AL\n"
                        + "12tJ17q3_0AP\n"
                        + "12tJ18ZO_009\n"
                        + "12tJ19Fu_00g\n"
                        + "12tJ12wE_0wh\n"
                        + "12tJ174C_0zq\n"
                        + "12tJ174C_0zr\n"
                        + "12tJ19Dj_00d\n"
                        + "12tJ174z_0wA\n"
                        + "12tJ174z_0wB\n"
                        + "12tJ174z_0wC\n"
                        + "12tJ174z_0wD\n"
                        + "12tJ174z_0wE\n"
                        + "12tJ174z_0wF\n"
                        + "12tJ174z_0wG\n"
                        + "12tJ174z_0wH\n"
                        + "12tJ174z_0wI\n"
                        + "12tJ174z_0wJ\n"
                        + "12tJ174z_0wK\n"
                        + "12tJ174z_0wL\n"
                        + "12tJ174z_0wM\n"
                        + "12tJ174z_0wN\n"
                        + "12tJ174z_0wO\n"
                        + "12tJ174z_0wP\n"
                        + "12tJ174z_0wQ\n"
                        + "12tJ174z_0wR\n"
                        + "12tJ174z_0wS\n"
                        + "12tJ174z_0wT\n"
                        + "12tJ174z_0wU\n"
                        + "12tJ174z_0wV\n"
                        + "12tJ174z_0wW\n"
                        + "12tJ174z_0wX\n"
                        + "12tJ174z_0wY\n"
                        + "12tJ174z_0wZ\n"
                        + "12tJ12u6_0gt\n"
                        + "12tJ174z_0x3\n"
                        + "12tJ174z_0x4\n"
                        + "12tJ174z_0x0\n"
                        + "12tJ174z_0x1\n"
                        + "12tJ174z_0x2\n"
                        + "12tJ174z_0x8\n"
                        + "12tJ174z_0x9\n"
                        + "12tJ174z_0x5\n"
                        + "12tJ174z_0x6\n"
                        + "12tJ174z_0x7\n"
                        + "12tJ1anR_00w\n"
                        + "device_001  \n"
                        + "12tJ174z_0wx\n"
                        + "12tJ174z_0wy\n"
                        + "12tJ174z_0wz\n"
                        + "12tJ174z_0xa\n"
                        + "12tJ174z_0xb\n"
                        + "12tJ174z_0xc\n"
                        + "12tJ174z_0xd\n"
                        + "12tJ174z_0xe\n"
                        + "12tJ174z_0xf\n"
                        + "12tJ174z_0xg\n"
                        + "12tJ174z_0xh\n"
                        + "12tJ174B_0zd\n"
                        + "12tJ174B_0ze\n"
                        + "12tJ174B_0zf\n"
                        + "12tJ174B_0zg\n"
                        + "12tJ174B_0zh\n"
                        + "12tJ174B_0zi\n"
                        + "12tJ174B_0zj\n"
                        + "12tJ174B_0zk\n"
                        + "12tJ174B_0zl\n"
                        + "12tJ174B_0zm\n"
                        + "12tJ174B_0zn\n"
                        + "12tJ174B_0zo\n"
                        + "12tJ174B_0zp\n"
                        + "12tJ174A_0xA\n"
                        + "12tJ174A_0xB\n"
                        + "12tJ19Di_00a\n"
                        + "12tJ19Di_00b\n"
                        + "12tJ174A_0xD\n"
                        + "12tJ174A_0xC\n"
                        + "12tJ174A_0xF\n"
                        + "12tJ174A_0xE\n"
                        + "12tJ174A_0xH\n"
                        + "12tJ174A_0xG\n"
                        + "12tJ174A_0xJ\n"
                        + "12tJ174A_0xI\n"
                        + "12tJ174A_0xL\n"
                        + "12tJ174A_0xK\n"
                        + "12tJ174A_0xN\n"
                        + "12tJ174A_0xM\n"
                        + "12tJ174A_0xP\n"
                        + "12tJ174A_0xO\n"
                        + "12tJ174A_0xR\n"
                        + "12tJ174A_0xQ\n"
                        + "12tJ174A_0xT\n"
                        + "12tJ174A_0xS\n"
                        + "12tJ174A_0xV\n"
                        + "12tJ174A_0xU\n"
                        + "12tJ174A_0xX\n"
                        + "12tJ174A_0xW\n"
                        + "12tJ174A_0xZ\n"
                        + "12tJ174A_0xY\n"
                        + "12tJ174A_0y4\n"
                        + "12tJ174A_0y0\n"
                        + "12tJ174A_0y1\n"
                        + "12tJ174A_0y2\n"
                        + "12tJ174A_0y3\n"
                        + "12tJ174A_0y9\n"
                        + "12tJ174A_0yD\n"
                        + "12tJ174A_0yE\n"
                        + "12tJ174A_0y7\n"
                        + "12tJ174A_0y8\n"
                        + "12tJ174A_0yG\n"
                        + "12tJ174A_0yC\n"
                        + "12tJ174A_0yJ\n"
                        + "12tJ174A_0yK\n"
                        + "12tJ174A_0yB\n"
                        + "12tJ174A_0yL\n"
                        + "12tJ174A_0yM\n"
                        + "12tJ174A_0yI\n"
                        + "12tJ174A_0yO\n"
                        + "12tJ174A_0yN\n"
                        + "12tJ174A_0yQ\n"
                        + "12tJ174A_0yP\n"
                        + "12tJ174A_0yS\n"
                        + "12tJ174A_0yR\n"
                        + "12tJ174A_0yU\n"
                        + "12tJ174A_0yT\n"
                        + "12tJ174A_0yW\n"
                        + "12tJ174A_0yV\n"
                        + "12tJ174A_0yY\n"
                        + "12tJ174A_0yX\n"
                        + "12tJ174A_0z3\n"
                        + "12tJ174A_0yZ\n"
                        + "12tJ174A_0z0\n"
                        + "12tJ174A_0z1\n"
                        + "12tJ174A_0z2\n"
                        + "12tJ174A_0ya\n"
                        + "12tJ174A_0z4\n"
                        + "12tJ174A_0yb\n"
                        + "12tJ174A_0yd\n"
                        + "12tJ174A_0ye\n"
                        + "12tJ174A_0yc\n"
                        + "12tJ174A_0xj\n"
                        + "12tJ174A_0yg\n"
                        + "12tJ174A_0yf\n"
                        + "12tJ174A_0yi\n"
                        + "12tJ174A_0yh\n"
                        + "12tJ174A_0yk\n"
                        + "12tJ174A_0yj\n"
                        + "12tJ174A_0ym\n"
                        + "12tJ174A_0yl\n"
                        + "12tJ174A_0yo\n"
                        + "12tJ174A_0yn\n"
                        + "12tJ174A_0yq\n"
                        + "12tJ174A_0yp\n"
                        + "12tJ174A_0ys\n"
                        + "12tJ1akE_00u\n"
                        + "12tJ174A_0yu\n"
                        + "12tJ174A_0yt\n"
                        + "12tJ174A_0yr\n"
                        + "12tJ174A_0yv\n"
                        + "12tJ174A_0yw\n"
                        + "12tJ174A_0yx\n"
                        + "12tJ174A_0yy\n"
                        + "12tJ174A_0yz\n"
                        + "12tJ174A_0xp\n"
                        + "12tJ174A_0xq\n"
                        + "12tJ174A_0xr\n"
                        + "12tJ174A_0xi\n"
                        + "12tJ174A_0xt\n"
                        + "12tJ174A_0y5\n"
                        + "12tJ174A_0y6\n"
                        + "12tJ174A_0xm\n"
                        + "12tJ174A_0xn\n"
                        + "12tJ174A_0xo\n"
                        + "12tJ174A_0yF\n"
                        + "12tJ174A_0yH\n"
                        + "12tJ174A_0xs\n"
                        + "12tJ174A_0z6\n"
                        + "12tJ174A_0xu\n"
                        + "12tJ174A_0xk\n"
                        + "12tJ174A_0xw\n"
                        + "12tJ174A_0xx\n"
                        + "12tJ174A_0xy\n"
                        + "12tJ174A_0xz\n"
                        + "12tJ174A_0za\n"
                        + "12tJ174A_0z5\n"
                        + "12tJ174A_0zc\n"
                        + "12tJ174A_0z7\n"
                        + "12tJ174A_0xv\n"
                        + "12tJ174A_0z9\n"
                        + "12tJ174A_0xl\n"
                        + "12tJ174A_0yA\n"
                        + "12tJ174A_0zb\n"
                        + "12tJ174A_0z8\n"
                        + "12tJ19He_00l\n"
                        + "12tJ19He_00m\n"
                        + "12tJ19He_00n\n"
                        + "12tJ174J_0AG\n"
                        + "12tJ174J_0AI\n"
                        + "12tJ19Hd_00k\n"
                        + "12tJ17q6_0DF\n"
                        + "12tJ17q6_0DG\n"
                        + "12tJ18ZK_008\n"
                        + "12tJ18WW_001\n"
                        + "12tJ18WW_002\n"
                        + "12tJ18WW_003\n"
                        + "12tJ19Fq_00f\n"
                        + "12tJ174G_0zZ\n"
                        + "12tJ174I_0AB\n"
                        + "12tJ174I_0AC\n"
                        + "12tJ174I_0AD\n"
                        + "12tJ174I_0AE\n"
                        + "deviceid_202\n"
                        + "12tJ17q5_0DA\n"
                        + "12tJ17q5_0DB\n"
                        + "12tJ17q5_0DC\n"
                        + "12tJ17q5_0DD\n"
                        + "12tJ17q5_0DE\n"
                        + "12tJ18Yi_004\n"
                        + "12tJ18Yi_005\n"
                        + "deviceid_001\n"
                        + "deviceid_002\n"
                        + "deviceid_003\n"
                        + "deviceid_004\n"
                        + "12tJ17q5_0Dy\n"
                        + "12tJ17q5_0Dz\n"
                        + "12tJ174H_0AA\n"
                        + "12tJ17q4_0D0\n"
                        + "12tJ17q4_0D1\n"
                        + "12tJ17q4_0D2\n"
                        + "12tJ17q4_0D3\n"
                        + "12tJ17q4_0D4\n"
                        + "12tJ17q4_0D5\n"
                        + "12tJ17q4_0CU\n"
                        + "12tJ17q4_0CV\n"
                        + "12tJ17q4_0CW\n"
                        + "12tJ17q4_0CX\n"
                        + "12tJ17q4_0D9\n"
                        + "12tJ17q4_0CY\n"
                        + "12tJ17q4_0CZ\n"
                        + "12tJ17q4_0D7\n"
                        + "12tJ17q4_0D8\n"
                        + "12tJ17q4_0CT\n"
                        + "12tJ17q4_0D6\n"
                        + "12tJ17q4_0CQ\n"
                        + "12tJ1akr_00q\n"
                        + "12tJ17q4_0CS\n"
                        + "12tJ174H_0Aj\n"
                        + "12tJ174H_0Ak\n"
                        + "12tJ17q4_0CP\n"
                        + "12tJ18YX_006\n"
                        + "12tJ16Ym_0wv\n"
                        + "12tJ174H_0Ao\n"
                        + "12tJ174H_0Ap\n"
                        + "12tJ174H_0Am\n"
                        + "12tJ174H_0Ai\n"
                        + "12tJ19Hr_00p\n"
                        + "12tJ174H_0Ay\n"
                        + "12tJ17q4_0CR\n"
                        + "12tJ174H_0Ag\n"
                        + "12tJ174H_0Az\n"
                        + "12tJ17kT_0AJ\n"
                        + "12tJ174H_0Aq\n"
                        + "12tJ174H_0Aw\n"
                        + "12tJ174H_0Af\n"
                        + "12tJ174H_0An\n"
                        + "12tJ174H_0Ah\n"
                        + "12tJ174H_0Al\n"
                        + "12tJ17q4_0Da\n"
                        + "12tJ17q4_0Db\n"
                        + "12tJ17q4_0Dc\n"
                        + "12tJ17q4_0Dd\n"
                        + "12tJ17q4_0De\n"
                        + "12tJ17q4_0Df\n"
                        + "12tJ17q4_0Dg\n"
                        + "12tJ17q4_0Dh\n"
                        + "12tJ17q4_0Di\n"
                        + "12tJ17q4_0Dj\n"
                        + "12tJ17q4_0Dk\n"
                        + "12tJ17q4_0Dl\n"
                        + "12tJ17q4_0Dm\n"
                        + "12tJ17q4_0Dn\n"
                        + "12tJ17q4_0Do\n"
                        + "12tJ17q4_0Dp\n"
                        + "12tJ17q4_0Dq\n"
                        + "12tJ17q3_0B1\n"
                        + "12tJ17q3_0AQ\n"
                        + "12tJ17q3_0AR\n"
                        + "12tJ17q3_0AS\n"
                        + "12tJ17q3_0B5\n"
                        + "12tJ17q3_0B6\n"
                        + "12tJ17q3_0B7\n"
                        + "12tJ17q3_0B8\n"
                        + "12tJ17q3_0B9\n"
                        + "12tJ17q3_0AY\n"
                        + "12tJ17q3_0AZ\n"
                        + "12tJ17q3_0B2\n"
                        + "12tJ17q3_0B3\n"
                        + "12tJ17q3_0B4\n"
                        + "12tJ17q4_0Dv\n"
                        + "12tJ17q4_0Dr\n"
                        + "12tJ17q3_0BA\n"
                        + "12tJ17q3_0BB\n"
                        + "12tJ17q3_0BC\n"
                        + "12tJ17q3_0BD\n"
                        + "12tJ17q3_0BE\n"
                        + "12tJ17q3_0BF\n"
                        + "12tJ17q3_0BG\n"
                        + "12tJ17q3_0BH\n"
                        + "12tJ17q3_0BI\n"
                        + "12tJ17q3_0BJ\n"
                        + "12tJ17q3_0BK\n"
                        + "12tJ17q4_0Dx\n"
                        + "12tJ17q3_0BM\n"
                        + "12tJ17q3_0BN\n"
                        + "12tJ17q3_0BO\n"
                        + "12tJ17q3_0BP\n"
                        + "12tJ17q3_0BQ\n"
                        + "12tJ17q3_0BR\n"
                        + "12tJ17q3_0BS\n"
                        + "12tJ17q3_0BT\n"
                        + "12tJ17q3_0BU\n"
                        + "12tJ17q3_0BV\n"
                        + "12tJ17q3_0BW\n"
                        + "12tJ17q3_0BX\n"
                        + "12tJ17q3_0BY\n"
                        + "12tJ17q3_0BZ\n"
                        + "12tJ17q3_0C9\n"
                        + "12tJ17q3_0C7\n"
                        + "12tJ17q3_0C8\n"
                        + "12tJ17q3_0C0\n"
                        + "12tJ17q3_0C1\n"
                        + "12tJ17q3_0CA\n"
                        + "12tJ17q3_0Ba\n"
                        + "12tJ17q3_0Bb\n"
                        + "12tJ17q3_0Bc\n"
                        + "12tJ17q3_0Bd\n"
                        + "12tJ17q3_0Be\n"
                        + "12tJ17q3_0Bf\n"
                        + "12tJ17q3_0Bg\n"
                        + "12tJ17q3_0Bh\n"
                        + "12tJ17q3_0Bi\n"
                        + "12tJ17q3_0Bj\n"
                        + "12tJ17q3_0Bk\n"
                        + "12tJ17q3_0Bl\n"
                        + "12tJ17q3_0Bm\n"
                        + "12tJ17q3_0Bn\n"
                        + "12tJ17q3_0Bo\n"
                        + "12tJ17q3_0CK\n"
                        + "12tJ17q3_0CL\n"
                        + "12tJ17q3_0CM\n"
                        + "12tJ17q3_0CN\n"
                        + "12tJ17q3_0CO\n"
                        + "12tJ17q3_0Bt\n"
                        + "12tJ17q3_0Bp\n"
                        + "12tJ17q3_0Bq\n"
                        + "12tJ17q3_0Bx\n"
                        + "12tJ17q3_0By\n"
                        + "12tJ17q3_0Bz\n"
                        + "12tJ17q3_0Bu\n"
                        + "12tJ17q3_0Br\n"
                        + "12tJ17q3_0Bs\n"
                        + "12tJ17q3_0CJ\n"
                        + "12tJ17q3_0CD\n"
                        + "12tJ17q3_0Ca\n"
                        + "12tJ17q3_0Cb\n"
                        + "12tJ17q3_0Cc\n"
                        + "12tJ17q3_0Cd\n"
                        + "12tJ17q3_0Ce\n"
                        + "12tJ17q3_0Bv\n"
                        + "12tJ17q3_0Bw\n"
                        + "12tJ17q3_0Cg\n"
                        + "12tJ17q3_0CE\n"
                        + "12tJ17q3_0Cj\n"
                        + "12tJ17q3_0Ck\n"
                        + "12tJ17q3_0Cl\n"
                        + "12tJ17q3_0Cm\n"
                        + "12tJ17q3_0Cn\n"
                        + "12tJ17q3_0Co\n"
                        + "12tJ17q3_0Cp\n"
                        + "12tJ17q3_0Cq\n"
                        + "12tJ17q3_0Cr\n"
                        + "12tJ17q3_0Cs\n"
                        + "12tJ17q3_0Ct\n"
                        + "12tJ17q3_0C3\n"
                        + "12tJ17q3_0C4\n"
                        + "12tJ17q3_0C5\n"
                        + "12tJ17q3_0C6\n"
                        + "12tJ17q3_0CB\n"
                        + "12tJ17q3_0CC\n"
                        + "12tJ17q3_0Cu\n"
                        + "12tJ17q3_0Cv\n"
                        + "12tJ17q3_0Cw\n"
                        + "12tJ17q3_0Ci\n"
                        + "12tJ17q3_0CH\n"
                        + "12tJ17q3_0C2\n"
                        + "12tJ174E_0zN\n"
                        + "12tJ17q3_0Cf\n"
                        + "12tJ17q3_0Cy\n"
                        + "12tJ17q3_0Ch\n"
                        + "12tJ174E_0zD\n"
                        + "12tJ174E_0zE\n"
                        + "12tJ17q3_0Cx\n"
                        + "12tJ17q3_0CF\n"
                        + "12tJ17q3_0CG\n"
                        + "12tJ174E_0zI\n"
                        + "12tJ17q3_0CI\n"
                        + "12tJ17q4_0Dt\n"
                        + "12tJ17q4_0Du\n"
                        + "12tJ174E_0zM\n"
                        + "12tJ17q3_0Cz\n"
                        + "12tJ174E_0zO\n"
                        + "12tJ174E_0zA\n"
                        + "12tJ174E_0zB\n"
                        + "12tJ174E_0zC";

        String[] systemIDList = systemIds.split("\n");
        System.out.println(systemIDList.length);

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder
                .append("INSERT VERTEX ")
                .append("Object")
                .append("(")
                .append("asset_id")
                .append(") VALUES ");

        for (int i = 0; i < systemIDList.length; i++) {
            String value = systemIDList[i];
            queryBuilder.append("\"").append(value).append("\":(\"").append(value).append("\")");
            if (i < systemIDList.length - 1) {
                queryBuilder.append(",");
            }
        }

        String query = queryBuilder.toString();
        System.out.println(query);
    }
}
