package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/10 13:38 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblEdge {

    private String fromVid;

    private String toVid;

    private String edgeTypeId;

    private String subGraphId;

    private String propValue;

    private String treeNodeId;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
