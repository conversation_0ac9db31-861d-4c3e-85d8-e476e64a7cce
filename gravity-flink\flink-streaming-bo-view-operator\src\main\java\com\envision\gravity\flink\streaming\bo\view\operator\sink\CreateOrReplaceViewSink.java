package com.envision.gravity.flink.streaming.bo.view.operator.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.bo.view.operator.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.config.TableEngineConfig;

import java.sql.SQLException;
import java.sql.Statement;


import com.eniot.tableengine.RegisterIgniteTableConfig;
import com.eniot.tableengine.TableEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
@Slf4j
public class CreateOrReplaceViewSink
        extends RichSinkFunction<Tuple3<String, RegisterIgniteTableConfig, String>> {
    private static final long serialVersionUID = 6983047933163934709L;
    private transient SqlSessionFactory sqlSessionFactory;
    private transient TableEngine tableEngine;

    @Override
    public void open(Configuration parameters) throws Exception {
        sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        tableEngine = TableEngineConfig.buildTableEngine();
    }

    @Override
    public void close() throws Exception {
        try {
            PGDataSourceConfig.closeDataSource();
            TableEngineConfig.closeTableEngine();
        } catch (Exception e) {
            log.error("Close resource error.", e);
            throw new GravityRuntimeException("Close resource error.", e);
        }
    }

    @Override
    public void invoke(Tuple3<String, RegisterIgniteTableConfig, String> value, Context context) {
        if (value.f0 != null) {
            try {
                createOrReplaceView(value);
            } catch (SQLException e) {
                log.error(
                        "Create or replace view sink invoke error, cause: {}, value: {} ",
                        e.getMessage(),
                        value.f0,
                        e);
                log.info("Try drop view and then create view again...");
                try {
                    tryDropAndCreateView(value);
                } catch (SQLException ex) {
                    log.error("Drop and Recreate view error, cause: {} ", ex.getMessage(), ex);
                } catch (Exception ex) {
                    log.error("Unknown error, cause: {} ", ex.getMessage(), ex);
                    throw new GravityRuntimeException("Unknown error!", e);
                }
            } catch (Exception e) {
                log.error("Unknown error, cause: {} ", e.getMessage(), e);
                throw new GravityRuntimeException("Unknown error!", e);
            }
        }
    }

    private void createOrReplaceView(Tuple3<String, RegisterIgniteTableConfig, String> value)
            throws Exception {
        long executeStartTime = System.currentTimeMillis();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            try (Statement statement = session.getConnection().createStatement()) {
                // Create or Replace view
                statement.executeUpdate(value.f0);
            }
        }
        long createPgViewEndTime = System.currentTimeMillis();

        RegisterIgniteTableConfig registerIgniteTableConfig = value.f1;
        tableEngine.register(registerIgniteTableConfig);
        long executeEndTime = System.currentTimeMillis();

        log.info(
                "Create or replace bo view success, schemaName: [{}], viewName: [{}], "
                        + "create pg view time cost: [{}], register ignite table time cost: [{}].",
                value.f1.getTargetSchema(),
                value.f1.getTargetTable(),
                (createPgViewEndTime - executeStartTime) / 1000.0 + "s",
                (executeEndTime - createPgViewEndTime) / 1000.0 + "s");
    }

    private void tryDropAndCreateView(Tuple3<String, RegisterIgniteTableConfig, String> value)
            throws Exception {
        long executeStartTime = System.currentTimeMillis();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            try (Statement statement = session.getConnection().createStatement()) {
                // Drop view
                statement.executeUpdate(value.f2);
                // Create or replace view
                statement.executeUpdate(value.f0);
            }
        }
        long createPgViewEndTime = System.currentTimeMillis();

        RegisterIgniteTableConfig registerIgniteTableConfig = value.f1;
        tableEngine.register(registerIgniteTableConfig);
        long executeEndTime = System.currentTimeMillis();

        log.info(
                "Try recreate view success, schemaName: [{}], viewName: [{}], "
                        + "create pg view time cost: [{}], register ignite table time cost: [{}].",
                value.f1.getTargetSchema(),
                value.f1.getTargetTable(),
                (createPgViewEndTime - executeStartTime) / 1000.0 + "s",
                (executeEndTime - createPgViewEndTime) / 1000.0 + "s");
    }
}
