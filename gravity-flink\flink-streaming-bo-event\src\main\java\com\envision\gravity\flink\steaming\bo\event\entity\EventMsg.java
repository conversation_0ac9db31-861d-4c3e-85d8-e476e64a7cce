package com.envision.gravity.flink.steaming.bo.event.entity;

import com.envision.gravity.common.definition.DefinitionEntity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventMsg {
    private Long tsMs;
    private Long eventTime;
    private String eventSource;
    private String eventType;
    private String orgId;
    private DefinitionEntity payload;
    private String version;
    @JsonIgnore private String key;
}
