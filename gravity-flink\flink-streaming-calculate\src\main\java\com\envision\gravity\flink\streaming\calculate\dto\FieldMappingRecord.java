package com.envision.gravity.flink.streaming.calculate.dto;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class FieldMappingRecord extends FieldMappingKey {

    private String rawFieldId;

    private Integer fieldIndex;

    private Boolean horizontal;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((rawFieldId == null) ? 0 : rawFieldId.hashCode());
        result = prime * result + ((fieldIndex == null) ? 0 : fieldIndex.hashCode());
        result = prime * result + ((horizontal == null) ? 0 : horizontal.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        FieldMappingRecord other = (FieldMappingRecord) obj;

        if (rawFieldId == null) {
            if (other.rawFieldId != null) {
                return false;
            }
        } else if (!rawFieldId.equals(other.rawFieldId)) {
            return false;
        }

        if (fieldIndex == null) {
            if (other.fieldIndex != null) {
                return false;
            }
        } else if (!fieldIndex.equals(other.fieldIndex)) {
            return false;
        }

        if (horizontal == null) {
            if (other.horizontal != null) {
                return false;
            }
        } else if (!horizontal.equals(other.horizontal)) {
            return false;
        }

        return true;
    }

    @Override
    public String toString() {
        return "FieldMappingRecord{"
                + "compId='"
                + getCompId()
                + '\''
                + ", prefId='"
                + getPrefId()
                + '\''
                + ", fieldId='"
                + getFieldId()
                + '\''
                + ", rawFieldId='"
                + rawFieldId
                + '\''
                + ", fieldIndex="
                + fieldIndex
                + ", horizontal="
                + horizontal
                + '}';
    }
}
