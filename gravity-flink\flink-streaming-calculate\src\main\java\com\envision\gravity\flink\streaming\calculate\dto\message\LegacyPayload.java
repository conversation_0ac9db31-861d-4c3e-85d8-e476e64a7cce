package com.envision.gravity.flink.streaming.calculate.dto.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息载体
 * 
 * 用于：
 * 1. 单个资产的数据载体
 * 2. 包含资产的所有测量点数据
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LegacyPayload implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 时间戳
     */
    private long time;
    
    /**
     * 测量点数据
     * Key: 测量点名称
     * Value: 测量点值
     */
    @Builder.Default
    private Map<String, Object> points = new HashMap<>();
    
    /**
     * 检查是否为有效的载体
     */
    public boolean isValid() {
        return assetId != null && !assetId.isEmpty() &&
               time > 0 &&
               points != null && !points.isEmpty();
    }
    
    /**
     * 添加测量点数据
     */
    public void addPoint(String pointName, Object value) {
        if (points == null) {
            points = new HashMap<>();
        }
        points.put(pointName, value);
    }
    
    /**
     * 获取测量点数据
     */
    public Object getPoint(String pointName) {
        return points != null ? points.get(pointName) : null;
    }
    
    /**
     * 检查是否包含指定测量点
     */
    public boolean hasPoint(String pointName) {
        return points != null && points.containsKey(pointName);
    }
    
    /**
     * 获取测量点数量
     */
    public int getPointCount() {
        return points != null ? points.size() : 0;
    }
    
    /**
     * 移除测量点数据
     */
    public Object removePoint(String pointName) {
        return points != null ? points.remove(pointName) : null;
    }
    
    /**
     * 清空所有测量点数据
     */
    public void clearPoints() {
        if (points != null) {
            points.clear();
        }
    }
    
    @Override
    public String toString() {
        return String.format("LegacyPayload{assetId='%s', time=%d, pointCount=%d}", 
                           assetId, time, getPointCount());
    }
}
