package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.common.util.GravityCommonLionConfigs;
import com.envision.gravity.low.level.api.rest.util.LionUtil;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/** @Author: qi.jiang2 @Date: 2024/03/05 15:20 @Description: */
@Configuration
@MapperScan(
        basePackages = "com.envision.gravity.low.level.api.rest.dao.ignite",
        sqlSessionFactoryRef = "sqlSessionFactoryIgnite")
public class IgniteDataSourceCfg {

    @Bean(name = "igniteDataSource")
    public HikariDataSource igniteDataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setDriverClassName(
                LionUtil.getStringValue(
                        GravityCommonLionConfigs.IGNITE_JDBC_DRIVER,
                        GravityCommonLionConfigs.IGNITE_JDBC_DRIVER_DEFAULT));
        ds.setJdbcUrl(LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_JDBC_URL));
        ds.setUsername(LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_USERNAME));
        ds.setPassword(LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_PASSWORD));

        return ds;
    }

    @Bean(name = "sqlSessionFactoryIgnite")
    @Primary
    public SqlSessionFactory sqlSessionFactoryIgnite(
            @Qualifier("igniteDataSource") DataSource datasource) throws Exception {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factory.setMapperLocations(resolver.getResources("classpath*:/ignite-mapper/*.xml"));
        // factory.setTypeAliasesPackage("com.envision.gravity.low.level.api.rest.dao.ignite");
        factory.setDataSource(datasource);
        return factory.getObject();
    }

    @Bean("sqlSessionTemplateIgnite")
    @Primary
    public SqlSessionTemplate sqlSessionTemplateCatalog(
            @Qualifier("sqlSessionFactoryIgnite") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "transactionManagerIgnite")
    public PlatformTransactionManager transactionManagerCatalog() {
        return new DataSourceTransactionManager(igniteDataSource());
    }
}
