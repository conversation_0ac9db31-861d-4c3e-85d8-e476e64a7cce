package com.envision.gravity.flink.streaming.calculate.dto.recalc;

import java.io.Serializable;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算属性元数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcPropertyMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 测量点名称 */
    private String prefName;

    /** 数据类型 */
    private String dataType;

    /** 测量点ID */
    private String prefId;

    /** 组件ID */
    private String compId;

    /** 类别 */
    private String category;

    /** 单位 */
    private String unit;

    /** 描述 */
    private String description;

    @Override
    public String toString() {
        return "CalcPropertyMeta{"
                + "prefName='"
                + prefName
                + '\''
                + ", dataType='"
                + dataType
                + '\''
                + ", prefId='"
                + prefId
                + '\''
                + ", compId='"
                + compId
                + '\''
                + ", category='"
                + category
                + '\''
                + '}';
    }
}
