package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblPref {
    @KeyColumn(name = "pref_id")
    @ColumnName("pref_id")
    @RequiredField(message = "pref_id field is required")
    private String prefId;

    @ValueColumn(name = "pref_name")
    @ColumnName("pref_name")
    @RequiredField(message = "pref_name field is required")
    private String prefName;

    @ValueColumn(name = "pref_display_name")
    @ColumnName("pref_display_name")
    @RequiredField(message = "pref_display_name field is required")
    private String prefDisplayName;

    @ValueColumn(name = "pref_type")
    @ColumnName("pref_type")
    @RequiredField(message = "pref_type field is required")
    private String prefType;

    @ValueColumn(name = "description")
    @ColumnName("description")
    private String description;

    @ValueColumn(name = "comment")
    @ColumnName("comment")
    private String comment;

    @ValueColumn(name = "writable", type = Boolean.class)
    @ColumnName("writable")
    private boolean writable;

    @ValueColumn(name = "required", type = Boolean.class)
    @ColumnName("required")
    private boolean required;

    @ValueColumn(name = "default_value")
    @ColumnName("default_value")
    private String defaultValue;

    @ValueColumn(name = "has_quality", type = Boolean.class)
    @ColumnName("has_quality")
    private boolean hasQuality;

    @ValueColumn(name = "pref_data_type")
    @ColumnName("pref_data_type")
    private String prefDataType;

    @ValueColumn(name = "pref_signal_type")
    @ColumnName("pref_signal_type")
    private String prefSignalType;

    @ValueColumn(name = "data_definition")
    @ColumnName("data_definition")
    private String dataDefinition;

    @ValueColumn(name = "request")
    @ColumnName("request")
    private String request;

    @ValueColumn(name = "response")
    @ColumnName("response")
    private String response;

    @ValueColumn(name = "unit")
    @ColumnName("unit")
    @RequiredField(message = "unit field is required")
    private String unit;

    @ValueColumn(name = "lower_limit")
    @ColumnName("lower_limit")
    private String lowerLimit;

    @ValueColumn(name = "upper_limit")
    @ColumnName("upper_limit")
    private String upperLimit;

    @ValueColumn(name = "dimensions")
    @ColumnName("dimensions")
    private String dimensions;

    @ValueColumn(name = "intervals")
    @ColumnName("intervals")
    private String intervals;

    @ValueColumn(name = "default_agg_method")
    @ColumnName("default_agg_method")
    private String defaultAggMethod;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
