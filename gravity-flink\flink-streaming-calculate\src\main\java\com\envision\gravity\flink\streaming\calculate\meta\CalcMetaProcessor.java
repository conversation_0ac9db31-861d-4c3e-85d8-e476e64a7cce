package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.InvalidCalcMetaType;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.*;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.dto.*;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.utils.UpstreamCalcUtils;

import java.util.*;
import java.util.stream.Collectors;


import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcMetaProcessor {

    private static final Logger logger = LoggerFactory.getLogger(CalcMetaProcessor.class);

    private static volatile CalcMetaProcessor uniqueInstance;

    private final String TABLE_NAME = "TBL_PROPERTY_UPSTREAM_RULE";

    private final CalcPrefCache calcPrefCache;

    private final ModelMetaQueryHandler modelMetaQueryHandler;

    private final DirectMappingProcessor directMappingProcessor;

    public static CalcMetaProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (CalcMetaProcessor.class) {
                if (uniqueInstance == null) {
                    try {
                        System.out.println("=== Creating CalcMetaProcessor instance ===");
                        uniqueInstance = new CalcMetaProcessor();
                        System.out.println(
                                "=== CalcMetaProcessor instance created successfully ===");
                    } catch (Exception e) {
                        System.out.println(
                                "=== CalcMetaProcessor instance creation FAILED: "
                                        + e.getMessage()
                                        + " ===");
                        logger.error("CalcMetaProcessor instance creation failed", e);
                        throw e;
                    }
                }
            }
        }
        return uniqueInstance;
    }

    // orgId => list of CalcJobMetaInfo
    public List<CalcJobMetaInfo> fetchJobs() throws Exception {
        List<CalcJobMetaInfo> allCalcJobs = new ArrayList<>();
        List<String> schemas = this.modelMetaQueryHandler.getSchemas();
        logger.info("Found schemas: {}", schemas);

        for (String schema : schemas) {
            if (this.modelMetaQueryHandler.isTableExists(schema, TABLE_NAME)) {
                UpstreamCalcUtils.queryTblUpstreamRuleWithPagination(
                        schema, this::buildCalcJobMetaInfo);
            } else {
                logger.info("Table [{}] not exist in database [{}]", TABLE_NAME, schema);
            }

            if (logger.isDebugEnabled()) {
                CacheFactory.getCalcPrefCache().formatCacheContent(schema);
            }
        }

        return allCalcJobs;
    }

    public List<CalcJobMetaInfo> buildCalcJobMetaInfo(
            String orgId, Map<PropertyId, List<BaseCalcPropertyMeta>> baseTargetPropertyInfoMap) {
        List<CalcJobMetaInfo> calcJobMetaInfoList = new ArrayList<>();
        for (Map.Entry<PropertyId, List<BaseCalcPropertyMeta>> targetPropEntry :
                baseTargetPropertyInfoMap.entrySet()) {
            PropertyId propertyId = targetPropEntry.getKey();
            String targetCompId = propertyId.getCompId();
            String targetPrefId = propertyId.getPrefId();
            Set<String> targetModelIds =
                    CacheFactory.getCalcPrefCache()
                            .getModelByTargetPref(orgId, targetCompId, targetPrefId);

            for (BaseCalcPropertyMeta baseRuleInfo : targetPropEntry.getValue()) {
                Optional<CalcPropertyMeta> calcPropMeta =
                        CacheFactory.getCalcPrefCache()
                                .getByTargetPref(
                                        orgId,
                                        targetCompId,
                                        targetPrefId,
                                        baseRuleInfo.getSrcCategoryId());
                if (!calcPropMeta.isPresent()) {
                    logger.error(
                            "Failed to build calc job, target property calc meta not found, "
                                    + "org: [{}], prefRuleId: [{}], targetCompId: [{}], targetPrefId: [{}], srcCategoryId: [{}]",
                            orgId,
                            baseRuleInfo.getPrefRuleId(),
                            targetCompId,
                            targetPrefId,
                            baseRuleInfo.getSrcCategoryId());
                    continue;
                }

                String jobId = baseRuleInfo.getPrefRuleId() + "-" + System.currentTimeMillis();
                calcJobMetaInfoList.add(
                        CalcJobMetaInfo.builder()
                                .orgId(orgId)
                                .jobId(jobId)
                                .ruleInfo(baseRuleInfo)
                                .targetModelIds(targetModelIds)
                                .targetPropertyMeta(calcPropMeta.get())
                                .build());
            }
        }
        return calcJobMetaInfoList;
    }

    public void batchLoad() {
        try {
            logger.info("CalcMetaProcessor.batchLoad() started");

            // Get all schemas and filter those starting with 'O'
            List<String> schemas = this.modelMetaQueryHandler.getSchemas();
            logger.info("Found schemas: {}", schemas);

            for (String schema : schemas) {
                if (this.modelMetaQueryHandler.isTableExists(schema, TABLE_NAME)) {
                    UpstreamCalcUtils.queryTblUpstreamRuleWithPagination(
                            schema, this::refreshTargetPropertyCache);
                } else {
                    logger.info("Table [{}] not exist in database [{}]", TABLE_NAME, schema);
                }

                if (logger.isDebugEnabled()) {
                    CacheFactory.getCalcPrefCache().formatCacheContent(schema);
                }
            }

            logger.info("CalcMetaProcessor.batchLoad() completed successfully");
        } catch (Exception e) {
            logger.error("CalcMetaProcessor.batchLoad() failed", e);
            throw e; // 重新抛出异常以便调用者知道失败
        }
    }

    private CalcMetaProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        this.directMappingProcessor = DirectMappingProcessor.getInstance();
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
    }

    public void refreshTargetPropertyByCompIdAndPrefId(
            String orgId, List<PropertyId> targetPropertyIds) {
        if (GTCommonUtils.emptyCollection(targetPropertyIds)) {
            logger.warn("Target property key is empty, orgId: [{}]", orgId);
            return;
        }

        StringJoiner conditions = new StringJoiner(" OR ");
        for (PropertyId pk : targetPropertyIds) {
            String compId = pk.getCompId();
            String prefId = pk.getPrefId();
            conditions.add(
                    String.format(
                            "(TARGET_COMP_ID = '%s' AND TARGET_PREF_ID = '%s')", compId, prefId));
        }

        String sql =
                String.format(
                        "SELECT PREF_RULE_ID, TARGET_CATEGORY, TARGET_COMP_ID, TARGET_PREF_ID, SRC_CATEGORY, EXPRESSION, CALC_TYPE "
                                + "FROM %s.%s WHERE %s",
                        orgId, TABLE_NAME, conditions);
        List<List<?>> queryResult = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(queryResult)) {
            logger.warn(
                    "Query upstream result is empty, orgId: [{}], params: [{}]",
                    orgId,
                    targetPropertyIds);
            return;
        }

        Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropMetaMap =
                new HashMap<>(queryResult.size());
        for (List<?> row : queryResult) {
            String prefRuleId = (String) row.get(0);
            String targetCategoryId = (String) row.get(1);
            String targetCompId = (String) row.get(2);
            String targetPrefId = (String) row.get(3);
            String srcCategoryId = (String) row.get(4);
            String expression = (String) row.get(5);
            Integer calcType = (Integer) row.get(6);
            targetPropMetaMap
                    .computeIfAbsent(
                            new PropertyId(targetCompId, targetPrefId), k -> new ArrayList<>())
                    .add(
                            BaseCalcPropertyMeta.builder()
                                    .prefRuleId(prefRuleId)
                                    .targetCategoryId(targetCategoryId)
                                    .targetCompId(targetCompId)
                                    .targetPrefId(targetPrefId)
                                    .srcCategoryId(srcCategoryId)
                                    .expression(expression)
                                    .calcType(calcType)
                                    .build());
        }

        refreshTargetPropertyCache(orgId, targetPropMetaMap);
    }

    public Optional<TargetPropertyMetaInfo> queryTargetPropertyMeta(
            String orgId, Map<PropertyId, List<BaseCalcPropertyMeta>> baseTargetPropertyInfoMap) {
        // Result's key propertyId with modelId, targetPropertyMap's key only has compId and prefId
        Map<PropertyId, PropertyInfo> propertyInfos =
                this.modelMetaQueryHandler.getPropertyByCompIdAndPrefId(
                        orgId, baseTargetPropertyInfoMap.keySet());
        if (GTCommonUtils.isEmpty(propertyInfos)) {
            logger.error(
                    "Target property not found in ignite, orgId: [{}], params: [{}]",
                    orgId,
                    baseTargetPropertyInfoMap);
            return Optional.empty();
        }

        // Filter targetPropertyMap's records with no model definition by propertyInfos
        Map<PropertyId, List<BaseCalcPropertyMeta>> notDefinedTargetPropertyMap =
                UpstreamCalcUtils.filterNotDefinedProperty(
                        orgId, baseTargetPropertyInfoMap, propertyInfos);
        if (!GTCommonUtils.isEmpty(notDefinedTargetPropertyMap)) {
            logger.error(
                    "Some target property not found in model definition, orgId: [{}], records: [{}]",
                    orgId,
                    notDefinedTargetPropertyMap);
        }

        Map<String, String> srcModel2Category =
                getSrcModelCategoryMap(orgId, propertyInfos, baseTargetPropertyInfoMap);

        // targetCompId + targetPrefId => <targetModelId, <srcCategory, meta>>
        Map<PropertyId, Map<String, Map<String, CalcPropertyMeta>>> targetPrefCacheEntries =
                new HashMap<>(propertyInfos.size());
        // srcPrefName => <srcModelId, List of target pref>
        Map<String, Map<String, Set<PropertyId>>> srcPrefCacheEntries =
                new HashMap<>(propertyInfos.size());

        List<CalcPropertyMeta> directMappingProperties = new ArrayList<>();
        Set<PropertyInfo> allSrcModelPrefNameSet = new HashSet<>();

        for (PropertyInfo prop : propertyInfos.values()) {
            String targetModelId = prop.getModelId();
            String targetCompId = prop.getCompId();
            String targetPrefId = prop.getPrefId();
            String targetPrefName = prop.getPrefName();

            PropertyId targetPropertyId = new PropertyId(targetCompId, targetPrefId);

            // Create target and source cache entries
            Map<String, Map<String, CalcPropertyMeta>> targetModel2CategoryExprMap =
                    targetPrefCacheEntries.computeIfAbsent(targetPropertyId, t -> new HashMap<>());
            List<BaseCalcPropertyMeta> targetExprList =
                    baseTargetPropertyInfoMap.get(targetPropertyId);
            List<CalcPropertyMeta> exprParsedResult =
                    UpstreamCalcUtils.parseExpr(
                            targetModelId, targetCompId, targetPrefId, targetExprList);
            Map<String, CalcPropertyMeta> calcPrefMetaMap = new HashMap<>(exprParsedResult.size());

            /**
             * Ignore expressions if their dependent original points have metadata issues after
             * batch parsing all expressions for a measurement point
             */
            for (CalcPropertyMeta parseRes : exprParsedResult) {
                boolean isValidExpr = checkExprSrcCategory(orgId, parseRes, srcModel2Category);
                byte invalidType = InvalidCalcMetaType.NONE.getCode();
                if (!isValidExpr) {
                    // 这里是表达式依赖的 SourceProperty 异常，所以需要标记 Target Rule 异常
                    invalidType = InvalidCalcMetaType.INVALID_SRC_CATEGORY_ID.getCode();
                }

                for (SrcPrefItem srcPref : parseRes.getSrcPrefItems()) {
                    String srcModelId = srcPref.getModelId();
                    String srcPrefName = srcPref.getPrefName();

                    allSrcModelPrefNameSet.add(
                            PropertyInfo.builder()
                                    .modelId(srcModelId)
                                    .prefName(srcPrefName)
                                    .build());

                    Map<String, Set<PropertyId>> srcModel2TargetPref =
                            srcPrefCacheEntries.computeIfAbsent(srcPrefName, s -> new HashMap<>());
                    srcModel2TargetPref
                            .computeIfAbsent(srcModelId, s -> new HashSet<>())
                            .add(new PropertyId(targetModelId, targetCompId, targetPrefId));
                }

                boolean isDirectMapping =
                        isValidExpr
                                && parseRes.isDirectMapping()
                                && (CalcCommonUtils.isMeasurePoint(prop.getPrefType()));

                CalcPropertyMeta calcPrefMeta =
                        CalcPropertyMeta.builder()
                                .targetCategoryId(parseRes.getTargetCategoryId())
                                .targetCompId(parseRes.getTargetCompId())
                                .targetPrefId(parseRes.getTargetPrefId())
                                .srcCategoryId(parseRes.getSrcCategoryId())
                                .expression(parseRes.getExpression())
                                .prefName(targetPrefName)
                                .isDirectMapping(isDirectMapping)
                                .srcPrefItems(parseRes.getSrcPrefItems())
                                .isExprUseCurrentModel(parseRes.isExprUseCurrentModel())
                                .isValidExpr(isValidExpr)
                                .invalidType(invalidType)
                                .build();
                calcPrefMetaMap.put(parseRes.getSrcCategoryId(), calcPrefMeta);

                if (isDirectMapping) {
                    directMappingProperties.add(calcPrefMeta);
                }
            }

            targetModel2CategoryExprMap.put(targetModelId, calcPrefMetaMap);
        }

        return Optional.of(
                TargetPropertyMetaInfo.builder()
                        .targetPropertyMetaMap(targetPrefCacheEntries)
                        .srcPropertyMetaMap(srcPrefCacheEntries)
                        .directMappingProperties(directMappingProperties)
                        .allSrcModelPrefNameSet(allSrcModelPrefNameSet)
                        .build());
    }

    /**
     * 1. Parse deps source properties 2. Load in cache 3. Process direct mapping
     *
     * @param orgId
     * @param baseTargetPropertyInfoMap
     */
    public TargetPropertyMetaInfo refreshTargetPropertyCache(
            String orgId, Map<PropertyId, List<BaseCalcPropertyMeta>> baseTargetPropertyInfoMap) {
        Optional<TargetPropertyMetaInfo> targetPropertyMetaInfo =
                queryTargetPropertyMeta(orgId, baseTargetPropertyInfoMap);
        if (!targetPropertyMetaInfo.isPresent()) {
            logger.error(
                    "Failed to query target property meta, orgId: [{}], param: [{}]",
                    orgId,
                    baseTargetPropertyInfoMap);
            throw new IllegalArgumentException("Failed to query target property meta");
        }

        TargetPropertyMetaInfo targetPropertyMetaExt = targetPropertyMetaInfo.get();
        Map<PropertyId, Map<String, Map<String, CalcPropertyMeta>>> targetPrefCacheEntries =
                targetPropertyMetaExt.getTargetPropertyMetaMap();
        Map<String, Map<String, Set<PropertyId>>> srcPrefCacheEntries =
                targetPropertyMetaExt.getSrcPropertyMetaMap();
        List<CalcPropertyMeta> directMappingProperties =
                targetPropertyMetaExt.getDirectMappingProperties();
        Set<PropertyInfo> allSrcModelPrefNameSet =
                targetPropertyMetaExt.getAllSrcModelPrefNameSet();

        // Get field info by source pref
        Map<Pair<String, String>, PropertyFieldInfo> srcPrefFieldInfos =
                this.modelMetaQueryHandler.getPropertyFieldInfoByModelPrefName(
                        orgId, allSrcModelPrefNameSet);

        // Update source property's prefType
        for (Map.Entry<PropertyId, Map<String, Map<String, CalcPropertyMeta>>> targetEntry :
                targetPrefCacheEntries.entrySet()) {
            for (Map.Entry<String, Map<String, CalcPropertyMeta>> targetModelEntry :
                    targetEntry.getValue().entrySet()) {
                for (Map.Entry<String, CalcPropertyMeta> srcCategoryEntry :
                        targetModelEntry.getValue().entrySet()) {
                    CalcPropertyMeta calcPrefMeta = srcCategoryEntry.getValue();
                    for (SrcPrefItem srcPrefItem : calcPrefMeta.getSrcPrefItems()) {
                        PropertyFieldInfo srcPrefFieldInfo =
                                srcPrefFieldInfos.get(
                                        Pair.of(
                                                srcPrefItem.getModelId(),
                                                srcPrefItem.getPrefName()));
                        srcPrefItem.setPrefType(srcPrefFieldInfo.getPrefType());
                    }
                }
            }
        }

        for (Map.Entry<PropertyId, Map<String, Map<String, CalcPropertyMeta>>> entry :
                targetPrefCacheEntries.entrySet()) {
            String compId = entry.getKey().getCompId();
            String prefId = entry.getKey().getPrefId();
            calcPrefCache.updateTargetPref(orgId, compId, prefId, entry.getValue());
        }

        for (Map.Entry<String, Map<String, Set<PropertyId>>> entry :
                srcPrefCacheEntries.entrySet()) {
            String prefName = entry.getKey();
            calcPrefCache.batchUpdateSrcPref(orgId, prefName, entry.getValue());
        }

        // Process direct mapping
        Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap =
                this.directMappingProcessor.getFieldMappings(
                        orgId, directMappingProperties, srcPrefFieldInfos);
        if (GTCommonUtils.nonEmptyMap(fieldMappingRecordMap)) {
            this.directMappingProcessor.updateCompPrefFieldMapping(orgId, fieldMappingRecordMap);
        }

        return targetPropertyMetaExt;
    }

    private Map<String, String> getSrcModelCategoryMap(
            String orgId,
            Map<PropertyId, PropertyInfo> propertyInfos,
            Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropertyMap) {
        Set<String> srcModelIds = new HashSet<>();
        for (PropertyInfo prop : propertyInfos.values()) {
            String targetModelId = prop.getModelId();
            String targetCompId = prop.getCompId();
            String targetPrefId = prop.getPrefId();

            PropertyId targetPropertyId = new PropertyId(targetCompId, targetPrefId);
            List<BaseCalcPropertyMeta> targetExprList = targetPropertyMap.get(targetPropertyId);
            List<CalcPropertyMeta> exprParsedResult =
                    UpstreamCalcUtils.parseExpr(
                            targetModelId, targetCompId, targetPrefId, targetExprList);

            for (CalcPropertyMeta parseRes : exprParsedResult) {
                for (SrcPrefItem srcPrefItem : parseRes.getSrcPrefItems()) {
                    srcModelIds.add(srcPrefItem.getModelId());
                }
            }
        }

        return this.modelMetaQueryHandler.getModelCategory(orgId, srcModelIds);
    }

    // Check upstream expression's source model's category whether equals src_category field
    private boolean checkExprSrcCategory(
            String orgId, CalcPropertyMeta exprMeta, Map<String, String> srcModel2Category) {
        String prefRuleId = exprMeta.getPrefRuleId();
        String targetCategoryId = exprMeta.getTargetCategoryId();
        String targetCompId = exprMeta.getTargetCompId();
        String targetPrefId = exprMeta.getTargetPrefId();
        String srcCategoryId = exprMeta.getSrcCategoryId();
        String expression = exprMeta.getExpression();
        boolean isExprUseCurrentModel = exprMeta.isExprUseCurrentModel();

        for (SrcPrefItem srcPref : exprMeta.getSrcPrefItems()) {
            String srcPrefName = srcPref.getPrefName();
            String srcModelId = srcPref.getModelId();

            if (!isExprUseCurrentModel
                    && !srcCategoryId.equals(srcModel2Category.get(srcModelId))) {
                logger.error(
                        "Rule expression is invalid, src category not match, orgId: {}, ruleId: {}, srcCategoryId: {}, expression: {}, targetCompId: {}, targetPrefId: {}, targetCategoryId: {}",
                        orgId,
                        prefRuleId,
                        srcCategoryId,
                        expression,
                        targetCompId,
                        targetPrefId,
                        targetCategoryId);
                return false;
            }
        }
        return true;
    }

    public void deleteTargetProperty(String orgId, List<BaseCalcPropertyMeta> toDeleteMetas) {
        // Delete direct mapping
        List<CalcPropertyMeta> directMappingPrefs = new ArrayList<>(toDeleteMetas.size());
        Set<PropertyInfo> allSrcModelPrefNameSet = new HashSet<>();
        for (BaseCalcPropertyMeta meta : toDeleteMetas) {
            String compId = meta.getTargetCompId();
            String prefId = meta.getTargetPrefId();
            String srcCategoryId = meta.getSrcCategoryId();
            Optional<CalcPropertyMeta> metaCacheRecord =
                    calcPrefCache.getByTargetPref(orgId, compId, prefId, srcCategoryId);
            if (metaCacheRecord.isPresent() && metaCacheRecord.get().isDirectMapping()) {
                directMappingPrefs.add(metaCacheRecord.get());
                for (SrcPrefItem srcPrefItem : metaCacheRecord.get().getSrcPrefItems()) {
                    allSrcModelPrefNameSet.add(
                            PropertyInfo.builder()
                                    .modelId(srcPrefItem.getModelId())
                                    .prefName(srcPrefItem.getPrefName())
                                    .build());
                }
            }
        }

        // Get field info by source pref
        Map<Pair<String, String>, PropertyFieldInfo> srcPrefFieldInfos =
                this.modelMetaQueryHandler.getPropertyFieldInfoByModelPrefName(
                        orgId, allSrcModelPrefNameSet);

        // According target pref get field mappings, then delete it
        Map<FieldMappingKey, FieldMappingRecord> deleteCompPrefFieldMapping =
                this.directMappingProcessor.getFieldMappings(
                        orgId, directMappingPrefs, srcPrefFieldInfos);
        this.directMappingProcessor.deleteCompPrefFieldMapping(
                orgId, deleteCompPrefFieldMapping.keySet());

        // Delete cache
        for (BaseCalcPropertyMeta meta : toDeleteMetas) {
            String compId = meta.getTargetCompId();
            String prefId = meta.getTargetPrefId();
            String srcCategoryId = meta.getSrcCategoryId();
            calcPrefCache.delete(orgId, compId, prefId, srcCategoryId);
        }
    }

    public Map<FieldMappingKey, FieldMappingRecord> getDirectMappingTargetPropertyBySrc(
            String orgId, TblComponentPref srcPrefInfo) {
        Set<PropertyId> updateSrcPropertyIds =
                Collections.singleton(
                        PropertyId.builder()
                                .compId(srcPrefInfo.getCompId())
                                .prefId(srcPrefInfo.getPrefId())
                                .build());
        Map<PropertyId, PropertyInfo> srcPropertyInfos =
                this.modelMetaQueryHandler.getPropertyByCompIdAndPrefId(
                        orgId, updateSrcPropertyIds);
        if (GTCommonUtils.isEmpty(srcPropertyInfos)) {
            logger.error(
                    "Source property not found in ignite, orgId: [{}], meta: [{}]",
                    orgId,
                    updateSrcPropertyIds);
            return Collections.emptyMap();
        }

        List<SrcPrefItem> srcPrefItems =
                srcPropertyInfos.values().stream()
                        .map(
                                prop ->
                                        SrcPrefItem.builder()
                                                .modelId(prop.getModelId())
                                                .prefName(prop.getPrefName())
                                                .build())
                        .collect(Collectors.toList());

        Map<SrcPrefItem, Set<PropertyId>> srcRelatedTargetPrefs =
                this.calcPrefCache.batchGetTargetBySrcPref(orgId, srcPrefItems);
        if (GTCommonUtils.isEmpty(srcRelatedTargetPrefs)) {
            logger.info(
                    "Updated component pref records have no related target properties, orgId: [{}], srcPrefs: [{}]",
                    orgId,
                    srcPrefItems);
            return Collections.emptyMap();
        }

        Set<String> srcModelIds =
                srcPrefItems.stream().map(SrcPrefItem::getModelId).collect(Collectors.toSet());
        Map<String, String> srcModel2Category =
                this.modelMetaQueryHandler.getModelCategory(orgId, srcModelIds);

        Set<TargetPropertyKey> allTargetPropKeys = new HashSet<>();
        for (Map.Entry<SrcPrefItem, Set<PropertyId>> entry : srcRelatedTargetPrefs.entrySet()) {
            SrcPrefItem srcPrefItem = entry.getKey();
            Set<PropertyId> targetPropertyIds = entry.getValue();
            String srcCategoryId = srcModel2Category.get(srcPrefItem.getModelId());
            for (PropertyId targetPropertyId : targetPropertyIds) {
                TargetPropertyKey targetPrefKey =
                        TargetPropertyKey.builder()
                                .srcCategoryId(srcCategoryId)
                                .compId(targetPropertyId.getCompId())
                                .prefId(targetPropertyId.getPrefId())
                                .build();
                allTargetPropKeys.add(targetPrefKey);
            }
        }

        List<CalcPropertyMeta> targetPrefMetas = new ArrayList<>(allTargetPropKeys.size());
        for (TargetPropertyKey tpk : allTargetPropKeys) {
            String compId = tpk.getCompId();
            String prefId = tpk.getPrefId();
            String srcCategoryId = tpk.getSrcCategoryId();
            Optional<CalcPropertyMeta> targetPrefMetaInfo =
                    this.calcPrefCache.getByTargetPref(orgId, compId, prefId, srcCategoryId);
            targetPrefMetaInfo.ifPresent(targetPrefMetas::add);
        }

        Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap =
                new HashMap<>(targetPrefMetas.size());
        for (CalcPropertyMeta targetPrefMeta : targetPrefMetas) {
            if (targetPrefMeta.isValidExpr() && targetPrefMeta.isDirectMapping()) {
                fieldMappingRecordMap.put(
                        new FieldMappingKey(
                                targetPrefMeta.getTargetCompId(),
                                targetPrefMeta.getTargetPrefId(),
                                srcPrefInfo.getFieldId()),
                        FieldMappingRecord.builder()
                                .compId(targetPrefMeta.getTargetCompId())
                                .prefId(targetPrefMeta.getTargetPrefId())
                                .fieldId(srcPrefInfo.getFieldId())
                                .rawFieldId(srcPrefInfo.getRawFieldId())
                                .fieldIndex(srcPrefInfo.getFieldIndex())
                                .horizontal(srcPrefInfo.isHorizontal())
                                .build());
            }
        }

        return fieldMappingRecordMap;
    }
}
