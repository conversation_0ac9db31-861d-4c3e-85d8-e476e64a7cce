package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.low.level.api.rest.dto.ObjAttrValueSet;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.util.AuditUtil;
import com.envision.gravity.low.level.api.rest.util.RestQueryUtils;

import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/12
 * @description
 */
public class TblBOSqlProvider {

    public String unbindSystemIds(String orgId, Set<String> systemIds) {
        SQL sql = new SQL();
        sql.UPDATE(orgId + ".tbl_bo");
        sql.SET("system_id = null");
        sql.SET("modified_time = now()");
        sql.WHERE(String.format("system_id in (%s)", RestQueryUtils.concatStr(systemIds)));
        return sql.toString();
    }

    public String batchUpdateAttrModifiedTimeBySystemIds(String orgId, List<String> systemIds) {
        String replacedSystemIds =
                systemIds.stream()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.UPDATE(orgId + ".tbl_bo");

        sql.SET("attr_modified_time = CURRENT_TIMESTAMP");

        sql.WHERE("system_id in ( " + replacedSystemIds + " )");

        return sql.toString();
    }

    public String updateAttrModifiedTimeByPrimaryKey(String orgId, String assetId) {
        SQL sql = new SQL();
        sql.UPDATE(orgId + ".tbl_bo");

        sql.SET("attr_modified_time = CURRENT_TIMESTAMP");

        sql.WHERE("asset_id = #{assetId, jdbcType=VARCHAR}");

        return sql.toString();
    }

    public String batchUpdateAttrModifiedTimeByPrimaryKey(String orgId, Set<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.UPDATE(orgId + ".tbl_bo");

        sql.SET("attr_modified_time = CURRENT_TIMESTAMP");

        sql.WHERE("asset_id in ( " + assetIds + " )");

        return sql.toString();
    }

    public String selectAssetIdList(String orgId, List<String> groupIdList) {
        String groupIds =
                groupIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tb.asset_id");
                        FROM(orgId + ".tbl_bo tb");
                        INNER_JOIN(
                                orgId
                                        + ".tbl_bo_group_relation tbgr on tb.asset_id = tbgr.asset_id");
                        INNER_JOIN(orgId + ".tbl_bo_group tbg on tbgr.group_id = tbg.group_id");
                        WHERE("tbg.group_id in ( " + groupIds + " )");
                    }
                };

        return sql.toString();
    }

    public String queryByaAssetId(String orgId, List<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.SELECT("asset_id");
        sql.SELECT("system_id");
        sql.SELECT("asset_display_name");
        sql.FROM(orgId + ".tbl_bo");
        sql.WHERE(String.format("asset_id IN (%s)", assetIds));
        return sql.toString();
    }

    public String batchRefreshObjectDetailBySystemIds(
            String orgId, Set<String> systemIdList, AuditHeader auditHeader) {
        String auditUsername = AuditUtil.getAuditUsername(auditHeader);
        String propertyName = auditUsername != null ? String.format("'%s'", auditUsername) : null;

        String systemIds = RestQueryUtils.concatStr(systemIdList, true);
        SQL sql = new SQL();
        sql.SELECT(
                orgId
                        + ".batch_refresh_object_detail_by_asset_ids_with_audit(ARRAY(\n SELECT tb.asset_id\n"
                        + "        FROM "
                        + orgId
                        + ".tbl_bo tb\n"
                        + "        WHERE tb.system_id in ( "
                        + systemIds
                        + " )), "
                        + propertyName
                        + ");");

        sql.SELECT(
                String.format(
                        "%s.batch_refresh_object_detail_by_asset_ids_with_audit("
                                + "ARRAY(SELECT tb.asset_id FROM %s.tbl_bo tb WHERE tb.system_id in (%s)), %s);",
                        orgId, orgId, systemIds, propertyName));
        return sql.toString();
    }

    // inc_refresh_object_detail_with_attr_value
    public String incRefreshObjectDetailWithAttrValue(
            String orgId, AuditHeader auditHeader, ObjAttrValueSet objAttrValueSet) {
        String auditUsername = AuditUtil.getAuditUsername(auditHeader);
        auditUsername = auditUsername != null ? String.format("'%s'", auditUsername) : null;

        SQL sql = new SQL();
        sql.SELECT(
                String.format(
                        "%s.inc_refresh_object_detail_with_attr_value("
                                + "ARRAY(SELECT tb.asset_id FROM %s.tbl_bo tb WHERE tb.system_id in (%s))::varchar[],"
                                + "ARRAY[%s]::varchar[],"
                                + "ARRAY[%s]::int[],"
                                + "ARRAY[%s]::boolean[],"
                                + "ARRAY[%s]::varchar[],"
                                + "ARRAY[%s]::bigint[],"
                                + "ARRAY[%s]::decimal[],"
                                + "ARRAY[%s]::jsonb[],"
                                + "ARRAY[%s]::timestamp[],"
                                + "%s::varchar);",
                        orgId,
                        orgId,
                        objAttrValueSet.getAssetIdsExpr(),
                        objAttrValueSet.getSystemIdsExpr(),
                        objAttrValueSet.getFieldIndexesExpr(),
                        objAttrValueSet.getBoolValuesExpr(),
                        objAttrValueSet.getStringValuesExpr(),
                        objAttrValueSet.getLongValuesExpr(),
                        objAttrValueSet.getDoubleValuesExpr(),
                        objAttrValueSet.getJsonValuesExpr(),
                        objAttrValueSet.getModifiedTimesExpr(),
                        auditUsername));

        return sql.toString();
    }
}
