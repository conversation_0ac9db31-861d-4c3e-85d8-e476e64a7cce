package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2024/7/9
 * @description
 */
@Slf4j
public class RefreshReqAggregator
        extends ProcessWindowFunction<RefreshReq, List<RefreshReq>, String, TimeWindow> {
    private static final long serialVersionUID = -3832046593889700604L;

    @Override
    public void process(
            String s,
            ProcessWindowFunction<RefreshReq, List<RefreshReq>, String, TimeWindow>.Context context,
            Iterable<RefreshReq> elements,
            Collector<List<RefreshReq>> out)
            throws Exception {
        try {
            Set<String> uniqueSchemaNames = new HashSet<>();
            List<RefreshReq> deduplicatedList =
                    StreamSupport.stream(elements.spliterator(), false)
                            .filter(element -> uniqueSchemaNames.add(element.getSchemaName()))
                            .collect(Collectors.toList());
            out.collect(deduplicatedList);
        } catch (Exception e) {
            log.error("Aggregated refresh request error", e);
        }
    }
}
