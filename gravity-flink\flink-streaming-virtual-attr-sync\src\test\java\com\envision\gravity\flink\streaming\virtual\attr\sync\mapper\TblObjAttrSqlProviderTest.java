package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import java.util.Arrays;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @description
 */
class TblObjAttrSqlProviderTest {

    @Test
    void queryAttrValue() {
        TblObjAttrSqlProvider tblObjAttrSqlProvider = new TblObjAttrSqlProvider();
        String schemaName = "o17231990549791976";
        String attr = "BRANCHMAX__BIGINT";
        System.out.println(
                tblObjAttrSqlProvider.queryAttrValue(
                        schemaName,
                        attr,
                        Arrays.asList("demo193f5841cfa26000", "06ydp06ye900s4", "06ydp06zab015v")));
    }
}
