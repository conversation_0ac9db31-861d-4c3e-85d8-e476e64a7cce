package com.envision.gravity.low.level.api.rest.config;

import com.envision.apim.poseidon.config.PConfig;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/2/19
 * @description
 */
@Configuration
public class PoseidonCfg {
    public static String EMPTY_UUID = "00000000-0000-0000-0000-000000000000";
    public static final PConfig POSEIDON_CONFIG =
            PConfig.init().appKey(EMPTY_UUID).appSecret(EMPTY_UUID).verifySSL(false).debug();
}
