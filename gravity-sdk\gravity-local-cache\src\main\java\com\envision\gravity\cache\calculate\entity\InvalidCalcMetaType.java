package com.envision.gravity.cache.calculate.entity;

public enum InvalidCalcMetaType {
    NONE((byte) 0),
    // Expression's source model category not equal to rule's src_category field
    INVALID_SRC_CATEGORY_ID((byte) 1),
    SRC_MODEL_DELETED((byte) 2);

    private final byte code;

    InvalidCalcMetaType(byte code) {
        this.code = code;
    }

    public static InvalidCalcMetaType getByCode(byte code) {
        for (InvalidCalcMetaType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return NONE;
    }

    public byte getCode() {
        return code;
    }
}
