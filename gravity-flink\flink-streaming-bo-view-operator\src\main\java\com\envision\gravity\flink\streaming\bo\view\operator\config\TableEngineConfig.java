package com.envision.gravity.flink.streaming.bo.view.operator.config;


import com.eniot.tableengine.TableEngine;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @description
 */
@Slf4j
public class TableEngineConfig {
    private static volatile TableEngine TABLE_ENGINE;

    public static TableEngine buildTableEngine() {
        if (TABLE_ENGINE == null) {
            synchronized (TableEngineConfig.class) {
                if (TABLE_ENGINE == null) {
                    log.info("Start build table engine...");

                    TABLE_ENGINE =
                            TableEngine.builder()
                                    .withIgniteIp(LionConfig.getIgniteHostname())
                                    .withClientPort(LionConfig.getIgniteClientPort())
                                    .withIgniteUser(LionConfig.getIgniteUsername())
                                    .withIgnitePasswd(LionConfig.getIgnitePassword())
                                    .build();
                    log.info("Build table engine success.");
                }
            }
        }
        return TABLE_ENGINE;
    }

    public static void closeTableEngine() throws Exception {
        if (TABLE_ENGINE != null) {
            synchronized (TableEngineConfig.class) {
                if (TABLE_ENGINE != null) {
                    TABLE_ENGINE.close();
                    TABLE_ENGINE = null;
                    log.info("Close table engine success.");
                }
            }
        }
    }
}
