package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcMetaInfo;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MyBatis TypeHandler for RecCalcMetaInfo JSON serialization/deserialization Handles conversion
 * between RecCalcMetaInfo objects and PostgreSQL JSONB
 *
 * <AUTHOR>
 */
public class RecCalcMetaInfoTypeHandler extends BaseTypeHandler<RecCalcMetaInfo> {

    private static final Logger logger = LoggerFactory.getLogger(RecCalcMetaInfoTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(
            PreparedStatement ps, int i, RecCalcMetaInfo parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            // Convert RecCalcMetaInfo to JSON string
            String jsonString = objectMapper.writeValueAsString(parameter);

            // Create PostgreSQL JSONB object
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");
            jsonObject.setValue(jsonString);

            ps.setObject(i, jsonObject);

            logger.debug("Serialized RecCalcMetaInfo to JSONB: {}", jsonString);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize RecCalcMetaInfo to JSON", e);
            throw new SQLException("Failed to serialize RecCalcMetaInfo to JSON", e);
        }
    }

    @Override
    public RecCalcMetaInfo getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonToRecCalcMetaInfo(rs.getString(columnName));
    }

    @Override
    public RecCalcMetaInfo getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonToRecCalcMetaInfo(rs.getString(columnIndex));
    }

    @Override
    public RecCalcMetaInfo getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        return parseJsonToRecCalcMetaInfo(cs.getString(columnIndex));
    }

    /** Parse JSON string to RecCalcMetaInfo object */
    private RecCalcMetaInfo parseJsonToRecCalcMetaInfo(String jsonString) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            RecCalcMetaInfo result = objectMapper.readValue(jsonString, RecCalcMetaInfo.class);
            logger.debug("Deserialized JSONB to RecCalcMetaInfo: {}", jsonString);
            return result;
        } catch (JsonProcessingException e) {
            logger.error("Failed to deserialize JSON to RecCalcMetaInfo: {}", jsonString, e);
            throw new SQLException("Failed to deserialize JSON to RecCalcMetaInfo", e);
        }
    }
}
