package com.envision.gravity.low.level.api.sql.common;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;
import com.envision.gravity.low.level.api.sql.table.persistent.*;


import lombok.Getter;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheMode;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
@Getter
public enum BOCorePersistentTableEnum {
    // Persistent Table
    TBL_CATEGORY(
            "TBL_CATEGORY",
            CacheMode.REPLICATED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblCategoryPersistentTableInfo()),
    TBL_FIELD(
            "TBL_FIELD",
            CacheMode.REPLICATED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblFieldPersistentTableInfo()),
    TBL_OBJ(
            "TBL_OBJ_PART",
            CacheMode.PARTITIONED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblObjPersistentTableInfo()),
    TBL_OBJ_ATTRIBUTE(
            "TBL_OBJ_ATTRIBUTE_PART",
            CacheMode.PARTITIONED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblObjAttributePersistentTableInfo()),
    TBL_OBJ_POINT(
            "TBL_OBJ_POINT_PART",
            CacheMode.PARTITIONED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblObjPointPersistentTableInfo()),
    TBL_TSDB_META(
            "TBL_TSDB_META_PART",
            CacheMode.PARTITIONED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new TblTSDBMetaPersistentTableInfo()),
    SA_ADMIN(
            "SA_ADMIN",
            CacheMode.REPLICATED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new SaAdminPersistentTableInfo()),
    USER_RESOURCE(
            "USER_RESOURCE",
            CacheMode.REPLICATED.name(),
            CacheAtomicityMode.ATOMIC.name(),
            new UserResourcePersistentTableInfo());

    BOCorePersistentTableEnum(
            String tableName,
            String cacheMode,
            String atomicityMode,
            PersistentTableInfo persistentTableInfo) {
        this.tableName = tableName;
        this.cacheMode = cacheMode;
        this.atomicityMode = atomicityMode;
        this.persistentTableInfo = persistentTableInfo;
    }

    private final String tableName;
    private final String cacheMode;
    private final String atomicityMode;
    private final PersistentTableInfo persistentTableInfo;
}
