package com.envision.gravity.manager.nebula;

import com.envision.gravity.common.exception.InternalException;
import com.envision.gravity.manager.nebula.pool.NebulaSessionPool;

import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.net.UnknownHostException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description
 */
@Slf4j
public class NebulaClientTest {

    @Test
    public void initOU() throws UnknownHostException {
        while (true) {
            showSpaces();
        }
    }

    private void showSpaces() throws UnknownHostException {
        String nebulaAddress = "ignite8001:9669,nebula8001:9669,influxdb8001:9669";
        String nebulaUser = "root";
        String nebulaPassword = "Envisi0n4321!";

        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(10);
        nebulaPoolConfig.setMinClusterHealthRate(0.5);
        List<HostAddress> addresses = NebulaSessionPool.initNebulaAddress(nebulaAddress);

        NebulaPool pool = new NebulaPool();
        Session session = null;

        // Retry logic (max 2 attempts: initial + 1 retry)
        int maxAttempts = 2;
        int attempt = 0;
        boolean succeeded = false;
        pool.init(addresses, nebulaPoolConfig);

        while (attempt < maxAttempts && !succeeded) {
            attempt++;
            try {

                session = pool.getSession(nebulaUser, nebulaPassword, true);

                String showSpaces = "show spaces;";
                ResultSet showSpacesResp = session.execute(showSpaces);

                if (!showSpacesResp.isSucceeded()) {
                    log.error(
                            "Attempt {}: Show spaces error, cause: {}",
                            attempt,
                            showSpacesResp.getErrorMessage());
                    if (attempt == maxAttempts) {
                        throw new InternalException(
                                String.format(
                                        "Show spaces failed after %d attempts, cause: %s",
                                        maxAttempts, showSpacesResp.getErrorMessage()));
                    }
                    // Wait briefly before retry
                    Thread.sleep(1000);
                    continue;
                }

                log.info(showSpacesResp.toString());
                succeeded = true;

            } catch (Exception e) {
                if (attempt == maxAttempts) {
                    log.info("Operation failed after " + maxAttempts + " attempts", e);
                    throw new RuntimeException(
                            "Operation failed after " + maxAttempts + " attempts", e);
                }
                log.info("Attempt {} failed, will retry. Error: {}", attempt, e.getMessage());
                try {
                    Thread.sleep(1000); // Wait before retry
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } finally {
                if (succeeded || attempt == maxAttempts) {
                    if (session != null) {
                        session.release();
                    }
                    pool.close();
                }
            }
        }
    }
}
