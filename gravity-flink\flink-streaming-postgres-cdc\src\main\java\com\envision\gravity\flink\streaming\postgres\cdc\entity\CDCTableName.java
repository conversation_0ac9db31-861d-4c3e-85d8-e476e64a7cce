package com.envision.gravity.flink.streaming.postgres.cdc.entity;

import com.envision.gravity.flink.streaming.postgres.cdc.side.SideOutputs;

import java.util.Optional;


import lombok.Getter;
import org.apache.flink.util.OutputTag;

/** <AUTHOR> 2024/6/26 */
@Getter
public enum CDCTableName {
    TBL_BO_MODEL("tbl_bo_model", SideOutputs.REFRESH_TAG),
    TBL_COMPONENT("tbl_component", SideOutputs.REFRESH_TAG),
    TBL_BO_MODEL_COMP("tbl_bo_model_comp", SideOutputs.REFRESH_TAG),
    TBL_COMPONENT_PREF("tbl_component_pref", SideOutputs.REFRESH_TAG),
    TBL_PREF("tbl_pref", SideOutputs.REFRESH_TAG),
    TBL_BO("tbl_bo", SideOutputs.REFRESH_TAG);

    private final String name;
    private final OutputTag<ParsedCdcRecord> outputTag;

    CDCTableName(String name, OutputTag<ParsedCdcRecord> outputTag) {
        this.name = name;
        this.outputTag = outputTag;
    }

    public static Optional<CDCTableName> find(String expr) {
        for (CDCTableName t : CDCTableName.values()) {
            if (expr.equalsIgnoreCase(t.getName())) {
                return Optional.of(t);
            }
        }

        return Optional.empty();
    }
}
