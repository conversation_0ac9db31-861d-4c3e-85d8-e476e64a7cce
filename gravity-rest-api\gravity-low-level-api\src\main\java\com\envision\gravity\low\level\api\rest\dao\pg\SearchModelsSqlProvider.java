package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.vo.search.Sorter;

import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/03/14 18:35 @Description: */
public class SearchModelsSqlProvider {

    public String selectModelPathByModelIds(List<String> modelIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("model_id");
        sql.SELECT("model_path");
        sql.FROM(orgId + ".model_detail_origin");
        List<String> replaceModelIds = new ArrayList<>();
        for (String modelId : modelIds) {
            replaceModelIds.add("'" + modelId + "'");
        }
        sql.WHERE("model_id in (" + String.join(", ", replaceModelIds) + ")");
        return sql.toString();
    }

    public String selectByExpression(
            String expression, int limit, int offset, List<Sorter> sorters, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("model_id");
        sql.SELECT("model_display_name");
        sql.SELECT("description");
        sql.SELECT("org_id");
        sql.SELECT("model_path");
        sql.SELECT("model_tags");
        sql.SELECT("properties");
        sql.SELECT("model_created_time");
        sql.SELECT("model_created_user");
        sql.SELECT("model_modified_time");
        sql.SELECT("model_modified_user");
        sql.FROM(orgId + ".model_detail_origin");
        if (!expression.isEmpty()) {
            sql.WHERE(expression);
        }
        StringBuilder order = new StringBuilder();
        for (int i = 0; i < sorters.size(); i++) {
            Sorter sorter = sorters.get(i);
            order.append(sorter.getField()).append(" ").append(sorter.getOrder());
            if (i < sorters.size() - 1) {
                order.append(", ");
            }
        }
        if (!order.toString().isEmpty()) {
            sql.ORDER_BY(order.toString());
        }
        sql.LIMIT(limit);
        sql.OFFSET(offset);
        return sql.toString();
    }

    public String selectByExpressionNotWithPagination(String expression, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("model_id");
        sql.SELECT("model_display_name");
        sql.SELECT("description");
        sql.SELECT("org_id");
        sql.SELECT("model_path");
        sql.SELECT("model_tags");
        sql.SELECT("properties");
        sql.SELECT("model_created_time");
        sql.SELECT("model_created_user");
        sql.SELECT("model_modified_time");
        sql.SELECT("model_modified_user");
        sql.FROM(orgId + ".model_detail_origin");
        if (!expression.isEmpty()) {
            sql.WHERE(expression);
        }
        return sql.toString();
    }

    public String countByExpression(String expression, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(*)");
        sql.FROM(orgId + ".model_detail_origin");
        if (!expression.isEmpty()) {
            sql.WHERE(expression);
        }
        return sql.toString();
    }
}
