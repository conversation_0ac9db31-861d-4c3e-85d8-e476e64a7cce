package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Flink Job Submitter Factory for ReCalc Batch Jobs Provides unified interface and delegates to
 * appropriate implementation
 *
 * <AUTHOR>
 */
public class FlinkJobSubmitter {

    private static final Logger logger = LoggerFactory.getLogger(FlinkJobSubmitter.class);

    private final FlinkJobSubmitterInterface delegate;

    public FlinkJobSubmitter() {
        logger.info("Using FlinkJobSubmitterByRest for pre-uploaded JAR");
        this.delegate = new FlinkJobSubmitterByRest();
    }

    /** Submit ReCalc batch job */
    public String submitReCalcBatchJob(TblCalcJobInfo jobInfo) throws Exception {
        return delegate.submitReCalcBatchJob(jobInfo);
    }

    /** Check if Flink cluster is available */
    public boolean isClusterAvailable() {
        return delegate.isClusterAvailable();
    }

    /** Close resources */
    public void close() {
        delegate.close();
    }

    /** Common interface for both implementations */
    public interface FlinkJobSubmitterInterface {
        String submitReCalcBatchJob(TblCalcJobInfo jobInfo) throws Exception;

        boolean isClusterAvailable();

        void close();
    }
}
