package com.envision.gravity.common.service.id;

import com.envision.gravity.common.enums.IDType;
import com.envision.gravity.common.exception.BadRequestException;
import com.envision.gravity.common.exception.InitException;
import com.envision.gravity.common.service.id.segment.SegmentIDGenImpl;
import com.envision.gravity.common.service.id.segment.repository.IDAllocRepositoryIgniteClientImpl;
import com.envision.gravity.common.service.id.segment.repository.IDAllocRepositoryIgniteImpl;
import com.envision.gravity.common.util.Base36Converter;
import com.envision.gravity.common.util.Base62Converter;

import java.util.ArrayList;
import java.util.List;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.IgniteClient;

/**
 * <AUTHOR>
 * @date 2024/3/13
 * @description
 */
@Slf4j
public class IDService {
    private static volatile IDService idService;
    private IDGen idGen;
    /** GRAVITY_ID_ALLOC cache name. */
    public static final String GRAVITY_ID_ALLOC_CACHE_NAME = "ID_ALLOC";

    private IDService() {}

    public static IDService getInstance(Ignite ignite) {
        if (idService == null) {
            synchronized (IDService.class) {
                if (idService == null) {
                    try {
                        idService = new IDService();
                        IgniteCache<Object, Object> idAllocCache =
                                ignite.cache(GRAVITY_ID_ALLOC_CACHE_NAME);
                        if (idAllocCache == null) {
                            log.error("Cache [ID_ALLOC] does not exist!");
                            throw new InitException("Cache [ID_ALLOC] does not exist!");
                        }
                        idService.init(idAllocCache);
                    } catch (Exception e) {
                        log.error("Failed to initialize IDService", e);
                        throw new InitException("Failed to initialize IDService", e);
                    }
                }
            }
        }
        return idService;
    }

    public static IDService getInstance(IgniteClient igniteClient) {
        if (idService == null) {
            synchronized (IDService.class) {
                if (idService == null) {
                    try {
                        idService = new IDService();
                        ClientCache<Object, Object> idAllocCache =
                                igniteClient.cache(GRAVITY_ID_ALLOC_CACHE_NAME);
                        if (idAllocCache == null) {
                            log.error("Cache [ID_ALLOC] does not exist!");
                            throw new InitException("Cache [ID_ALLOC] does not exist!");
                        }
                        idService.init(idAllocCache);
                    } catch (Exception e) {
                        log.error("Failed to initialize IDService", e);
                        throw new InitException("Failed to initialize IDService", e);
                    }
                }
            }
        }
        return idService;
    }

    private void init(IgniteCache<?, ?> igniteCache) {
        idGen = new SegmentIDGenImpl();
        ((SegmentIDGenImpl) idGen)
                .setIdAllocRepository(new IDAllocRepositoryIgniteImpl(igniteCache));
    }

    private void init(ClientCache<?, ?> igniteClientCache) {
        idGen = new SegmentIDGenImpl();
        ((SegmentIDGenImpl) idGen)
                .setIdAllocRepository(new IDAllocRepositoryIgniteClientImpl(igniteClientCache));
    }

    /**
     * Get id with id type
     *
     * @param count Number of generated IDs
     * @param idType {@link IDType}
     * @return result
     */
    public List<String> getIds(int count, IDType idType) {
        if (count <= 0) {
            log.error("Count must be greater than 0!");
            throw new BadRequestException("Count must be greater than 0!");
        }

        if (idType == null) {
            log.error("IDType is null!");
            throw new BadRequestException("IdType must not be null!");
        }

        List<String> result = new ArrayList<>(count);
        if (IDType.COMMON_ID == idType) {
            String idPrefix = idGen.getIdPrefix(idType);
            String dateTimeToBase36 = Base36Converter.convertDateTimeToBase36();
            for (int i = 0; i < count; i++) {
                String genId = idGen.getGeneratedId(idType);
                String formattedId = String.format("%s%s%s", idPrefix, dateTimeToBase36, genId);
                result.add(formattedId);
            }
        } else if (IDType.OBJECT_ID == idType) {
            for (int i = 0; i < count; i++) {
                String genId = idGen.getGeneratedId(idType);
                String dateToBase62 = Base62Converter.convertDateToBase62();
                String formattedId = String.format("%s_%s", dateToBase62, genId);
                result.add(formattedId);
            }
        }

        return result;
    }
}
