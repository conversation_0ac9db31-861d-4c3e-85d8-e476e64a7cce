package com.envision.gravity.low.level.api.rest.enums;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description
 */
public enum SyncSourceEnum {
    BO,
    BUSINESS_OBJECT,
    DATA_OBJECT,
    BUSINESS_OBJECT_RELATION,
    BO_RELATION;

    public static Optional<SyncSourceEnum> find(String expr) {
        for (SyncSourceEnum t : SyncSourceEnum.values()) {
            if (expr.equalsIgnoreCase(t.name())) {
                return Optional.of(t);
            }
        }

        return Optional.empty();
    }
}
