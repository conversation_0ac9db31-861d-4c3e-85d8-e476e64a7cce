package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StandardFieldLib {

    private String rawFieldId;

    private int fieldIndex;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
