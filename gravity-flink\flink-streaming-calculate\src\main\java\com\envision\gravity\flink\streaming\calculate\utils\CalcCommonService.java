package com.envision.gravity.flink.streaming.calculate.utils;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.*;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


import com.univers.business.object.calc.dto.ExpressionParserInput;
import com.univers.business.object.calc.dto.ExpressionParserOutput;
import com.univers.business.object.calc.request.ParseExpressionRequest;
import com.univers.business.object.calc.response.ParseExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcCommonService {

    private static final Logger logger = LoggerFactory.getLogger(CalcCommonService.class);

    private static final String TBL_PROPERTY_UPSTREAM_RULE = "TBL_PROPERTY_UPSTREAM_RULE";

    private static volatile CalcCommonService uniqueInstance;

    private final ModelMetaQueryHandler modelMetaQueryHandler;

    public static CalcCommonService getInstance() {
        if (uniqueInstance == null) {
            synchronized (CalcCommonService.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new CalcCommonService();
                }
            }
        }
        return uniqueInstance;
    }

    private CalcCommonService() {
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
    }

    /**
     * 将 model2AssetInfoMap 按照 modelId 维度进行拆分
     *
     * @param model2AssetInfoMap 待拆分的映射，key 是 modelId，value 是 assetId 到 AssetInfo 的映射
     * @return 拆分后的结果列表，每个元素是一个只包含一个 modelId 的映射
     */
    public List<Map<String, Map<String, AssetInfo>>> splitModelAssetInfoMap(
            Map<String, Map<String, AssetInfo>> model2AssetInfoMap) {

        List<Map<String, Map<String, AssetInfo>>> result = new ArrayList<>();

        // 获取拆分大小配置
        int splitSize = CalcLionConfig.getCalcAssetSplitSize();

        // 遍历每个 modelId
        for (Map.Entry<String, Map<String, AssetInfo>> entry : model2AssetInfoMap.entrySet()) {
            String modelId = entry.getKey();
            Map<String, AssetInfo> assetInfoMap = entry.getValue();

            // 如果当前 modelId 下的 assetInfoMap 大小不超过拆分大小，直接添加到结果中
            if (assetInfoMap.size() <= splitSize) {
                Map<String, Map<String, AssetInfo>> singleModelMap = new HashMap<>();
                singleModelMap.put(modelId, assetInfoMap);
                result.add(singleModelMap);
                continue;
            }

            // 需要拆分的情况
            List<Map<String, AssetInfo>> splitAssetInfoMaps =
                    splitAssetInfoMap(assetInfoMap, splitSize);

            // 为每个拆分后的 assetInfoMap 创建一个新的 model2AssetInfoMap
            for (Map<String, AssetInfo> splitAssetInfoMap : splitAssetInfoMaps) {
                Map<String, Map<String, AssetInfo>> singleModelMap = new HashMap<>();
                singleModelMap.put(modelId, splitAssetInfoMap);
                result.add(singleModelMap);
            }
        }

        return result;
    }

    /**
     * 将 assetInfoMap 按照指定大小进行拆分
     *
     * @param assetInfoMap 待拆分的映射，key 是 assetId，value 是 AssetInfo
     * @param splitSize 拆分大小
     * @return 拆分后的结果列表
     */
    private static List<Map<String, AssetInfo>> splitAssetInfoMap(
            Map<String, AssetInfo> assetInfoMap, int splitSize) {

        List<Map<String, AssetInfo>> result = new ArrayList<>();
        Map<String, AssetInfo> currentMap = new HashMap<>();
        int currentSize = 0;

        for (Map.Entry<String, AssetInfo> entry : assetInfoMap.entrySet()) {
            // 如果当前 map 已满，添加到结果并创建新 map
            if (currentSize >= splitSize) {
                result.add(currentMap);
                currentMap = new HashMap<>();
                currentSize = 0;
            }

            // 添加当前元素到 map
            currentMap.put(entry.getKey(), entry.getValue());
            currentSize++;
        }

        // 添加最后一个 map（如果有元素）
        if (!currentMap.isEmpty()) {
            result.add(currentMap);
        }

        return result;
    }

    /** ✅ 查询目标资产 modelId => <assetId, info> */
    public Map<String, Map<String, AssetInfo>> queryTargetAssets(
            String orgId, CalcJobMetaInfo calcjobMetaInfo) {
        Set<String> targetModelIds = calcjobMetaInfo.getTargetModelIds();
        Set<String> modelIdSet = new HashSet<>(targetModelIds);

        // ✅ 使用分页查询获取所有资产信息
        int pageSize = CalcLionConfig.getCalcQueryAssetPageSize();
        Map<String, List<AssetInfo>> assetInfosByModel =
                modelMetaQueryHandler.getAssetInfoByModelIdsWithPagination(
                        orgId, modelIdSet, pageSize);

        // 将按模型分组的资产信息合并为资产ID列表
        Set<AssetInfo> allTargetAssetInfos =
                assetInfosByModel.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());

        Set<String> srcAssetIds =
                allTargetAssetInfos.stream()
                        .map(AssetInfo::getSystemId)
                        .collect(Collectors.toSet());
        Map<String, AssetInfo> srcAssetInfoMap =
                this.modelMetaQueryHandler.getAssetInfos(orgId, srcAssetIds);

        // Filter target asset by rule's srcCategory
        Map<String, Map<String, AssetInfo>> model2TargetAssetInfoMap = new HashMap<>();
        String srcCategory = calcjobMetaInfo.getTargetPropertyMeta().getSrcCategoryId();

        for (AssetInfo targetAssetInfo : allTargetAssetInfos) {
            Map<String, AssetInfo> targetAssetInfoMap =
                    model2TargetAssetInfoMap.computeIfAbsent(
                            targetAssetInfo.getModelId(), k -> new HashMap<>());
            AssetInfo srcAssetInfo = srcAssetInfoMap.get(targetAssetInfo.getSystemId());
            if (srcCategory.equals(srcAssetInfo.getCategoryId())) {
                targetAssetInfoMap.put(targetAssetInfo.getAssetId(), targetAssetInfo);
            }
        }

        int targetAssetNums = model2TargetAssetInfoMap.values().stream().mapToInt(Map::size).sum();
        logger.info(
                "Queried {} target assets for job: {} with models: {}",
                targetAssetNums,
                calcjobMetaInfo.getJobId(),
                targetModelIds);

        return model2TargetAssetInfoMap;
    }

    /**
     * 通用分页查询方法
     *
     * @param orgId 组织ID
     * @param processor 结果处理器
     */
    public <O> void queryTblUpstreamRuleWithPagination(
            String orgId, TblUpstreamPageResultProcessor<O> processor, List<O> collector) {
        int offset = 0;
        boolean hasMore = true;
        int totalProcessed = 0;
        int pageSize = CalcLionConfig.getCalcMetaLoadPageSize();

        while (hasMore) {
            String sql =
                    String.format(
                            "SELECT PREF_RULE_ID, TARGET_CATEGORY, TARGET_COMP_ID, TARGET_PREF_ID, SRC_CATEGORY, EXPRESSION, CALC_TYPE "
                                    + "FROM %s.%s WHERE CALC_TYPE = 0"
                                    + "ORDER BY CREATED_TIME "
                                    + "LIMIT %d OFFSET %d",
                            orgId, TBL_PROPERTY_UPSTREAM_RULE, pageSize, offset);
            List<List<?>> results = IgniteUtil.query(orgId, sql);
            if (GTCommonUtils.emptyCollection(results)) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
                continue;
            }

            // compId + prefId => metas
            Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropMetaMap =
                    new HashMap<>(results.size());
            for (List<?> row : results) {
                String prefRuleId = (String) row.get(0);
                String targetCategoryId = (String) row.get(1);
                String targetCompId = (String) row.get(2);
                String targetPrefId = (String) row.get(3);
                String srcCategoryId = (String) row.get(4);
                String expression = (String) row.get(5);
                Integer calcType = (Integer) row.get(6);

                targetPropMetaMap
                        .computeIfAbsent(
                                new PropertyId(targetCompId, targetPrefId), k -> new ArrayList<>())
                        .add(
                                BaseCalcPropertyMeta.builder()
                                        .prefRuleId(prefRuleId)
                                        .targetCategoryId(targetCategoryId)
                                        .targetCompId(targetCompId)
                                        .targetPrefId(targetPrefId)
                                        .srcCategoryId(srcCategoryId)
                                        .expression(expression)
                                        .calcType(calcType)
                                        .build());
                totalProcessed++;
            }

            // Post process
            if (processor != null && collector != null) {
                processor.process(orgId, targetPropMetaMap, collector);
            }

            offset += pageSize;

            // If the number of results is less than the page size, we've reached the last page
            if (results.size() < pageSize) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
            }
        }
    }

    /**
     * Filter targetPropertyMap's records with no model definition by propertyInfos
     *
     * @param orgId
     * @param targetPropertyMap
     * @return
     */
    public Map<PropertyId, List<BaseCalcPropertyMeta>> filterNotDefinedProperty(
            String orgId,
            Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropertyMap,
            Map<PropertyId, PropertyInfo> propertyInfos) {
        Map<PropertyId, List<BaseCalcPropertyMeta>> notDefinedTargetPropertyMap = new HashMap<>(1);
        for (Map.Entry<PropertyId, List<BaseCalcPropertyMeta>> targetMetaEntry :
                targetPropertyMap.entrySet()) {
            boolean isNotDefined = true;
            PropertyId targetMetaId = targetMetaEntry.getKey();
            for (Map.Entry<PropertyId, PropertyInfo> propInfoEntry : propertyInfos.entrySet()) {
                PropertyId propInfoId = propInfoEntry.getKey();
                if (propInfoId.getCompId().equals(targetMetaId.getCompId())
                        && propInfoId.getPrefId().equals(targetMetaId.getPrefId())) {
                    isNotDefined = false;
                    break;
                }
            }

            if (isNotDefined) {
                notDefinedTargetPropertyMap.put(targetMetaId, targetMetaEntry.getValue());
            }
        }
        return notDefinedTargetPropertyMap;
    }

    public List<CalcPropertyMeta> parseExpr(
            String targetModelId,
            String targetCompId,
            String targetPrefId,
            List<BaseCalcPropertyMeta> baseCalcPropertyMetas) {
        ParseExpressionRequest parseExprReq = new ParseExpressionRequest();
        List<ExpressionParserInput> inputs =
                baseCalcPropertyMetas.stream()
                        .map(
                                tp -> {
                                    Map<String, Object> args = new HashMap<>();
                                    args.put("prefRuleId", tp.getPrefRuleId());
                                    args.put("targetCategoryId", tp.getTargetCategoryId());
                                    args.put("targetCompId", targetCompId);
                                    args.put("targetPrefId", targetPrefId);
                                    args.put("srcCategoryId", tp.getSrcCategoryId());
                                    args.put("expression", tp.getExpression());

                                    return ExpressionParserInput.builder()
                                            .expression(tp.getExpression())
                                            .additionalArgs(args)
                                            .build();
                                })
                        .collect(Collectors.toList());
        parseExprReq.setParseInputs(inputs);

        List<ParseExpressionResponse> parseExprRes = new ArrayList<>();
        try {
            parseExprRes = ExpressionUtil.parseExpression(parseExprReq);
        } catch (Exception e) {
            logger.error(
                    "Parse expression failed, param: {}, error: {}", parseExprReq, e.getMessage());
        }

        return parseExprRes.stream()
                .map(
                        pr -> {
                            ExpressionParserOutput output = pr.getExpressionParsedResult();
                            Map<String, Object> args = output.getAdditionalArgs();

                            List<SrcPrefItem> srcPrefItems =
                                    output.getSrcProperties().stream()
                                            .map(
                                                    sp -> {
                                                        // If the modelId is 'thisModel', use the
                                                        // target
                                                        // modelId
                                                        // current target modelId with 'thisModel'
                                                        // according to isExprUseCurrentModel flag
                                                        String srcModelId =
                                                                CalcConstant.THIS.equals(
                                                                                sp.getModelId())
                                                                        ? targetModelId
                                                                        : sp.getModelId();

                                                        return SrcPrefItem.builder()
                                                                .modelId(srcModelId)
                                                                .prefName(sp.getPropertyName())
                                                                .build();
                                                    })
                                            .collect(Collectors.toList());
                            boolean isExprUseCurrentModel =
                                    isExprUseCurrentModel(targetModelId, srcPrefItems);

                            return CalcPropertyMeta.builder()
                                    .prefRuleId((String) args.get("prefRuleId"))
                                    .targetCategoryId((String) args.get("targetCategoryId"))
                                    .targetCompId(targetCompId)
                                    .targetPrefId(targetPrefId)
                                    .srcCategoryId((String) args.get("srcCategoryId"))
                                    .expression((String) args.get("expression"))
                                    .srcPrefItems(srcPrefItems)
                                    .isDirectMapping(output.getIsDirectMapping())
                                    .isExprUseCurrentModel(isExprUseCurrentModel)
                                    .build();
                        })
                .collect(Collectors.toList());
    }

    private boolean isExprUseCurrentModel(String targetModelId, List<SrcPrefItem> srcPrefItems) {
        for (SrcPrefItem spi : srcPrefItems) {
            if (targetModelId.equals(spi.getModelId())) {
                return true;
            }
        }
        return false;
    }
}
