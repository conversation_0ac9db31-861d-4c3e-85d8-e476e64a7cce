package com.envision.gravity.calculate.api.rest.exception;

public class CalcFailedException extends CalcRuntimeException {
    private static final long serialVersionUID = 4817451827499797361L;

    public CalcFailedException() {
        super();
    }

    public CalcFailedException(String message) {
        super(message);
    }

    public CalcFailedException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public CalcFailedException(Throwable throwable) {
        super(throwable);
    }
}
