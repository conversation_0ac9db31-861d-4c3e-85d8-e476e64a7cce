package com.envision.gravity.low.level.api.rest.common;

import com.envision.gravity.low.level.api.rest.config.GTRestLionConfig;
import com.envision.gravity.low.level.api.rest.util.LionUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/19
 * @description
 */
public class Constants {
    // common
    public static final int MAX_REQUEST_SIZE =
            LionUtil.getIntValue(GTRestLionConfig.MAX_REQUEST_SIZE, 1000);

    // sync BO constants
    // --------------------------------------------------------------------------------
    public static final String DFLT_USER = "gravity";

    public static final String DFLT_TAG_GROUP_ID = "EnOS";

    public static final String DFLT_TAG_SOURCE = "model_management";

    public static final String DFLT_OBJECT_TAG_TYPE = "OBJECT";

    public static final String DFLT_CATEGORY_ID = "Gravity_Common_Category_Id";

    public static final String DFLT_CATEGORY_DISPLAY_NAME = "{\"default\": \"通用category\"}";

    public static final String DFLT_OBJECT_RELATION_GRAPH_TAG_TYPE = "OBJECT_RELATION_GRAPH";

    public static final int TAG_SERVICE_SUCCESS_CODE = 0;
    public static final int GRAVITY_REST_API_SERVICE_SUCCESS_CODE = 0;

    public static final String ATTRIBUTE_FIELD_TYPE = "ATTRIBUTE";

    public static final Set<String> COMMON_SPEC_ATTR =
            new HashSet<>(Arrays.asList("_name", "_timezone", "_description"));

    public static final Set<String> DCM_SPEC_ATTR =
            new HashSet<>(
                    Arrays.asList(
                            "_name",
                            "_timezone",
                            "_description",
                            "activeTime",
                            "assetTreeBusinessInfoTypeField",
                            "certs",
                            "currentStatus",
                            "deviceBusinessInfoTypeField",
                            "deviceKey",
                            "deviceSecret",
                            "encryptDeviceSecret",
                            "firmwareVersion",
                            "flags",
                            "gatewayGraphId",
                            "gwGroupName",
                            "label",
                            "mirrorSource",
                            "nodeType",
                            "offlineTime",
                            "onlineTime",
                            "productKey",
                            "sourceInfo",
                            "sourceType",
                            "stateField",
                            "statusSourceField",
                            "statusUpdateTime",
                            "topoPath",
                            "treeIds"));
}
