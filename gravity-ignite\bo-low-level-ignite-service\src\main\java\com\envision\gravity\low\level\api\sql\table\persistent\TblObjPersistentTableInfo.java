package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblObjPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.TBL_OBJ_PART"
                    + "(\n"
                    + "    SYSTEM_ID            VARCHAR,\n"
                    + "    SYSTEM_DISPLAY_NAME  VARCHAR,\n"
                    + "    CATEGORY_ID          VARCHAR,\n"
                    + "    CREATED_TIME         TIMESTAMP,\n"
                    + "    CREATED_USER         VARCHAR,\n"
                    + "    MODIFIED_TIME        TIMESTAMP,\n"
                    + "    point_last_process_time        TIMESTAMP,\n"
                    + "    MODIFIED_USER        VARCHAR,\n"
                    + "    \"DCM__DEVICEKEY__VARCHAR\"        VARCHAR,\n"
                    + "    PRIMARY KEY (SYSTEM_ID)\n"
                    + "    )WITH \"template=%s,AFFINITY_KEY=SYSTEM_ID,BACKUPS=1,ATOMICITY=%s,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Arrays.asList(
                        "CREATE INDEX IF NOT EXISTS IDX_TBL_OBJ_SYSTEM_ID ON %s.TBL_OBJ_PART (SYSTEM_ID);",
                        "CREATE INDEX IF NOT EXISTS IDX_TBL_OBJ_CATEGORY_ID ON %s.TBL_OBJ_PART (CATEGORY_ID);",
                        "CREATE INDEX IF NOT EXISTS TBL_OBJ_PART_DEVICE_KEY_SYSTEM_ID_ASC_IDX ON %s.TBL_OBJ_PART (DCM__DEVICEKEY__VARCHAR, SYSTEM_ID);");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
