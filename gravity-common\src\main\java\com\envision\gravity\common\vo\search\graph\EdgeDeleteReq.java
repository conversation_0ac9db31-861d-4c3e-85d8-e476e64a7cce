package com.envision.gravity.common.vo.search.graph;

import com.envision.gravity.common.vo.topo.EdgeDeleteParam;

import javax.validation.Valid;

import java.util.List;


import lombok.Data;

/** @Author: qi.jiang2 @Date: 2024/05/10 16:48 @Description: */
@Data
public class EdgeDeleteReq {

    @Valid private List<EdgeDeleteParam> edges;

    private boolean cascade;

    private boolean syncRequest = false;
}
