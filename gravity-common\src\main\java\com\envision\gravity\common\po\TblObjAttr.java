package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/15
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblObjAttr {
    private String systemId;
    private Integer fieldIndex;
    private Boolean valueBool;
    private String valueString;
    private Long valueLong;
    private Double valueDouble;
    private String valueJson;
    private Timestamp createdTime;
    private String createdUser;
    private Timestamp modifiedTime;
    private String modifiedUser;
}
