package com.envision.gravity.flink.streaming.bo.sync.sink;

import com.envision.gravity.common.definition.bo.BODefinition;
import com.envision.gravity.common.definition.graph.BORelationDataDefinition;
import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.sync.SyncBORelationsReq;
import com.envision.gravity.common.vo.sync.SyncBORelationsResp;
import com.envision.gravity.common.vo.sync.SyncBOsReq;
import com.envision.gravity.common.vo.sync.SyncBOsResp;
import com.envision.gravity.flink.streaming.bo.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.bo.sync.config.PoseidonConfig;
import com.envision.gravity.flink.streaming.bo.sync.entity.Msg;
import com.envision.gravity.flink.streaming.bo.sync.entity.PayloadKey;
import com.envision.gravity.flink.streaming.bo.sync.entity.SourceKey;
import com.envision.gravity.flink.streaming.bo.sync.enums.OPEnum;
import com.envision.gravity.flink.streaming.bo.sync.enums.SourceEnum;
import com.envision.gravity.flink.streaming.bo.sync.sink.req.DeleteBORelationsReq;
import com.envision.gravity.flink.streaming.bo.sync.sink.req.DeleteBOsReq;
import com.envision.gravity.flink.streaming.bo.sync.sink.req.UpsertBORelationsReq;
import com.envision.gravity.flink.streaming.bo.sync.sink.req.UpsertBOsReq;
import com.envision.gravity.flink.streaming.bo.sync.util.RetryUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import com.envision.apim.poseidon.core.Poseidon;
import com.envision.apim.poseidon.exception.PoseidonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;

/**
 * <AUTHOR>
 * @date 2025/3/2
 * @description
 */
@Slf4j
public class GravityRestApiSink
        extends RichSinkFunction<Map<PayloadKey, Map<SourceKey, List<Msg>>>> {

    private static final long serialVersionUID = -2750509736083977815L;
    private static final String GRAVITY_SERVICE_URL = LionConfig.getGravityRestApiUrl();
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                    .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    public void open(Configuration parameters) {
        try {
            RetryUtil.initKafkaProducer();
        } catch (Exception e) {
            log.error("Create resource error.", e);
        }
    }

    @Override
    public void close() throws Exception {
        try {
            RetryUtil.closeKafkaProducer();
        } catch (Exception e) {
            log.error("Close resource error.", e);
        }
    }

    @Override
    public void invoke(Map<PayloadKey, Map<SourceKey, List<Msg>>> value, Context context) {
        if (value == null || value.isEmpty()) {
            return;
        }

        int currentIndex = 0;

        while (true) {
            Map<PayloadKey, List<Msg>> messagesAtIndex = getMessagesByIndex(value, currentIndex);
            if (messagesAtIndex.isEmpty()) {
                break;
            }

            handleMessagesAtIndex(messagesAtIndex);

            currentIndex++;
        }
    }

    /**
     * Get messages by index from nested map, maintaining PayloadKey mapping
     *
     * @param messageMap nested map containing messages
     * @param index index of message to retrieve from each list
     * @return map of PayloadKey to messages at the specified index, or empty map if no messages
     *     found
     */
    private Map<PayloadKey, List<Msg>> getMessagesByIndex(
            Map<PayloadKey, Map<SourceKey, List<Msg>>> messageMap, int index) {
        Map<PayloadKey, List<Msg>> result = new HashMap<>();
        boolean hasMessage = false;

        // Iterate through outer map
        for (Map.Entry<PayloadKey, Map<SourceKey, List<Msg>>> entry : messageMap.entrySet()) {
            PayloadKey payloadKey = entry.getKey();
            List<Msg> messagesForKey = new ArrayList<>();

            // Iterate through inner map
            for (List<Msg> msgList : entry.getValue().values()) {
                // Check if index is valid
                if (msgList != null && index < msgList.size()) {
                    messagesForKey.add(msgList.get(index));
                    hasMessage = true;
                }
            }

            // Only add to result if we found messages for this PayloadKey
            if (!messagesForKey.isEmpty()) {
                result.put(payloadKey, messagesForKey);
            }
        }

        return hasMessage ? result : Collections.emptyMap();
    }

    private void handleMessagesAtIndex(Map<PayloadKey, List<Msg>> messagesAtIndex) {
        messagesAtIndex.forEach(
                (payloadKey, msgList) -> {
                    String syncId = payloadKey.getSyncId();
                    String orgId = payloadKey.getOrgId();
                    // key:source, value: key:op, value: msg
                    Map<String, Map<String, List<Msg>>> sourceOPMap =
                            msgList.stream()
                                    .filter(
                                            msg -> {
                                                String source = msg.getValue().getSource();
                                                String op = msg.getValue().getOp();
                                                return StringUtils.isNotEmpty(source)
                                                        && StringUtils.isNotEmpty(op);
                                            })
                                    .collect(
                                            Collectors.groupingBy(
                                                    msg -> msg.getValue().getSource().toUpperCase(),
                                                    Collectors.groupingBy(
                                                            msg ->
                                                                    msg.getValue()
                                                                            .getOp()
                                                                            .toUpperCase(),
                                                            Collectors.mapping(
                                                                    msg -> msg,
                                                                    Collectors.toList()))));

                    processBOMsgList(syncId, orgId, sourceOPMap);

                    processBORelationMsgList(syncId, orgId, sourceOPMap);
                });
    }

    private void processBOMsgList(
            String syncId, String orgId, Map<String, Map<String, List<Msg>>> sourceOPMap) {
        Optional.ofNullable(sourceOPMap.get(SourceEnum.BO.name()))
                .ifPresent(
                        boMsgList -> {
                            List<Msg> upsertOPList = boMsgList.get(OPEnum.UPSERT.name());
                            List<Msg> delOPList = boMsgList.get(OPEnum.DELETE.name());

                            if (upsertOPList != null && !upsertOPList.isEmpty()) {
                                List<SyncBOsReq> bos =
                                        upsertOPList.stream()
                                                .map(
                                                        msg ->
                                                                SyncBOsReq.builder()
                                                                        .boDefinition(
                                                                                (BODefinition)
                                                                                        msg.getValue()
                                                                                                .getPayload())
                                                                        .syncModelFirst(
                                                                                msg.getValue()
                                                                                        .isSyncModelFirst())
                                                                        .forceUpdate(
                                                                                msg.getValue()
                                                                                        .isForceUpdate())
                                                                        .build())
                                                .collect(Collectors.toList());
                                upsertBOs(syncId, orgId, bos, upsertOPList);
                            }

                            if (delOPList != null && !delOPList.isEmpty()) {
                                List<String> assetIdList =
                                        delOPList.stream()
                                                .filter(
                                                        msg ->
                                                                ((BODefinition)
                                                                                                msg.getValue()
                                                                                                        .getPayload())
                                                                                        .getBusinessObject()
                                                                                != null
                                                                        && StringUtils.isNotEmpty(
                                                                                ((BODefinition)
                                                                                                msg.getValue()
                                                                                                        .getPayload())
                                                                                        .getBusinessObject()
                                                                                        .getAssetId()))
                                                .map(
                                                        msg ->
                                                                ((BODefinition)
                                                                                msg.getValue()
                                                                                        .getPayload())
                                                                        .getBusinessObject()
                                                                        .getAssetId())
                                                .collect(Collectors.toList());
                                delBOs(syncId, orgId, assetIdList, delOPList);
                            }
                        });
    }

    private void processBORelationMsgList(
            String syncId, String orgId, Map<String, Map<String, List<Msg>>> sourceOPMap) {
        Optional.ofNullable(sourceOPMap.get(SourceEnum.BO_RELATION.name()))
                .ifPresent(
                        boRelationMsgList -> {
                            List<Msg> upsertOPList = boRelationMsgList.get(OPEnum.UPSERT.name());
                            List<Msg> delOPList = boRelationMsgList.get(OPEnum.DELETE.name());

                            if (upsertOPList != null && !upsertOPList.isEmpty()) {
                                List<SyncBORelationsReq> boRelationsReqs =
                                        upsertOPList.stream()
                                                .map(
                                                        msg ->
                                                                SyncBORelationsReq.builder()
                                                                        .boRelationDataDefinition(
                                                                                (BORelationDataDefinition)
                                                                                        msg.getValue()
                                                                                                .getPayload())
                                                                        .forceUpdate(
                                                                                msg.getValue()
                                                                                        .isForceUpdate())
                                                                        .build())
                                                .collect(Collectors.toList());
                                upsertBORelations(syncId, orgId, boRelationsReqs, upsertOPList);
                            }

                            if (delOPList != null && !delOPList.isEmpty()) {
                                List<String> graphIdList =
                                        delOPList.stream()
                                                .filter(
                                                        msg ->
                                                                StringUtils.isNotEmpty(
                                                                        ((BORelationDataDefinition)
                                                                                        msg.getValue()
                                                                                                .getPayload())
                                                                                .getGraphId()))
                                                .map(
                                                        msg ->
                                                                ((BORelationDataDefinition)
                                                                                msg.getValue()
                                                                                        .getPayload())
                                                                        .getGraphId())
                                                .collect(Collectors.toList());
                                delBORelations(syncId, orgId, graphIdList, delOPList);
                            }
                        });
    }

    private void upsertBOs(
            String syncId, String orgId, List<SyncBOsReq> bos, List<Msg> upsertOPList) {
        if (StringUtils.isBlank(syncId) || StringUtils.isBlank(orgId)) {
            log.warn("Sync UPSERT BOs, but syncId or orgId is blank, return.");
            return;
        }

        if (bos.isEmpty() || upsertOPList.isEmpty()) {
            log.warn("NoNeedUpsertBOList, syncId: {}, orgId: {}", syncId, orgId);
            return;
        }

        Set<String> assetIdList =
                upsertOPList.stream()
                        .map(Msg::getKey)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

        log.info(
                "Start sync UPSERT BOs, syncId: {}, orgId: {}, requestAssetCount: {}, requestAssetIdList: {}.",
                syncId,
                orgId,
                assetIdList.size(),
                assetIdList);
        try {
            long syncStartTime = System.currentTimeMillis();
            // Build and send request
            UpsertBOsReq req =
                    UpsertBOsReq.Builder.newBuilder()
                            .setSyncId(syncId)
                            .setOrgId(orgId)
                            .setBOs(bos)
                            .build();

            ResponseResult<?> response =
                    Poseidon.config(PoseidonConfig.POSEIDON_CONFIG)
                            .url(GRAVITY_SERVICE_URL)
                            .getResponse(req, ResponseResult.class);
            long syncEndTime = System.currentTimeMillis();

            // Log response result
            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode()) {
                log.info(
                        "Invoke sync UPSERT BOs success, total time cost: {}ms, response data: {}.",
                        (syncEndTime - syncStartTime),
                        response.getData());
            } else {
                log.error(
                        "Invoke sync UPSERT BOs error, total time cost: {}ms, response code: {}, response message: {}.",
                        (syncEndTime - syncStartTime),
                        response.getCode(),
                        response.getMessage());
            }

            // Handle Sync Upsert Failed BOs
            handleFailedMessages(
                    response, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO);
        } catch (PoseidonException e) {
            log.error(
                    "Failed to request gravity service to sync UPSERT BOs for syncId: {}, orgId: {}, "
                            + "assetCount: {}, assetIdList: {}.",
                    syncId,
                    orgId,
                    assetIdList.size(),
                    assetIdList,
                    e);
            handleFailedMessages(null, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO);
        } catch (Exception e) {
            log.error(
                    "Unexpected error during sync UPSERT BOs for syncId: {}, orgId: {}, "
                            + "assetCount: {}, assetIdList: {}.",
                    syncId,
                    orgId,
                    assetIdList.size(),
                    assetIdList,
                    e);
            handleFailedMessages(null, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO);
        }
    }

    private void delBOs(
            String syncId, String orgId, List<String> assetIdList, List<Msg> delOPList) {
        log.info(
                "Start sync DELETE BOs, syncId: {}, orgId: {}, requestAssetCount: {}, requestAssetIdList: {}.",
                syncId,
                orgId,
                assetIdList.size(),
                assetIdList);
        try {
            long syncStartTime = System.currentTimeMillis();
            DeleteBOsReq req =
                    DeleteBOsReq.Builder.newBuilder()
                            .setSyncId(syncId)
                            .setOrgId(orgId)
                            .setAssetIdList(assetIdList)
                            .build();

            ResponseResult<?> response =
                    Poseidon.config(PoseidonConfig.POSEIDON_CONFIG)
                            .url(GRAVITY_SERVICE_URL)
                            .getResponse(req, ResponseResult.class);
            long syncEndTime = System.currentTimeMillis();

            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode()) {
                log.info(
                        "Invoke sync DELETE BOs success, total time cost: {}ms, response data: {}.",
                        (syncEndTime - syncStartTime),
                        response.getData());
            } else {
                log.error(
                        "Invoke sync DELETE BOs error, total time cost: {}ms, response code: {}, response message: {}.",
                        (syncEndTime - syncStartTime),
                        response.getCode(),
                        response.getMessage());

                handleFailedMessages(null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO);
            }
        } catch (PoseidonException e) {
            log.error(
                    "Failed to request gravity service to sync DELETE BOs for syncId: {}, orgId: {}, "
                            + "assetCount: {}, assetIdList: {}.",
                    syncId,
                    orgId,
                    assetIdList.size(),
                    assetIdList,
                    e);
            handleFailedMessages(null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO);
        } catch (Exception ex) {
            log.error(
                    "Unexpected error during sync DELETE BOs for syncId: {}, orgId: {}, "
                            + "assetCount: {}, assetIdList: {}.",
                    syncId,
                    orgId,
                    assetIdList.size(),
                    assetIdList,
                    ex);
            handleFailedMessages(null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO);
        }
    }

    private void upsertBORelations(
            String syncId,
            String orgId,
            List<SyncBORelationsReq> boRelationsReqs,
            List<Msg> upsertOPList) {

        Set<String> graphIdList =
                upsertOPList.stream()
                        .map(Msg::getKey)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
        log.info(
                "Start sync UPSERT graphs, syncId: {}, orgId: {}, requestGraphCount: {}, requestGraphIdList: {}.",
                syncId,
                orgId,
                graphIdList.size(),
                graphIdList);

        try {
            long syncStartTime = System.currentTimeMillis();
            UpsertBORelationsReq req =
                    UpsertBORelationsReq.Builder.newBuilder()
                            .setSyncId(syncId)
                            .setOrgId(orgId)
                            .setBORelations(boRelationsReqs)
                            .build();

            ResponseResult<?> response =
                    Poseidon.config(PoseidonConfig.POSEIDON_CONFIG)
                            .url(GRAVITY_SERVICE_URL)
                            .getResponse(req, ResponseResult.class);
            long syncEndTime = System.currentTimeMillis();

            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode()) {
                log.info(
                        "Invoke sync UPSERT graphs success, total time cost: {}ms, response data: {}.",
                        (syncEndTime - syncStartTime),
                        response.getData());
            } else {
                log.error(
                        "Invoke sync UPSERT graphs error, total time cost: {}ms, response code: {}, response message: {}.",
                        (syncEndTime - syncStartTime),
                        response.getCode(),
                        response.getMessage());
            }

            handleFailedMessages(
                    response, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO_RELATION);
        } catch (PoseidonException e) {
            log.error(
                    "Failed to request gravity service to sync UPSERT graphs for syncId: {}, orgId: {}, "
                            + "graphCount: {}, graphIdList: {}.",
                    syncId,
                    orgId,
                    graphIdList.size(),
                    graphIdList,
                    e);
            handleFailedMessages(
                    null, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO_RELATION);
        } catch (Exception ex) {
            log.error(
                    "Unexpected error during sync UPSERT graphs for syncId: {}, orgId: {}, "
                            + "graphCount: {}, graphIdList: {}.",
                    syncId,
                    orgId,
                    graphIdList.size(),
                    graphIdList,
                    ex);
            handleFailedMessages(
                    null, syncId, orgId, upsertOPList, OPEnum.UPSERT, SourceEnum.BO_RELATION);
        }
    }

    private void delBORelations(
            String syncId, String orgId, List<String> graphIdList, List<Msg> delOPList) {
        log.info(
                "Start sync DELETE graphs, syncId: {}, orgId: {}, requestGraphCount: {}, requestGraphIdList: {}.",
                syncId,
                orgId,
                graphIdList.size(),
                graphIdList);
        try {
            long syncStartTime = System.currentTimeMillis();
            DeleteBORelationsReq req =
                    DeleteBORelationsReq.Builder.newBuilder()
                            .setSyncId(syncId)
                            .setOrgId(orgId)
                            .setGraphIdList(graphIdList)
                            .build();

            ResponseResult<?> response =
                    Poseidon.config(PoseidonConfig.POSEIDON_CONFIG)
                            .url(GRAVITY_SERVICE_URL)
                            .getResponse(req, ResponseResult.class);
            long syncEndTime = System.currentTimeMillis();

            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode()) {
                log.info(
                        "Invoke sync DELETE graphs success, total time cost: {}ms, response data: {}.",
                        (syncEndTime - syncStartTime),
                        response.getData());
            } else {
                log.error(
                        "Invoke sync DELETE graphs error, total time cost: {}ms, response code: {}, response message: {}.",
                        (syncEndTime - syncStartTime),
                        response.getCode(),
                        response.getMessage());

                handleFailedMessages(
                        null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO_RELATION);
            }
        } catch (PoseidonException e) {
            log.error(
                    "Failed to request gravity service to sync DELETE graphs for syncId: {}, orgId: {}, "
                            + "graphCount: {}, graphIdList: {}.",
                    syncId,
                    orgId,
                    graphIdList.size(),
                    graphIdList,
                    e);
            handleFailedMessages(
                    null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO_RELATION);
        } catch (Exception ex) {
            log.error(
                    "Unexpected error during sync DELETE graphs for syncId: {}, orgId: {}, "
                            + "graphCount: {}, graphIdList: {}.",
                    syncId,
                    orgId,
                    graphIdList.size(),
                    graphIdList,
                    ex);
            handleFailedMessages(
                    null, syncId, orgId, delOPList, OPEnum.DELETE, SourceEnum.BO_RELATION);
        }
    }

    /**
     * Handle failed messages and send them to retry queue
     *
     * @param response Response from service (can be null)
     * @param syncId Sync operation ID
     * @param orgId Organization ID
     * @param msgList List of messages for potential retry
     * @param sourceType Type of response (BO, GRAPH, or null for direct retry)
     */
    private void handleFailedMessages(
            ResponseResult<?> response,
            String syncId,
            String orgId,
            List<Msg> msgList,
            OPEnum opEnum,
            SourceEnum sourceType) {
        try {
            // If response is null or list is empty, retry all messages
            if (response == null) {
                Set<String> keyList = new HashSet<>();

                msgList.stream()
                        .filter(msg -> msg != null && msg.getKey() != null)
                        .forEach(
                                msg -> {
                                    keyList.add(msg.getKey());
                                    sendMessageToRetry(msg, msg.getKey(), syncId, orgId);
                                });

                if (!keyList.isEmpty()) {
                    log.info(
                            "Unexpected exception during sync {} {}s, start retry, syncId: {}, orgId: {}, keyCount: {}, keyList: {}.",
                            opEnum.name(),
                            sourceType.name(),
                            syncId,
                            orgId,
                            keyList.size(),
                            keyList);
                }
                return;
            }

            // If response data is null, no need to process
            if (response.getData() == null) {
                return;
            }

            // Get failed IDs based on response type
            Set<String> failedIds = getFailedIds(response, sourceType);
            if (failedIds.isEmpty()) {
                return;
            }

            // Create key to message mapping
            Map<String, Msg> keyMsgMap =
                    msgList.stream()
                            .filter(msg -> msg != null && msg.getKey() != null)
                            .collect(
                                    Collectors.toMap(
                                            Msg::getKey,
                                            msg -> msg,
                                            (existing, replacement) -> replacement));

            // Process failed messages
            failedIds.forEach(
                    id -> {
                        Msg msg = keyMsgMap.get(id);
                        if (msg == null) {
                            log.warn(
                                    "Message not found in keyMsgMap. keyId: {}, syncId: {}, orgId: {}",
                                    id,
                                    syncId,
                                    orgId);
                            return;
                        }
                        sendMessageToRetry(msg, id, syncId, orgId);
                    });

            log.info(
                    "There are failures in sync {} {}s, start retry, syncId: {}, orgId: {}, keyCount:{}, keyList:{}.",
                    opEnum.name(),
                    sourceType.name(),
                    syncId,
                    orgId,
                    failedIds.size(),
                    failedIds);
        } catch (Exception e) {
            log.error(
                    "Handle failed messages error, OP:{}, SourceType: {}, SyncId: {}, OrgId: {}, Response: {}, MsgList: {}.",
                    opEnum.name(),
                    sourceType.name(),
                    syncId,
                    orgId,
                    response,
                    msgList,
                    e);
        }
    }

    /** Get failed IDs from response based on response type */
    private Set<String> getFailedIds(ResponseResult<?> response, SourceEnum sourceType) {
        try {
            if (sourceType == SourceEnum.BO) {
                SyncBOsResp resp =
                        OBJECT_MAPPER.readValue(response.getData().toString(), SyncBOsResp.class);
                return Stream.of(
                                Optional.ofNullable(resp.getFailedBOs())
                                        .orElse(Collections.emptySet()),
                                Optional.ofNullable(resp.getFailedDOs())
                                        .orElse(Collections.emptySet()),
                                Optional.ofNullable(resp.getFailedRelations())
                                        .orElse(Collections.emptySet()))
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet());
            } else if (sourceType == SourceEnum.BO_RELATION) {
                SyncBORelationsResp resp =
                        OBJECT_MAPPER.readValue(
                                response.getData().toString(), SyncBORelationsResp.class);
                return Stream.of(
                                Optional.ofNullable(resp.getFailedGraphIds())
                                        .orElse(Collections.emptySet()))
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet());
            }
        } catch (JsonProcessingException e) {
            log.error(
                    "Parse response data error, sourceType: {}, response: {}.",
                    sourceType,
                    response);
        }
        return Collections.emptySet();
    }

    /** Send message to retry queue */
    private void sendMessageToRetry(Msg msg, String key, String syncId, String orgId) {
        int maxRetries = LionConfig.getMaxRetries();
        try {
            long retryCount = msg.getValue().getRetryCount();
            if (maxRetries <= 0 || retryCount <= maxRetries) {
                String value = OBJECT_MAPPER.writeValueAsString(msg.getValue());
                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", syncId.getBytes()));
                RetryUtil.sendToRetryQueue(headers, key, value);
            } else {
                log.warn(
                        "Exceeded max retries, will be discarded, key: {}, retryCount: {}, syncId: {}, orgId: {}, msg: {}",
                        key,
                        retryCount,
                        syncId,
                        orgId,
                        msg);
            }
        } catch (JsonProcessingException e) {
            log.error(
                    "Failed to serialize retry message, keyId: {}, syncId: {}, orgId: {}, msg: {}",
                    key,
                    syncId,
                    orgId,
                    msg,
                    e);
        }
    }
}
