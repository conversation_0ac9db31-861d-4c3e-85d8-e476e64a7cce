package com.envision.gravity.common.vo.sync;

import com.envision.gravity.common.definition.bo.BODefinition;


import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncBOsReq extends BaseSyncReq {
    private BODefinition boDefinition;
    private boolean forceUpdate;
    private boolean syncModelFirst;
}
