package com.envision.gravity.flink.streaming.virtual.attr.sync.config;

import java.io.IOException;


import com.eniot.metricengine.MetricEngine;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @description
 */
@Slf4j
public class MetricEngineConfig {
    private static volatile MetricEngine METRIC_ENGINE;

    public static MetricEngine buildMetricEngine() {
        if (METRIC_ENGINE == null) {
            synchronized (MetricEngineConfig.class) {
                if (METRIC_ENGINE == null) {
                    log.info("Start build metric engine...");
                    System.setProperty("calcite.default.charset", "UTF-8");
                    System.setProperty("file.encoding", "UTF-8");
                    System.setProperty("user.timezone", "UTC");

                    METRIC_ENGINE =
                            MetricEngine.builder()
                                    .withIgniteIp(LionConfig.getIgniteHostname())
                                    .withClientPort(LionConfig.getIgniteClientPort())
                                    .withIgniteUser(LionConfig.getIgniteUsername())
                                    .withIgnitePasswd(LionConfig.getIgnitePassword())
                                    .withMetricMetaIp(LionConfig.getPgJdbcUrl())
                                    .withMetricMetaUser(LionConfig.getPgUsername())
                                    .withMetricMetaPasswd(LionConfig.getPgPassword())
                                    .build();
                    log.info("Build metric engine success.");
                }
            }
        }
        return METRIC_ENGINE;
    }

    public static void closeMetricEngine() throws IOException {
        if (METRIC_ENGINE != null) {
            synchronized (MetricEngineConfig.class) {
                if (METRIC_ENGINE != null) {
                    METRIC_ENGINE.close();
                    METRIC_ENGINE = null;
                    log.info("Close metric engine success.");
                }
            }
        }
    }
}
