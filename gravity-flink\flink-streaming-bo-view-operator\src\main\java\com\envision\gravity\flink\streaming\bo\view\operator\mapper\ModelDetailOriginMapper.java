package com.envision.gravity.flink.streaming.bo.view.operator.mapper;

import com.envision.gravity.flink.streaming.bo.view.operator.model.pg.ModelProperties;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/7/3
 * @description
 */
public interface ModelDetailOriginMapper {
    /**
     * @param SchemaName SchemaName
     * @param modelId modelId
     * @return {@link ModelProperties}
     */
    @SelectProvider(type = ModelDetailOriginSqlProvider.class, method = "selectModelProperties")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "properties", property = "properties", jdbcType = JdbcType.VARCHAR)
    })
    ModelProperties selectModelProperties(String SchemaName, String modelId);
}
