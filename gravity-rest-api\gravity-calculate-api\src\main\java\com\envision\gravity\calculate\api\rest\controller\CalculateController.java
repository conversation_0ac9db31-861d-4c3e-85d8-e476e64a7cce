package com.envision.gravity.calculate.api.rest.controller;

import com.envision.gravity.calculate.api.rest.dto.DownstreamRequest;
import com.envision.gravity.calculate.api.rest.dto.DownstreamResponse;
import com.envision.gravity.calculate.api.rest.service.DownstreamCalcService;
import com.envision.gravity.calculate.api.rest.service.DownstreamRuleManager;
import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

import static java.util.Objects.requireNonNull;

@RestController
@RequestMapping("/calculate")
public class CalculateController {

    private final DownstreamCalcService downstreamCalcService;
    private final DownstreamRuleManager downstreamRuleManager;

    @Autowired
    public CalculateController(
            DownstreamCalcService downstreamCalcService,
            DownstreamRuleManager downstreamRuleManager) {
        this.downstreamCalcService =
                requireNonNull(downstreamCalcService, "downstreamCalcService is null");
        this.downstreamRuleManager =
                requireNonNull(downstreamRuleManager, "downstreamRuleManager is null");
    }

    /**
     * TODO OpenAPI configuration; batch processing limit for a single request (refer to sbux)
     *
     * @param orgId
     * @param downstreamRequests
     * @return
     */
    @PostMapping(value = "/downstream")
    public ResponseResult<?> downstream(
            @RequestParam String orgId, @RequestBody List<DownstreamRequest> downstreamRequests) {
        List<DownstreamResponse> downstreamResponses =
                downstreamCalcService.calculate(orgId, downstreamRequests);
        return ResponseResult.createResult(
                ResponseCodeEnum.SUCCESS.getCode(),
                ResponseCodeEnum.SUCCESS.getMessage(),
                downstreamResponses);
    }

    @GetMapping("/cacheView")
    public ResponseResult<?> cacheView(
            @RequestParam @NotBlank(message = "orgId can't be blank") String orgId) {
        Map<String, Object> data = downstreamRuleManager.getCacheView(orgId);
        return ResponseResult.createResult(
                ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMessage(), data);
    }
}
