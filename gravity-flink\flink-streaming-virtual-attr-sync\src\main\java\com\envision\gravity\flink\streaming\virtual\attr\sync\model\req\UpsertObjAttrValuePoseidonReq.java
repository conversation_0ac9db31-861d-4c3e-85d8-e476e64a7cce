package com.envision.gravity.flink.streaming.virtual.attr.sync.model.req;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.alibaba.fastjson.JSON;
import com.envision.apim.poseidon.request.PoseidonRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/7/9
 * @description
 */
@Getter
public class UpsertObjAttrValuePoseidonReq extends PoseidonRequest implements Serializable {

    private static final long serialVersionUID = -8942998288794226543L;
    private String orgId;

    @Setter private List<UpsertObjAttrValueReq> objAttrs;

    @Override
    public String baseUri() {
        return "/gravity-service/v3.0/obj/attr/batch-upsert";
    }

    @Override
    public String method() {
        return "POST";
    }

    @Override
    public Map<String, String> headerParams() {
        Map<String, String> params = new HashMap<>(2);
        params.put("x-audit", "{\"internal_only\":true}");
        return params;
    }

    public UpsertObjAttrValuePoseidonReq(Builder builder) {
        setOrgId(builder.orgId);
        setObjAttrs(builder.objAttrs);
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
        queryParams().put("orgId", orgId);
    }

    @Override
    public String jsonBodyString() {
        return JSON.toJSONString(objAttrs);
    }

    public static final class Builder {

        private String orgId;
        private List<UpsertObjAttrValueReq> objAttrs;

        private Builder() {}

        public static Builder newBuilder() {
            return new Builder();
        }

        public Builder setObjAttrs(List<UpsertObjAttrValueReq> objAttrs) {
            this.objAttrs = objAttrs;
            return this;
        }

        public Builder setOrgId(String orgId) {
            this.orgId = orgId;
            return this;
        }

        public UpsertObjAttrValuePoseidonReq build() {
            return new UpsertObjAttrValuePoseidonReq(this);
        }
    }
}
