package com.envision.gravity.ignite.tsdb.loader;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/** <AUTHOR> 2024/3/29 */
class TSDBLoaderUtilsTest {

    @Test
    public void flattenElements() {
        Set<String> elements = new HashSet<>(Arrays.asList("a", "b", "c"));
        Assertions.assertEquals("'a','b','c'", TSDBLoaderUtils.flattenElements(elements));
    }

    @Test
    public void splitMap() {
        Map<String, String> data = new HashMap<>();
        data.put("k1", "v1");
        data.put("k2", "v2");
        data.put("k3", "v3");

        List<Map<String, String>> splittedData = TSDBLoaderUtils.splitMap(data, 2);
        System.out.println(splittedData);
    }
}
