package com.envision.gravity.flink.warm.model;

import com.envision.gravity.common.po.PropFieldMeta;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.common.util.RawFieldIdUtil;
import com.envision.gravity.flink.warm.util.LionConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModelCache {
    private static final String SQL_QUERY_PREF =
            "SELECT\n"
                    + "    tbl_bo_model_comp.model_id,\n"
                    + "    CASE\n"
                    + "        WHEN tbl_component.anonymous = true THEN tbl_pref.pref_name\n"
                    + "        WHEN tbl_component.anonymous = false THEN concat(tbl_component.comp_name, ':', tbl_pref.pref_name)\n"
                    + "    END as pref_name,\n"
                    + "    tbl_field.field_id,\n"
                    + "    tbl_field.data_type,\n"
                    + "    tbl_component.comp_name,\n"
                    + "    tbl_field.category_id,\n"
                    + "    tbl_pref.pref_type\n"
                    + "FROM\n"
                    + "    tbl_bo_model_comp\n"
                    + "        JOIN tbl_component ON\n"
                    + "            tbl_component.comp_id = tbl_bo_model_comp.comp_id\n"
                    + "        JOIN tbl_component_pref ON\n"
                    + "            tbl_component_pref.comp_id = tbl_component.comp_id\n"
                    + "        JOIN tbl_pref ON\n"
                    + "            tbl_pref.pref_id = tbl_component_pref.pref_id\n"
                    + "        JOIN tbl_field ON\n"
                    + "            tbl_field.field_id = tbl_component_pref.field_id\n"
                    + "WHERE\n"
                    + "    tbl_bo_model_comp.model_id in ('%s') AND\n"
                    + "    tbl_field.field_id is not null;";

    private Map<String, Cache<String, List<PrefValue>>> modelPrefCache = new HashMap<>();

    private Cache<String, List<PrefValue>> getOrgCache(String orgId) {
        return modelPrefCache.computeIfAbsent(
                orgId,
                k ->
                        Caffeine.newBuilder()
                                .expireAfterWrite(
                                        LionConfig.getModelCacheExpireTimeSeconds(),
                                        TimeUnit.SECONDS)
                                .build());
    }

    public List<PrefValue> getOrLoadModelPref(String orgId, String modelId) {
        Cache<String, List<PrefValue>> cache = getOrgCache(orgId);
        List<PrefValue> prefValues = cache.getIfPresent(modelId);
        if (prefValues != null) {
            return prefValues;
        }
        prefValues = new ArrayList<>();
        List<List<?>> queryResult = IgniteUtil.query(orgId, String.format(SQL_QUERY_PREF, modelId));
        if (queryResult == null || queryResult.isEmpty()) {
            log.warn("No pref value found for modelId: {}", modelId);
            return prefValues;
        }

        for (List<?> row : queryResult) {
            String prefName = (String) row.get(1);
            String fieldId = (String) row.get(2);
            String fieldDataType = (String) row.get(3);
            String compName = (String) row.get(4);
            String categoryId = (String) row.get(5);
            String prefType = (String) row.get(6);
            boolean isAttribute = "ATTRIBUTE".equals(prefType);

            prefValues.add(
                    new PrefValue(
                            prefName, fieldId, fieldDataType, compName, categoryId, isAttribute));
        }

        setRawFieldId(prefValues);
        for (PrefValue prefValue : prefValues) {
            if (prefValue.hasNull()) {
                log.error("pref value has null, please check {}", prefValue);
            }
        }
        log.info("load model pref for modelId: {}, prefValues: {}", modelId, prefValues);
        return prefValues;
    }

    private void setRawFieldId(List<PrefValue> prefValues) {
        List<PropFieldMeta> propFieldMetas =
                prefValues.stream()
                        .map(
                                prefValue ->
                                        PropFieldMeta.builder()
                                                .fieldId(prefValue.getFieldId())
                                                .fieldDataType(prefValue.getFieldDataType())
                                                .prefName(prefValue.getPrefName())
                                                .compName(prefValue.getCompName())
                                                .categoryId(prefValue.getCategoryId())
                                                .build())
                        .collect(Collectors.toList());
        Map<String, String> rawFieldIdMap = RawFieldIdUtil.genRawFieldIdByPropMeta(propFieldMetas);
        prefValues.forEach(
                prefValue -> prefValue.setRawFieldId(rawFieldIdMap.get(prefValue.getFieldId())));
    }
}
