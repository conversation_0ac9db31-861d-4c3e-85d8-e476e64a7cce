package com.envisioniot.dp.sql.router.parser.gsql.validation.validator.update;

import com.envisioniot.dp.sql.router.parser.gsql.validation.CallbackFunction;
import com.envisioniot.dp.sql.router.utils.TimeUtils;

import net.sf.jsqlparser.statement.update.Update;

public class PropertyDownstreamRuleUpdateValidator extends BaseUpdateValidator {
    @Override
    protected CallbackFunction getPostExecution() {
        return null;
    }

    @Override
    protected void customValidation(String databaseName, Update update) {
        autoUpdateModifiedTime(update, TimeUtils.getCurrentUTCTimeString());
    }
}
