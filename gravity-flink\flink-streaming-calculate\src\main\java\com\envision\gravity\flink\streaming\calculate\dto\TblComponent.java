package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblComponent implements CdcTableEntity {
    private static final long serialVersionUID = 3255496534096094523L;
    private String compId;
    private String compName;
    private String compDisplayName;
    private String description;
    private String comment;
    private String anonymous;
    private String template;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
