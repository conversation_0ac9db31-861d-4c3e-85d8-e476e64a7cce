SELECT
    tbl_component.comp_name,
    tbl_component.anonymous,
    tbl_pref.pref_name,
    tbl_component_pref.field_index,
    tbl_component_pref.horizontal,
    tbl_component_pref.raw_field_id,
    tbl_pref.pref_type,
    CASE
        WHEN tbl_pref.pref_data_type = 'ENUM' AND tbl_pref.data_definition LIKE '%"@type":["integer"]%' THEN 'INTEGER'
        ELSE tbl_pref.pref_data_type
    END AS data_type
FROM
    (
        SELECT
            comp_id
        FROM
            "${orgId}".tbl_bo_model_comp
        WHERE
            model_id = '${modelId}'
    ) AS bo_model_comp
    LEFT JOIN "${orgId}".tbl_component ON tbl_component.comp_id = bo_model_comp.comp_id
    LEFT JOIN "${orgId}".tbl_component_pref_field_mapping AS tbl_component_pref ON tbl_component_pref.comp_id = tbl_component.comp_id
    LEFT JOIN "${orgId}".tbl_pref ON tbl_pref.pref_id = tbl_component_pref.pref_id
WHERE
    tbl_pref.pref_name IN (${prefNames}) AND
    (
        tbl_component.anonymous = true OR
        (
            tbl_component.anonymous = false
            AND CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name) IN (${pointNames})
        )
    );