package com.envision.gravity.flink.streaming.bo.view.operator.function;

import com.envision.gravity.flink.streaming.bo.view.operator.entity.Constants;
import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;
import com.envision.gravity.flink.streaming.bo.view.operator.model.ModelDetailOrigin;
import com.envision.gravity.flink.streaming.bo.view.operator.request.DestroyCacheReq;
import com.envision.gravity.flink.streaming.bo.view.operator.util.NameUtil;


import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description Tuple2: dropViewSql, {@link DestroyCacheReq}
 */
@Slf4j
public class GenDropReq
        extends ProcessFunction<ModelDetailOriginCdcRecord, Tuple2<String, DestroyCacheReq>> {
    private static final long serialVersionUID = 8479738540665480243L;

    @Override
    public void processElement(
            ModelDetailOriginCdcRecord value,
            ProcessFunction<ModelDetailOriginCdcRecord, Tuple2<String, DestroyCacheReq>>.Context
                    ctx,
            Collector<Tuple2<String, DestroyCacheReq>> out) {
        try {
            String schemaName = value.getSource().getSchema();
            ModelDetailOrigin before = value.getBefore();
            String viewName = NameUtil.genViewName(before);

            log.info(
                    "Start delete bo view, schemaName: [{}], modelId: [{}], tsMs: [{}]",
                    schemaName,
                    before.getModelId(),
                    value.getTsMs());
            out.collect(
                    Tuple2.of(
                            genDropViewSql(schemaName, viewName),
                            DestroyCacheReq.builder()
                                    .orgId(schemaName)
                                    .schema(Constants.BO)
                                    .table(viewName)
                                    .build()));
        } catch (JsonProcessingException e) {
            log.error("Parse view name error, value:{}", value, e);
        } catch (Exception e) {
            log.error("Unknown error, value:{}", value, e);
        }
    }

    private String genDropViewSql(String schemaName, String viewName) {
        // DROP VIEW IF EXISTS your_schema_name.your_view_name;
        return "DROP VIEW IF EXISTS " + schemaName + ".\"" + viewName + "\";";
    }
}
