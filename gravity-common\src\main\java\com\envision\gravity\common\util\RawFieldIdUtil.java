package com.envision.gravity.common.util;

import com.envision.gravity.common.enums.IgniteDataTypeEnum;
import com.envision.gravity.common.po.PropFieldMeta;
import com.envision.gravity.common.po.TblField;
import com.envision.gravity.common.vo.field.InternalFieldReq;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RawFieldIdUtil {

    private static final Logger logger = LoggerFactory.getLogger(RawFieldIdUtil.class);

    private static final String SCHEMA_GRAVITY = "GRAVITY";

    private static final String RAW_FIELD_ID_SEPARATOR = "__";

    private static final String PREF_TYPE_POINT = "measurepoint";
    private static final String PREF_TYPE_ATTR = "attribute";

    // fieldId => rawFieldId
    public static Map<String, String> genRawFieldIdByField(
            String orgId,
            Set<String> fieldIds,
            Map<String, TblField> curFieldMap,
            Map<String, InternalFieldReq> reqFieldMap) {
        Map<String, String> rawFieldIdMap = new HashMap<>(fieldIds.size());

        Map<String, PropFieldMeta> propMetas = getPropMetaByFieldIds(orgId, fieldIds);
        if (GTCommonUtils.isEmpty(propMetas)) {
            logger.info("propMetas is empty, try to fallback ...");
            genRawFieldIdFallBack(fieldIds, curFieldMap, rawFieldIdMap, reqFieldMap);
            return rawFieldIdMap;
        }

        Map<String, Boolean> fieldCompressConfigs =
                getFieldCompressConfigs(new ArrayList<>(propMetas.values()));
        for (Map.Entry<String, PropFieldMeta> entry : propMetas.entrySet()) {
            String fieldId = entry.getKey();
            TblField curField = curFieldMap.get(fieldId);
            String curFieldDataType = curField.getDataType();
            String newFieldDataType = reqFieldMap.get(fieldId).getDataType();
            String fieldDataType =
                    isDataTypeChanged(curFieldDataType, newFieldDataType)
                            ? newFieldDataType
                            : curFieldDataType;
            String rawFieldId =
                    concatRawFieldId(
                            !fieldCompressConfigs.get(fieldId), entry.getValue(), fieldDataType);
            rawFieldIdMap.put(fieldId, rawFieldId);
        }

        // fallback
        Set<String> propNotFoundFieldIds =
                fieldIds.stream()
                        .filter(fieldId -> !rawFieldIdMap.containsKey(fieldId))
                        .collect(Collectors.toSet());
        if (!GTCommonUtils.emptyCollection(propNotFoundFieldIds)) {
            logger.info(
                    "propNotFoundFieldIds, try to fallback, {}",
                    GTCommonUtils.concatStr(propNotFoundFieldIds));
            genRawFieldIdFallBack(propNotFoundFieldIds, curFieldMap, rawFieldIdMap, reqFieldMap);
        }

        return rawFieldIdMap;
    }

    public static Map<String, String> genRawFieldIdByPropMeta(List<PropFieldMeta> propMetas) {
        Map<String, Boolean> fieldCompressConfigs = getFieldCompressConfigs(propMetas);
        Map<String, String> rawFieldIdMap = new HashMap<>(propMetas.size());
        for (PropFieldMeta propMeta : propMetas) {
            String fieldId = propMeta.getFieldId();
            String rawFieldId =
                    concatRawFieldId(
                            !fieldCompressConfigs.get(fieldId),
                            propMeta,
                            propMeta.getFieldDataType());
            rawFieldIdMap.put(fieldId, rawFieldId);
        }

        return rawFieldIdMap;
    }

    // prefix__DataType
    public static Pair<String, String> parseDataType(String rawFieldId) {
        if (StringUtils.isEmpty(rawFieldId)) {
            throw new IllegalArgumentException("Parse data type failed: rawFieldId is empty ...");
        }

        int lastIndex = rawFieldId.lastIndexOf(RAW_FIELD_ID_SEPARATOR);
        if (lastIndex != -1) {
            String prefix = rawFieldId.substring(0, lastIndex);
            String dataType = rawFieldId.substring(lastIndex + 2); // +2 skip '__'
            return Pair.of(prefix, dataType);
        }

        // 兼容旧版本，随机字符串的 rawFieldId 是用 _ 做分隔符
        int randLastIndex = rawFieldId.lastIndexOf("_");
        if (randLastIndex != -1) {
            String prefix = rawFieldId.substring(0, randLastIndex);
            String dataType = rawFieldId.substring(randLastIndex + 1); // +1 skip '_'
            return Pair.of(prefix, dataType);
        }

        throw new IllegalArgumentException("Parse data type failed: delimiter __ not found ...");
    }

    // ============================================================================================
    private static Map<String, PropFieldMeta> getPropMetaByFieldIds(
            String orgId, Set<String> fieldIds) {
        long start = System.currentTimeMillis();
        // Query TBL_COMPONENT_PREF by fieldId
        // if one fieldId return multi propNames, just get the first one
        String getPropMetaSql =
                String.format(
                        "SELECT\n"
                                + "tcp.field_id AS field_id,\n"
                                + "CASE WHEN tc.anonymous THEN tp.pref_name ELSE CONCAT(tc.comp_name, ':', tp.pref_name) END AS pref_name,\n"
                                + "tf.data_type AS field_data_type,\n"
                                + "tf.category_id AS category_id,\n"
                                + "tc.comp_name AS comp_name\n"
                                + "FROM TBL_COMPONENT_PREF tcp\n"
                                + "INNER JOIN TBL_COMPONENT tc ON tcp.comp_id = tc.comp_id\n"
                                + "INNER JOIN TBL_PREF tp ON tcp.pref_id = tp.pref_id\n"
                                + "INNER JOIN TBL_FIELD tf ON tcp.field_id = tf.field_id\n"
                                + "WHERE tcp.field_id IN (%s)",
                        GTCommonUtils.concatStr(fieldIds));
        List<List<?>> propMetaResult = IgniteUtil.query(orgId, getPropMetaSql);
        logger.info(
                "getPropMetaSql: {}, time: {}ms",
                getPropMetaSql,
                System.currentTimeMillis() - start);
        if (GTCommonUtils.emptyCollection(propMetaResult)) {
            return Collections.emptyMap();
        }

        return propMetaResult.stream()
                .map(
                        r ->
                                PropFieldMeta.builder()
                                        .fieldId((String) r.get(0))
                                        .prefName((String) r.get(1))
                                        .fieldDataType((String) r.get(2))
                                        .categoryId((String) r.get(3))
                                        .compName((String) r.get(4))
                                        .build())
                .collect(Collectors.toMap(PropFieldMeta::getFieldId, Function.identity()));
    }

    private static void genRawFieldIdFallBack(
            Set<String> fallbackFieldIds,
            Map<String, TblField> curFieldMap,
            Map<String, String> rawFieldIdMap,
            Map<String, InternalFieldReq> reqFieldMap) {
        logger.warn("NOTICE: fallback fieldIds {}", fallbackFieldIds);
        // fieldId => rawFieldId
        Map<String, String> fbRawFieldIds =
                doGenRawFieldIdFallBack(fallbackFieldIds, curFieldMap, reqFieldMap);
        rawFieldIdMap.putAll(fbRawFieldIds);
    }

    /**
     * @param fieldIds
     *     <p>fieldId example1: dtmi:DCM:rerer;1__attribute___timezone
     *     ($categoryId__$prefType__$prefName)
     *     <p>fieldId example2: dtmi:DCM:rerer;1__DCM__attribute___name
     *     ($categoryId__$compName__$prefType__$prefName)
     * @param curFieldMap
     * @return
     *     <p>fieldId => rawFieldId
     */
    private static Map<String, String> doGenRawFieldIdFallBack(
            Set<String> fieldIds,
            Map<String, TblField> curFieldMap,
            Map<String, InternalFieldReq> reqFieldMap) {
        Map<String, String> rawFieldIds = new HashMap<>(fieldIds.size());
        for (String fieldId : fieldIds) {
            String curFieldDataType = curFieldMap.get(fieldId).getDataType();
            String newFieldDataType = reqFieldMap.get(fieldId).getDataType();
            String fieldDataType =
                    isDataTypeChanged(curFieldDataType, newFieldDataType)
                            ? newFieldDataType
                            : curFieldDataType;
            IgniteDataTypeEnum typeEnum = IgniteDataTypeEnum.findByDataType(fieldDataType);

            String id = null;
            int subIndex = fieldId.indexOf(RAW_FIELD_ID_SEPARATOR);
            if (subIndex >= 0 && fieldId.length() > subIndex + RAW_FIELD_ID_SEPARATOR.length()) {
                String subStr = fieldId.substring(subIndex + RAW_FIELD_ID_SEPARATOR.length());
                String[] subStrTokens = subStr.split(RAW_FIELD_ID_SEPARATOR);
                if (subStrTokens.length == 3
                        && (subStrTokens[1].equals(PREF_TYPE_POINT)
                                || subStrTokens[1].equals(PREF_TYPE_ATTR))) {
                    // $compName__$prefName__$igniteDataType
                    id =
                            subStrTokens[0]
                                    + RAW_FIELD_ID_SEPARATOR
                                    + subStrTokens[2]
                                    + RAW_FIELD_ID_SEPARATOR
                                    + typeEnum.getIgniteDataType();
                } else if (subStrTokens.length == 2
                        && (subStrTokens[0].equals(PREF_TYPE_POINT)
                                || subStrTokens[0].equals(PREF_TYPE_ATTR))) {
                    // $prefName__$igniteDataType
                    id = subStrTokens[1] + RAW_FIELD_ID_SEPARATOR + typeEnum.getIgniteDataType();
                }
            }

            if (StringUtils.isEmpty(id)) {
                // fallback
                id = fieldId + "_" + GTCommonUtils.genShortUUID(8);
            }

            // '.' and ':' symbol replace with '__'
            rawFieldIds.put(fieldId, id.replaceAll("[.:]", RAW_FIELD_ID_SEPARATOR).toUpperCase());
        }
        return rawFieldIds;
    }

    // non-anonymous component need replace '.' and ':' to '__'
    // NOTICE: only support field data type, because when pref data type defined as ENUM,
    // raw_field_id will be wrong
    private static String concatRawFieldId(
            boolean notCompress, PropFieldMeta propMeta, String fieldDataType) {
        IgniteDataTypeEnum dataTypeEnum = IgniteDataTypeEnum.findByDataType(fieldDataType);
        StringBuilder sb = new StringBuilder();
        if (notCompress) {
            sb.append(propMeta.getCategoryId());
            sb.append(RAW_FIELD_ID_SEPARATOR);
        }
        sb.append(propMeta.getPrefName().replaceAll("[.:]", RAW_FIELD_ID_SEPARATOR));
        sb.append(RAW_FIELD_ID_SEPARATOR);
        sb.append(dataTypeEnum.getIgniteDataType());

        return sb.toString().toUpperCase();
    }

    private static Map<String, Boolean> getFieldCompressConfigs(List<PropFieldMeta> propMetas) {
        Set<String> allExprSet = new HashSet<>(propMetas.size());
        Map<String, Set<String>> fieldExprMap = new HashMap<>(propMetas.size());
        Map<String, Boolean> fieldCompressConfigs = new HashMap<>(propMetas.size());

        for (PropFieldMeta propMeta : propMetas) {
            String categoryIdExpr = String.format("%s:*:*", propMeta.getCategoryId());
            String compNameExpr = String.format("*:%s:*", propMeta.getCompName());
            String compNamePrefNameExpr =
                    String.format("*:%s:%s", propMeta.getCompName(), propMeta.getPrefName());
            String prefNameExpr = String.format("*:*:%s", propMeta.getPrefName());

            Set<String> fieldExprSet =
                    fieldExprMap.computeIfAbsent(
                            propMeta.getFieldId(), fieldId -> new HashSet<>(4));
            fieldExprSet.add(categoryIdExpr);
            fieldExprSet.add(compNameExpr);
            fieldExprSet.add(compNamePrefNameExpr);
            fieldExprSet.add(prefNameExpr);

            allExprSet.addAll(fieldExprSet);
            // init result
            fieldCompressConfigs.put(propMeta.getFieldId(), false);
        }

        String sql =
                String.format(
                        "SELECT EXPRESSION FROM %s.TBL_COMPRESS_FIELD_CONFIG WHERE EXPRESSION IN (%s)",
                        SCHEMA_GRAVITY, GTCommonUtils.concatStr(allExprSet));
        List<List<?>> queryResult = IgniteUtil.query(SCHEMA_GRAVITY, sql);
        if (GTCommonUtils.emptyCollection(queryResult)) {
            logger.warn(
                    "All fields disable compression, fieldIds: {}, expressionSet: {}",
                    GTCommonUtils.concatStr(fieldExprMap.keySet()),
                    GTCommonUtils.concatStr(allExprSet));
            // all fields are false
            return fieldCompressConfigs;
        }

        Set<String> exprSet =
                queryResult.stream()
                        .map(list -> String.valueOf(list.get(0)))
                        .collect(Collectors.toSet());
        for (Map.Entry<String, Set<String>> fieldExprEntry : fieldExprMap.entrySet()) {
            String fieldId = fieldExprEntry.getKey();
            Set<String> fieldExprSet = fieldExprEntry.getValue();
            for (String fieldExpr : fieldExprSet) {
                if (exprSet.contains(fieldExpr)) {
                    fieldCompressConfigs.put(fieldId, true);
                    break;
                }
            }
        }

        return fieldCompressConfigs;
    }

    private static boolean isDataTypeChanged(String originDataType, String newDataType) {
        if (StringUtils.isEmpty(newDataType)) {
            return false;
        }

        String originSqlDataType =
                IgniteDataTypeEnum.findByDataType(originDataType).getIgniteDataType();
        String newSqlDataType = IgniteDataTypeEnum.findByDataType(newDataType).getIgniteDataType();
        return !originSqlDataType.equals(newSqlDataType);
    }
}
