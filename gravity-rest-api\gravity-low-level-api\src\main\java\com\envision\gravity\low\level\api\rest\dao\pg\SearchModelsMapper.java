package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblPgModel;
import com.envision.gravity.common.vo.search.Sorter;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/03/14 18:27 @Description: */
public interface SearchModelsMapper {

    @SelectProvider(type = SearchModelsSqlProvider.class, method = "selectByExpression")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_display_name",
                property = "modelDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
        @Result(column = "org_id", property = "orgId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_path", property = "modelPath", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_tags",
                property = "modelTags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "properties",
                property = "properties",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "model_created_time",
                property = "modelCreatedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "model_created_user",
                property = "modelCreatedUser",
                jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_modified_time",
                property = "modelModifiedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "model_modified_user",
                property = "modelModifiedUser",
                jdbcType = JdbcType.VARCHAR)
    })
    List<TblPgModel> selectByExpression(
            String expression, int limit, int offset, List<Sorter> sorters, String orgId);

    @SelectProvider(
            type = SearchModelsSqlProvider.class,
            method = "selectByExpressionNotWithPagination")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_display_name",
                property = "modelDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
        @Result(column = "org_id", property = "orgId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_path", property = "modelPath", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_tags",
                property = "modelTags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "properties",
                property = "properties",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "model_created_time",
                property = "modelCreatedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "model_created_user",
                property = "modelCreatedUser",
                jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "model_modified_time",
                property = "modelModifiedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "model_modified_user",
                property = "modelModifiedUser",
                jdbcType = JdbcType.VARCHAR)
    })
    List<TblPgModel> selectByExpressionNotWithPagination(String expression, String orgId);

    @SelectProvider(type = SearchModelsSqlProvider.class, method = "selectModelPathByModelIds")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_path", property = "modelPath", jdbcType = JdbcType.VARCHAR)
    })
    List<TblPgModel> selectModelPathByModelIds(List<String> modelIds, String orgId);

    @SelectProvider(type = SearchModelsSqlProvider.class, method = "countByExpression")
    int countByExpression(String expression, String orgId);
}
