package com.envision.gravity.flink.streaming.calculate.sqlgateway;

import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.PropertyInfo;
import com.envision.gravity.common.enums.PrefType;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyPayload;

import javax.sql.DataSource;

import java.io.StringWriter;
import java.sql.*;
import java.util.*;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Common helper class for calculation services (Singleton) Provides reusable functionality for SQL
 * Gateway, Velocity Engine, and data querying
 */
public class CalculationServiceHelper {

    private static final Logger logger = LoggerFactory.getLogger(CalculationServiceHelper.class);

    private static volatile CalculationServiceHelper instance;
    private static final Object lock = new Object();

    private DataSource sqlGatewayDataSource;
    private VelocityEngine velocityEngine;

    /** Private constructor - initializes services automatically */
    private CalculationServiceHelper() {
        initSqlGatewayDataSource();
        initVelocityEngine();
    }

    /** Get singleton instance */
    public static CalculationServiceHelper getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new CalculationServiceHelper();
                }
            }
        }
        return instance;
    }

    /** Initialize SQL Gateway data source */
    private void initSqlGatewayDataSource() {
        try {
            HikariConfig config = new HikariConfig();
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
            config.setJdbcUrl(CalcLionConfig.getSqlGatewayJdbcUrl());
            config.setUsername(CalcLionConfig.getSqlGatewayUserName());
            config.setPassword(CalcLionConfig.getSqlGatewayPassword());
            config.setMaximumPoolSize(10);
            config.setConnectionTestQuery("SELECT 1");
            config.setConnectionTimeout(30000);
            config.setIdleTimeout(600000);
            config.setMaxLifetime(1800000);

            this.sqlGatewayDataSource = new HikariDataSource(config);
            logger.info("SQL Gateway data source initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize SQL Gateway data source: {}", e.getMessage(), e);
            throw new RuntimeException("SQL Gateway data source initialization failed", e);
        }
    }

    /** Initialize Velocity template engine */
    private void initVelocityEngine() {
        try {
            this.velocityEngine = new VelocityEngine();
            velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
            velocityEngine.setProperty(
                    "classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
            velocityEngine.init();
            logger.info("Velocity engine initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize Velocity engine: {}", e.getMessage(), e);
            throw new RuntimeException("Velocity engine initialization failed", e);
        }
    }

    public Map<String, LegacyPayload> queryLatestValues(
            String orgId, String modelId, LatestQueryEntity queryEntity) {
        // TODO implement
    }

    public Map<String, LegacyPayload> queryTSValues(
            String orgId, String modelId, TSQueryEntity queryEntity) {
        // TODO implement
    }

    /** Query latest values for assets from SQL Gateway */
    public Map<String, LegacyPayload> queryLatestValues(
            String orgId, String modelId, Map<String, Set<PropertyInfo>> assetPropertyInfoMap) {

        if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
            logger.debug("assetPropertyInfoMap is null or empty, returning empty result");
            return new HashMap<>();
        }

        // Build dynamic SQL
        String sql = buildLatestDynamicSql(orgId, modelId, assetPropertyInfoMap);
        if (sql == null) {
            logger.warn("Failed to build SQL, returning empty result");
            return new HashMap<>();
        }

        logger.debug("Executing SQL Gateway query: {}", sql);

        Map<String, LegacyPayload> result = new HashMap<>();

        try (Connection connection = sqlGatewayDataSource.getConnection();
                PreparedStatement statement = connection.prepareStatement(sql);
                ResultSet resultSet = statement.executeQuery()) {

            result = parseResultSetToLegacyPayload(resultSet, assetPropertyInfoMap);

        } catch (SQLException e) {
            logger.error("SQL Gateway query failed: {}", e.getMessage(), e);
            // Return empty result instead of throwing exception to allow partial processing
            return new HashMap<>();
        }

        logger.debug("SQL Gateway query completed, returned {} assets", result.size());
        return result;
    }

    /** Group SrcPrefItems by modelId for SQL Gateway queries */
    public Map<String, Map<String, Set<PropertyInfo>>> groupSrcPrefItemsForSqlGateway(
            List<SrcPrefItem> srcPrefItems,
            String payloadSrcModelId,
            String targetAssetId,
            LegacyPayload srcPayload) {
        Map<String, Map<String, Set<PropertyInfo>>> result = new HashMap<>();
        if (GTCommonUtils.emptyCollection(srcPrefItems)) {
            return result;
        }

        for (SrcPrefItem srcPrefItem : srcPrefItems) {
            String modelId = srcPrefItem.getModelId();
            String prefName = srcPrefItem.getPrefName();
            PrefType prefType = srcPrefItem.getPrefType();

            // Skip current model measurement points (get from LegacyPayload directly)
            if (modelId.equals(payloadSrcModelId) && srcPayload.getPoints().containsKey(prefName)) {
                logger.debug("Skip srcPayload model [{}] point [{}]", payloadSrcModelId, prefName);
                continue;
            }

            // Include current model attributes, current models points and other model data which
            // not exist in payload
            Map<String, Set<PropertyInfo>> modelMap =
                    result.computeIfAbsent(modelId, k -> new HashMap<>());

            // For each target asset, add the property info
            String queryAssetId =
                    modelId.equals(payloadSrcModelId) ? srcPayload.getAssetId() : targetAssetId;
            Set<PropertyInfo> propertyList =
                    modelMap.computeIfAbsent(queryAssetId, k -> new HashSet<>());

            PropertyInfo propertyInfo =
                    PropertyInfo.builder()
                            .modelId(modelId)
                            .prefName(prefName)
                            .prefType(prefType)
                            .build();
            propertyList.add(propertyInfo);
        }

        logger.debug("Grouped SrcPrefItems: {}", result.keySet());
        return result;
    }

    /** Close resources */
    public void close() {
        if (sqlGatewayDataSource instanceof HikariDataSource) {
            ((HikariDataSource) sqlGatewayDataSource).close();
            logger.info("SQL Gateway data source closed");
        }
    }

    /** Build dynamic SQL query statement using Velocity template */
    private String buildLatestDynamicSql(
            String orgId, String modelId, Map<String, Set<PropertyInfo>> assetPropertyInfoMap) {

        if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
            logger.warn("assetPropertyInfoMap is null or empty, skipping query");
            return null;
        }

        // Collect all unique PropertyInfo by iterating through assetPropertyInfoMap values
        Set<PropertyInfo> allProperties = new HashSet<>();
        for (Set<PropertyInfo> propertyList : assetPropertyInfoMap.values()) {
            if (propertyList != null) {
                allProperties.addAll(propertyList);
            }
        }

        if (allProperties.isEmpty()) {
            logger.warn("No properties found in assetPropertyInfoMap, skipping query");
            return null;
        }

        // Prepare Velocity context
        VelocityContext context = new VelocityContext();
        context.put("orgId", orgId);
        context.put("modelId", modelId);
        context.put("allProperties", allProperties);
        context.put("assetIds", assetPropertyInfoMap.keySet());

        // Render template
        StringWriter writer = new StringWriter();
        try {
            Template template = velocityEngine.getTemplate("query-latest-values.vm");
            template.merge(context, writer);
            return writer.toString();
        } catch (Exception e) {
            logger.error("Failed to render SQL template: {}", e.getMessage(), e);
            return null;
        }
    }

    /** Parse ResultSet to LegacyPayload map */
    private Map<String, LegacyPayload> parseResultSetToLegacyPayload(
            ResultSet resultSet, Map<String, Set<PropertyInfo>> assetPropertyInfoMap)
            throws SQLException {

        Map<String, LegacyPayload> result = new HashMap<>();

        while (resultSet.next()) {
            String assetId = resultSet.getString("asset_id");

            LegacyPayload payload =
                    result.computeIfAbsent(
                            assetId,
                            k -> {
                                LegacyPayload newPayload = new LegacyPayload();
                                newPayload.setAssetId(assetId);
                                newPayload.setPoints(new HashMap<>());
                                return newPayload;
                            });

            // Get property list for this asset
            Set<PropertyInfo> propertyList = assetPropertyInfoMap.get(assetId);
            if (propertyList == null) {
                continue;
            }

            Long maxTimestamp = payload.getTime();

            // Process each property
            for (PropertyInfo property : propertyList) {
                String prefName = property.getPrefName();
                PrefType prefType = property.getPrefType();

                try {
                    if (prefType == PrefType.ATTRIBUTE) {
                        // For attributes: get value directly
                        Object value = resultSet.getObject(prefName);
                        if (value != null) {
                            payload.getPoints().put(prefName, value);
                        }

                    } else if (prefType == PrefType.MEASUREPOINT) {
                        // For measurement points: get time and value
                        String timeColumn = prefName + "_time";
                        String valueColumn = prefName + "_value";

                        Object value = resultSet.getObject(valueColumn);
                        Object timeObj = resultSet.getObject(timeColumn);

                        if (value != null) {
                            payload.getPoints().put(prefName, value);

                            // Update timestamp if available
                            if (timeObj != null) {
                                long timestamp = convertToTimestamp(timeObj);
                                maxTimestamp = Math.max(maxTimestamp, timestamp);
                            }
                        }
                    }
                } catch (SQLException e) {
                    logger.warn("Failed to parse property {}: {}", prefName, e.getMessage());
                    // Continue processing other properties
                }
            }

            // Update payload timestamp to maximum timestamp found
            if (maxTimestamp != null) {
                payload.setTime(maxTimestamp);
            } else {
                logger.warn(
                        "No timestamp found for asset {}, using current timestamp, points: {}",
                        assetId,
                        payload.getPoints());
                payload.setTime(System.currentTimeMillis());
            }
        }

        return result;
    }

    /** Convert various time objects to timestamp */
    private long convertToTimestamp(Object timeObj) {
        if (timeObj instanceof Long) {
            return (Long) timeObj;
        } else if (timeObj instanceof Timestamp) {
            return ((Timestamp) timeObj).getTime();
        } else if (timeObj instanceof java.util.Date) {
            return ((java.util.Date) timeObj).getTime();
        } else {
            // Try to parse as string
            try {
                return Long.parseLong(timeObj.toString());
            } catch (NumberFormatException e) {
                logger.warn("Unable to parse timestamp: {}", timeObj);
                return System.currentTimeMillis();
            }
        }
    }

    // Getters for accessing initialized components
    public DataSource getSqlGatewayDataSource() {
        return sqlGatewayDataSource;
    }

    public VelocityEngine getVelocityEngine() {
        return velocityEngine;
    }
}
