package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;


import org.apache.ibatis.annotations.DeleteProvider;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
public interface ObjectDetailOriginMapper {
    @DeleteProvider(type = ObjectDetailOriginSqlProvider.class, method = "batchDeleteByPrimaryKey")
    int batchDeleteByPrimaryKey(
            String schemaName, List<String> assetIdList, List<String> modelIdList);

    @DeleteProvider(type = ObjectDetailOriginSqlProvider.class, method = "batchDeleteByAssetIdList")
    int batchDeleteByAssetIdList(String schemaName, List<String> assetIdList);
}
