package com.envision.gravity.low.level.api.sql.common;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;
import com.envision.gravity.low.level.api.sql.table.cache.*;


import lombok.Getter;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheMode;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
@Getter
public enum BOCoreCacheTableEnum {
    // Cache Table
    TBL_BO_MODEL(
            "TBL_BO_MODEL",
            "TBL_BO_MODEL",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblBOModelCacheTableInfo()),
    TBL_COMPONENT(
            "TBL_COMPONENT",
            "TBL_COMPONENT",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblComponentCacheTableInfo()),
    TBL_MODEL_RELATION(
            "TBL_MODEL_RELATION",
            "TBL_MODEL_RELATION",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblModelRelationCacheTableInfo()),
    TBL_BO_MODEL_COMPONENT(
            "TBL_BO_MODEL_COMP",
            "TBL_BO_MODEL_COMP",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblBOModelComponentCacheTableInfo()),
    TBL_COMPONENT_PREF(
            "TBL_COMPONENT_PREF",
            "TBL_COMPONENT_PREF",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblComponentPrefCacheTableInfo()),
    TBL_COMPONENT_PREF_FIELD_MAPPING(
            "TBL_COMPONENT_PREF_FIELD_MAPPING",
            "TBL_COMPONENT_PREF_FIELD_MAPPING",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblComponentPrefFieldMappingCacheTableInfo()),
    TBL_PREF(
            "TBL_PREF",
            "TBL_PREF",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblPrefCacheTableInfo()),
    TBL_PREF_EXT(
            "TBL_PREF_EXT",
            "TBL_PREF_EXT",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblPrefExtCacheTableInfo()),
    TBL_BO_GROUP(
            "TBL_BO_GROUP",
            "TBL_BO_GROUP",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblBOGroupCacheTableInfo()),
    TBL_BO_GROUP_RELATION_PART(
            "TBL_BO_GROUP_RELATION",
            "TBL_BO_GROUP_RELATION_PART",
            CacheMode.PARTITIONED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblBOGroupRelationCacheTableInfo()),
    TBL_BO_PART(
            "TBL_BO",
            "TBL_BO_PART",
            CacheMode.PARTITIONED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblBOCacheTableInfo()),
    TBL_TAG(
            "TBL_TAG",
            "TBL_TAG",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblTagCacheTableInfo()),
    TBL_SUB_GRAPH(
            "TBL_SUB_GRAPH",
            "TBL_SUB_GRAPH",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            true,
            new TblSubGraphCacheTableInfo()),
    TBL_EDGE(
            "TBL_EDGE",
            "TBL_EDGE",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblEdgeCacheTableInfo()),
    TBL_EDGE_TYPE(
            "TBL_EDGE_TYPE",
            "TBL_EDGE_TYPE",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            true,
            new TblEdgeTypeCacheTableInfo()),
    TBL_EDGE_TYPE_PROP(
            "TBL_EDGE_TYPE_PROP",
            "TBL_EDGE_TYPE_PROP",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            true,
            new TblEdgeTypePropCacheTableInfo()),
    TBL_START_VID(
            "TBL_START_VID",
            "TBL_START_VID",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            true,
            new TblStartVidCacheTableInfo()),
    TBL_PROPERTY_UPSTREAM_RULE(
            "TBL_PROPERTY_UPSTREAM_RULE",
            "TBL_PROPERTY_UPSTREAM_RULE",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblPropertyUpstreamRuleCacheTableInfo()),
    TBL_PROPERTY_DOWNSTREAM_RULE(
            "TBL_PROPERTY_DOWNSTREAM_RULE",
            "TBL_PROPERTY_DOWNSTREAM_RULE",
            CacheMode.REPLICATED,
            CacheAtomicityMode.ATOMIC,
            false,
            false,
            new TblPropertyDownstreamRuleCacheTableInfo());

    BOCoreCacheTableEnum(
            String dataSourceTableName,
            String queryEntityTableName,
            CacheMode cacheMode,
            CacheAtomicityMode atomicityMode,
            boolean readThrough,
            boolean writeThrough,
            CacheTableInfo cacheTableInfo) {
        this.dataSourceTableName = dataSourceTableName;
        this.queryEntityTableName = queryEntityTableName;
        this.cacheMode = cacheMode;
        this.atomicityMode = atomicityMode;
        this.readThrough = readThrough;
        this.writeThrough = writeThrough;
        this.cacheTableInfo = cacheTableInfo;
    }

    private final String dataSourceTableName;
    private final String queryEntityTableName;
    private final CacheMode cacheMode;
    private final CacheAtomicityMode atomicityMode;
    private final boolean readThrough;
    private final boolean writeThrough;
    private final CacheTableInfo cacheTableInfo;
}
