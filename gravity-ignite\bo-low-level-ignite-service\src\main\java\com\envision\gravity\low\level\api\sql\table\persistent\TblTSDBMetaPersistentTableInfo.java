package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblTSDBMetaPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.TBL_TSDB_META_PART\n"
                    + "(\n"
                    + "    SERIES_ID       VARCHAR NOT NULL,\n"
                    + "    ALLOCATED_NODE1 BIGINT,\n"
                    + "    ALLOCATED_NODE2 BIGINT,\n"
                    + "    CREATED_TIME    TIMESTAMP,\n"
                    + "    MODIFIED_TIME   TIMESTAMP,\n"
                    + "    RECOVERY_ID     VARCHAR,\n"
                    + "    PRIMARY KEY (SERIES_ID)\n"
                    + ") WITH \"template=%s,atomicity=%s,BACKUPS=1,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Arrays.asList(
                        "CREATE INDEX IF NOT EXISTS IDX_ALLOCATED_NODE1 ON %s.TBL_TSDB_META_PART (ALLOCATED_NODE1);",
                        "CREATE INDEX IF NOT EXISTS IDX_ALLOCATED_NODE2 ON %s.TBL_TSDB_META_PART (ALLOCATED_NODE2);");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
