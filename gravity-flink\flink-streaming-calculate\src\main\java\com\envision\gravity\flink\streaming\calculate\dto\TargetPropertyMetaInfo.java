package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.calculate.PropertyInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TargetPropertyMetaInfo {

    // targetCompId + targetPrefId => <targetModelId, <srcCategory, meta>>
    private Map<PropertyId, Map<String, Map<String, CalcPropertyMeta>>> targetPropertyMetaMap;

    // srcPrefName => <srcModelId, List of target pref>
    private Map<String, Map<String, Set<PropertyId>>> srcPropertyMetaMap;

    private List<CalcPropertyMeta> directMappingProperties;

    private Set<PropertyInfo> allSrcModelPrefNameSet;
}
