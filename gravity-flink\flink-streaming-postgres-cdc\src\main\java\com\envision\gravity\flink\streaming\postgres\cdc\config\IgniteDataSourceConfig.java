package com.envision.gravity.flink.streaming.postgres.cdc.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignition;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;

/**
 * <AUTHOR>
 * @date 2024/10/23
 * @description
 */
@Slf4j
public class IgniteDataSourceConfig {
    private static volatile IgniteClient IGNITE_CLIENT;

    public static IgniteClient getIgniteClient() {
        if (IGNITE_CLIENT == null) {
            synchronized (IgniteDataSourceConfig.class) {
                if (IGNITE_CLIENT == null) {
                    ClientConfiguration cfg =
                            new ClientConfiguration()
                                    .setAddresses(LionConfig.getIgniteAddress().split(","))
                                    .setPartitionAwarenessEnabled(true)
                                    .setUserName(LionConfig.getIgniteUsername())
                                    .setUserPassword(LionConfig.getIgnitePassword());
                    IGNITE_CLIENT = Ignition.startClient(cfg);
                }
            }
        }
        return IGNITE_CLIENT;
    }

    public static void closeIgniteClient() {
        if (IGNITE_CLIENT != null) {
            synchronized (IgniteClient.class) {
                if (IGNITE_CLIENT != null) {
                    IGNITE_CLIENT.close();
                    IGNITE_CLIENT = null;
                    log.info("Close ignite client success.");
                }
            }
        }
    }
}
