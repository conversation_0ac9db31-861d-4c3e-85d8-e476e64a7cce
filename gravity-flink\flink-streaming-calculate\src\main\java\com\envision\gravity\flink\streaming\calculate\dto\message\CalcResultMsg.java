package com.envision.gravity.flink.streaming.calculate.dto.message;

import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;

import java.io.Serializable;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算结果消息
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcResultMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 目标模型ID到消息的映射 */
    private Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap;
}
