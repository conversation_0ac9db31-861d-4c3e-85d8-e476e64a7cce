package com.envision.gravity.flink.streaming.calculate.integration;

import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/** Manages calculation rules for integration tests */
@Slf4j
public class CalcRuleManager {

    @Data
    public static class CalcRule {
        private String orgId;
        private String prefRuleId;
        private String targetCategory;
        private String targetCompId;
        private String targetPrefId;
        private String srcCategory;
        private String expression;
        private int calcType;
    }

    private HikariDataSource dataSource;
    private final String sqlGatewayUrl;

    public CalcRuleManager() {
        this.sqlGatewayUrl = CalcLionConfig.getSqlGatewayJdbcUrl();
        // Lazy initialization - only create data source when needed
    }

    public CalcRuleManager(String sqlGatewayUrl) {
        this.sqlGatewayUrl = sqlGatewayUrl;
        // Lazy initialization - only create data source when needed
    }

    /** Get data source with lazy initialization */
    private synchronized HikariDataSource getDataSource() {
        if (dataSource == null) {
            dataSource = initSqlGatewayDataSource(sqlGatewayUrl);
        }
        return dataSource;
    }

    /** Initialize SQL Gateway data source with custom URL */
    private HikariDataSource initSqlGatewayDataSource(String jdbcUrl) {
        try {
            HikariConfig config = new HikariConfig();
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
            config.setJdbcUrl(jdbcUrl);
            config.setUsername(CalcLionConfig.getSqlGatewayUserName());
            config.setPassword(CalcLionConfig.getSqlGatewayPassword());
            config.setMaximumPoolSize(10);
            config.setConnectionTestQuery("SELECT 1");
            config.setConnectionTimeout(30000);
            config.setIdleTimeout(600000);
            config.setMaxLifetime(1800000);

            HikariDataSource dataSource = new HikariDataSource(config);
            log.info("SQL Gateway data source initialized successfully");
            return dataSource;
        } catch (Exception e) {
            log.error("Failed to initialize SQL Gateway data source: {}", e.getMessage(), e);
            throw new RuntimeException("SQL Gateway data source initialization failed", e);
        }
    }

    /** Close the data source */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            log.info("SQL Gateway data source closed");
        }
    }

    /** Load calculation rules from CSV file */
    public List<CalcRule> loadRulesFromCsv(String csvPath) throws IOException, CsvException {
        List<CalcRule> rules = new ArrayList<>();

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(csvPath);
                CSVReader reader = new CSVReader(new InputStreamReader(inputStream))) {

            List<String[]> records = reader.readAll();
            // Skip header row
            for (int i = 1; i < records.size(); i++) {
                String[] record = records.get(i);
                CalcRule rule = new CalcRule();
                rule.setOrgId(record[0]);
                rule.setPrefRuleId(record[1]);
                rule.setTargetCategory(record[2]);
                rule.setTargetCompId(record[3]);
                rule.setTargetPrefId(record[4]);
                rule.setSrcCategory(record[5]);
                rule.setExpression(record[6]);
                rule.setCalcType(Integer.parseInt(record[7]));
                rules.add(rule);
            }
        }

        log.info("Loaded {} calculation rules from {}", rules.size(), csvPath);
        return rules;
    }

    /** Setup calculation rules in the database */
    public void setupRules(List<CalcRule> rules) throws SQLException {
        for (CalcRule rule : rules) {
            setupRule(rule);
        }
    }

    /** Setup a single calculation rule */
    private void setupRule(CalcRule rule) throws SQLException {
        // 1. Query if rule exists
        boolean exists = queryRuleExists(rule.getOrgId(), rule.getPrefRuleId());

        // 2. If exists, delete it first
        if (exists) {
            deleteRule(rule.getOrgId(), rule.getPrefRuleId());
            log.debug("Deleted existing rule: {}", rule.getPrefRuleId());
        }

        // 3. Insert the new rule
        insertRule(rule);
        log.debug("Inserted rule: {}", rule.getPrefRuleId());
    }

    private boolean queryRuleExists(String orgId, String prefRuleId) throws SQLException {
        String sql =
                String.format(
                        "/*+ ORG('%s') */ /*+ ENGINE('bo_low_level') */ "
                                + "SELECT pref_rule_id FROM _PROPERTY_UPSTREAM_RULE WHERE pref_rule_id = ?",
                        orgId);

        try (Connection conn = getDataSource().getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, prefRuleId);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    private void deleteRule(String orgId, String prefRuleId) throws SQLException {
        String sql =
                String.format(
                        "/*+ ORG('%s') */ /*+ ENGINE('bo_low_level') */ "
                                + "DELETE FROM _PROPERTY_UPSTREAM_RULE WHERE pref_rule_id = ?",
                        orgId);

        try (Connection conn = getDataSource().getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, prefRuleId);
            stmt.executeUpdate();
        }
    }

    private void insertRule(CalcRule rule) throws SQLException {
        String sql =
                String.format(
                        "/*+ ORG('%s') */ /*+ ENGINE('bo_low_level') */ "
                                + "REPLACE INTO _PROPERTY_UPSTREAM_RULE("
                                + "pref_rule_id, target_category, target_comp_id, target_pref_id, "
                                + "src_category, expression, calc_type, created_user, modified_user, "
                                + "created_time, modified_time) "
                                + "VALUES(?, ?, ?, ?, ?, ?, ?, 'test', 'test', NOW(), NOW())",
                        rule.getOrgId());

        try (Connection conn = getDataSource().getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, rule.getPrefRuleId());
            stmt.setString(2, rule.getTargetCategory());
            stmt.setString(3, rule.getTargetCompId());
            stmt.setString(4, rule.getTargetPrefId());
            stmt.setString(5, rule.getSrcCategory());
            stmt.setString(6, rule.getExpression());
            stmt.setInt(7, rule.getCalcType());
            stmt.executeUpdate();
        }
    }

    /** Cleanup rules after test */
    public void cleanupRules(List<CalcRule> rules) {
        for (CalcRule rule : rules) {
            try {
                deleteRule(rule.getOrgId(), rule.getPrefRuleId());
                log.debug("Cleaned up rule: {}", rule.getPrefRuleId());
            } catch (SQLException e) {
                log.warn("Failed to cleanup rule {}: {}", rule.getPrefRuleId(), e.getMessage());
            }
        }
    }
}
