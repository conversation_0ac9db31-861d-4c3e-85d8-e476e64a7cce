package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.vo.field.FieldReq;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.util.RestQueryUtils;

import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/** @Author: qi.jiang2 @Date: 2024/04/26 11:21 @Description: */
public class TblFieldSqlProvider {

    public String queryByFieldIndex(Set<Integer> fieldIndexSet, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("field_index");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        sql.WHERE(
                String.format(
                        "field_index in (%s)", RestQueryUtils.concatStr(fieldIndexSet, false)));
        return sql.toString();
    }

    public String queryRawFieldIdByFieldIds(List<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("raw_field_id");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (String fieldId : fieldIds) {
            replace.add("'" + fieldId + "'");
        }
        sql.WHERE("field_id in (" + String.join(", ", replace) + ")");
        return sql.toString();
    }

    public String queryFieldIdsByCategoryIdAndFieldIds(
            String categoryId, List<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("field_index");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (String fieldId : fieldIds) {
            replace.add("'" + fieldId + "'");
        }
        sql.WHERE(
                "category_id = '"
                        + categoryId
                        + "' and field_id in ("
                        + String.join(", ", replace)
                        + ")");
        return sql.toString();
    }

    public String queryFieldsByCategoryId(String categoryId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("field_index");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        sql.WHERE("category_id = '" + categoryId + "'");
        return sql.toString();
    }

    public String queryFieldsByFieldIds(Set<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("raw_field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("field_index");
        sql.SELECT("horizontal");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (String fieldId : fieldIds) {
            replace.add("'" + fieldId + "'");
        }
        sql.WHERE("field_id in (" + String.join(", ", replace) + ")");
        return sql.toString();
    }

    public String queryFieldHasData(String rawFieldId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        sql.WHERE("\"" + rawFieldId + "\" is not null");
        sql.LIMIT(1);
        return sql.toString();
    }

    public String queryFieldsByFieldNameAndCategory(List<FieldReq> fieldReqs, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        if (fieldReqs != null && !fieldReqs.isEmpty()) {
            for (FieldReq fieldReq : fieldReqs) {
                replace.add(
                        "(field_name = '"
                                + fieldReq.getFieldName()
                                + "' and category_id ='"
                                + fieldReq.getCategoryReq().getCategoryId()
                                + "')");
            }
        }
        sql.WHERE(String.join(" or ", replace));
        return sql.toString();
    }

    public String queryFieldByFieldNameAndCategory(
            String fieldName, String category, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("field_id");
        sql.SELECT("field_name");
        sql.SELECT("field_display_name");
        sql.SELECT("category_id");
        sql.SELECT("field_type");
        sql.SELECT("data_type");
        sql.SELECT("unit");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_FIELD_TABLE_NAME);
        sql.WHERE("field_name = '" + fieldName + "' and category_id ='" + category + "'");
        return sql.toString();
    }

    public String queryFieldsBySystemIds(Set<String> systemIdList, String orgId) {
        String systemIds =
                systemIdList.stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT(
                                "tb.system_id, tbm.model_id, tbm.model_path, tp.pref_name, tp.pref_id, tcp.field_id");
                        FROM(orgId + ".tbl_bo_part tb");
                        INNER_JOIN(
                                orgId
                                        + ".tbl_bo_group_relation_part tbgr on tb.asset_id = tbgr.asset_id");
                        INNER_JOIN(orgId + ".tbl_bo_model tbm on tbm.group_id = tbgr.group_id");
                        INNER_JOIN(
                                orgId + ".tbl_bo_model_comp tbmc on tbmc.model_id = tbm.model_id");
                        INNER_JOIN(orgId + ".tbl_component tc on tc.comp_id = tbmc.comp_id");
                        INNER_JOIN(orgId + ".tbl_component_pref tcp on tcp.comp_id = tbmc.comp_id");
                        INNER_JOIN(
                                orgId
                                        + ".tbl_pref tp on tcp.pref_id = tp.pref_id and tp.pref_type = 'ATTRIBUTE'");
                        INNER_JOIN(orgId + ".tbl_field tf ON tcp.field_id = tf.field_id");
                        WHERE("tb.system_id in ( " + systemIds + " ) ");
                    }
                };

        return sql.toString();
    }
}
