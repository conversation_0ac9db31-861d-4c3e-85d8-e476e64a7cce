package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.dto.CalcType;
import com.envision.gravity.flink.streaming.calculate.dto.TblPropertyUpstreamRule;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;
import com.envision.gravity.flink.streaming.calculate.meta.DirectMappingProcessor;

import java.util.*;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblPropertyUpstreamRuleCdcProcessor {

    private static final Logger logger =
            LoggerFactory.getLogger(TblPropertyUpstreamRuleCdcProcessor.class);

    private static volatile TblPropertyUpstreamRuleCdcProcessor uniqueInstance;

    public static TblPropertyUpstreamRuleCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblPropertyUpstreamRuleCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblPropertyUpstreamRuleCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private final CalcPrefCache calcPrefCache;

    private final DirectMappingProcessor directMappingProcessor;

    private final CalcMetaProcessor calcMetaProcessor;

    private TblPropertyUpstreamRuleCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        this.directMappingProcessor = DirectMappingProcessor.getInstance();
        this.calcMetaProcessor = CalcMetaProcessor.getInstance();
    }

    /**
     * WARNING: CDC 处理原则： 1. Target 缓存的增删改根据 PropertyUpstreamRule 表的变更，以及 Model, Component, Property
     * 相关的表的变更 2. Source 缓存根据 Model, Component, Property 相关的表的变更，例如 SourceProperty 的删除，需要标记关联的
     * TargetProperty， 方便在后续计算时能够识别
     *
     * @param orgId
     * @param before
     * @param after
     * @param op
     */
    public void process(
            String orgId,
            TblPropertyUpstreamRule before,
            TblPropertyUpstreamRule after,
            OPEnum op) {
        if (op == OPEnum.c) {
            if (after == null) {
                return;
            }
            if (after.getCalcType() == 1) {
                logger.error("New created upstream rule is adhoc type, skip it ...");
                return;
            }
            create(orgId, after);
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error("Upstream cdc params invalid, before or after is null");
                return;
            }

            if (before.getCalcTypeEnum() == CalcType.ADHOC
                    && after.getCalcTypeEnum() == CalcType.ADHOC) {
                logger.error("Upstream rule is adhoc type, skip it...");
                return;
            }

            // Update calc_type from adhoc to preCalc, ignore before record, save after record
            if (before.getCalcTypeEnum() == CalcType.ADHOC
                    && after.getCalcTypeEnum() == CalcType.PREF_CALC) {
                logger.warn(
                        "Record type changed from adhoc to preCalc, before: {}, after: {}",
                        before,
                        after);
                create(orgId, after);
                return;
            }

            // Update calc_type from preCalc to adhoc, ignore after record, delete before record
            if (before.getCalcTypeEnum() == CalcType.PREF_CALC
                    && after.getCalcTypeEnum() == CalcType.ADHOC) {
                delete(orgId, before);
                return;
            }

            // Delete old rule and add new rule
            delete(orgId, before);
            create(orgId, after);
        } else if (op == OPEnum.d) {
            if (before == null) {
                return;
            }
            if (before.getCalcType() == 1) {
                logger.error("Deleted upstream rule is adhoc type, skip it ...");
                return;
            }
            delete(orgId, before);
        } else {
            logger.warn("Undefined cdc op: {}", op);
        }
    }

    private void delete(String orgId, TblPropertyUpstreamRule rule) {
        List<BaseCalcPropertyMeta> toDeleteMetas = new ArrayList<>(1);
        toDeleteMetas.add(
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId(rule.getTargetCategory())
                        .targetCompId(rule.getTargetCompId())
                        .targetPrefId(rule.getTargetPrefId())
                        .srcCategoryId(rule.getSrcCategory())
                        .expression(rule.getExpression())
                        .build());
        this.calcMetaProcessor.deleteTargetProperty(orgId, toDeleteMetas);

        if (logger.isDebugEnabled()) {
            CacheFactory.getCalcPrefCache().formatCacheContent(orgId);
        }
    }

    private void create(String orgId, TblPropertyUpstreamRule rule) {
        Map<PropertyId, List<BaseCalcPropertyMeta>> toCreateMetas = new HashMap<>(1);
        List<BaseCalcPropertyMeta> targetPropertyInfoList = new ArrayList<>(1);
        targetPropertyInfoList.add(
                BaseCalcPropertyMeta.builder()
                        .targetCategoryId(rule.getTargetCategory())
                        .targetCompId(rule.getTargetCompId())
                        .targetPrefId(rule.getTargetPrefId())
                        .srcCategoryId(rule.getSrcCategory())
                        .expression(rule.getExpression())
                        .calcType(rule.getCalcType())
                        .build());
        toCreateMetas.put(
                new PropertyId(rule.getTargetCompId(), rule.getTargetPrefId()),
                targetPropertyInfoList);
        this.calcMetaProcessor.refreshTargetPropertyCache(orgId, toCreateMetas, new ArrayList<>(1));

        if (logger.isDebugEnabled()) {
            CacheFactory.getCalcPrefCache().formatCacheContent(orgId);
        }
    }
}
