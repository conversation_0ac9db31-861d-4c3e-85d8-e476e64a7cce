package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.common.po.TblObjAttr;
import com.envision.gravity.flink.streaming.postgres.cdc.config.LionConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.Constants;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblBOMapper;
import com.envision.gravity.flink.streaming.postgres.cdc.utils.ListUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.client.IgniteClient;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class TblBORepository {

    private final SqlSessionFactory sqlSessionFactory;
    private final IgniteClient igniteClient;
    private final ExecutorService executorService;
    private static final int REFRESH_OBJECT_BATCH_SIZE = LionConfig.getRefreshObjectBatchSize();

    public TblBORepository(
            SqlSessionFactory sqlSessionFactory,
            IgniteClient igniteClient,
            ExecutorService executorService) {
        this.sqlSessionFactory = sqlSessionFactory;
        this.igniteClient = igniteClient;
        this.executorService = executorService;
    }

    public int updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> assetIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (assetIdList.isEmpty()) {
                return 0;
            }

            TblBOMapper tblBOMapper = session.getMapper(TblBOMapper.class);
            return tblBOMapper.updateSysModifiedTimeByPrimaryKeys(schemaName, assetIdList);
        } catch (Exception e) {
            log.error("Update bo sys_modified_time error.", e);
            throw new GravityRuntimeException("Update bo sys_modified_time error.", e);
        }
    }

    public void batchRefreshObjectDetailByAssetIds(String schemaName, List<String> assetIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (assetIdList.isEmpty()) {
                return;
            }

            // refresh object detail concurrently
            long refreshObjectDetailConcurrentlyStartTime = System.currentTimeMillis();
            // Fix pg deadlock bug: sort by assetId in ascending order
            Collections.sort(assetIdList);

            List<List<String>> subLists =
                    ListUtils.splitList(assetIdList, REFRESH_OBJECT_BATCH_SIZE);

            CompletableFuture.allOf(
                            subLists.stream()
                                    .map(
                                            assetIdSubList ->
                                                    CompletableFuture.runAsync(
                                                            () ->
                                                                    batchRefreshObjectDetail(
                                                                            session,
                                                                            schemaName,
                                                                            assetIdSubList),
                                                            executorService))
                                    .toArray(CompletableFuture[]::new))
                    .get();
            long refreshObjectDetailConcurrentlyEndTime = System.currentTimeMillis();
            log.info(
                    "Refresh object detail total time cost: [{}], "
                            + "schemaName: [{}], asset count: [{}].",
                    (refreshObjectDetailConcurrentlyEndTime
                                            - refreshObjectDetailConcurrentlyStartTime)
                                    / 1000.0
                            + "s",
                    schemaName,
                    assetIdList.size());
        } catch (Exception e) {
            log.error("Refresh object detail error.", e);
            throw new GravityRuntimeException("Refresh object detail error.", e);
        }
    }

    private void batchRefreshObjectDetail(
            SqlSession session, String schemaName, List<String> assetIdList) {
        long batchRefreshObjectDetailStartTime = System.currentTimeMillis();
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));
        // 1 query obj attr
        String queryObjAttrSql =
                String.format(
                        "select toa.system_id,\n"
                                + "       toa.field_index,\n"
                                + "       toa.value_bool,\n"
                                + "       toa.value_string,\n"
                                + "       toa.value_long,\n"
                                + "       toa.value_double,\n"
                                + "       toa.value_json,\n"
                                + "       toa.modified_time\n"
                                + "from %s toa\n"
                                + "         inner join %s tb on toa.system_id = tb.system_id\n"
                                + "where tb.asset_id in (%s);",
                        Constants.TBL_OBJ_ATTRIBUTE_TABLE_NAME,
                        Constants.TBL_BO_TABLE_NAME,
                        assetIds);

        List<List<?>> objAttributeResultSet =
                igniteClient
                        .query(
                                new SqlFieldsQuery(queryObjAttrSql)
                                        .setSchema(schemaName.toUpperCase())
                                        .setDistributedJoins(true))
                        .getAll();

        List<TblObjAttr> tblObjAttrList = extractTblObjAttrList(objAttributeResultSet);
        long queryAttributeEndTime = System.currentTimeMillis();

        // 2 full_refresh_object_detail_with_attr_value
        TblBOMapper tblBOMapper = session.getMapper(TblBOMapper.class);
        tblBOMapper.fullRefreshObjectDetailWithAttrValue(schemaName, assetIdList, tblObjAttrList);
        long batchRefreshObjectDetailEndTime = System.currentTimeMillis();
        log.info(
                "Batch refresh object detail time cost: [{}], schemaName: [{}], "
                        + "asset count: [{}], attr count: [{}], "
                        + "query attribute time cost: [{}], full refresh object detail time cost: [{}].",
                (batchRefreshObjectDetailEndTime - batchRefreshObjectDetailStartTime) / 1000.0
                        + "s",
                schemaName,
                assetIdList.size(),
                tblObjAttrList.size(),
                (queryAttributeEndTime - batchRefreshObjectDetailStartTime) / 1000.0 + "s",
                (batchRefreshObjectDetailEndTime - queryAttributeEndTime) / 1000.0 + "s");
    }

    private List<TblObjAttr> extractTblObjAttrList(List<List<?>> data) {
        List<TblObjAttr> tblObjAttrList = new ArrayList<>();
        for (List<?> row : data) {
            tblObjAttrList.add(
                    TblObjAttr.builder()
                            .systemId(String.valueOf(row.get(0)))
                            .fieldIndex(Integer.valueOf(String.valueOf(row.get(1))))
                            .valueBool(
                                    row.get(2) != null
                                            ? Boolean.valueOf(String.valueOf(row.get(2)))
                                            : null)
                            .valueString(row.get(3) != null ? String.valueOf(row.get(3)) : null)
                            .valueLong(
                                    row.get(4) != null
                                            ? Long.valueOf(String.valueOf(row.get(4)))
                                            : null)
                            .valueDouble(
                                    row.get(5) != null
                                            ? Double.valueOf(String.valueOf(row.get(5)))
                                            : null)
                            .valueJson(row.get(6) != null ? String.valueOf(row.get(6)) : null)
                            .modifiedTime(Timestamp.valueOf(String.valueOf(row.get(7))))
                            .build());
        }
        return tblObjAttrList;
    }
}
