package com.envision.gravity.ignite.tsdb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


import io.eniot.tsdb.common.TSDBNodeStatus;
import io.eniot.tsdb.common.sharding.ResponseResult;
import io.eniot.tsdb.common.sharding.ShardingRule;
import io.eniot.tsdb.common.sharding.TSDBMode;
import io.eniot.tsdb.common.sharding.TSDBNode;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ignite.Ignite;
import org.apache.ignite.Ignition;
import org.apache.ignite.cluster.ClusterState;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/** <AUTHOR> 2024/8/14 */
public class MetaServiceClientTest {

    private TSDBMetaServiceImpl tsdbMetaService;

    @BeforeEach
    void setUp() {
        // 在每个测试方法执行前初始化
        Ignite ignite = Ignition.start("tsdb-meta-config.xml");
        ignite.cluster().state(ClusterState.ACTIVE);
        System.out.println(">>> " + ignite.cacheNames());

        tsdbMetaService = new TSDBMetaServiceImpl();
        tsdbMetaService.setIgnite(ignite);
        tsdbMetaService.setLogger(ignite.log());
    }

    @Test
    public void highWeightTest() {
        int max = 1000000;
        // int max = 5;
        Set<Pair<String, String>> series = new HashSet<>(max);
        for (int i = 0; i < max; i++) {
            series.add(Pair.of("asset" + i, "pt" + i));
        }

        List<TSDBNode> allNodes = new ArrayList<>(3);
        TSDBNode n1 = new TSDBNode("influxdb2.apaas-beta30.eniot.io");
        n1.nodeHashId(3006269876L);
        n1.setWeight(1);

        TSDBNode n2 = new TSDBNode("influxdb-shard1.apaas-beta30.eniot.io");
        n2.nodeHashId(3222603144L);
        n2.setWeight(1);

        TSDBNode n3 = new TSDBNode("influxdb-shard2.apaas-beta30.eniot.io");
        n3.nodeHashId(2030897778L);
        n3.setWeight(8);

        allNodes.add(n1);
        allNodes.add(n2);
        allNodes.add(n3);

        Map<Long, TSDBNode> nodeMap = getNodeMap(allNodes);

        long start = System.currentTimeMillis();
        ResponseResult<Map<Pair<String, String>, List<Long>>> writeResult =
                tsdbMetaService.getShardsV2(
                        "o18729267101629999",
                        "TSDB_DEAULT",
                        series,
                        allNodes,
                        ShardingRule.SHARDING_BY_MEASUREMENT,
                        2,
                        TSDBMode.WRITE);
        long init = System.currentTimeMillis();
        System.out.printf("init time %d ms", init - start);
        System.out.println("\nGrouping write: ");
        groupByNode(writeResult.getData(), nodeMap);

        ResponseResult<Map<Pair<String, String>, List<Long>>> readResult =
                tsdbMetaService.getShardsV2(
                        "o18729267101629999",
                        "TSDB_DEAULT",
                        series,
                        allNodes,
                        ShardingRule.SHARDING_BY_MEASUREMENT,
                        2,
                        TSDBMode.READ);
        System.out.println("Grouping read: ");
        groupByNode(readResult.getData(), nodeMap);

        System.out.println("111111");
    }

    @Test
    public void initSeriesReplicas() {
        TSDBMetaServiceImpl metaService = new TSDBMetaServiceImpl();

        // N=1
        TSDBNode oneNode =
                new TSDBNode("http://192.168.100.001:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.ONLINE);
        List<TSDBNode> oneNodeCluster = new ArrayList<>();
        oneNodeCluster.add(oneNode);
        List<TSDBNode> oneReplicas = metaService.initSeriesReplicas(oneNode, oneNodeCluster, 1);

        // N=2 ONLINE=2
        TSDBNode twoNodeA =
                new TSDBNode("http://192.168.100.001:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.ONLINE);
        TSDBNode twoNodeB =
                new TSDBNode("http://192.168.100.002:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.ONLINE);
        List<TSDBNode> twoNodeCluster1 = new ArrayList<>();
        twoNodeCluster1.add(twoNodeA);
        twoNodeCluster1.add(twoNodeB);
        List<TSDBNode> twoReplicas1 = metaService.initSeriesReplicas(twoNodeA, twoNodeCluster1, 2);
        Assertions.assertEquals(2, twoReplicas1.size());
        twoReplicas1.forEach(r -> Assertions.assertEquals(r.getStatus(), TSDBNodeStatus.ONLINE));

        // N=2 ONLINE=1, RECOVERY=1
        TSDBNode twoNodeC =
                new TSDBNode("http://192.168.100.001:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.ONLINE);
        TSDBNode twoNodeD =
                new TSDBNode("http://192.168.100.002:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.RECOVERY);
        List<TSDBNode> twoNodeCluster2 = new ArrayList<>();
        twoNodeCluster2.add(twoNodeC);
        twoNodeCluster2.add(twoNodeD);
        List<TSDBNode> twoReplicas2 = metaService.initSeriesReplicas(twoNodeC, twoNodeCluster2, 2);
        Assertions.assertEquals(2, twoReplicas2.size());
        List<TSDBNode> twoReplicas2_Online =
                twoReplicas2.stream()
                        .filter(r -> r.getStatus() == TSDBNodeStatus.ONLINE)
                        .collect(Collectors.toList());
        List<TSDBNode> twoReplicas2_Recovery =
                twoReplicas2.stream()
                        .filter(r -> r.getStatus() == TSDBNodeStatus.RECOVERY)
                        .collect(Collectors.toList());
        Assertions.assertEquals(1, twoReplicas2_Online.size());
        Assertions.assertEquals(1, twoReplicas2_Recovery.size());

        // N=2 ONLINE=0, RECOVERY=2
        TSDBNode twoNodeE =
                new TSDBNode("http://192.168.100.001:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.RECOVERY);
        TSDBNode twoNodeF =
                new TSDBNode("http://192.168.100.002:8086?readTimeout=5000")
                        .clusterName("default")
                        .userName("admin")
                        .auth("password")
                        .weight(1)
                        .seriesCount(0)
                        .status(TSDBNodeStatus.RECOVERY);
        List<TSDBNode> twoNodeCluster3 = new ArrayList<>();
        twoNodeCluster3.add(twoNodeE);
        twoNodeCluster3.add(twoNodeF);
        List<TSDBNode> twoReplicas3 = metaService.initSeriesReplicas(twoNodeE, twoNodeCluster3, 2);
        Assertions.assertEquals(2, twoReplicas3.size());
        List<TSDBNode> twoReplicas3_Online =
                twoReplicas3.stream()
                        .filter(r -> r.getStatus() == TSDBNodeStatus.ONLINE)
                        .collect(Collectors.toList());
        List<TSDBNode> twoReplicas3_Recovery =
                twoReplicas3.stream()
                        .filter(r -> r.getStatus() == TSDBNodeStatus.RECOVERY)
                        .collect(Collectors.toList());
        Assertions.assertEquals(0, twoReplicas3_Online.size());
        Assertions.assertEquals(2, twoReplicas3_Recovery.size());
    }

    // =========================================================================================
    //
    // =========================================================================================
    private void groupByNode(
            Map<Pair<String, String>, List<Long>> meta, Map<Long, TSDBNode> nodes) {
        Map<Long, Long> node1Count = new HashMap<>(3);
        Map<Long, Long> node2Count = new HashMap<>(3);

        for (Map.Entry<Pair<String, String>, List<Long>> entry : meta.entrySet()) {
            List<Long> shards = entry.getValue();
            Long nodeId1 = shards.get(0);
            Long nodeId2 = shards.get(1);
            node1Count.put(nodeId1, node1Count.getOrDefault(nodeId1, 0L) + 1);
            node2Count.put(nodeId2, node2Count.getOrDefault(nodeId2, 0L) + 1);
        }

        node1Count.forEach(
                (nodeId, cnt) -> {
                    TSDBNode node = nodes.get(nodeId);
                    System.out.printf(
                            "| node1: %-40s | weight: %2d | count: %d\n",
                            node.getAddress(), node.getWeight(), cnt);
                });
        System.out.print("\n");
        node2Count.forEach(
                (nodeId, cnt) -> {
                    TSDBNode node = nodes.get(nodeId);
                    System.out.printf(
                            "| node2: %-40s | weight: %2d | count: %d\n",
                            node.getAddress(), node.getWeight(), cnt);
                });
    }

    private Map<Long, TSDBNode> getNodeMap(List<TSDBNode> nodes) {
        return nodes.stream().collect(Collectors.toMap(TSDBNode::getNodeHashId, node -> node));
    }
}
