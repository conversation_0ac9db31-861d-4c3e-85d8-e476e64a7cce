package com.envision.gravity.flink.streaming.bo.sync;

import com.envision.gravity.flink.streaming.bo.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.bo.sync.deserializer.KafkaRecordDeserializer;
import com.envision.gravity.flink.streaming.bo.sync.function.MsgAggFunction;
import com.envision.gravity.flink.streaming.bo.sync.function.ParseMsgFunction;
import com.envision.gravity.flink.streaming.bo.sync.function.WindowCollector;
import com.envision.gravity.flink.streaming.bo.sync.sink.GravityRestApiSink;
import com.envision.gravity.flink.streaming.bo.sync.util.KeySelectUtil;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.triggers.CountTrigger;
import org.apache.flink.streaming.api.windowing.triggers.ProcessingTimeoutTrigger;
import org.apache.flink.streaming.api.windowing.triggers.PurgingTrigger;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Slf4j
public class FlinkStreamBOSync {
    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().enableObjectReuse();

        int timeWindowIntervalInMs = LionConfig.getTimeWindowIntervalInMs();
        int windowCountSize = LionConfig.getWindowCountSize();

        // 1.source
        List<String> topics =
                Arrays.stream(LionConfig.getKafkaTopics().split(","))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());

        KafkaSource<ConsumerRecord<byte[], byte[]>> source =
                KafkaSource.<ConsumerRecord<byte[], byte[]>>builder()
                        .setBootstrapServers(LionConfig.getKafkaServers())
                        .setGroupId(LionConfig.getKafkaConsumerGroup())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setDeserializer(
                                new KafkaRecordDeserializer()) // Use the raw ConsumerRecord
                        .setProperty("enable.auto.commit", "true")
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setTopics(topics)
                        .build();

        DataStreamSource<ConsumerRecord<byte[], byte[]>> kafkaSource =
                env.fromSource(source, WatermarkStrategy.noWatermarks(), "Kafka Source");

        int parallelism = env.getParallelism();
        int[] keySelectorArray = KeySelectUtil.createRebalanceKeys(parallelism);
        log.info("Gravity-Flink: Current parallelism: {}.", parallelism);

        // 2.process
        kafkaSource
                .process(new ParseMsgFunction())
                .keyBy(
                        parsedResult ->
                                (keySelectorArray[parsedResult.f1.getPartition() % (parallelism)]))
                .countWindow(windowCountSize)
                .trigger(
                        PurgingTrigger.of(
                                ProcessingTimeoutTrigger.of(
                                        CountTrigger.of(windowCountSize),
                                        Duration.ofMillis(timeWindowIntervalInMs))))
                .process(new WindowCollector())
                .process(new MsgAggFunction())
                .addSink(new GravityRestApiSink());

        env.execute("Flink Streaming BO Sync");
    }
}
