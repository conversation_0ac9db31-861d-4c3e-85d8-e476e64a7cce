package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.repository.PGCatalogRepository;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.ibatis.session.SqlSessionFactory;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
@Slf4j
public class ScheduledSource extends RichSourceFunction<RefreshReq> {

    private static final long serialVersionUID = 4252668923211125652L;
    private static final int SCHEDULE_INTERVAL_IN_HOURS =
            LionConfig.getScheduleTimeIntervalInHours();
    private transient ScheduledExecutorService scheduledExecutorService;
    private transient LinkedBlockingQueue<RefreshReq> blockingQueue;

    private transient PGCatalogRepository pgCatalogRepository;
    private volatile boolean isRunning = true;

    @Override
    public void open(Configuration parameters) throws Exception {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        scheduledExecutorService =
                new ScheduledThreadPoolExecutor(
                        1,
                        new ThreadFactory() {
                            private final AtomicInteger poolNumber = new AtomicInteger(1);

                            @Override
                            public Thread newThread(@NotNull Runnable r) {
                                return new Thread(
                                        r,
                                        String.format(
                                                "Virtual-Attr-Sync-Scheduled-Thread-Pool-%d",
                                                poolNumber.getAndIncrement()));
                            }
                        });
        blockingQueue = new LinkedBlockingQueue<>();
        pgCatalogRepository = new PGCatalogRepository(sqlSessionFactory);
        scheduledExecutorService.scheduleWithFixedDelay(
                () -> {
                    try {
                        log.info("Fetch and emit data.");
                        List<String> pgSchemeList = pgCatalogRepository.queryScheme();
                        for (String schemeName : pgSchemeList) {
                            blockingQueue.put(RefreshReq.builder().schemaName(schemeName).build());
                        }
                    } catch (Exception e) {
                        log.error("Fetch and emit data error!", e);
                    }
                },
                SCHEDULE_INTERVAL_IN_HOURS,
                SCHEDULE_INTERVAL_IN_HOURS,
                TimeUnit.HOURS);
    }

    @Override
    public void close() throws Exception {
        try {
            isRunning = false;
            scheduledExecutorService.shutdownNow();
            PGDataSourceConfig.closeDataSource();
            log.info("Scheduled source is closed.");
        } catch (Exception e) {
            log.error("Close scheduled source error.", e);
            throw new GravityRuntimeException("Close scheduled source error.", e);
        }
    }

    @Override
    public void run(SourceContext<RefreshReq> ctx) throws Exception {
        if (!isRunning) {
            return;
        }

        while (isRunning) {
            log.info("Start virtual attr sync scheduler.");
            ctx.collect(blockingQueue.take());
        }
    }

    @Override
    public void cancel() {
        isRunning = false;
        log.info("Scheduled source is cancelled.");
    }
}
