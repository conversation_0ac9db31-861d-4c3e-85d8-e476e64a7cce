package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.common.utils.FlinkCommonUtils;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcRouter;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcWindowCollector;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaCdcProcessor;

import java.time.Duration;
import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.triggers.CountTrigger;
import org.apache.flink.streaming.api.windowing.triggers.ProcessingTimeoutTrigger;
import org.apache.flink.streaming.api.windowing.triggers.PurgingTrigger;

@Slf4j
public class MetaFlow {

    public MetaFlow() {}

    /**
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        FlinkCommonUtils.tryRunAsLocalModel(args, env);

        // Create CDC source stream
        // Process CDC records through router and window collector
        DataStream<ConvertedCdcRecord> cdcStream =
                env.addSource(initPgCdcSource())
                        .name("pgcdc")
                        .setParallelism(1)
                        .process(new CalcCdcRouter())
                        .name("cdc-router")
                        .keyBy(record -> record.getTable() + "_" + record.getPrimaryKey())
                        .countWindow(CalcLionConfig.getCalcMetaPgCdcWindowBatchSize())
                        .trigger(
                                PurgingTrigger.of(
                                        ProcessingTimeoutTrigger.of(
                                                CountTrigger.of(
                                                        CalcLionConfig
                                                                .getCalcMetaPgCdcWindowBatchSize()),
                                                Duration.ofSeconds(
                                                        CalcLionConfig
                                                                .getCalcMetaPgCdcWindowTimeoutSeconds()))))
                        .process(new CalcCdcWindowCollector())
                        .name("cdc-window-collector");

        // Process merged CDC records
        cdcStream.process(new CalcMetaCdcProcessor()).name("cdc-processor");

        env.execute("Gravity calculate meta flow");
    }

    private static SourceFunction<String> initPgCdcSource() {
        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", CalcLionConfig.getCalcMetaPgCdcSchemaList());
        properties.setProperty("table.include.list", CalcLionConfig.getCalcMetaPgCdcTableList());
        //        properties.setProperty(
        //                "column.include.list", CalculateConfigOptions.getPgCdcColumnList());
        properties.setProperty("max.batch.size", CalcLionConfig.getCalcMetaPgCdcMaxBatchSize());
        properties.setProperty("max.queue.size", CalcLionConfig.getCalcMetaPgCdcMaxQueueSize());

        return PostgreSQLSource.<String>builder()
                .hostname(CalcLionConfig.getPgHostname())
                .port(CalcLionConfig.getPgPort())
                .database(CalcLionConfig.getPgDatabase()) // monitor postgres database
                .username(CalcLionConfig.getPgUsername())
                .password(CalcLionConfig.getPgPassword())
                .decodingPluginName("pgoutput")
                .slotName(CalcLionConfig.getCalcMetaPgCdcSlotName())
                .debeziumProperties(properties)
                .deserializer(new JsonDebeziumDeserializationSchema())
                .build();
    }
}
