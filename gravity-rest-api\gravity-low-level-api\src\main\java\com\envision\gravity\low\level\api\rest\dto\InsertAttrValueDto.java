package com.envision.gravity.low.level.api.rest.dto;

import com.envision.gravity.common.po.TblObjField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/** @Author: qi.jiang2 @Date: 2024/05/17 10:17 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsertAttrValueDto {

    Map<String, Map<String, Object>> needInsertValue;

    List<TblObjField> needAddObjField;
}
