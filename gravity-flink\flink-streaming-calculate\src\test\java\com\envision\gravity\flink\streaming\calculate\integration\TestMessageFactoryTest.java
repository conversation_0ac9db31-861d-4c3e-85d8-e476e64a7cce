package com.envision.gravity.flink.streaming.calculate.integration;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Test class for YamlTestMessageFactory */
public class TestMessageFactoryTest {
    private static final Logger logger = LoggerFactory.getLogger(TestMessageFactoryTest.class);

    @Test
    void testLoadYamlScenarios() throws Exception {
        TestMessageFactory factory = new TestMessageFactory();

        // Load scenarios from JSON file
        List<TestMessageFactory.YamlTestScenario> scenarios =
                factory.loadScenariosFromYaml("test-data/test-messages2.json");

        // Verify scenarios were loaded
        assertNotNull(scenarios);
        assertFalse(scenarios.isEmpty());

        logger.info("Loaded {} JSON scenarios", scenarios.size());

        // Verify first scenario
        TestMessageFactory.YamlTestScenario firstScenario = scenarios.get(0);
        assertNotNull(firstScenario);
        assertEquals("one_source_multiple_targets", firstScenario.getName());
        assertEquals("o17186913277371853", firstScenario.getOrgId());

        // Verify input message
        assertNotNull(firstScenario.getInputMessage());
        assertNotNull(firstScenario.getInputJson());

        // Verify output messages
        assertNotNull(firstScenario.getExpectedOutputMessages());
        assertNotNull(firstScenario.getExpectedOutputJsons());
        assertEquals(2, firstScenario.getExpectedOutputMessages().size());
        assertEquals(2, firstScenario.getExpectedOutputJsons().size());

        // Verify categorization
        assertNotNull(firstScenario.getDirectMappingOutputs());
        assertNotNull(firstScenario.getNonDirectMappingOutputs());

        logger.info("First scenario: {}", firstScenario.getName());
        logger.info("  Input: {}", firstScenario.getInputMessage());
        logger.info("  Expected outputs: {}", firstScenario.getExpectedOutputMessages().size());
        logger.info("  Direct mapping outputs: {}", firstScenario.getDirectMappingOutputs().size());
        logger.info(
                "  Non-direct mapping outputs: {}",
                firstScenario.getNonDirectMappingOutputs().size());

        // Log output details
        for (int i = 0; i < firstScenario.getExpectedOutputMessages().size(); i++) {
            logger.info("  Output {}: {}", i + 1, firstScenario.getExpectedOutputMessages().get(i));
        }
    }

    @Test
    void testOutputCategorization() throws Exception {
        TestMessageFactory factory = new TestMessageFactory();

        List<TestMessageFactory.YamlTestScenario> scenarios =
                factory.loadScenariosFromYaml("test-data/test-messages2.json");

        TestMessageFactory.YamlTestScenario scenario = scenarios.get(0);

        // Verify that outputs are properly categorized
        List<JsonNode> directOutputs = scenario.getDirectMappingOutputs();
        List<JsonNode> nonDirectOutputs = scenario.getNonDirectMappingOutputs();

        // Total outputs should equal direct + non-direct
        assertEquals(
                scenario.getExpectedOutputJsons().size(),
                directOutputs.size() + nonDirectOutputs.size());

        logger.info("Categorization results:");
        logger.info("  Total outputs: {}", scenario.getExpectedOutputJsons().size());
        logger.info("  Direct mapping outputs: {}", directOutputs.size());
        logger.info("  Non-direct mapping outputs: {}", nonDirectOutputs.size());

        // Analyze each output
        for (int i = 0; i < scenario.getExpectedOutputJsons().size(); i++) {
            JsonNode output = scenario.getExpectedOutputJsons().get(i);
            JsonNode points = output.get("payload").get(0).get("points");

            logger.info("  Output {} points: {}", i + 1, points);

            // Check if this output contains calculated points
            boolean hasCalculatedPoints = false;
            java.util.Iterator<String> iterator = points.fieldNames();
            while (iterator.hasNext()) {
                String pointName = iterator.next();
                if (pointName.contains("_Plus") || pointName.contains("_Calc")) {
                    hasCalculatedPoints = true;
                    break;
                }
            }

            logger.info("    Has calculated points: {}", hasCalculatedPoints);
        }
    }

    @Test
    void testTopicNameGeneration() throws Exception {
        TestMessageFactory factory = new TestMessageFactory();

        List<TestMessageFactory.YamlTestScenario> scenarios =
                factory.loadScenariosFromYaml("test-data/test-messages2.json");

        TestMessageFactory.YamlTestScenario scenario = scenarios.get(0);

        String inputTopic = factory.getInputTopicName(scenario);
        String outputTopic = factory.getOutputTopicName(scenario);

        assertNotNull(inputTopic);
        assertNotNull(outputTopic);

        logger.info("Topic names:");
        logger.info("  Input topic: {}", inputTopic);
        logger.info("  Output topic: {}", outputTopic);

        // Verify topic format
        assertTrue(inputTopic.contains(scenario.getOrgId()));
        assertTrue(outputTopic.contains(scenario.getOrgId()));
    }

    @Test
    void testDirectVsNonDirectSeparation() throws Exception {
        TestMessageFactory factory = new TestMessageFactory();

        List<TestMessageFactory.YamlTestScenario> scenarios =
                factory.loadScenariosFromYaml("test-data/test-messages2.json");

        for (TestMessageFactory.YamlTestScenario scenario : scenarios) {
            logger.info("Testing output separation for scenario: {}", scenario.getName());

            List<JsonNode> directOutputs = scenario.getDirectMappingOutputs();
            List<JsonNode> nonDirectOutputs = scenario.getNonDirectMappingOutputs();

            // Verify direct mapping outputs contain only input point names
            for (JsonNode directOutput : directOutputs) {
                JsonNode inputPoints = scenario.getInputJson().get("payload").get(0).get("points");
                JsonNode outputPoints = directOutput.get("payload").get(0).get("points");

                outputPoints
                        .fieldNames()
                        .forEachRemaining(
                                outputPointName -> {
                                    assertTrue(
                                            inputPoints.has(outputPointName),
                                            "Direct mapping output should only contain input point names: "
                                                    + outputPointName);
                                });
            }

            // Verify non-direct mapping outputs contain calculated point names
            for (JsonNode nonDirectOutput : nonDirectOutputs) {
                JsonNode outputPoints = nonDirectOutput.get("payload").get(0).get("points");
                boolean hasCalculatedPoints = false;

                java.util.Iterator<String> iterator = outputPoints.fieldNames();
                while (iterator.hasNext()) {
                    String pointName = iterator.next();
                    if (pointName.contains("_Plus")
                            || pointName.contains("_Calc")
                            || pointName.contains("Efficiency")
                            || pointName.contains("Ratio")
                            || pointName.contains("Index")
                            || pointName.contains("Doubled")) {
                        hasCalculatedPoints = true;
                        break;
                    }
                }

                assertTrue(
                        hasCalculatedPoints,
                        "Non-direct mapping output should contain calculated point names");
            }

            logger.info(
                    "  Direct outputs: {}, Non-direct outputs: {}",
                    directOutputs.size(),
                    nonDirectOutputs.size());
        }
    }
}
