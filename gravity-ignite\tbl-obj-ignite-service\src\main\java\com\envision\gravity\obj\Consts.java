package com.envision.gravity.obj;

public class Consts {
    public static final String TBL_OBJ_CACHE_NAME_PATTERN = "%s_TBL_OBJ";
    public static final String TBL_OBJ_CACHE_KEY_TYPE_PATTERN = "%s_TBL_OBJ_KEY";
    public static final String TBL_OBJ_KEY_FIELD = "SYSTEM_ID";

    public static final String LAST_CHANGE_CACHE_NAME_PATTERN = "%s_TBL_LAST_CHANGED";
    public static final String LAST_CHANGE_CACHE_KEY_TYPE_PATTERN = "%s_TBL_LAST_CHANGED_KEY";
    public static final String LAST_CHANGE_CACHE_VALUE_TYPE_PATTERN = "%s_TBL_LAST_CHANGED_VALUE";
    public static final String LAST_CHANGE_KEY_FIELD_SYSTEM_ID = "SYSTEM_ID";
    public static final String LAST_CHANGE_KEY_FIELD_RAW_FILED_ID = "RAW_FIELD_ID";

    public static final String TBL_OBJ_POINT_CACHE_NAME_PATTERN = "%s_TBL_OBJ_POINT_PART";
    public static final String TBL_OBJ_POINT_CACHE_KEY_TYPE_PATTERN = "%s_TBL_OBJ_POINT_PART_KEY";
    public static final String TBL_OBJ_POINT_CACHE_VALUE_TYPE_PATTERN =
            "%s_TBL_OBJ_POINT_PART_VALUE";
    public static final String TBL_OBJ_POINT_KEY_SERIES_ID = "SERIES_ID";

    public static final String TBL_OBJ_PART_CACHE_NAME_PATTERN = "%s_TBL_OBJ_PART";
    public static final String TBL_OBJ_PART_CACHE_KEY_TYPE_PATTERN = "%s_TBL_OBJ_PART_KEY";
    public static final String TBL_OBJ_PART_KEY_FIELD = "SYSTEM_ID";
}
