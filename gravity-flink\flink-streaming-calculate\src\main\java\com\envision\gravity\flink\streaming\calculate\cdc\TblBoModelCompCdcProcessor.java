package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.dto.TblBoModelComp;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblBoModelCompCdcProcessor {

    private static final Logger logger = LoggerFactory.getLogger(TblBoModelCompCdcProcessor.class);

    private static volatile TblBoModelCompCdcProcessor uniqueInstance;

    public static TblBoModelCompCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblBoModelCompCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblBoModelCompCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private final CalcPrefCache calcPrefCache;

    private TblBoModelCompCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
    }

    public void process(String orgId, TblBoModelComp before, TblBoModelComp after, OPEnum op) {
        if (op == OPEnum.c) {
            logger.info("New created model component, skip it...");
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error(
                        "Model component cdc params invalid when update, before or after is null");
                return;
            }
        } else if (op == OPEnum.d) {
            if (before == null) {
                logger.error("Model component cdc params invalid when delete, before is null");
                return;
            }
            // target compId has been removed from target model, need to remark related rules
            // invalid
            this.calcPrefCache.deleteByTargetModelIdAndCompId(
                    orgId, before.getModelId(), before.getCompId());
        }
    }
}
