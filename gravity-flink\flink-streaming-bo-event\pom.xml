<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-flink</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>flink-streaming-bo-event</artifactId>
    <packaging>jar</packaging>
    <version>${release.version}</version>
    <name>flink-streaming-bo-event</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- flink -->
        <dependency>
            <groupId>com.ververica</groupId>
            <artifactId>flink-sql-connector-postgres-cdc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.version}</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients_${scala.version}</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-runtime-web_${scala.version}</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>1.17.1</version>
            <scope>provided</scope>
        </dependency>
        <!-- flink -->

        <!-- gravity -->
        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ignite-core</artifactId>
                    <groupId>org.apache.ignite</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>perf4j</artifactId>
                    <groupId>org.perf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- gravity -->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.envision.arch.lion</groupId>
            <artifactId>lion-client</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>bo-event-${project.version}</finalName>
    </build>
</project>