package com.envision.gravity.flink.streaming.bo.sync.config;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Slf4j
public class LionConfig {
    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getKafkaTopics() {
        return getStringValue(
                "gravity-flink.bo-sync.kafka.topics",
                "GRAVITY_BO_SYNC_TOPIC,GRAVITY_BO_SYNC_RETRY_TOPIC");
    }

    public static String getKafkaServers() {
        return getStringValue("Camel.kafka-common", null);
    }

    public static String getKafkaConsumerGroup() {
        return getStringValue(
                "gravity-flink.bo-sync.kafka.consumer.group", "gravity_flink_bo_sync");
    }

    public static int getTimeWindowIntervalInMs() {
        return getIntValue("gravity-flink.bo-sync.time.window.interval.in.ms", 3000);
    }

    public static int getWindowCountSize() {
        return getIntValue("gravity-flink.bo-sync.window.count.size", 100);
    }

    public static String getGravityRestApiUrl() {
        return getStringValue("gravity-common.rest.url", "http://localhost:18080");
    }

    // retry
    public static String getRetryKafkaTopic() {
        return getStringValue(
                "gravity-flink.bo-sync.retry.kafka.topic", "GRAVITY_BO_SYNC_RETRY_TOPIC");
    }

    public static int getMaxRetries() {
        return getIntValue("gravity-flink.bo-sync.max.retries", -1);
    }
}
