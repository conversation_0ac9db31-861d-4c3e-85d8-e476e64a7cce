package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsg;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgList;

import java.util.ArrayList;
import java.util.List;


import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.GlobalWindow;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Window collector that aggregates messages within a window and forwards them as a batch. Collects
 * individual messages and creates message lists for batch processing.
 *
 * <AUTHOR>
 */
public class WindowCollector
        extends ProcessWindowFunction<
                Tuple2<LegacyMsg, OffsetInfo>,
                Tuple2<LegacyMsgList, OffsetInfo>,
                Integer,
                GlobalWindow> {
    private static final Logger logger = LoggerFactory.getLogger(WindowCollector.class);

    @Override
    public void process(
            Integer s,
            ProcessWindowFunction<
                                    Tuple2<LegacyMsg, OffsetInfo>,
                                    Tuple2<LegacyMsgList, OffsetInfo>,
                                    Integer,
                                    GlobalWindow>
                            .Context
                    context,
            Iterable<Tuple2<LegacyMsg, OffsetInfo>> elements,
            Collector<Tuple2<LegacyMsgList, OffsetInfo>> out)
            throws Exception {

        List<LegacyMsg> data = new ArrayList<>();
        OffsetInfo memo = new OffsetInfo();
        int messageCount = 0;

        for (Tuple2<LegacyMsg, OffsetInfo> value : elements) {
            if (value.f0 != null) {
                data.add(value.f0);
                messageCount++;
                logger.debug(
                        "Collecting message #{}: orgId={}, modelId={}",
                        messageCount,
                        value.f0.getOrgId(),
                        value.f0.getModelId());
            }
            memo.collect(value.f1);
        }

        LegacyMsgList msgList = new LegacyMsgList(data);
        Tuple2<LegacyMsgList, OffsetInfo> output = new Tuple2<>(msgList, memo);

        logger.debug(
                "Window processing completed, collected {} messages, sending downstream",
                messageCount);
        logger.debug(
                "Output type: {}, message list size: {}",
                output.getClass().getName(),
                msgList.getLegacyMsgList().size());

        try {
            out.collect(output);
            logger.debug("Data successfully sent to downstream processor");
        } catch (Exception e) {
            logger.error("Exception occurred while sending data downstream: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void close() {}
}
