package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblModelRelation {
    @KeyColumn(name = "from_model_id")
    @ColumnName("from_model_id")
    @RequiredField(message = "from_model_id field is required")
    private String fromModelId;

    @KeyColumn(name = "to_model_id")
    @ColumnName("to_model_id")
    @RequiredField(message = "to_model_id field is required")
    private String toModelId;

    @KeyColumn(name = "relation_type")
    @ColumnName("relation_type")
    @RequiredField(message = "relation_type field is required")
    private String relationType;

    @KeyColumn(name = "level", type = Integer.class)
    @ColumnName("level")
    @RequiredField(message = "level field is required")
    private int level;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
