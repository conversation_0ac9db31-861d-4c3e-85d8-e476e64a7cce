package com.envision.gravity.flink.steaming.bo.event.function;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.steaming.bo.event.entity.EventMsg;
import com.envision.gravity.flink.steaming.bo.event.entity.EventTableName;
import com.envision.gravity.flink.steaming.bo.event.generator.AssetEventGenerator;
import com.envision.gravity.flink.steaming.bo.event.generator.Generator;
import com.envision.gravity.flink.steaming.bo.event.generator.GraphEventGenerator;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Slf4j
public class EventGenerator extends ProcessFunction<ConvertedCdcRecord, EventMsg> {
    private static final long serialVersionUID = -7405565071169418495L;
    private final Map<EventTableName, Generator> generatorMap = new HashMap<>();

    @Override
    public void open(Configuration params) {
        AssetEventGenerator assetEventGenerator = new AssetEventGenerator();
        GraphEventGenerator graphEventGenerator = new GraphEventGenerator();

        generatorMap.put(EventTableName.OBJECT_DETAIL_ORIGIN, assetEventGenerator);
        generatorMap.put(EventTableName.TBL_START_VID, assetEventGenerator);
        generatorMap.put(EventTableName.TBL_EDGE, assetEventGenerator);
        generatorMap.put(EventTableName.TBL_SUB_GRAPH, graphEventGenerator);
    }

    public void close() {}

    @Override
    public void processElement(
            ConvertedCdcRecord value,
            ProcessFunction<ConvertedCdcRecord, EventMsg>.Context ctx,
            Collector<EventMsg> out) {
        EventMsg eventMsg = null;

        String tableName = value.getTable();
        String op = value.getOp();
        Optional<EventTableName> eventTableName = EventTableName.find(tableName);
        if (eventTableName.isPresent()) {
            Generator generator = generatorMap.get(eventTableName.get());

            if (generator != null) {
                if (OPEnum.c.name().equalsIgnoreCase(op)) {
                    eventMsg = generator.generateByCreate(value);
                } else if (OPEnum.u.name().equalsIgnoreCase(op)) {
                    eventMsg = generator.generateByUpdate(value);
                } else if (OPEnum.d.name().equalsIgnoreCase(op)) {
                    eventMsg = generator.generateByDelete(value);
                } else {
                    log.warn("Undefined cdc op: {}, cdc record:{}", op, value);
                }
            } else {
                log.warn("No generator found for table: {}", tableName);
            }
        } else {
            log.warn("Undefined table: {}", tableName);
        }

        if (eventMsg != null) {
            out.collect(eventMsg);
        }
    }
}
