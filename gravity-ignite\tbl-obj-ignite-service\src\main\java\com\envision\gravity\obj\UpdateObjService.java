package com.envision.gravity.obj;

import com.envision.gravity.obj.entity.LatestMeasurePointEntity;
import com.envision.gravity.obj.entity.MeasurePointUpdateResult;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javafx.util.Pair;

import static com.envision.gravity.obj.Consts.*;

import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.IgniteLogger;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.internal.IgniteEx;
import org.apache.ignite.resources.IgniteInstanceResource;
import org.apache.ignite.resources.LoggerResource;

public class UpdateObjService implements UpdateObjServiceInterface {

    private static final long serialVersionUID = 9033384158524153286L;

    @IgniteInstanceResource private static Ignite ignite;
    @IgniteInstanceResource private transient IgniteEx igniteEx;
    @LoggerResource private IgniteLogger log;
    protected transient CompletionService<List<Pair<String, String>>> service;

    private final Map<String, IgniteCache<BinaryObject, BinaryObject>> cacheMap = new HashMap<>();

    @Override
    public void init() {
        service =
                new ExecutorCompletionService<>(
                        new ThreadPoolExecutor(
                                5,
                                1000,
                                0L,
                                TimeUnit.MILLISECONDS,
                                new LinkedBlockingQueue<Runnable>()));
        log.info("measure point update service started on : " + ignite.cluster().localNode());
    }

    public MeasurePointUpdateResult updateMeasurePoint(
            String database, TreeMap<String, List<LatestMeasurePointEntity>> measurePointsMap) {
        final String databaseLowCase = database.toLowerCase();
        MeasurePointUpdateResult updateResult = new MeasurePointUpdateResult();
        String tblObjCacheName = String.format(TBL_OBJ_PART_CACHE_NAME_PATTERN, databaseLowCase);
        String tblObjPointCacheName =
                String.format(Consts.TBL_OBJ_POINT_CACHE_NAME_PATTERN, databaseLowCase);
        IgniteCache<BinaryObject, BinaryObject> tblObjCache = getCache(tblObjCacheName);
        IgniteCache<BinaryObject, BinaryObject> tblObjPointCache = getCache(tblObjPointCacheName);
        if (tblObjCache == null || tblObjPointCache == null) {
            log.error(database + " tblObjCache or tblObjPointCache is null!");
            updateResult.setResultType(ResultType.ALL_FAILED);
            updateResult.setAllFailedMsg("Cache not exists");
            return updateResult;
        }

        measurePointsMap.values().forEach(Collections::sort);
        Future<List<Pair<String, String>>> updateWideTableFuture =
                service.submit(
                        () ->
                                updateMeasurePointWideTable(
                                        tblObjCache, databaseLowCase, measurePointsMap));
        updateResult.setTallTableFailedRows(
                updateMeasurePointTallTable(tblObjPointCache, databaseLowCase, measurePointsMap));
        try {
            List<Pair<String, String>> wideTableFailedList = updateWideTableFuture.get();
            updateResult.setWideTableFailedRows(wideTableFailedList);
        } catch (Exception e) {
            log.error("Get wide table update future error. ", e);
            throw new RuntimeException(e);
        }

        if (!updateResult.getTallTableFailedRows().isEmpty()
                || !updateResult.getWideTableFailedRows().isEmpty()) {
            updateResult.setResultType(ResultType.PARTIAL_SUCCESS);
        } else {
            updateResult.setResultType(ResultType.SUCCESS);
        }

        return updateResult;
    }

    private List<Pair<String, String>> updateMeasurePointWideTable(
            IgniteCache<BinaryObject, BinaryObject> tblObjCache,
            String database,
            TreeMap<String, List<LatestMeasurePointEntity>> tallTableMeasurePointMap) {

        List<Pair<String, String>> failedRows = new ArrayList<>();
        if (tallTableMeasurePointMap.isEmpty()) {
            log.info("Zero rows for wide table. skip");
            return failedRows;
        }

        String tblObjKeyType = String.format(TBL_OBJ_PART_CACHE_KEY_TYPE_PATTERN, database);

        AtomicInteger totalCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        Map<BinaryObject, BinaryObject> container = new LinkedHashMap<>();
        Map<BinaryObject, List<LatestMeasurePointEntity>> toGetAllMap = new LinkedHashMap<>();
        tallTableMeasurePointMap.forEach(
                (systemId, measurePointList) -> {
                    try {
                        BinaryObjectBuilder keyBuilder = ignite.binary().builder(tblObjKeyType);
                        keyBuilder.setField(TBL_OBJ_PART_KEY_FIELD, systemId, String.class);
                        BinaryObject key = keyBuilder.build();
                        toGetAllMap.put(key, measurePointList);
                    } catch (Throwable t) {
                        log.warning(
                                "update measurepoint get key for systemId: " + systemId + " failed",
                                t);
                        String stack =
                                Arrays.stream(t.getStackTrace())
                                        .map(StackTraceElement::toString)
                                        .collect(Collectors.joining(","));
                        log.warning("stack trace" + stack);
                        failedRows.add(new Pair<>(systemId, t.getMessage()));
                    }
                });

        long buildKeyEndTime = System.currentTimeMillis();
        Map<BinaryObject, BinaryObject> fetchedValues =
                tblObjCache.getAllOutTx(toGetAllMap.keySet());
        long getAllValueEndTime = System.currentTimeMillis();

        toGetAllMap.forEach(
                (keyBinaryObject, measurePointList) -> {
                    try {
                        BinaryObject value = fetchedValues.get(keyBinaryObject);
                        if (value == null) {
                            String systemId = keyBinaryObject.field("system_id");
                            log.error(
                                    "get null value for systemId: "
                                            + keyBinaryObject.field("system_id"));
                            failedRows.add(new Pair<>(systemId, "key not found in tbl_obj"));
                            return;
                        }
                        BinaryObjectBuilder valueBuilder = value.toBuilder();

                        for (LatestMeasurePointEntity measurePointEntity : measurePointList) {
                            if (measurePointEntity.getHorizontal()) {
                                java.sql.Timestamp originTime =
                                        valueBuilder.getField(
                                                measurePointEntity.getRowFieldId() + "_time");

                                // skip set current field if origin time newer than this one
                                if (originTime != null
                                        && originTime.getTime() > measurePointEntity.getTime()) {
                                    continue;
                                }
                                // value
                                valueBuilder.setField(
                                        measurePointEntity.getRowFieldId() + "_value",
                                        measurePointEntity.getValue());
                                // time
                                valueBuilder.setField(
                                        measurePointEntity.getRowFieldId() + "_time",
                                        new java.sql.Timestamp(measurePointEntity.getTime()));
                                // data quality
                                if (measurePointEntity.getQuality() != null) {
                                    valueBuilder.setField(
                                            measurePointEntity.getRowFieldId() + "_quality",
                                            measurePointEntity.getQuality());
                                }
                                totalCount.incrementAndGet();
                            }
                        }
                        // point last process time
                        valueBuilder.setField(
                                "point_last_process_time", new java.sql.Timestamp(startTime));
                        container.put(keyBinaryObject, valueBuilder.build());
                    } catch (Throwable t) {
                        String systemId = keyBinaryObject.field("system_id");
                        log.warning(
                                "update measurepoint build value for systemId: "
                                        + systemId
                                        + " failed",
                                t);
                        String stack =
                                Arrays.stream(t.getStackTrace())
                                        .map(StackTraceElement::toString)
                                        .collect(Collectors.joining(","));
                        log.warning("stack trace" + stack);
                        failedRows.add(new Pair<>(systemId, t.getMessage()));
                    }
                });

        long buildAllValueEndTime = System.currentTimeMillis();
        tblObjCache.putAll(container);
        long putAllEndTime = System.currentTimeMillis();
        // commit the transaction
        log.info(
                "update tbl obj in "
                        + database
                        + " "
                        + tallTableMeasurePointMap.size()
                        + " rows, "
                        + totalCount.get()
                        + " points, cost "
                        + (putAllEndTime - startTime)
                        + " ms, build keys total cost "
                        + (buildKeyEndTime - startTime)
                        + " ms, get all value cost "
                        + (getAllValueEndTime - buildKeyEndTime)
                        + "ms, build all value cost "
                        + (buildAllValueEndTime - getAllValueEndTime)
                        + "ms, put all cost "
                        + (putAllEndTime - buildAllValueEndTime)
                        + " ms.");
        return failedRows;
    }

    private List<Pair<String, String>> updateMeasurePointTallTable(
            IgniteCache<BinaryObject, BinaryObject> tblObjPointCache,
            String database,
            TreeMap<String, List<LatestMeasurePointEntity>> tallTableMeasurePointMap) {

        String keyTypeName = String.format(TBL_OBJ_POINT_CACHE_KEY_TYPE_PATTERN, database);

        String valueTypeName = String.format(TBL_OBJ_POINT_CACHE_VALUE_TYPE_PATTERN, database);

        List<Pair<String, String>> failedRows = new ArrayList<>();
        Map<BinaryObject, BinaryObject> container = new LinkedHashMap<>();
        Map<Pair<String, BinaryObject>, LatestMeasurePointEntity> toGetAllMap =
                new LinkedHashMap<>();
        long startTime = System.currentTimeMillis();

        tallTableMeasurePointMap.forEach(
                (systemId, measurePointEntityList) -> {
                    measurePointEntityList.forEach(
                            measurePointEntity -> {
                                if (measurePointEntity.getTime() == null
                                        || measurePointEntity.getValue() == null
                                        || measurePointEntity.getFieldIndex() == null) {
                                    log.warning(
                                            "update tbl obj tall table meet null value. "
                                                    + measurePointEntity);
                                    return;
                                }
                                try {

                                    BinaryObjectBuilder keyBuilder =
                                            ignite.binary().builder(keyTypeName);
                                    keyBuilder.setField(
                                            TBL_OBJ_POINT_KEY_SERIES_ID,
                                            systemId + "@" + measurePointEntity.getFieldIndex(),
                                            String.class);
                                    BinaryObject key = keyBuilder.build();
                                    toGetAllMap.put(new Pair<>(systemId, key), measurePointEntity);
                                } catch (Throwable t) {
                                    String rowKey =
                                            "systemId: "
                                                    + systemId
                                                    + ", field index: "
                                                    + measurePointEntity.getFieldIndex();
                                    log.warning(
                                            "update tbl obj attribute for systemId: "
                                                    + rowKey
                                                    + " failed",
                                            t);
                                    failedRows.add(new Pair<>(rowKey, t.getMessage()));
                                }
                            });
                });

        long buildKeyEndTime = System.currentTimeMillis();
        Map<BinaryObject, BinaryObject> fetchedValues =
                tblObjPointCache.getAllOutTx(
                        toGetAllMap.keySet().stream()
                                .map(Pair::getValue)
                                .collect(Collectors.toSet()));
        long getAllValueEndTime = System.currentTimeMillis();

        toGetAllMap.forEach(
                (systemIdAndKeyBinaryObject, measurePointEntity) -> {
                    String systemId = systemIdAndKeyBinaryObject.getKey();
                    BinaryObject keyBinaryObject = systemIdAndKeyBinaryObject.getValue();
                    try {
                        BinaryObjectBuilder valueBuilder;
                        BinaryObject valueBinaryObject = fetchedValues.get(keyBinaryObject);
                        Timestamp currentTime = new Timestamp(startTime);
                        Timestamp originTime = null;
                        if (valueBinaryObject == null) {
                            valueBuilder = ignite.binary().builder(valueTypeName);
                            valueBuilder.setField("SYSTEM_ID", systemId);
                            valueBuilder.setField(
                                    "FIELD_INDEX", measurePointEntity.getFieldIndex());
                            valueBuilder.setField("CREATE_TIME", currentTime);
                            valueBuilder.setField("MODIFIED_TIME", currentTime);
                        } else {
                            originTime = valueBinaryObject.field("TIME");
                            // skip if origin time greater than lastChangeValue.getTime()
                            if (originTime != null
                                    && originTime.getTime() > measurePointEntity.getTime()) {
                                return;
                            }
                            valueBuilder = valueBinaryObject.toBuilder();
                            valueBuilder.setField("MODIFIED_TIME", currentTime);
                        }
                        valueBuilder.setField("TIME", new Timestamp(measurePointEntity.getTime()));

                        if (measurePointEntity.getValue() instanceof String) {
                            updateTallTableLastChangeField(
                                    valueBuilder, "VALUE_STRING", measurePointEntity, originTime);
                        } else if (measurePointEntity.getValue() instanceof Boolean) {
                            updateTallTableLastChangeField(
                                    valueBuilder, "VALUE_BOOL", measurePointEntity, originTime);
                        } else if (measurePointEntity.getValue() instanceof Double) {
                            updateTallTableLastChangeField(
                                    valueBuilder, "VALUE_DOUBLE", measurePointEntity, originTime);
                        } else if (measurePointEntity.getValue() instanceof Long) {
                            updateTallTableLastChangeField(
                                    valueBuilder, "VALUE_LONG", measurePointEntity, originTime);

                        } else {
                            log.error(
                                    "Unexpected point value. JAVA class: "
                                            + measurePointEntity.getValue().getClass()
                                            + ", value: "
                                            + measurePointEntity.getValue());
                        }

                        if (measurePointEntity.getQuality() != null) {
                            valueBuilder.setField("QUALITY", measurePointEntity.getQuality());
                        }

                        container.put(keyBinaryObject, valueBuilder.build());
                    } catch (Throwable t) {
                        log.warning(
                                "update tall table for systemId: "
                                        + systemId
                                        + " value: "
                                        + measurePointEntity
                                        + " failed",
                                t);
                        failedRows.add(
                                new Pair<>(
                                        systemId + ", " + measurePointEntity.getRowFieldId(),
                                        t.getMessage()));
                    }
                });

        long buildAllValueEndTime = System.currentTimeMillis();
        tblObjPointCache.putAll(container);
        long putAllEndTime = System.currentTimeMillis();
        log.info(
                "update tall table in "
                        + database
                        + " "
                        + container.size()
                        + " rows, total cost "
                        + (putAllEndTime - startTime)
                        + " ms, build keys cost "
                        + (buildKeyEndTime - startTime)
                        + " ms, get all value cost "
                        + (getAllValueEndTime - buildKeyEndTime)
                        + "ms, build all value cost "
                        + (buildAllValueEndTime - getAllValueEndTime)
                        + "ms, put all cost "
                        + (putAllEndTime - buildAllValueEndTime)
                        + " ms.");

        return failedRows;
    }

    private IgniteCache<BinaryObject, BinaryObject> getCache(String cacheName) {
        IgniteCache<BinaryObject, BinaryObject> cache = cacheMap.get(cacheName);
        if (cache == null) {
            cache = ignite.cache(cacheName);
            if (cache == null) {
                return null;
            }
            cache = cache.withKeepBinary();
            cacheMap.put(cacheName, cache);
        }
        return cache;
    }

    private int getLatestTotalPointCount(
            TreeMap<String, List<LatestMeasurePointEntity>> measurePointsMap) {
        return measurePointsMap.values().stream()
                .map(
                        measureList -> {
                            if (measureList == null) {
                                return 0;
                            } else {
                                return measureList.size();
                            }
                        })
                .reduce(0, Integer::sum);
    }

    private void updateTallTableLastChangeField(
            BinaryObjectBuilder valueBuilder,
            String fieldName,
            LatestMeasurePointEntity measurePointEntity,
            Timestamp originTime) {
        Object originValue = valueBuilder.getField(fieldName);
        if (originValue != null
                && originValue.equals(measurePointEntity.getValue())
                && originTime != null) {
            if (Boolean.TRUE.equals(measurePointEntity.getAlreadyChanged())
                    && measurePointEntity.getPrePointTime() > originTime.getTime()) {
                valueBuilder.setField(
                        "LAST_CHANGED_TIME", new Timestamp(measurePointEntity.getTime()));
                return;
            }
        }
        if (!measurePointEntity.getValue().equals(originValue)) {
            valueBuilder.setField("LAST_CHANGED_TIME", new Timestamp(measurePointEntity.getTime()));
            valueBuilder.setField(fieldName, measurePointEntity.getValue());
        }
    }

    @Override
    public UpdateResult updateTblObjMeasurePoint(
            String database, TreeMap<String, List<MeasurePointEntity>> measurePointsMap) {
        UpdateResult updateResult = new UpdateResult();
        String cacheName = String.format(Consts.TBL_OBJ_CACHE_NAME_PATTERN, database.toLowerCase());
        IgniteCache<BinaryObject, BinaryObject> igniteCache = getCache(cacheName);
        if (igniteCache == null) {
            log.error("Cache" + cacheName + " is null!");
            updateResult.setResultType(ResultType.ALL_FAILED);
            updateResult.setAllFailedMsg("Cache " + cacheName + "not exists");
            return updateResult;
        }

        String tblObjKeyType =
                String.format(TBL_OBJ_CACHE_KEY_TYPE_PATTERN, database.toLowerCase());
        int totalCount = getTotalPointCount(measurePointsMap);
        List<Pair<String, String>> failedRows = new ArrayList<>();
        int retryCount = 3;
        int retries = 0;

        while (retries < retryCount) {
            retries++;
            failedRows.clear();
            try {
                long startTime = System.currentTimeMillis();
                Map<BinaryObject, BinaryObject> container = new LinkedHashMap<>();
                Map<BinaryObject, List<MeasurePointEntity>> toGetAllMap = new LinkedHashMap<>();
                measurePointsMap.forEach(
                        (systemId, measurePointList) -> {
                            try {
                                BinaryObjectBuilder keyBuilder =
                                        ignite.binary().builder(tblObjKeyType);
                                keyBuilder.setField(TBL_OBJ_KEY_FIELD, systemId, String.class);
                                BinaryObject key = keyBuilder.build();

                                Collections.sort(measurePointList);
                                toGetAllMap.put(key, measurePointList);
                            } catch (Throwable t) {
                                log.warning(
                                        "update measurepoint get key for systemId: "
                                                + systemId
                                                + " failed",
                                        t);
                                String stack =
                                        Arrays.stream(t.getStackTrace())
                                                .map(StackTraceElement::toString)
                                                .collect(Collectors.joining(","));
                                log.warning("stack trace" + stack);
                                failedRows.add(new Pair<>(systemId, t.getMessage()));
                            }
                        });

                long buildKeyEndTime = System.currentTimeMillis();
                Map<BinaryObject, BinaryObject> fetchedValues =
                        igniteCache.getAllOutTx(toGetAllMap.keySet());
                long getAllValueEndTime = System.currentTimeMillis();

                toGetAllMap.forEach(
                        (keyBinaryObject, measurePointList) -> {
                            try {
                                BinaryObject value = fetchedValues.get(keyBinaryObject);
                                if (value == null) {
                                    String systemId = keyBinaryObject.field("system_id");
                                    log.error(
                                            "get null value for systemId: "
                                                    + keyBinaryObject.field("system_id"));
                                    failedRows.add(
                                            new Pair<>(systemId, "key not found in tbl_obj"));
                                    return;
                                }
                                BinaryObjectBuilder valueBuilder = value.toBuilder();

                                for (MeasurePointEntity measurePointEntity : measurePointList) {
                                    java.sql.Timestamp originTime =
                                            valueBuilder.getField(
                                                    measurePointEntity.getRowFieldId() + "_time");

                                    // skip set current field if origin time newer than this one
                                    if (originTime != null
                                            && originTime.getTime()
                                                    > measurePointEntity.getTime()) {
                                        continue;
                                    }
                                    // value
                                    valueBuilder.setField(
                                            measurePointEntity.getRowFieldId() + "_value",
                                            measurePointEntity.getValue());
                                    // time
                                    valueBuilder.setField(
                                            measurePointEntity.getRowFieldId() + "_time",
                                            new java.sql.Timestamp(measurePointEntity.getTime()));
                                    // data quality
                                    if (measurePointEntity.getQuality() != null) {
                                        valueBuilder.setField(
                                                measurePointEntity.getRowFieldId() + "_quality",
                                                measurePointEntity.getQuality());
                                    }
                                }
                                container.put(keyBinaryObject, valueBuilder.build());
                            } catch (Throwable t) {
                                String systemId = keyBinaryObject.field("system_id");
                                log.warning(
                                        "update measurepoint build value for systemId: "
                                                + systemId
                                                + " failed",
                                        t);
                                String stack =
                                        Arrays.stream(t.getStackTrace())
                                                .map(StackTraceElement::toString)
                                                .collect(Collectors.joining(","));
                                log.warning("stack trace" + stack);
                                failedRows.add(new Pair<>(systemId, t.getMessage()));
                            }
                        });

                long buildAllValueEndTime = System.currentTimeMillis();
                igniteCache.putAll(container);
                long putAllEndTime = System.currentTimeMillis();
                // commit the transaction
                log.info(
                        "update tbl obj in "
                                + database
                                + " "
                                + measurePointsMap.size()
                                + " rows, "
                                + totalCount
                                + " points, cost "
                                + (putAllEndTime - startTime)
                                + " ms, build keys total cost "
                                + (buildKeyEndTime - startTime)
                                + " ms, get all value cost "
                                + (getAllValueEndTime - buildKeyEndTime)
                                + "ms, build all value cost "
                                + (buildAllValueEndTime - getAllValueEndTime)
                                + "ms, put all cost "
                                + (putAllEndTime - buildAllValueEndTime)
                                + " ms.");
                // the transaction succeeded. Leave the while loop.
                break;
            } catch (Throwable t) {
                log.warning(
                        "update tbl_obj has failed, retrying. Current time: "
                                + retries
                                + ", max: "
                                + retryCount,
                        t);
                // Transaction has failed. Retry.
            }
        }

        // updateLastChange(database, lastUpdateMap);

        if (!failedRows.isEmpty()) {
            updateResult.setResultType(ResultType.PARTIAL_SUCCESS);
            updateResult.setFailedRows(failedRows);
        } else {
            updateResult.setResultType(ResultType.SUCCESS);
        }
        return updateResult;
    }

    @Override
    public UpdateResult updateLastChange(
            String database, TreeMap<LastChangeKey, LastChangeValue> updateMap) {
        UpdateResult updateResult = new UpdateResult();
        String cacheName =
                String.format(Consts.LAST_CHANGE_CACHE_NAME_PATTERN, database.toLowerCase());
        IgniteCache<BinaryObject, BinaryObject> lastChangeCache = getCache(cacheName);

        if (lastChangeCache == null) {
            log.error("Cache" + cacheName + " is null!");
            updateResult.setResultType(ResultType.ALL_FAILED);
            updateResult.setAllFailedMsg("Cache " + cacheName + "not exists");
            return updateResult;
        }
        String keyTypeName =
                String.format(LAST_CHANGE_CACHE_KEY_TYPE_PATTERN, database.toLowerCase());

        String valueTypeName =
                String.format(LAST_CHANGE_CACHE_VALUE_TYPE_PATTERN, database.toLowerCase());

        List<Pair<String, String>> failedRows = new ArrayList<>();
        Map<BinaryObject, BinaryObject> container = new LinkedHashMap<>();
        Map<BinaryObject, LastChangeValue> toGetAllMap = new LinkedHashMap<>();
        long startTime = System.currentTimeMillis();
        updateMap.forEach(
                (keyPair, lastChangeValue) -> {
                    try {
                        if (keyPair.getSystemId() == null
                                || keyPair.getRawFiledId() == null
                                || lastChangeValue.getTime() == null
                                || lastChangeValue.getValue() == null) {
                            log.warning(
                                    "update last change meet null value. key field: "
                                            + keyPair
                                            + ", value field: "
                                            + lastChangeValue);
                            return;
                        }

                        BinaryObjectBuilder keyBuilder = ignite.binary().builder(keyTypeName);
                        keyBuilder.setField(
                                LAST_CHANGE_KEY_FIELD_SYSTEM_ID,
                                keyPair.getSystemId(),
                                String.class);
                        keyBuilder.setField(
                                LAST_CHANGE_KEY_FIELD_RAW_FILED_ID,
                                keyPair.getRawFiledId(),
                                String.class);
                        BinaryObject key = keyBuilder.build();
                        toGetAllMap.put(key, lastChangeValue);
                    } catch (Throwable t) {
                        log.warning("update last changed for key: " + keyPair + " failed", t);
                        failedRows.add(new Pair<>(keyPair.toString(), t.getMessage()));
                    }
                });
        long buildKeyEndTime = System.currentTimeMillis();
        Map<BinaryObject, BinaryObject> fetchedValues =
                lastChangeCache.getAllOutTx(toGetAllMap.keySet());
        long getAllValueEndTime = System.currentTimeMillis();

        toGetAllMap.forEach(
                (keyBinaryObject, lastChangeValue) -> {
                    try {
                        BinaryObjectBuilder valueBuilder;
                        BinaryObject valueBinaryObject = fetchedValues.get(keyBinaryObject);
                        Timestamp currentTime = new Timestamp(startTime);
                        Timestamp originTime = null;
                        if (valueBinaryObject == null) {
                            valueBuilder = ignite.binary().builder(valueTypeName);
                            valueBuilder.setField("CREATE_TIME", currentTime);
                            valueBuilder.setField("UPDATE_TIME", currentTime);
                        } else {
                            originTime = valueBinaryObject.field("LAST_CHANGED_TIME");
                            // skip if origin time greater than lastChangeValue.getTime()
                            if (originTime != null
                                    && originTime.getTime() > lastChangeValue.getTime()) {
                                return;
                            }
                            valueBuilder = valueBinaryObject.toBuilder();
                            valueBuilder.setField("UPDATE_TIME", currentTime);
                        }

                        if (lastChangeValue.getValue() instanceof String) {
                            updateLastChangeValueField(
                                    valueBuilder, "VALUE_STRING", lastChangeValue, originTime);
                        } else if (lastChangeValue.getValue() instanceof Boolean) {
                            updateLastChangeValueField(
                                    valueBuilder, "VALUE_BOOL", lastChangeValue, originTime);
                        } else if (lastChangeValue.getValue() instanceof Double) {
                            updateLastChangeValueField(
                                    valueBuilder, "VALUE_DOUBLE", lastChangeValue, originTime);
                        } else if (lastChangeValue.getValue() instanceof Long) {
                            updateLastChangeValueField(
                                    valueBuilder, "VALUE_LONG", lastChangeValue, originTime);

                        } else {
                            log.error(
                                    "Unexpected last changed value. JAVA class: "
                                            + lastChangeValue.getValue().getClass()
                                            + ", value: "
                                            + lastChangeValue.getValue());
                        }

                        container.put(keyBinaryObject, valueBuilder.build());
                    } catch (Throwable t) {
                        log.warning(
                                "update last changed for value: "
                                        + toGetAllMap.get(keyBinaryObject)
                                        + " failed",
                                t);
                        failedRows.add(
                                new Pair<>(
                                        toGetAllMap.get(keyBinaryObject).toString(),
                                        t.getMessage()));
                    }
                });

        long buildAllValueEndTime = System.currentTimeMillis();
        lastChangeCache.putAll(container);
        long putAllEndTime = System.currentTimeMillis();
        if (!failedRows.isEmpty()) {
            updateResult.setResultType(ResultType.PARTIAL_SUCCESS);
            updateResult.setFailedRows(failedRows);
        } else {
            updateResult.setResultType(ResultType.SUCCESS);
        }
        log.info(
                "update last change in "
                        + database
                        + " "
                        + container.size()
                        + " rows, total cost "
                        + (putAllEndTime - startTime)
                        + " ms, build keys cost "
                        + (buildKeyEndTime - startTime)
                        + " ms, get all value cost "
                        + (getAllValueEndTime - buildKeyEndTime)
                        + "ms, build all value cost "
                        + (buildAllValueEndTime - getAllValueEndTime)
                        + "ms, put all cost "
                        + (putAllEndTime - buildAllValueEndTime)
                        + " ms.");

        return updateResult;
    }

    private int getTotalPointCount(TreeMap<String, List<MeasurePointEntity>> measurePointsMap) {
        return measurePointsMap.values().stream()
                .map(
                        measureList -> {
                            if (measureList == null) {
                                return 0;
                            } else {
                                return measureList.size();
                            }
                        })
                .reduce(0, Integer::sum);
    }

    private void updateLastChangeValueField(
            BinaryObjectBuilder valueBuilder,
            String fieldName,
            LastChangeValue lastChangeValue,
            Timestamp originTime) {
        Object originValue = valueBuilder.getField(fieldName);
        if (originValue != null
                && originValue.equals(lastChangeValue.getValue())
                && originTime != null) {
            if (Boolean.TRUE.equals(lastChangeValue.getAlreadyChanged())
                    && lastChangeValue.getPrePointTime() > originTime.getTime()) {
                valueBuilder.setField(
                        "LAST_CHANGED_TIME", new Timestamp(lastChangeValue.getTime()));
                return;
            }
        }
        if (!lastChangeValue.getValue().equals(originValue)) {
            valueBuilder.setField("LAST_CHANGED_TIME", new Timestamp(lastChangeValue.getTime()));
            valueBuilder.setField(fieldName, lastChangeValue.getValue());
        }
    }
}
