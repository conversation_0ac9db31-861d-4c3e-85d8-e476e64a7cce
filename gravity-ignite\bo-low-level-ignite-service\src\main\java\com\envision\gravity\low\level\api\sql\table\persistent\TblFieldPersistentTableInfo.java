package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblFieldPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.TBL_FIELD\n"
                    + "(\n"
                    + "    FIELD_ID             VARCHAR,\n"
                    + "    RAW_FIELD_ID         VARCHAR,\n"
                    + "    FIELD_NAME           VARCHAR,\n"
                    + "    FIELD_DISPLAY_NAME   VARCHAR,\n"
                    + "    CATEGORY_ID          VARCHAR,\n"
                    + "    FIELD_TYPE           VARCHAR,\n"
                    + "    DATA_TYPE            VARCHAR,\n"
                    + "    UNIT                 VARCHAR,\n"
                    + "    CREATED_TIME         TIMESTAMP,\n"
                    + "    CREATED_USER         VARCHAR,\n"
                    + "    MODIFIED_TIME        TIMESTAMP,\n"
                    + "    MODIFIED_USER        VARCHAR,\n"
                    + "    FIELD_INDEX          INT,\n"
                    + "    HORIZONTAL           BOOLEAN,\n"
                    + "    PRIMARY KEY (FIELD_ID)\n"
                    + "    )WITH \"template=%s,ATOMICITY=%s,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Arrays.asList(
                        "CREATE INDEX IF NOT EXISTS IDX_TBL_FIELD_FIELD_ID ON %s.TBL_FIELD (FIELD_ID);",
                        "CREATE INDEX IF NOT EXISTS IDX_TBL_FIELD_FIELD_NAME_CATEGORY_ID ON %s.TBL_FIELD (FIELD_NAME, CATEGORY_ID);");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
