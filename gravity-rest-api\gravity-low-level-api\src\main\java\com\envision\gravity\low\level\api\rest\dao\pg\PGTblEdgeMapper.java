package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblEdge;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;

import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/3
 * @description
 */
public interface PGTblEdgeMapper {

    @SelectProvider(type = PGTblEdgeSqlProvider.class, method = "selectGraphIdByAssetIds")
    List<Map<String, String>> selectGraphIdByAssetIds(Set<String> assetIds, String orgId);

    @UpdateProvider(type = PGTblEdgeSqlProvider.class, method = "insertUpdateEdges")
    int insertUpdateEdges(List<TblEdge> tblEdges, String orgId, AuditHeader auditHeader);

    @DeleteProvider(type = PGTblEdgeSqlProvider.class, method = "batchDeleteByPrimaryKey")
    int batchDeleteByPrimaryKey(List<TblEdge> tblEdges, String orgId);
}
