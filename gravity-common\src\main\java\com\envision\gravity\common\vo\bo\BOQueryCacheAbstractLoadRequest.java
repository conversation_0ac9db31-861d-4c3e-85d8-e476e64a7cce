package com.envision.gravity.common.vo.bo;

import java.util.List;
import java.util.Objects;

public abstract class BOQueryCacheAbstractLoadRequest {

    private final String orgId;
    private final String database;
    private final List<String> assetIds;
    private final List<BOQueryCachePoint> points;
    private final boolean latestQuery;
    private final long startTime;
    private final long endTime;
    private final List<BOQueryCacheHint> hints;

    public BOQueryCacheAbstractLoadRequest(
            String orgId,
            String database,
            List<String> assetIds,
            List<BOQueryCachePoint> points,
            boolean latestQuery,
            long startTime,
            long endTime,
            List<BOQueryCacheHint> hints) {
        this.orgId = orgId;
        this.database = database;
        this.assetIds = assetIds;
        this.points = points;
        this.latestQuery = latestQuery;
        this.startTime = startTime;
        this.endTime = endTime;
        this.hints = hints;
    }

    public String getRequestId() {
        return String.valueOf(hashCode());
    }

    public String getOrgId() {
        return orgId;
    }

    public String getDatabase() {
        return database;
    }

    public List<String> getAssetIds() {
        return assetIds;
    }

    public List<BOQueryCachePoint> getPoints() {
        return points;
    }

    public boolean isLatestQuery() {
        return latestQuery;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public List<BOQueryCacheHint> getHints() {
        return hints;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BOQueryCacheAbstractLoadRequest that = (BOQueryCacheAbstractLoadRequest) o;

        if (latestQuery != that.latestQuery) return false;
        if (startTime != that.startTime) return false;
        if (endTime != that.endTime) return false;
        if (!Objects.equals(orgId, that.orgId)) return false;
        if (!Objects.equals(database, that.database)) return false;
        if (!Objects.equals(assetIds, that.assetIds)) return false;
        if (!Objects.equals(points, that.points)) return false;
        return Objects.equals(hints, that.hints);
    }

    @Override
    public int hashCode() {
        int result = orgId != null ? orgId.hashCode() : 0;
        result = 31 * result + (database != null ? database.hashCode() : 0);
        result = 31 * result + (assetIds != null ? assetIds.hashCode() : 0);
        result = 31 * result + (points != null ? points.hashCode() : 0);
        result = 31 * result + (latestQuery ? 1 : 0);
        result = 31 * result + (int) (startTime ^ (startTime >>> 32));
        result = 31 * result + (int) (endTime ^ (endTime >>> 32));
        result = 31 * result + (hints != null ? hints.hashCode() : 0);
        return result;
    }
}
