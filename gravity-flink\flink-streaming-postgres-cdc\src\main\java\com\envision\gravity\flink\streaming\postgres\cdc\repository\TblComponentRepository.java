package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblComponentMapper;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
@Slf4j
public class TblComponentRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblComponentRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<ModelGroup> selectModelGroupList(String schemaName, List<String> compIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (compIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblComponentMapper tblComponentMapper = session.getMapper(TblComponentMapper.class);
            return tblComponentMapper.selectModelGroupList(schemaName, compIdList);
        } catch (Exception e) {
            log.error("Select model group list error.", e);
            throw new GravityRuntimeException("Select model group list error.", e);
        }
    }

    public List<ModelGroup> selectModelGroupByComp(String schemaName, List<String> compIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (compIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblComponentMapper tblComponentMapper = session.getMapper(TblComponentMapper.class);
            return tblComponentMapper.selectModelGroupByComp(schemaName, compIdList);
        } catch (Exception e) {
            log.error("Select model group list by comp error.", e);
            throw new GravityRuntimeException("Select model group list by comp error.", e);
        }
    }
}
