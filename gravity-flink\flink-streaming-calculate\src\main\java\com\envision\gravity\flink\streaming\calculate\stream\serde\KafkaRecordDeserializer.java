package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Kafka record deserializer that converts Kafka ConsumerRecord to Tuple2 containing message content
 * and offset information. Used for deserializing streaming messages from Kafka topics.
 *
 * <AUTHOR>
 */
public class KafkaRecordDeserializer
        implements KafkaRecordDeserializationSchema<Tuple2<String, OffsetInfo>> {

    private static final long serialVersionUID = -83577669661675434L;
    private static final Logger logger = LoggerFactory.getLogger(KafkaRecordDeserializer.class);

    @Override
    public void deserialize(
            ConsumerRecord<byte[], byte[]> record, Collector<Tuple2<String, OffsetInfo>> out)
            throws IOException {
        logger.debug(
                "Received message: topic={}, partition={}, offset={}",
                record.topic(),
                record.partition(),
                record.offset());

        String msg = new String(record.value(), StandardCharsets.UTF_8);
        logger.debug("Message content: {}", msg);

        if (!msg.isEmpty()) {
            logger.debug("Sending message to downstream processing");
            out.collect(
                    new Tuple2<>(
                            msg,
                            new OffsetInfo(record.topic(), record.partition(), record.offset())));
        } else {
            logger.warn("Message is empty, skipping processing");
        }
    }

    @Override
    public TypeInformation<Tuple2<String, OffsetInfo>> getProducedType() {
        return TypeInformation.of(new TypeHint<Tuple2<String, OffsetInfo>>() {});
    }
}
