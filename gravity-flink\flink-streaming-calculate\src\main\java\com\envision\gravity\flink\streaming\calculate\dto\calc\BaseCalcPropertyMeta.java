package com.envision.gravity.flink.streaming.calculate.dto.calc;

import java.io.Serializable;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础计算属性元数据
 *
 * <p>用于： 1. CalcJobMetaInfo 的 ruleInfo 字段 2. 计算规则的基础信息
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseCalcPropertyMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 属性规则ID */
    private String prefRuleId;

    /** 源类别ID */
    private String srcCategoryId;

    /** 目标模型ID列表 */
    private List<String> targetModelIds;

    /** 组织ID */
    private String orgId;

    /** 规则名称 */
    private String ruleName;

    /** 规则描述 */
    private String ruleDescription;

    /** 是否启用 */
    private boolean enabled;

    /** 创建时间 */
    private long createTime;

    /** 更新时间 */
    private long updateTime;

    /** 检查是否为有效的规则元数据 */
    public boolean isValid() {
        return prefRuleId != null
                && !prefRuleId.isEmpty()
                && srcCategoryId != null
                && !srcCategoryId.isEmpty()
                && targetModelIds != null
                && !targetModelIds.isEmpty()
                && orgId != null
                && !orgId.isEmpty();
    }

    @Override
    public String toString() {
        return String.format(
                "BaseCalcPropertyMeta{prefRuleId='%s', srcCategoryId='%s', "
                        + "targetModels=%d, orgId='%s', enabled=%s}",
                prefRuleId,
                srcCategoryId,
                targetModelIds != null ? targetModelIds.size() : 0,
                orgId,
                enabled);
    }
}
