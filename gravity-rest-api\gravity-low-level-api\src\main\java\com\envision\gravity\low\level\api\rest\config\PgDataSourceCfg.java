package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.common.util.GravityCommonLionConfigs;
import com.envision.gravity.low.level.api.rest.util.LionUtil;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/** @Author: qi.jiang2 @Date: 2024/03/05 15:27 @Description: */
@Configuration
@MapperScan(
        basePackages = "com.envision.gravity.low.level.api.rest.dao.pg",
        sqlSessionFactoryRef = "sqlSessionFactoryPg")
public class PgDataSourceCfg {

    @Bean(name = "pgDataSource")
    public HikariDataSource pgDataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setDriverClassName(
                LionUtil.getStringValue(
                        GravityCommonLionConfigs.PGSQL_JDBC_DRIVER,
                        GravityCommonLionConfigs.PGSQL_JDBC_DRIVER_DEFAULT));
        ds.setJdbcUrl(LionUtil.getStringValue(GravityCommonLionConfigs.PGSQL_JDBC_URL));
        ds.setUsername(LionUtil.getStringValue(GravityCommonLionConfigs.PGSQL_USERNAME));
        ds.setPassword(LionUtil.getStringValue(GravityCommonLionConfigs.PGSQL_PASSWORD));
        ds.setMaxLifetime(60000);
        ds.setMaximumPoolSize(
                LionUtil.getIntValue(
                        GTRestLionConfig.PGSQL_MAX_POOL_SIZE,
                        GTRestLionConfig.PGSQL_MAX_POOL_SIZE_DEFAULT));
        return ds;
    }

    @Bean(name = "sqlSessionFactoryPg")
    public SqlSessionFactory sqlSessionFactoryPg(@Qualifier("pgDataSource") DataSource datasource)
            throws Exception {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        factory.setMapperLocations(resolver.getResources("classpath*:/pg-mapper/*.xml"));
        // factory.setTypeAliasesPackage("com.envision.gravity.low.level.api.rest.dao.pg");
        factory.setDataSource(datasource);
        return factory.getObject();
    }

    @Bean("sqlSessionTemplatePg")
    public SqlSessionTemplate sqlSessionTemplateCatalog(
            @Qualifier("sqlSessionFactoryPg") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "transactionManagerPg")
    public PlatformTransactionManager transactionManagerCatalog() {
        return new DataSourceTransactionManager(pgDataSource());
    }
}
