<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation=" http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/util
       http://www.springframework.org/schema/util/spring-util.xsd ">

    <bean class="org.apache.ignite.configuration.IgniteConfiguration" id="ignite.cfg">
        <property name="workDirectory" value="/opt/ignite/apache-ignite/work"/>
        <property name="snapshotPath" value="/opt/ignite/apache-ignite/snapshots"/>
        <property name="clientMode" value="false"/>
        <property name="networkTimeout" value="120000"/>
        <!-- 失败检测 超时时长 -->
        <property name="failureDetectionTimeout" value="#{60 * 60 * 1000}"/>
        <!--  服务worker 之间交互 timeout 时间，默认 10s  -->
        <property name="systemWorkerBlockedTimeout" value="#{60 * 60 * 1000}"/>
        <!--  服务出现故障自动重启  -->
        <property name="failureHandler">
            <bean class="org.apache.ignite.failure.RestartProcessFailureHandler"/>
        </property>
        <!--  公共线程池负责Ignite的计算网格，所有的计算任务都由这个线程池接收然后处理  -->
        <property name="publicThreadPoolSize" value="24"/>
        <!--  系统线程池处理所有与缓存相关的操作  -->
        <property name="systemThreadPoolSize" value="24"/>
        <!--  查询线程池处理集群内所有的SQL、扫描和SPI查询  -->
        <property name="queryThreadPoolSize" value="24"/>
        <!--  Enable peer class loading.  -->
        <property name="peerClassLoadingEnabled" value="true"/>
        <!--  Set deployment mode.  -->
        <property name="deploymentMode" value="CONTINUOUS"/>
        <!--  禁用丢失资源缓存  -->
        <property name="peerClassLoadingMissedResourcesCacheSize" value="0"/>
        <property name="authenticationEnabled" value="true"/>
        <!--  Set batch size.  -->
        <property name="rebalanceBatchSize" value="#{1 * 1024 * 1024}"/>
        <!--  Set throttle interval.  -->
        <property name="rebalanceThrottle" value="100"/>
        <!--  <property name="rebalanceThreadPoolSize" value="4"/>  -->
        <property name="rebalanceTimeout" value="#{120 * 1000L}"/>
        <!--   SQL Engine   -->
        <property name="sqlConfiguration">
            <bean class="org.apache.ignite.configuration.SqlConfiguration">
                <property name="sqlSchemas">
                    <list>
                        <value>GRAVITY</value>
                        <value>o16227961710541858</value>
                    </list>
                </property>
                <property name="queryEnginesConfiguration">
                    <list>
                        <bean class="org.apache.ignite.indexing.IndexingQueryEngineConfiguration">
                            <property name="default" value="true"/>
                        </bean>
                        <bean class="org.apache.ignite.calcite.CalciteQueryEngineConfiguration">
                            <property name="default" value="false"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>
        <property name="metricExporterSpi">
            <list>
                <bean class="org.apache.ignite.spi.metric.jmx.JmxMetricExporterSpi"/>
            </list>
        </property>
        <property name="dataStorageConfiguration">
            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">
                <property name="defaultWarmUpConfiguration">
                    <bean class="org.apache.ignite.configuration.LoadAllWarmUpConfiguration"/>
                </property>
                <property name="pageSize" value="#{4 * 1024}"/>
                <!--  FSYNC | LOG_ONLY | BACKGROUND | NONE  -->
                <property name="walMode" value="LOG_ONLY"/>
                <!--  disable wal archive  -->
                <property name="walPath" value="/opt/ignite/apache-ignite/wal"/>
                <property name="walArchivePath" value="/opt/ignite/apache-ignite/wal"/>
                <property name="maxWalArchiveSize" value="#{ 10 * 1024 * 1024L * 1024}"/>
                <property name="walSegmentSize" value="#{ 512 * 1024 * 1024}"/>
                <property name="checkpointFrequency" value="#{24 * 60 * 60 * 1000L}"/>
                <property name="checkpointThreads" value="1"/>
                <property name="writeThrottlingEnabled" value="true"/>
                <property name="metricsEnabled" value="true"/>
                <property name="defaultDataRegionConfiguration">
                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                        <property name="name" value="Default_Region"/>
                        <property name="initialSize" value="#{ 2 * 1024L * 1024L * 1024}"/>
                        <property name="maxSize" value="#{ 2 * 1024L * 1024 * 1024}"/>
                        <property name="persistenceEnabled" value="true"/>
                        <property name="checkpointPageBufferSize" value="#{ 512 * 1024L * 1024}"/>
                        <property name="metricsEnabled" value="true"/>
                    </bean>
                </property>
                <property name="dataRegionConfigurations">
                    <list>
                        <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                            <property name="name" value="InMemory_Region"/>
                            <property name="initialSize" value="#{4 * 1024L * 1024 * 1024}"/>
                            <property name="maxSize" value="#{4 * 1024L * 1024 * 1024}"/>
                            <property name="persistenceEnabled" value="false"/>
                            <property name="metricsEnabled" value="false"/>
                            <property name="pageEvictionMode" value="RANDOM_2_LRU"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>
        <property name="discoverySpi">
            <bean class="org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi">
                <property name="ipFinder">
                    <bean class="org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder">
                        <property name="addresses">
                            <list>
                                <value>ignite8001.eniot.io:47500..47509</value>
                                <value>influxdb8001.eniot.io:47500..47509</value>
                                <value>nebula8001.eniot.io:47500..47509</value>
                            </list>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>
        <property name="communicationSpi">
            <bean class="org.apache.ignite.spi.communication.tcp.TcpCommunicationSpi">
                <property name="messageQueueLimit" value="512"/>
                <property name="slowClientQueueLimit" value="256"/>
                <property name="idleConnectionTimeout" value="3600000"/>
                <property name="sharedMemoryPort" value="-1"/>
            </bean>
        </property>
        <property name="clientConnectorConfiguration">
            <bean class="org.apache.ignite.configuration.ClientConnectorConfiguration">
                <property name="thinClientEnabled" value="true"/>
                <property name="port" value="10800"/>
                <property name="portRange" value="500"/>
                <property name="sslEnabled" value="false"/>
                <property name="threadPoolSize" value="24"/>
            </bean>
        </property>
        <property name="serviceConfiguration">
            <list>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="BOLowLevelIgniteService"/>
                    <property name="service">
                        <bean class="com.envision.gravity.low.level.api.sql.service.impl.BOLowLevelServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="createCacheService"/>
                    <property name="service">
                        <bean class="com.eniot.tableengine.service.impl.CreateCacheServiceImpl"/>q
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="getCacheStoreFactoryService"/>
                    <property name="service">
                        <bean class="com.eniot.metricengine.service.impl.GetCacheStoreFactoryServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
                <bean class="org.apache.ignite.services.ServiceConfiguration">
                    <property name="name" value="loadCacheService"/>
                    <property name="service">
                        <bean class="com.eniot.metricengine.service.impl.LoadCacheServiceImpl"/>
                    </property>
                    <property name="maxPerNodeCount" value="1"/>
                </bean>
            </list>
        </property>
        <property name="cacheConfiguration">
            <list>
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="Gravity_UDF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="sqlFunctionClasses" value="com.envision.gravity.ignite.udf.GravitySqlFunctions"/>
                    <property name="sqlSchema" value="GRAVITY"/>
                </bean>
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="o16227961710541858_Gravity_UDF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="sqlFunctionClasses" value="com.envision.gravity.ignite.udf.GravitySqlFunctions"/>
                    <property name="sqlSchema" value="o16227961710541858"/>
                </bean>
                <!-- Configuration for TBL_BO_MODEL -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_MODEL"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_MODEL"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_MODEL"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_path"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_path"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_VALUE"/>
                                <property name="tableName" value="TBL_BO_MODEL"/>
                                <property name="keyFields">
                                    <list>
                                        <value>model_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>model_display_name</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="group_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="model_id" value="java.lang.String"/>
                                        <entry key="model_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="model_path" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_COMPONENT  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_COMPONENT"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_COMPONENT"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_COMPONENT"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="anonymous"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="anonymous"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="template"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="template"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_VALUE"/>
                                <property name="tableName" value="TBL_COMPONENT"/>
                                <property name="keyFields">
                                    <list>
                                        <value>comp_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>comp_name</value>
                                        <value>comp_display_name</value>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="comp_name" value="java.lang.String"/>
                                        <entry key="comp_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="anonymous" value="java.lang.Boolean"/>
                                        <entry key="template" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_MODEL_RELATION  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_MODEL_RELATION"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_MODEL_RELATION"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_MODEL_RELATION_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_MODEL_RELATION_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_MODEL_RELATION"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="relation_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="relation_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.INTEGER"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="level"/>
                                                    <constructor-arg value="java.lang.Integer"/>
                                                    <constructor-arg value="level"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="relation_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="relation_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="level"/>
                                                    <constructor-arg value="java.lang.Integer"/>
                                                    <constructor-arg value="level"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_MODEL_RELATION_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_MODEL_RELATION_VALUE"/>
                                <property name="tableName" value="TBL_MODEL_RELATION"/>
                                <property name="keyFields">
                                    <list>
                                        <value>from_model_id</value>
                                        <value>to_model_id</value>
                                        <value>relation_type</value>
                                        <value>level</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>from_model_id</value>
                                        <value>to_model_id</value>
                                        <value>relation_type</value>
                                        <value>level</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>from_model_id</value>
                                                    <value>relation_type</value>
                                                    <value>level</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>to_model_id</value>
                                                    <value>relation_type</value>
                                                    <value>level</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="from_model_id" value="java.lang.String"/>
                                        <entry key="to_model_id" value="java.lang.String"/>
                                        <entry key="relation_type" value="java.lang.String"/>
                                        <entry key="level" value="java.lang.Integer"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_BO_MODEL_COMPONENT  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_MODEL_COMP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_MODEL_COMP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_MODEL_COMP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_VALUE"/>
                                <property name="tableName" value="TBL_BO_MODEL_COMP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>comp_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>comp_id</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="model_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="model_id" value="java.lang.String"/>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_COMPONENT_PREF  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_COMPONENT_PREF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_COMPONENT_PREF"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_PREF_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_PREF_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_COMPONENT_PREF"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="metric_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="metric_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="raw_field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="raw_field_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_PREF_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_PREF_VALUE"/>
                                <property name="tableName" value="TBL_COMPONENT_PREF"/>
                                <property name="keyFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>pref_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>pref_id</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sys_modified_time"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="field_id" value="java.lang.String"/>
                                        <entry key="metric_id" value="java.lang.String"/>
                                        <entry key="raw_field_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_PREF  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_PREF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_PREF"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_PREF_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_PREF_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_PREF"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="writable"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="writable"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="required"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="required"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="default_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="default_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="has_quality"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="has_quality"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_signal_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_signal_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_definition"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_definition"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="request"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="request"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="response"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="response"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="unit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="unit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="lower_limit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="lower_limit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="upper_limit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="upper_limit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="dimensions"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="dimensions"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="intervals"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="intervals"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="default_agg_method"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="default_agg_method"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_PREF_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_PREF_VALUE"/>
                                <property name="tableName" value="TBL_PREF"/>
                                <property name="keyFields">
                                    <list>
                                        <value>pref_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>pref_name</value>
                                        <value>pref_display_name</value>
                                        <value>pref_type</value>
                                    </list>
                                </property>
                                <property name="defaultFieldValues">
                                    <map>
                                        <entry key="writable" value="false"/>
                                        <entry key="required" value="false"/>
                                        <entry key="has_quality" value="false"/>
                                    </map>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_type"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="pref_name" value="java.lang.String"/>
                                        <entry key="pref_display_name" value="java.lang.String"/>
                                        <entry key="pref_type" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="writable" value="java.lang.Boolean"/>
                                        <entry key="required" value="java.lang.Boolean"/>
                                        <entry key="default_value" value="java.lang.String"/>
                                        <entry key="has_quality" value="java.lang.Boolean"/>
                                        <entry key="pref_data_type" value="java.lang.String"/>
                                        <entry key="pref_signal_type" value="java.lang.String"/>
                                        <entry key="data_definition" value="java.lang.String"/>
                                        <entry key="request" value="java.lang.String"/>
                                        <entry key="response" value="java.lang.String"/>
                                        <entry key="unit" value="java.lang.String"/>
                                        <entry key="lower_limit" value="java.lang.String"/>
                                        <entry key="upper_limit" value="java.lang.String"/>
                                        <entry key="dimensions" value="java.lang.String"/>
                                        <entry key="intervals" value="java.lang.String"/>
                                        <entry key="default_agg_method" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_PREF_EXT  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_PREF_EXT"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_PREF_EXT"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_PREF_EXT_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_PREF_EXT_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_PREF_EXT"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="category"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="category"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="category"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="category"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="source_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="source_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="source_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="source_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BIGINT"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="metric_type"/>
                                                    <constructor-arg value="java.lang.Long"/>
                                                    <constructor-arg value="metric_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="db_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="db_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BIGINT"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="table_type"/>
                                                    <constructor-arg value="java.lang.Long"/>
                                                    <constructor-arg value="table_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="table_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="table_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="raw_expression"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="raw_expression"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="agg_expression"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="agg_expression"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="filter"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="filter"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="extra_tables"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="extra_tables"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="extra_attributes"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="extra_attributes"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_PREF_EXT_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_PREF_EXT_VALUE"/>
                                <property name="tableName" value="TBL_PREF_EXT"/>
                                <property name="keyFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>comp_id</value>
                                        <value>category</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>comp_id</value>
                                        <value>category</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="category"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="category" value="java.lang.String"/>
                                        <entry key="source_type" value="java.lang.String"/>
                                        <entry key="source_value" value="java.lang.String"/>
                                        <entry key="metric_type" value="java.lang.Long"/>
                                        <entry key="db_name" value="java.lang.String"/>
                                        <entry key="table_type" value="java.lang.Long"/>
                                        <entry key="table_name" value="java.lang.String"/>
                                        <entry key="raw_expression" value="java.lang.String"/>
                                        <entry key="agg_expression" value="java.lang.String"/>
                                        <entry key="filter" value="java.lang.String"/>
                                        <entry key="extra_tables" value="java.lang.String"/>
                                        <entry key="extra_attributes" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_BO_GROUP  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_GROUP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_GROUP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_GROUP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_VALUE"/>
                                <property name="tableName" value="TBL_BO_GROUP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>group_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>group_name</value>
                                        <value>group_display_name</value>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="group_name" value="java.lang.String"/>
                                        <entry key="group_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_BO_GROUP_RELATION  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_GROUP_RELATION"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_GROUP_RELATION"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_GROUP_RELATION"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_VALUE"/>
                                <property name="tableName" value="TBL_BO_GROUP_RELATION"/>
                                <property name="keyFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>asset_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>asset_id</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="group_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="asset_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="asset_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_BO  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="attr_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="attr_modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_VALUE"/>
                                <property name="tableName" value="TBL_BO"/>
                                <property name="keyFields">
                                    <list>
                                        <value>asset_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>asset_id</value>
                                        <value>asset_display_name</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="system_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="asset_id" value="java.lang.String"/>
                                        <entry key="asset_display_name" value="java.lang.String"/>
                                        <entry key="system_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="attr_modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_TAG  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_TAG"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_TAG"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_TAG_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_TAG_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_TAG"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_group"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_group"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="marker"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="marker"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_key"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_key"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_TAG_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_TAG_VALUE"/>
                                <property name="tableName" value="TBL_TAG"/>
                                <property name="keyFields">
                                    <list>
                                        <value>data_id</value>
                                        <value>tag_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>data_id</value>
                                        <value>tag_id</value>
                                        <value>tag_type</value>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="data_id" value="java.lang.String"/>
                                        <entry key="data_type" value="java.lang.String"/>
                                        <entry key="tag_type" value="java.lang.String"/>
                                        <entry key="tag_id" value="java.lang.String"/>
                                        <entry key="tag_group" value="java.lang.String"/>
                                        <entry key="marker" value="java.lang.String"/>
                                        <entry key="tag_key" value="java.lang.String"/>
                                        <entry key="tag_value" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_OBJ_ATTR  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_OBJ_ATTR"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_OBJ_ATTR"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_OBJ_ATTR_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_OBJ_ATTR_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_OBJ_ATTR"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BOOLEAN"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_bool"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="value_bool"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_string"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="value_string"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BIGINT"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_long"/>
                                                    <constructor-arg value="java.lang.Long"/>
                                                    <constructor-arg value="value_long"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.DOUBLE"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_double"/>
                                                    <constructor-arg value="java.lang.Double"/>
                                                    <constructor-arg value="value_double"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_json"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="value_json"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_OBJ_ATTR_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_OBJ_ATTR_VALUE"/>
                                <property name="tableName" value="TBL_OBJ_ATTR"/>
                                <property name="keyFields">
                                    <list>
                                        <value>system_id</value>
                                        <value>field_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>system_id</value>
                                        <value>field_id</value>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="system_id" value="java.lang.String"/>
                                        <entry key="field_id" value="java.lang.String"/>
                                        <entry key="value_bool" value="java.lang.Boolean"/>
                                        <entry key="value_string" value="java.lang.String"/>
                                        <entry key="VALUE_LONG" value="java.lang.Long"/>
                                        <entry key="value_double" value="java.lang.Double"/>
                                        <entry key="value_json" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_SUB_GRAPH  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_SUB_GRAPH"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_SUB_GRAPH"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_SUB_GRAPH_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_SUB_GRAPH_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_SUB_GRAPH"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BIGINT"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="rank"/>
                                                    <constructor-arg value="java.lang.Long"/>
                                                    <constructor-arg value="rank"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BOOLEAN"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tree"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="tree"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_SUB_GRAPH_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_SUB_GRAPH_VALUE"/>
                                <property name="tableName" value="TBL_SUB_GRAPH"/>
                                <property name="keyFields">
                                    <list>
                                        <value>sub_graph_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>sub_graph_id</value>
                                        <value>sub_graph_display_name</value>
                                        <value>rank</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sub_graph_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="rank"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="sub_graph_id" value="java.lang.String"/>
                                        <entry key="sub_graph_display_name" value="java.lang.String"/>
                                        <entry key="rank" value="java.lang.Long"/>
                                        <entry key="tree" value="java.lang.Boolean"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_EDGE  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tree_node_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tree_node_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_VALUE"/>
                                <property name="tableName" value="TBL_EDGE"/>
                                <property name="keyFields">
                                    <list>
                                        <value>from_vid</value>
                                        <value>to_vid</value>
                                        <value>edge_type_id</value>
                                        <value>sub_graph_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>from_vid</value>
                                        <value>to_vid</value>
                                        <value>edge_type_id</value>
                                        <value>sub_graph_id</value>
                                        <value>prop_value</value>
                                        <value>tree_node_id</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="from_vid"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="to_vid"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sub_graph_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>from_vid</value>
                                                    <value>edge_type_id</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>to_vid</value>
                                                    <value>edge_type_id</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="from_vid" value="java.lang.String"/>
                                        <entry key="to_vid" value="java.lang.String"/>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="sub_graph_id" value="java.lang.String"/>
                                        <entry key="prop_value" value="java.lang.String"/>
                                        <entry key="tree_node_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_EDGE_TYPE  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE_TYPE"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE_TYPE"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE_TYPE"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_VALUE"/>
                                <property name="tableName" value="TBL_EDGE_TYPE"/>
                                <property name="keyFields">
                                    <list>
                                        <value>edge_type_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>edge_type_display_name</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="edge_type_display_name" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_EDGE_TYPE_PROP  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE_TYPE_PROP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_name"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BOOLEAN"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="nullable"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="nullable"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="default_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="default_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_VALUE"/>
                                <property name="tableName" value="TBL_EDGE_TYPE_PROP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>prop_name</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>prop_name</value>
                                        <value>prop_data_type</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>edge_type_id</value>
                                                    <value>prop_name</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="prop_name" value="java.lang.String"/>
                                        <entry key="prop_data_type" value="java.lang.String"/>
                                        <entry key="nullable" value="java.lang.Boolean"/>
                                        <entry key="default_value" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_START_VID  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_START_VID"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_START_VID"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_START_VID_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_START_VID_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_START_VID"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="start_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="start_vid"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="start_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="start_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_START_VID_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_START_VID_VALUE"/>
                                <property name="tableName" value="TBL_START_VID"/>
                                <property name="keyFields">
                                    <list>
                                        <value>sub_graph_id</value>
                                        <value>start_vid</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sub_graph_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="start_vid"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="sub_graph_id" value="java.lang.String"/>
                                        <entry key="start_vid" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!--  Configuration for TBL_TASK  -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_TASK"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSourceGravity"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_TASK"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_TASK_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_TASK_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_TASK"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="task_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="task_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="task_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="task_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="storage_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="storage_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="operation_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="operation_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="database_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="database_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="table_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="table_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="request_params"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="request_params"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="status"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="status"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.INTEGER"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="retry_times"/>
                                                    <constructor-arg value="java.lang.Integer"/>
                                                    <constructor-arg value="retry_times"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sys_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="sys_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_TASK_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_TASK_VALUE"/>
                                <property name="tableName" value="TBL_TASK"/>
                                <property name="keyFields">
                                    <list>
                                        <value>task_id</value>
                                    </list>
                                </property>
                                <property name="notNullFields">
                                    <list>
                                        <value>storage_type</value>
                                        <value>operation_name</value>
                                        <value>request_params</value>
                                        <value>status</value>
                                    </list>
                                </property>
                                <!--  Defining indexed fields. -->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="task_id"/>
                                        </bean>
                                    </list>
                                </property>
                                <property name="fields">
                                    <map>
                                        <entry key="task_id" value="java.lang.String"/>
                                        <entry key="storage_type" value="java.lang.String"/>
                                        <entry key="operation_name" value="java.lang.String"/>
                                        <entry key="database_name" value="java.lang.String"/>
                                        <entry key="table_name" value="java.lang.String"/>
                                        <entry key="request_params" value="java.lang.String"/>
                                        <entry key="status" value="java.lang.String"/>
                                        <entry key="retry_times" value="java.lang.Integer"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_created_time" value="java.sql.Timestamp"/>
                                        <entry key="sys_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>

    <!--  Data source bean  -->
    <bean class="org.postgresql.ds.PGSimpleDataSource" id="postgresDataSource">
        <property name="URL" value="*******************************************************************"/>
        <property name="user" value="postgresadmin"/>
        <property name="password" value="admin12345!@"/>
    </bean>

    <bean class="org.postgresql.ds.PGSimpleDataSource" id="postgresDataSourceGravity">
        <property name="URL" value="********************************************************************"/>
        <property name="user" value="postgresadmin"/>
        <property name="password" value="admin12345!@"/>
    </bean>

    <bean id="igniteSchema" class="java.lang.String">
        <constructor-arg value="o16227961710541858"/>
    </bean>
    <bean id="o17137806171071264" class="java.lang.String">
        <constructor-arg value="o17137806171071264"/>
    </bean>
</beans>