package com.envision.gravity.calculate.api.rest.web;

import com.envision.gravity.calculate.api.rest.exception.CalcFailedException;
import com.envision.gravity.calculate.api.rest.exception.PrefTypeNotSupportException;
import com.envision.gravity.calculate.api.rest.exception.RuleInvalidException;
import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/** <AUTHOR> 2024/6/7 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ResponseBody
    @ExceptionHandler({IllegalArgumentException.class})
    public Object handleIllegalArgumentException(
            IllegalArgumentException ex, HttpServletRequest request) {
        String msg =
                StringUtils.isNotEmpty(ex.getMessage())
                        ? ex.getMessage()
                        : ResponseCodeEnum.INVALID_PARAMETERS.getMessage();
        return ResponseResult.createResult(ResponseCodeEnum.INVALID_PARAMETERS.getCode(), msg);
    }

    @ResponseBody
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Object handleMethodArgumentNotValidException(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        return ResponseResult.createResult(
                ResponseCodeEnum.INVALID_PARAMETERS.getCode(),
                ResponseCodeEnum.INVALID_PARAMETERS.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(CalcFailedException.class)
    public Object handleCalcFailedException(CalcFailedException ex, HttpServletRequest request) {
        return ResponseResult.createResult(
                ResponseCodeEnum.DOWNSTREAM_CALC_FAILED.getCode(),
                ResponseCodeEnum.DOWNSTREAM_CALC_FAILED.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(PrefTypeNotSupportException.class)
    public Object handlePrefTypeNotSupportException(
            PrefTypeNotSupportException ex, HttpServletRequest request) {
        return ResponseResult.createResult(
                ResponseCodeEnum.DOWNSTREAM_CALC_PREF_TYPE_NOT_SUPPORT.getCode(),
                ResponseCodeEnum.DOWNSTREAM_CALC_PREF_TYPE_NOT_SUPPORT.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(RuleInvalidException.class)
    public Object handleRuleInvalidException(RuleInvalidException ex, HttpServletRequest request) {
        return ResponseResult.createResult(
                ResponseCodeEnum.DOWNSTREAM_CALC_INVALID_RULE.getCode(),
                ResponseCodeEnum.DOWNSTREAM_CALC_INVALID_RULE.getMessage());
    }
}
