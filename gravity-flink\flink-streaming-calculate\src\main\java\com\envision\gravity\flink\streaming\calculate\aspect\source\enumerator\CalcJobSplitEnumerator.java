package com.envision.gravity.flink.streaming.calculate.aspect.source.enumerator;

import com.envision.gravity.flink.streaming.calculate.aspect.source.split.CalcJobEnumState;
import com.envision.gravity.flink.streaming.calculate.aspect.source.split.CalcJobSplit;

import javax.annotation.Nullable;

import java.util.*;
import java.util.concurrent.CompletableFuture;


import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.apache.flink.api.connector.source.SplitsAssignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ✅ Job分片枚举器，管理Job到SourceReader的分配
 *
 * <p>功能： 1. 中央协调器，负责Job的发现、分配和重分配 2. 支持动态Job发现 3. 负载均衡分配 4. 故障重分配
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class CalcJobSplitEnumerator implements SplitEnumerator<CalcJobSplit, CalcJobEnumState> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobSplitEnumerator.class);

    private final SplitEnumeratorContext<CalcJobSplit> context;
    private final int maxJobsPerReader;
    private final long taskCompletionCheckInterval;

    // ✅ 管理Job到SourceReader的分配
    private final Map<Integer, Set<String>> readerJobs; // readerId -> jobIds
    private final Set<String> unassignedJobs; // 未分配的jobIds
    private final Set<String> discoveredJobs; // 已发现的所有Job

    // 构造函数：新建枚举器
    public CalcJobSplitEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context,
            int maxJobsPerReader,
            long taskCompletionCheckInterval) {

        this.context = context;
        this.maxJobsPerReader = maxJobsPerReader;
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.readerJobs = new HashMap<>();
        this.unassignedJobs = new HashSet<>();
        this.discoveredJobs = new HashSet<>();

        logger.info(
                "Created CalcJobSplitEnumerator with maxJobsPerReader: {}, checkInterval: {}ms",
                maxJobsPerReader,
                taskCompletionCheckInterval);
    }

    // 构造函数：从checkpoint恢复
    public CalcJobSplitEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context,
            int maxJobsPerReader,
            long taskCompletionCheckInterval,
            CalcJobEnumState checkpoint) {

        this.context = context;
        this.maxJobsPerReader = maxJobsPerReader;
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;

        // ✅ 从checkpoint恢复状态
        this.readerJobs =
                checkpoint.getReaderJobs() != null
                        ? new HashMap<>(checkpoint.getReaderJobs())
                        : new HashMap<>();
        this.unassignedJobs =
                checkpoint.getUnassignedJobs() != null
                        ? new HashSet<>(checkpoint.getUnassignedJobs())
                        : new HashSet<>();

        // 重建已发现Job集合
        this.discoveredJobs = new HashSet<>();
        this.discoveredJobs.addAll(unassignedJobs);
        readerJobs.values().forEach(discoveredJobs::addAll);

        logger.info("Restored CalcJobSplitEnumerator from checkpoint: {}", checkpoint);
    }

    @Override
    public void start() {
        // ✅ 使用检查间隔参数注册定时器
        context.callAsync(
                this::checkForNewJobs,
                (result, throwable) -> {
                    if (throwable != null) {
                        logger.error("Error checking for new jobs", throwable);
                    } else if (result != null && !result.isEmpty()) {
                        // 发现新Job，添加到未分配集合
                        for (String jobId : result) {
                            if (!discoveredJobs.contains(jobId)) {
                                unassignedJobs.add(jobId);
                                discoveredJobs.add(jobId);
                                logger.info("Discovered new job: {}", jobId);
                            }
                        }

                        // 尝试分配新Job
                        assignUnassignedJobs();
                    }
                },
                0L, // 立即开始
                taskCompletionCheckInterval);

        logger.info("CalcJobSplitEnumerator started");
    }

    @Override
    public void handleSplitRequest(int subtaskId, @Nullable String requesterHostname) {
        // ✅ 使用maxJobsPerReader参数控制分配
        Set<String> currentJobs = readerJobs.getOrDefault(subtaskId, new HashSet<>());

        if (currentJobs.size() < maxJobsPerReader && !unassignedJobs.isEmpty()) {
            String jobId = selectJobForReader(subtaskId);

            if (jobId != null) {
                CalcJobSplit split = new CalcJobSplit(jobId);
                context.assignSplits(new SplitsAssignment<>(Map.of(subtaskId, List.of(split))));

                unassignedJobs.remove(jobId);
                currentJobs.add(jobId);
                readerJobs.put(subtaskId, currentJobs);

                logger.info(
                        "Assigned job {} to reader {}, total jobs: {}/{}",
                        jobId,
                        subtaskId,
                        currentJobs.size(),
                        maxJobsPerReader);
            }
        } else {
            logger.debug(
                    "Reader {} already has max jobs ({}/{}) or no unassigned jobs available",
                    subtaskId,
                    currentJobs.size(),
                    maxJobsPerReader);
        }
    }

    @Override
    public void addSplitsBack(List<CalcJobSplit> splits, int subtaskId) {
        // ✅ 处理SourceReader故障时的分片回收
        Set<String> readerJobSet = readerJobs.get(subtaskId);

        for (CalcJobSplit split : splits) {
            String jobId = split.getJobId();
            unassignedJobs.add(jobId);

            if (readerJobSet != null) {
                readerJobSet.remove(jobId);
            }

            logger.info(
                    "Added job {} back to unassigned queue from failed reader {}",
                    jobId,
                    subtaskId);
        }

        // 清理空的Reader记录
        if (readerJobSet != null && readerJobSet.isEmpty()) {
            readerJobs.remove(subtaskId);
        }

        // 重新分配给其他健康的SourceReader
        assignUnassignedJobs();
    }

    @Override
    public void addReader(int subtaskId) {
        logger.info("Added new reader: {}", subtaskId);
        // 尝试为新Reader分配Job
        assignUnassignedJobs();
    }

    @Override
    public CalcJobEnumState snapshotState(long checkpointId) throws Exception {
        CalcJobEnumState state =
                CalcJobEnumState.builder()
                        .readerJobs(new HashMap<>(readerJobs))
                        .unassignedJobs(new HashSet<>(unassignedJobs))
                        .build();

        logger.debug("Snapshot state for checkpoint {}: {}", checkpointId, state);
        return state;
    }

    @Override
    public void close() throws Exception {
        logger.info("CalcJobSplitEnumerator closed");
    }

    /** ✅ 动态添加新的Job */
    public void addNewJob(String jobId) {
        if (!discoveredJobs.contains(jobId)) {
            unassignedJobs.add(jobId);
            discoveredJobs.add(jobId);

            // 立即分配给负载最轻的SourceReader
            assignJobToLeastLoadedReader(jobId);

            logger.info("Added new job: {}", jobId);
        }
    }

    private void assignUnassignedJobs() {
        Iterator<String> iterator = unassignedJobs.iterator();
        while (iterator.hasNext()) {
            String jobId = iterator.next();
            int targetReader = findLeastLoadedReader();

            if (targetReader >= 0) {
                Set<String> readerJobSet = readerJobs.getOrDefault(targetReader, new HashSet<>());
                if (readerJobSet.size() < maxJobsPerReader) {
                    CalcJobSplit split = new CalcJobSplit(jobId);
                    context.assignSplits(
                            new SplitsAssignment<>(Map.of(targetReader, List.of(split))));

                    iterator.remove();
                    readerJobSet.add(jobId);
                    readerJobs.put(targetReader, readerJobSet);

                    logger.info("Auto-assigned job {} to reader {}", jobId, targetReader);
                }
            } else {
                break; // 没有可用的Reader
            }
        }
    }

    private void assignJobToLeastLoadedReader(String jobId) {
        int targetReader = findLeastLoadedReader();
        if (targetReader >= 0) {
            Set<String> readerJobSet = readerJobs.getOrDefault(targetReader, new HashSet<>());
            if (readerJobSet.size() < maxJobsPerReader) {
                CalcJobSplit split = new CalcJobSplit(jobId);
                context.assignSplits(new SplitsAssignment<>(Map.of(targetReader, List.of(split))));

                unassignedJobs.remove(jobId);
                readerJobSet.add(jobId);
                readerJobs.put(targetReader, readerJobSet);

                logger.info("Assigned new job {} to least loaded reader {}", jobId, targetReader);
            }
        }
    }

    private String selectJobForReader(int subtaskId) {
        return unassignedJobs.isEmpty() ? null : unassignedJobs.iterator().next();
    }

    private int findLeastLoadedReader() {
        return context.registeredReaders().keySet().stream()
                .min(
                        Comparator.comparingInt(
                                readerId ->
                                        readerJobs
                                                .getOrDefault(readerId, Collections.emptySet())
                                                .size()))
                .orElse(-1);
    }

    /** 检查新Job（与GetCalcJobInfoTimer集成） */
    private CompletableFuture<List<String>> checkForNewJobs() {
        return CompletableFuture.supplyAsync(
                () -> {
                    // TODO: 实际实现中应该与GetCalcJobInfoTimer集成
                    // 这里返回模拟的新Job列表
                    return new ArrayList<>();
                });
    }
}
