package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblCategoryPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.TBL_CATEGORY\n"
                    + "(\n"
                    + "    CATEGORY_ID            VARCHAR,\n"
                    + "    CATEGORY_DISPLAY_NAME  VARCHAR,\n"
                    + "    CREATED_TIME           TIMESTAMP,\n"
                    + "    CREATED_USER           VARCHAR,\n"
                    + "    MODIFIED_TIME          TIMESTAMP,\n"
                    + "    MODIFIED_USER          VARCHAR,\n"
                    + "    PRIMARY KEY (CATEGORY_ID)\n"
                    + "    )WITH \"template=%s,ATOMICITY=%s,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Collections.singletonList(
                        "CREATE INDEX IF NOT EXISTS INDEX_TBL_CATEGORY_CATEGORY_ID ON %s.TBL_CATEGORY (CATEGORY_ID);");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
