<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/util
       http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="igniteSchema" class="java.lang.String">
        <constructor-arg value="GRAVITY"/>
    </bean>

    <!-- Ignite Configuration -->
    <bean class="org.apache.ignite.configuration.IgniteConfiguration">
        <property name="cacheConfiguration">
            <list>
                <!-- Configuration for TBL_WRITE_BEHIND_TEST -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="writeBehindEnabled" value="true" />
                    <!-- Default flush size for write-behind cache store. 10K -->
                    <property name="writeBehindFlushSize" value="1024" />
                    <!-- Default flush frequency for write-behind cache store in milliseconds. 5000 -->
                    <property name="writeBehindFlushFrequency" value="1000" />
                    <!-- Default count of flush threads for write-behind cache store. 1 -->
                    <property name="writeBehindFlushThreadCount" value="1" />
                    <!-- Default batch size for write-behind cache store. 512 -->
                    <property name="writeBehindBatchSize" value="512" />
                    <!-- Default write coalescing for write-behind cache store. true-->
                    <property name="writeBehindCoalescing" value="true"/>


                    <property name="name" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_WRITE_BEHIND_TEST"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="test_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="test_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="test_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="test_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="test_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="test_name"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_WRITE_BEHIND_TEST_VALUE"/>
                                <property name="tableName" value="TBL_WRITE_BEHIND_TEST"/>
                                <property name="keyFields">
                                    <list>
                                        <value>test_id</value>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="test_id" value="java.lang.String"/>
                                        <entry key="test_name" value="java.lang.String"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>
</beans>
