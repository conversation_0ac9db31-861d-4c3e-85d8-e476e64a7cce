package com.envision.gravity.common.vo.man;

import javax.validation.constraints.NotBlank;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InitOUReq {
    private String influxdbBucketRetention;

    @NotBlank(message = "resourceLevel can not be blank")
    private String resourceLevel;
}
