package com.envision.gravity.flink.streaming.postgres.cdc.entity;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.*;

/** <AUTHOR> 2024/6/26 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParsedCdcRecord {
    private String schema;
    private String db;
    private CDCTableName table;

    // -----------------------------------------------
    private CDCTableEntity before;
    private CDCTableEntity after;
    private String op;
    private Long tsMs;
}
