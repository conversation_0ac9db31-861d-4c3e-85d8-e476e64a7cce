package com.envision.gravity.flink.streaming.calculate.aspect.source.split;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ✅ Job枚举器状态
 *
 * <p>用于StreamingCalcJobTaskSource的状态管理和恢复 保存Job分配和未分配的状态信息
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobEnumState implements Serializable {

    private static final long serialVersionUID = 1L;

    /** Reader到Job的分配映射 Key: readerId (subtaskId) Value: 分配给该Reader的Job ID集合 */
    private Map<Integer, Set<String>> readerJobs;

    /** 未分配的Job ID集合 */
    private Set<String> unassignedJobs;

    /** 获取总Job数量 */
    public int getTotalJobCount() {
        int assignedCount =
                readerJobs != null ? readerJobs.values().stream().mapToInt(Set::size).sum() : 0;
        int unassignedCount = unassignedJobs != null ? unassignedJobs.size() : 0;
        return assignedCount + unassignedCount;
    }

    /** 获取已分配Job数量 */
    public int getAssignedJobCount() {
        return readerJobs != null ? readerJobs.values().stream().mapToInt(Set::size).sum() : 0;
    }

    /** 获取未分配Job数量 */
    public int getUnassignedJobCount() {
        return unassignedJobs != null ? unassignedJobs.size() : 0;
    }

    /** 检查是否有未分配的Job */
    public boolean hasUnassignedJobs() {
        return unassignedJobs != null && !unassignedJobs.isEmpty();
    }

    /** 获取指定Reader的Job数量 */
    public int getJobCountForReader(int readerId) {
        if (readerJobs == null) {
            return 0;
        }
        Set<String> jobs = readerJobs.get(readerId);
        return jobs != null ? jobs.size() : 0;
    }

    @Override
    public String toString() {
        return String.format(
                "CalcJobEnumState{totalJobs=%d, assigned=%d, unassigned=%d, readers=%d}",
                getTotalJobCount(),
                getAssignedJobCount(),
                getUnassignedJobCount(),
                readerJobs != null ? readerJobs.size() : 0);
    }
}
