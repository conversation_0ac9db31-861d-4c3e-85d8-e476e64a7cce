package com.envision.gravity.flink.streaming.calculate.dto.calc;

/**
 * 属性类型枚举
 *
 * <p>用于： 1. 区分测量点和属性 2. 控制是否输出到Kafka
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public enum PrefType {

    /** 测量点（输出到Kafka） */
    MEASURE_POINT("measurepoint"),

    /** 属性（不输出到Kafka） */
    ATTRIBUTE("attribute");

    private final String value;

    PrefType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /** 根据字符串值获取枚举 */
    public static PrefType fromValue(String value) {
        for (PrefType type : PrefType.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PrefType value: " + value);
    }

    /** 检查是否为测量点 */
    public boolean isMeasurePoint() {
        return this == MEASURE_POINT;
    }

    /** 检查是否为属性 */
    public boolean isAttribute() {
        return this == ATTRIBUTE;
    }
}
