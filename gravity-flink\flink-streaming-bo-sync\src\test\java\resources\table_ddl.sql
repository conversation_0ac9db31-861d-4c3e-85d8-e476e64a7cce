CREATE TABLE IF NOT EXISTS public.bo_sync_log
(
    sync_id          varchar(512)  NOT NULL,
    org_id           varchar(512)  NOT NULL,
    bo_id            varchar(1024) NOT NULL,
    bo_type          varchar(128)  NOT NULL,
    operation        varchar(64)   NOT NULL,
    status           varchar(64)   NOT NULL,
    request_params   varchar       NULL,
    response_code    int4          NOT NULL,
    response_message varchar       NULL,
    response_detail  varchar       NULL,
    retry_count      int8        NOT NULL DEFAULT 0,
    created_time     timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_time    timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_user     varchar(64)   NULL,
    modified_user    varchar(64)   NULL,
    CONSTRAINT bo_sync_log_pkey PRIMARY KEY (sync_id, org_id, bo_id, bo_type)
);
CREATE INDEX idx_bsl_status ON public.bo_sync_log USING btree (status);




