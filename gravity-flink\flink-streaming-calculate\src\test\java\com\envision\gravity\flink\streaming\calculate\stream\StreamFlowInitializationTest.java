package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;
import com.envision.gravity.flink.streaming.calculate.meta.DirectMappingProcessor;


import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** 专门用于测试 StreamFlow 初始化过程的测试类 不启动实际的流处理，只测试各个组件的初始化 */
public class StreamFlowInitializationTest {

    private static final Logger logger =
            LoggerFactory.getLogger(StreamFlowInitializationTest.class);

    @Test
    public void testStreamFlowInitialization() {
        System.out.println("=== StreamFlow Initialization Test Started ===");

        try {
            // 步骤1：测试 PostgreSQL 连接
            System.out.println("=== Step 1: Testing PostgreSQL Connection ===");
            testPostgreSQLConnection();
            System.out.println("✅ PostgreSQL connection successful");

            // 步骤2：测试 Ignite 连接
            System.out.println("=== Step 2: Testing Ignite Connection ===");
            testIgniteConnection();
            System.out.println("✅ Ignite connection successful");

            // 步骤3：测试 DirectMappingProcessor 初始化
            System.out.println("=== Step 3: Testing DirectMappingProcessor Initialization ===");
            testDirectMappingProcessor();
            System.out.println("✅ DirectMappingProcessor initialization successful");

            // 步骤4：测试 CalcPrefCache 初始化
            System.out.println("=== Step 4: Testing CalcPrefCache Initialization ===");
            testCalcPrefCache();
            System.out.println("✅ CalcPrefCache initialization successful");

            // 步骤5：测试 ModelMetaQueryHandler 初始化
            System.out.println("=== Step 5: Testing ModelMetaQueryHandler Initialization ===");
            testModelMetaQueryHandler();
            System.out.println("✅ ModelMetaQueryHandler initialization successful");

            // 步骤6：测试 CalcMetaProcessor 初始化（但不调用 batchLoad）
            System.out.println("=== Step 6: Testing CalcMetaProcessor Initialization ===");
            testCalcMetaProcessorCreation();
            System.out.println("✅ CalcMetaProcessor creation successful");

            // 步骤7：测试 CalcMetaProcessor.batchLoad()
            System.out.println("=== Step 7: Testing CalcMetaProcessor.batchLoad() ===");
            testCalcMetaProcessorBatchLoad();
            System.out.println("✅ CalcMetaProcessor.batchLoad() successful");

            System.out.println("=== 🎉 All StreamFlow Initialization Tests Passed! ===");

        } catch (Exception e) {
            System.out.println("=== ❌ StreamFlow Initialization Test Failed ===");
            System.out.println("Exception: " + e.getClass().getSimpleName());
            System.out.println("Message: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    private void testPostgreSQLConnection() {
        CalcPGSourceConfig.getSqlSessionFactory();
    }

    private void testIgniteConnection() {
        com.envision.gravity.common.util.IgniteUtil.query("PUBLIC", "SELECT 1");
    }

    private void testDirectMappingProcessor() {
        DirectMappingProcessor processor = DirectMappingProcessor.getInstance();
        System.out.println("DirectMappingProcessor instance: " + processor);
    }

    private void testCalcPrefCache() {
        com.envision.gravity.cache.calculate.CalcPrefCache cache = CacheFactory.getCalcPrefCache();
        System.out.println("CalcPrefCache instance: " + cache);
    }

    private void testModelMetaQueryHandler() {
        ModelMetaQueryHandler handler = ModelMetaQueryHandler.getInstance();
        System.out.println("ModelMetaQueryHandler instance: " + handler);
    }

    private void testCalcMetaProcessorCreation() {
        // 只创建实例，不调用 batchLoad
        CalcMetaProcessor processor = CalcMetaProcessor.getInstance();
        System.out.println("CalcMetaProcessor instance: " + processor);
    }

    private void testCalcMetaProcessorBatchLoad() {
        // 调用 batchLoad 方法
        CalcMetaProcessor processor = CalcMetaProcessor.getInstance();
        processor.batchLoad();
    }

    @Test
    public void testKafkaMessageProcessorInitialization() {
        System.out.println("=== Testing KafkaMessageProcessor Initialization ===");

        try {
            // 模拟 KafkaMessageProcessor 的初始化过程
            // 这是 StreamFlow 中实际会调用的初始化逻辑
            System.out.println("=== About to call CalcMetaProcessor.getInstance().batchLoad() ===");
            logger.info("About to call CalcMetaProcessor.getInstance().batchLoad()");

            CalcMetaProcessor calcMetaProcessor = CalcMetaProcessor.getInstance();
            calcMetaProcessor.batchLoad();

            System.out.println("=== CalcMetaProcessor.batchLoad() completed successfully ===");
            logger.info("CalcMetaProcessor initialized successfully");

            System.out.println("✅ KafkaMessageProcessor initialization simulation successful");

        } catch (Exception e) {
            System.out.println(
                    "=== CalcMetaProcessor.batchLoad() FAILED: " + e.getMessage() + " ===");
            logger.error("CalcMetaProcessor initialization failed: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Test
    public void testIndividualComponentsStep() {
        System.out.println("=== Testing Individual Components Step by Step ===");

        // 逐步测试每个组件，遇到错误立即停止
        try {
            System.out.println("1. Testing DirectMappingProcessor...");
            DirectMappingProcessor.getInstance();
            System.out.println("   ✅ DirectMappingProcessor OK");

            System.out.println("2. Testing CalcPrefCache...");
            CacheFactory.getCalcPrefCache();
            System.out.println("   ✅ CalcPrefCache OK");

            System.out.println("3. Testing ModelMetaQueryHandler...");
            ModelMetaQueryHandler.getInstance();
            System.out.println("   ✅ ModelMetaQueryHandler OK");

            System.out.println("4. Testing CalcMetaProcessor creation...");
            CalcMetaProcessor.getInstance();
            System.out.println("   ✅ CalcMetaProcessor creation OK");

            System.out.println("5. Testing getSchemas()...");
            // 直接测试 getSchemas 调用
            com.envision.gravity.common.util.IgniteUtil.query(
                    "PUBLIC", "SELECT DISTINCT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA");
            System.out.println("   ✅ getSchemas() OK");

            System.out.println("6. Testing full batchLoad()...");
            CalcMetaProcessor.getInstance().batchLoad();
            System.out.println("   ✅ batchLoad() OK");

            System.out.println("=== All individual components tested successfully ===");

        } catch (Exception e) {
            System.out.println("=== Component test failed at step ===");
            System.out.println("Exception: " + e.getClass().getSimpleName());
            System.out.println("Message: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
