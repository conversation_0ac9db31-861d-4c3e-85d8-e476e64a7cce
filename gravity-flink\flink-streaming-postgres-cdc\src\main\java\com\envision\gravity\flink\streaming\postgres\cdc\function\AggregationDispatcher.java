package com.envision.gravity.flink.streaming.postgres.cdc.function;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.aggregator.*;
import com.envision.gravity.flink.streaming.postgres.cdc.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.CDCTableName;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.OPEnum;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.side.SideOutputs;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/15
 * @description
 */
@Slf4j
public class AggregationDispatcher
        extends ProcessWindowFunction<
                ParsedCdcRecord, List<AggregatedResults>, String, TimeWindow> {
    private static final long serialVersionUID = -8381381949507945216L;
    private transient ModelAggregator modelAggregator;
    private transient ModelCompAggregator modelCompAggregator;
    private transient ComponentAggregator componentAggregator;
    private transient CompPrefAggregator compPrefAggregator;
    private transient PrefAggregator prefAggregator;
    private transient BOAggregator boAggregator;

    @Override
    public void open(Configuration params) {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        modelAggregator = new ModelAggregator(sqlSessionFactory);
        modelCompAggregator = new ModelCompAggregator(sqlSessionFactory);
        componentAggregator = new ComponentAggregator(sqlSessionFactory);
        compPrefAggregator = new CompPrefAggregator(sqlSessionFactory);
        prefAggregator = new PrefAggregator(sqlSessionFactory);
        boAggregator = new BOAggregator(sqlSessionFactory);
    }

    public void close() throws Exception {
        PGDataSourceConfig.closeDataSource();
    }

    @Override
    public void process(
            String s,
            ProcessWindowFunction<ParsedCdcRecord, List<AggregatedResults>, String, TimeWindow>
                            .Context
                    context,
            Iterable<ParsedCdcRecord> elements,
            Collector<List<AggregatedResults>> out)
            throws Exception {
        try {
            // group by schemaName and  tableName
            Map<String, Map<CDCTableName, List<ParsedCdcRecord>>> groupedCdcRecord =
                    StreamSupport.stream(elements.spliterator(), false)
                            .collect(
                                    Collectors.groupingBy(
                                            ParsedCdcRecord::getSchema,
                                            Collectors.groupingBy(ParsedCdcRecord::getTable)));

            groupedCdcRecord.forEach(
                    (schemaName, tableCdcRecordsMap) -> {
                        List<AggregatedResults> resultsBySchema = new ArrayList<>();
                        // aggregate by table first
                        tableCdcRecordsMap.forEach(
                                (cdcTable, ParsedCdcRecordList) -> {
                                    // group by cdcOP
                                    Map<String, List<ParsedCdcRecord>> groupByCdcOP =
                                            ParsedCdcRecordList.stream()
                                                    .collect(
                                                            Collectors.groupingBy(
                                                                    ParsedCdcRecord::getOp));

                                    Aggregator aggregator = getAggregator(cdcTable);

                                    if (aggregator != null) {
                                        groupByCdcOP.forEach(
                                                (cdcOp, cdcRecordList) -> {
                                                    if (OPEnum.c.name().equalsIgnoreCase(cdcOp)) {
                                                        AggregatedResults aggregatedResults =
                                                                aggregator.aggregateCreatedData(
                                                                        cdcRecordList);
                                                        if (aggregatedResults != null) {
                                                            resultsBySchema.add(aggregatedResults);
                                                        }
                                                    } else if (OPEnum.u
                                                            .name()
                                                            .equalsIgnoreCase(cdcOp)) {
                                                        AggregatedResults aggregatedResults =
                                                                aggregator.aggregateUpdatedData(
                                                                        cdcRecordList);
                                                        if (aggregatedResults != null) {
                                                            resultsBySchema.add(aggregatedResults);
                                                        }
                                                    } else if (OPEnum.d
                                                            .name()
                                                            .equalsIgnoreCase(cdcOp)) {
                                                        AggregatedResults aggregatedResults =
                                                                aggregator.aggregateDeletedData(
                                                                        cdcRecordList);
                                                        if (aggregatedResults != null) {
                                                            resultsBySchema.add(aggregatedResults);
                                                        }
                                                    } else {
                                                        log.warn(
                                                                "Undefined cdc op: {}, table:{}",
                                                                cdcOp,
                                                                cdcTable);
                                                    }
                                                });
                                    }
                                });

                        if (!resultsBySchema.isEmpty()) {
                            distributeAggregatedRes(schemaName, resultsBySchema, context);
                        }
                    });
        } catch (Exception e) {
            log.error("Aggregation error!", e);
            throw new GravityRuntimeException("Aggregation error!", e);
        }
    }

    private Aggregator getAggregator(CDCTableName cdcTable) {
        Aggregator aggregator;
        switch (cdcTable) {
            case TBL_BO_MODEL:
                aggregator = modelAggregator;
                break;
            case TBL_COMPONENT:
                aggregator = componentAggregator;
                break;
            case TBL_BO_MODEL_COMP:
                aggregator = modelCompAggregator;
                break;
            case TBL_COMPONENT_PREF:
                aggregator = compPrefAggregator;
                break;
            case TBL_PREF:
                aggregator = prefAggregator;
                break;
            case TBL_BO:
                aggregator = boAggregator;
                break;
            default:
                log.warn("Undefined table: {}", cdcTable);
                return null;
        }
        return aggregator;
    }

    private void distributeAggregatedRes(
            String schemaName,
            List<AggregatedResults> resultsBySchema,
            ProcessWindowFunction<ParsedCdcRecord, List<AggregatedResults>, String, TimeWindow>
                            .Context
                    context) {
        List<RefreshModelReq> refreshModelReqList = new ArrayList<>();
        List<RefreshObjectReq> refreshObjectReqList = new ArrayList<>();
        List<NebulaGraphReq> nebulaGraphReqList = new ArrayList<>();

        for (AggregatedResults aggregatedResults : resultsBySchema) {
            if (aggregatedResults.getRefreshModelReq() != null) {
                refreshModelReqList.add(aggregatedResults.getRefreshModelReq());
            }

            if (aggregatedResults.getRefreshObjectReq() != null) {
                refreshObjectReqList.add(aggregatedResults.getRefreshObjectReq());
            }

            if (aggregatedResults.getNebulaGraphReq() != null) {
                nebulaGraphReqList.add(aggregatedResults.getNebulaGraphReq());
            }
        }

        if (!refreshModelReqList.isEmpty()) {
            context.output(
                    SideOutputs.REFRESH_MODEL_TAG, Tuple2.of(schemaName, refreshModelReqList));
        }

        if (!refreshObjectReqList.isEmpty()) {
            context.output(
                    SideOutputs.REFRESH_OBJECT_TAG, Tuple2.of(schemaName, refreshObjectReqList));
        }

        if (!nebulaGraphReqList.isEmpty()) {
            context.output(SideOutputs.NEBULA_GRAPH_TAG, Tuple2.of(schemaName, nebulaGraphReqList));
        }
    }
}
