package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.enums.FieldType;
import com.envision.gravity.common.po.TblField;
import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.field.*;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.exception.ParamInvalidException;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.repository.FieldRepository;
import com.envision.gravity.low.level.api.rest.repository.IgniteTblObjAttributeRepo;
import com.envision.gravity.low.level.api.rest.repository.IgniteTblObjPointRepo;
import com.envision.gravity.low.level.api.rest.service.FieldService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import io.eniot.tsdb.common.util.CommonUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.*;
import java.util.stream.Collectors;

import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.checkCategoryReqDefine;
import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.checkFieldReqDefine;

/**
 * <AUTHOR>
 * @date 2024/1/9
 * @description
 */
@Slf4j
@Validated
@Api(tags = "Field")
@RestController
@RequestMapping("/field")
public class FieldController {

    @Resource private FieldService fieldService;

    @Resource private FieldRepository fieldRepository;

    @Resource private IgniteTblObjAttributeRepo tblObjAttributeRepo;

    @Resource private IgniteTblObjPointRepo igniteTblObjPointRepo;

    @GravityLog
    @PostMapping(value = "/batch-add-or-update")
    public ResponseResult<?> batchCreateOrUpdateFields(
            @RequestBody BatchFieldReq batchFieldReq,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (batchFieldReq.getFields() == null || batchFieldReq.getFields().isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        Map<Integer, Object> dryRunResult;
        if (batchFieldReq.isDryRun()) {
            dryRunResult = new HashMap<>(8);
        } else {
            dryRunResult = null;
        }
        checkFieldReqDefine(batchFieldReq.getFields(), dryRunResult);

        checkCategoryReqDefine(
                batchFieldReq.getFields().stream()
                        .map(FieldReq::getCategoryReq)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()),
                dryRunResult);

        return fieldService.batchCreateOrUpdateFields(
                BatchFieldReq.builder()
                        .fields(batchFieldReq.getFields())
                        .dryRun(batchFieldReq.isDryRun())
                        .build(),
                dryRunResult,
                orgId,
                auditHeader);
    }

    @GravityLog
    @DeleteMapping(value = "/batch-delete")
    public ResponseResult<?> batchDeleteFields(
            @RequestBody FieldBatchDeleteReq fieldBatchDeleteReq,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId) {
        List<TblField> toDeleteFields = null;
        String categoryId = fieldBatchDeleteReq.getCategoryId();
        List<String> fieldIds = fieldBatchDeleteReq.getFieldIds();
        boolean isCategoryEmpty = (categoryId == null || categoryId.isEmpty());
        boolean isFieldIdsEmpty = (fieldIds == null || fieldIds.isEmpty());
        if (isCategoryEmpty && isFieldIdsEmpty) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (!isFieldIdsEmpty) {
            if (!isCategoryEmpty) {
                toDeleteFields =
                        fieldRepository.queryFieldIdsByCategoryIdAndFieldIds(
                                categoryId, fieldIds, orgId);
            } else {
                toDeleteFields =
                        fieldRepository.queryFieldsByFieldIds(new HashSet<>(fieldIds), orgId);
            }
        } else {
            toDeleteFields = fieldRepository.queryFieldIdsByCategoryId(categoryId, orgId);
        }

        if (CommonUtil.emptyCollection(toDeleteFields)) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        // FieldIndex => systemIds
        Set<Integer> attrFieldIndexSet =
                toDeleteFields.stream()
                        .filter(f -> FieldType.ATTRIBUTE.name().equals(f.getFieldType()))
                        .map(TblField::getFieldIndex)
                        .collect(Collectors.toSet());
        Set<Integer> pointFieldIndexSet =
                toDeleteFields.stream()
                        .filter(f -> FieldType.MEASUREPOINT.name().equals(f.getFieldType()))
                        .map(TblField::getFieldIndex)
                        .collect(Collectors.toSet());

        Map<Integer, Set<String>> attrFieldIdSystemIdsMap =
                this.tblObjAttributeRepo.findObjByFieldIndex(orgId, attrFieldIndexSet);
        Map<Integer, Set<String>> pointFieldIdSystemIdsMap =
                this.igniteTblObjPointRepo.findObjByFieldIndex(orgId, pointFieldIndexSet);

        for (TblField field : toDeleteFields) {
            Set<String> attrUsedSystemIds = attrFieldIdSystemIdsMap.get(field.getFieldIndex());
            if (!CommonUtil.emptyCollection(attrUsedSystemIds)) {
                throw new ParamInvalidException(
                        String.format(
                                "Field: %s can not delete, because field using by objs: %s.",
                                field.getFieldId(), attrUsedSystemIds));
            }

            Set<String> pointUsedSystemIds = pointFieldIdSystemIdsMap.get(field.getFieldIndex());
            if (!CommonUtil.emptyCollection(pointUsedSystemIds)) {
                throw new ParamInvalidException(
                        String.format(
                                "Field: %s can not delete, because field using by objs: %s.",
                                field.getFieldId(), pointUsedSystemIds));
            }
        }

        List<String> toDeleteFieldIds =
                toDeleteFields.stream().map(TblField::getFieldId).collect(Collectors.toList());
        return fieldService.batchDeleteFields(toDeleteFieldIds, orgId);
    }

    @GravityLog
    @PostMapping
    public ResponseResult<?> queryField(
            @RequestBody QueryFieldReq queryFieldReq,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        boolean isByFieldId =
                (queryFieldReq.getFieldId() != null && !queryFieldReq.getFieldId().isEmpty());
        boolean isByFieldNameAndCategory =
                (queryFieldReq.getFieldName() != null
                        && !queryFieldReq.getFieldName().isEmpty()
                        && queryFieldReq.getCategoryId() != null
                        && !queryFieldReq.getCategoryId().isEmpty());
        FieldResp fieldResp = null;
        if (isByFieldId || isByFieldNameAndCategory) {
            fieldResp =
                    fieldService.queryField(
                            QueryFieldReq.builder()
                                    .fieldId(queryFieldReq.getFieldId())
                                    .fieldName(queryFieldReq.getFieldName())
                                    .categoryId(queryFieldReq.getCategoryId())
                                    .build(),
                            language,
                            orgId);
        } else {
            throw new ParamInvalidException(
                    "query field only support by (fieldId), (fieldName and categoryId)");
        }
        log.info("Query field success.");
        return ResponseResult.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .message(ResponseCodeEnum.SUCCESS.getMessage())
                .data(fieldResp)
                .build();
    }

    @GravityLog
    @PostMapping(value = "/list")
    public ResponseResult<?> queryFieldList(
            @RequestBody @Valid QueryFieldListReq queryFieldListReq,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            HttpServletRequest request) {
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        QueryFieldListResp fieldRespList =
                fieldService.queryFieldList(queryFieldListReq, language, orgId);
        log.info("Query field list success.");
        return ResponseResult.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .message(ResponseCodeEnum.SUCCESS.getMessage())
                .data(fieldRespList)
                .build();
    }
}
