package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgList;


import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class OffsetExtractFunction
        extends ProcessFunction<Tuple2<LegacyMsgList, OffsetInfo>, OffsetInfo> {
    @Override
    public void processElement(
            Tuple2<LegacyMsgList, OffsetInfo> value,
            ProcessFunction<Tuple2<LegacyMsgList, OffsetInfo>, OffsetInfo>.Context ctx,
            Collector<OffsetInfo> out)
            throws Exception {
        out.collect(value.f1);
    }
}
