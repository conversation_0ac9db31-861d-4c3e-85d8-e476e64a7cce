package com.envision.gravity.low.level.api.rest.dao.sg;

import com.envision.gravity.common.po.TblBOGroupRelation;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19
 * @description
 */
public interface SGBOGroupRelationMapper {
    int batchReplace(
            @Param("orgId") String orgId,
            @Param("boGroupRelations") List<TblBOGroupRelation> boGroupRelations);

    int deleteByBOList(@Param("orgId") String orgId, @Param("boIds") List<String> boIds);
}
