package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblPrefMapper;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
@Slf4j
public class TblPrefRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblPrefRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<ModelGroup> selectModelGroupList(String schemaName, List<String> prefIdList) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (prefIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblPrefMapper tblPrefMapper = session.getMapper(TblPrefMapper.class);
            return tblPrefMapper.selectModelGroupList(schemaName, prefIdList);
        } catch (Exception e) {
            log.error("Select model group list error.", e);
            throw new GravityRuntimeException("Select model group list error.", e);
        }
    }

    public List<ModelGroup> selectModelGroupByPref(String schemaName, List<String> prefIdList) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (prefIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblPrefMapper tblPrefMapper = session.getMapper(TblPrefMapper.class);
            return tblPrefMapper.selectModelGroupByPref(schemaName, prefIdList);
        } catch (Exception e) {
            log.error("Select model group list by pref error.", e);
            throw new GravityRuntimeException("Select model group list by pref error.", e);
        }
    }
}
