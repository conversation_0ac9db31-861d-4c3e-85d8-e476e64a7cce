package com.envision.gravity.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/4/17
 * @description
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheValue {
    /** @return Name. */
    String name();

    /**
     * Specifies whether the specified field can be {@code null}.
     *
     * @return {@code True} if the field is not allowed to accept {@code null} values.
     */
    boolean notNull() default false;

    /** @return Type. */
    Class<?> type() default String.class;
}
