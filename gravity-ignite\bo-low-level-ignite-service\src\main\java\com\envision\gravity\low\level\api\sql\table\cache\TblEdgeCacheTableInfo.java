package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.QueryIndexType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblEdgeCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "from_vid", String.class, "from_vid"),
                        new JdbcTypeField(Types.VARCHAR, "to_vid", String.class, "to_vid"),
                        new JdbcTypeField(
                                Types.VARCHAR, "edge_type_id", String.class, "edge_type_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "sub_graph_id", String.class, "sub_graph_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "from_vid", String.class, "from_vid"),
                        new JdbcTypeField(Types.VARCHAR, "to_vid", String.class, "to_vid"),
                        new JdbcTypeField(
                                Types.VARCHAR, "edge_type_id", String.class, "edge_type_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "sub_graph_id", String.class, "sub_graph_id"),
                        new JdbcTypeField(Types.VARCHAR, "prop_value", String.class, "prop_value"),
                        new JdbcTypeField(
                                Types.VARCHAR, "tree_node_id", String.class, "tree_node_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("from_vid");
        QUERY_ENTITY_KEY_FIELDS.add("to_vid");
        QUERY_ENTITY_KEY_FIELDS.add("edge_type_id");
        QUERY_ENTITY_KEY_FIELDS.add("sub_graph_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("from_vid");
        NOT_NULL_FIELDS.add("to_vid");
        NOT_NULL_FIELDS.add("edge_type_id");
        NOT_NULL_FIELDS.add("sub_graph_id");
        NOT_NULL_FIELDS.add("prop_value");
        NOT_NULL_FIELDS.add("tree_node_id");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("sub_graph_id"),
                        new QueryIndex(
                                Arrays.asList("from_vid", "to_vid", "edge_type_id", "sub_graph_id"),
                                QueryIndexType.SORTED),
                        new QueryIndex(
                                Arrays.asList("to_vid", "from_vid", "edge_type_id", "sub_graph_id"),
                                QueryIndexType.SORTED));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("from_vid", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("to_vid", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("edge_type_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("sub_graph_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("prop_value", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("tree_node_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
