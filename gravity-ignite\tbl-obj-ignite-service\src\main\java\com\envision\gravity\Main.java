package com.envision.gravity;

import java.util.*;


import org.apache.ignite.Ignition;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;

public class Main {
    public static void main(String[] args) {
        createIgniteClient();
        System.out.println("suc");
    }

    private static IgniteClient createIgniteClient() {

        String serverAddress = "*************";
        int clientPort = 10800;
        String user = "ignite";
        String passwd = "ignite";

        List<String> serverIps = Arrays.asList(serverAddress.split(","));

        try {
            Set<String> addrs = new HashSet<>(serverIps.size());
            for (String serverIp : serverIps) {
                addrs.add(serverIp + ":" + clientPort);
            }
            ClientConfiguration conf =
                    new ClientConfiguration()
                            .setAddresses(addrs.toArray(new String[0]))
                            .setPartitionAwarenessEnabled(true)
                            .setTimeout(120000)
                            .setUserName(user)
                            .setUserPassword(passwd);
            return Ignition.startClient(conf);
        } catch (Exception e) {
            throw new RuntimeException("Create Ignite Client Error", e);
        }
    }
}
