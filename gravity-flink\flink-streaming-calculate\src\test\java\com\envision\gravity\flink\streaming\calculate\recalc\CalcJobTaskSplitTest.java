package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTaskStatusEnum;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * CalcJobTaskSplit 单元测试
 *
 * <AUTHOR>
 */
class CalcJobTaskSplitTest {

    private CalcJobTask testTask;
    private CalcJobTaskSplit testSplit;

    @BeforeEach
    void setUp() {
        testTask =
                CalcJobTask.builder()
                        .jobId("test_job_001")
                        .taskId("task_001")
                        .targetPrefName("test_pref")
                        .targetAssetIds(java.util.Arrays.asList("asset_001", "asset_002"))
                        .startTime(System.currentTimeMillis())
                        .endTime(System.currentTimeMillis() + 3600000)
                        .status(CalcJobTaskStatusEnum.INIT)
                        .build();

        testSplit = new CalcJobTaskSplit("split_001", testTask);
    }

    @Test
    void testSplitId() {
        assertEquals("split_001", testSplit.splitId());
    }

    @Test
    void testGetTask() {
        assertEquals(testTask, testSplit.getTask());
    }

    @Test
    void testIsCompleted_InitialState() {
        assertFalse(testSplit.isCompleted());
    }

    @Test
    void testMarkCompleted() {
        assertFalse(testSplit.isCompleted());

        testSplit.markCompleted();

        assertTrue(testSplit.isCompleted());
    }

    @Test
    void testToString() {
        String result = testSplit.toString();

        assertTrue(result.contains("CalcJobTaskSplit"));
        assertTrue(result.contains("split_001"));
        assertTrue(result.contains("task_001"));
        assertTrue(result.contains("isCompleted=false"));
    }

    @Test
    void testToString_WithNullTask() {
        CalcJobTaskSplit splitWithNullTask = new CalcJobTaskSplit("split_002", null);
        String result = splitWithNullTask.toString();

        assertTrue(result.contains("CalcJobTaskSplit"));
        assertTrue(result.contains("split_002"));
        assertTrue(result.contains("taskId='null'"));
    }

    @Test
    void testMarkCompleted_MultipleCallsIdempotent() {
        assertFalse(testSplit.isCompleted());

        testSplit.markCompleted();
        assertTrue(testSplit.isCompleted());

        testSplit.markCompleted(); // 再次调用
        assertTrue(testSplit.isCompleted()); // 仍然为 true
    }
}
