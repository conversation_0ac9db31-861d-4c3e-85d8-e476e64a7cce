package com.envision.gravity.common.vo.field;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/06/11 16:39 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchFieldReq {

    @Valid
    @NotNull(message = "fields can not be null")
    private List<FieldReq> fields;

    private boolean dryRun;
}
