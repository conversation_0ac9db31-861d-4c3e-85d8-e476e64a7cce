package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaCdcUtils;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Independent CDC processor that handles PostgreSQL CDC events. This processor runs separately from
 * the Kafka message processing.
 *
 * <AUTHOR>
 */
public class StreamCalcCdcProcessor extends ProcessFunction<ConvertedCdcRecord, Void> {

    private static final Logger logger = LoggerFactory.getLogger(StreamCalcCdcProcessor.class);
    private static final long serialVersionUID = 6341159111170527127L;

    @Override
    public void open(Configuration parameters) throws Exception {
        System.out.println("=== CdcProcessor.open() called ===");
        logger.info("CdcProcessor.open() called, initializing...");
        CalcMetaProcessor.getInstance().batchLoad();
        logger.info("CdcProcessor.open() completed successfully");
        System.out.println("=== CdcProcessor.open() completed ===");
    }

    @Override
    public void processElement(
            ConvertedCdcRecord value,
            ProcessFunction<ConvertedCdcRecord, Void>.Context context,
            Collector<Void> collector)
            throws Exception {
        System.out.println("=== CdcProcessor.processElement called ===");
        logger.info("CdcProcessor.processElement called with CDC record: {}", value);
        CalcMetaCdcUtils.process(value);
        logger.info("CdcProcessor.processElement completed");
        System.out.println("=== CdcProcessor.processElement completed ===");
    }
}
