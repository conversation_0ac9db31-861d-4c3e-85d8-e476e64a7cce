package com.envision.gravity.flink.warm.cdc;

import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.warm.model.ModelCache;
import com.envision.gravity.flink.warm.model.PrefValue;
import com.envision.gravity.flink.warm.util.LionConfig;
import com.envision.gravity.flink.warm.util.StandardFieldLib;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.client.ClientCache;

@Slf4j
public class WarmSink extends RichSinkFunction<ObjectWarmRecord> {

    private static final ModelCache modelCache = new ModelCache();
    private static final StandardFieldLib standardFieldLib = new StandardFieldLib();
    public static final String TBL_OBJ_PART_CACHE_NAME_PATTERN = "%s_TBL_OBJ_PART";
    public static final String TBL_OBJ_PART_CACHE_KEY_TYPE_PATTERN = "%s_TBL_OBJ_PART_KEY";
    public static final String TBL_OBJ_PART_KEY_FIELD = "SYSTEM_ID";
    private static final List<Tuple2<String, Class<?>>> fixedCols = LionConfig.getObjFixedCols();

    public void invoke(ObjectWarmRecord record, Context context) {
        String modelId = record.getModelId();
        String orgId = record.getOrgId();
        List<PrefValue> allPrefs = modelCache.getOrLoadModelPref(orgId, modelId);
        List<Tuple2<String, Class<?>>> wideTableColumns = getWideTableColumn(orgId, allPrefs);
        warnUpObjectCache(orgId, record.getSystemId(), wideTableColumns);
    }

    public void close() {
        IgniteUtil.close();
    }

    public static List<Tuple2<String, Class<?>>> getWideTableColumn(
            String orgId, List<PrefValue> allPrefs) {
        List<Tuple2<String, Class<?>>> wideTableColumns = new ArrayList<>(fixedCols);
        List<PrefValue> wideTablePrefs = new ArrayList<>();
        for (PrefValue pref : allPrefs) {
            if (standardFieldLib.exists(pref.getRawFieldId())) {
                wideTablePrefs.add(pref);
            }
        }

        wideTablePrefs.sort(Comparator.comparing(PrefValue::getRawFieldId));

        for (PrefValue pref : wideTablePrefs) {
            if (pref.isAttribute()) {
                wideTableColumns.add(
                        new Tuple2<>(
                                pref.getRawFieldId(),
                                LionConfig.getTypeClass(getType(pref.getRawFieldId()))));
            } else {
                wideTableColumns.add(
                        new Tuple2<>(
                                pref.getRawFieldId() + "_value",
                                LionConfig.getTypeClass(getType(pref.getRawFieldId()))));
                wideTableColumns.add(new Tuple2<>(pref.getRawFieldId() + "_time", Timestamp.class));
                wideTableColumns.add(new Tuple2<>(pref.getRawFieldId() + "_quality", Long.class));
            }
        }
        return wideTableColumns;
    }

    public static void warnUpObjectCache(
            String orgId, String systemId, List<Tuple2<String, Class<?>>> columns) {
        String tblObjKeyType = String.format(TBL_OBJ_PART_CACHE_KEY_TYPE_PATTERN, orgId);
        ClientCache<BinaryObject, BinaryObject> objCache =
                IgniteUtil.getCache(String.format(TBL_OBJ_PART_CACHE_NAME_PATTERN, orgId))
                        .withKeepBinary();

        try {
            BinaryObjectBuilder keyBuilder =
                    IgniteUtil.getIgniteClient().binary().builder(tblObjKeyType);
            keyBuilder.setField(TBL_OBJ_PART_KEY_FIELD, systemId, String.class);
            BinaryObject key = keyBuilder.build();
            BinaryObject value = objCache.get(key);
            if (value == null) {
                log.warn("value not found for systemId: " + systemId);
                return;
            }
            BinaryObjectBuilder valueBuilder = value.toBuilder();
            for (Tuple2<String, Class<?>> column : columns) {
                String colName = column.f0;
                Class<?> colClass = column.f1;
                if (valueBuilder.getField(colName) == null) {
                    valueBuilder.setField(colName, null, (Class<Object>) colClass);
                }
            }
            objCache.put(key, valueBuilder.build());
            log.info(
                    "warn up {} object cache for systemId: {}, column num {}, column list: {}",
                    orgId,
                    systemId,
                    columns.size(),
                    columns);

            System.out.println(
                    "warn up "
                            + orgId
                            + " object cache for systemId: "
                            + systemId
                            + ", column num "
                            + columns.size()
                            + ", column list: "
                            + columns);

        } catch (Throwable t) {
            log.error("worm tbl obj for systemId: " + systemId + " failed", t);
            String stack =
                    Arrays.stream(t.getStackTrace())
                            .map(StackTraceElement::toString)
                            .collect(Collectors.joining(","));
            log.warn("stack trace" + stack);
        }
    }

    public static void dryWarnUpObjectCache(
            String orgId, String systemId, List<Tuple2<String, Class<?>>> columns) {
        try {
            log.info(
                    "dry run warn up {} object cache for systemId: {}, column num {}, column list: {}",
                    orgId,
                    systemId,
                    columns.size(),
                    columns);

            System.out.println(
                    "dry run warn up "
                            + orgId
                            + " object cache for systemId: "
                            + systemId
                            + ", column num "
                            + columns.size()
                            + ", column list: "
                            + columns);

        } catch (Throwable t) {
            log.error("worm tbl obj for systemId: " + systemId + " failed", t);
            String stack =
                    Arrays.stream(t.getStackTrace())
                            .map(StackTraceElement::toString)
                            .collect(Collectors.joining(","));
            log.warn("stack trace" + stack);
        }
    }

    private static String getType(String rawFieldId) {
        if (rawFieldId.endsWith("VARCHAR")) {
            return "VARCHAR";
        } else if (rawFieldId.endsWith("BIGINT")) {
            return "BIGINT";
        } else if (rawFieldId.endsWith("DOUBLE")) {
            return "DOUBLE";
        } else if (rawFieldId.endsWith("BOOLEAN")) {
            return "BOOLEAN";
        } else {
            throw new RuntimeException("unsupported type for rawFieldId: " + rawFieldId);
        }
    }
}
