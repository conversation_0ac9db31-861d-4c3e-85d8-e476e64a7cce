package com.envision.gravity.common.vo.topo;

import com.envision.gravity.common.pojo.VertexRelationship;

import java.util.List;
import java.util.Map;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
public class SubGraphResp {
    private String subGraphName;
    private String rootNode;
    private List<Map<String, Map<String, Object>>> vertexesMap;
    private List<VertexRelationship> relationships;
}
