package com.envision.gravity.flink.streaming.calculate.dto.query;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 最新值查询实体
 *
 * <p>用于： 1. AspectCalcJobTaskProcessor 的最新值查询 2. CalculationServiceHelper.queryLatestValues() 方法参数
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LatestQueryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 组织ID */
    private String orgId;

    /** 模型ID */
    private String modelId;

    /** 资产ID列表 */
    @Builder.Default private List<String> assetIds = new ArrayList<>();

    /** 属性名称列表 */
    @Builder.Default private List<String> propertyNames = new ArrayList<>();

    /** 查询限制（可选） */
    private Integer limit;

    /** 检查是否为有效的查询实体 */
    public boolean isValid() {
        return orgId != null
                && !orgId.isEmpty()
                && modelId != null
                && !modelId.isEmpty()
                && assetIds != null
                && !assetIds.isEmpty()
                && propertyNames != null
                && !propertyNames.isEmpty();
    }

    /** 添加资产ID */
    public void addAssetId(String assetId) {
        if (assetIds == null) {
            assetIds = new ArrayList<>();
        }
        if (!assetIds.contains(assetId)) {
            assetIds.add(assetId);
        }
    }

    /** 添加属性名称 */
    public void addPropertyName(String propertyName) {
        if (propertyNames == null) {
            propertyNames = new ArrayList<>();
        }
        if (!propertyNames.contains(propertyName)) {
            propertyNames.add(propertyName);
        }
    }

    @Override
    public String toString() {
        return String.format(
                "LatestQueryEntity{orgId='%s', modelId='%s', assets=%d, properties=%d}",
                orgId,
                modelId,
                assetIds != null ? assetIds.size() : 0,
                propertyNames != null ? propertyNames.size() : 0);
    }
}
