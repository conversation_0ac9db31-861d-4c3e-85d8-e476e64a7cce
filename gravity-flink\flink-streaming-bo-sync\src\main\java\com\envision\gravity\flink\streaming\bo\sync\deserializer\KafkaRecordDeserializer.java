package com.envision.gravity.flink.streaming.bo.sync.deserializer;

import java.io.IOException;


import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
public class KafkaRecordDeserializer
        implements KafkaRecordDeserializationSchema<ConsumerRecord<byte[], byte[]>> {

    private static final long serialVersionUID = 2700382659318109026L;

    @Override
    public void deserialize(
            ConsumerRecord<byte[], byte[]> consumerRecord,
            Collector<ConsumerRecord<byte[], byte[]>> collector)
            throws IOException {
        collector.collect(consumerRecord);
    }

    @Override
    public TypeInformation<ConsumerRecord<byte[], byte[]>> getProducedType() {
        return TypeInformation.of(new TypeHint<ConsumerRecord<byte[], byte[]>>() {});
    }
}
