package com.envision.gravity.obj;

import com.envision.gravity.obj.entity.LatestMeasurePointEntity;
import com.envision.gravity.obj.entity.MeasurePointUpdateResult;

import java.util.List;
import java.util.TreeMap;


import org.apache.ignite.services.Service;

public interface UpdateObjServiceInterface extends Service {
    public MeasurePointUpdateResult updateMeasurePoint(
            String database, TreeMap<String, List<LatestMeasurePointEntity>> measurePointsMap);

    // for old version
    public UpdateResult updateTblObjMeasurePoint(
            String database, TreeMap<String, List<MeasurePointEntity>> measurePointsMap);

    // for old version
    public UpdateResult updateLastChange(
            String database, TreeMap<LastChangeKey, LastChangeValue> updateMap);
}
