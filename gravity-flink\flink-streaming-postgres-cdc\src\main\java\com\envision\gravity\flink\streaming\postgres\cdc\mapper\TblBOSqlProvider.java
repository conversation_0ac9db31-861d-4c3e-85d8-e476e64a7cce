package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import com.envision.gravity.common.po.TblObjAttr;
import com.envision.gravity.flink.streaming.postgres.cdc.utils.TimeUtils;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
public class TblBOSqlProvider {

    public String updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.UPDATE(schemaName + ".tbl_bo");
        sql.SET("sys_modified_time = CURRENT_TIMESTAMP");
        sql.WHERE("asset_id in ( " + assetIds + " )");
        return sql.toString();
    }

    public String batchRefreshObjectDetailByAssetIds(String schemaName, List<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.SELECT(
                schemaName + ".batch_refresh_object_detail_by_asset_ids(ARRAY[" + assetIds + "])");

        return sql.toString();
    }

    public String fullRefreshObjectDetailWithAttrValue(
            String schemaName, List<String> assetIdList, List<TblObjAttr> tblObjAttrList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String systemIds =
                tblObjAttrList.stream()
                        .map(tblObjAttr -> "'" + tblObjAttr.getSystemId() + "'")
                        .collect(Collectors.joining(", "));

        String fieldIndexes =
                tblObjAttrList.stream()
                        .map(tblObjAttr -> String.valueOf(tblObjAttr.getFieldIndex()))
                        .collect(Collectors.joining(", "));

        String boolValues =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getValueBool() != null
                                                ? String.valueOf(tblObjAttr.getValueBool())
                                                : null)
                        .collect(Collectors.joining(", "));

        String stringValues =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getValueString() != null
                                                ? "'"
                                                        + tblObjAttr
                                                                .getValueString()
                                                                .replace("'", "''")
                                                        + "'"
                                                : null)
                        .collect(Collectors.joining(", "));

        String longValues =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getValueLong() != null
                                                ? String.valueOf(tblObjAttr.getValueLong())
                                                : null)
                        .collect(Collectors.joining(", "));

        String doubleValues =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getValueDouble() != null
                                                ? String.valueOf(tblObjAttr.getValueDouble())
                                                : null)
                        .collect(Collectors.joining(", "));

        String jsonValues =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getValueJson() != null
                                                ? "'"
                                                        + tblObjAttr
                                                                .getValueJson()
                                                                .replace("'", "''")
                                                        + "'"
                                                : null)
                        .collect(Collectors.joining(", "));

        String modifiedTimeArr =
                tblObjAttrList.stream()
                        .map(
                                tblObjAttr ->
                                        tblObjAttr.getModifiedTime() != null
                                                ? "TIMESTAMP '"
                                                        + TimeUtils.getUTCTimeString(
                                                                tblObjAttr.getModifiedTime())
                                                        + "'"
                                                : null)
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.SELECT(
                schemaName
                        + ".full_refresh_object_detail_with_attr_value("
                        + "ARRAY["
                        + assetIds
                        + "] ::varchar[], "
                        + "ARRAY["
                        + systemIds
                        + "]::varchar[], "
                        + "ARRAY["
                        + fieldIndexes
                        + "]::int[], "
                        + "ARRAY["
                        + boolValues
                        + "]::boolean[], "
                        + "ARRAY["
                        + stringValues
                        + "]::varchar[], "
                        + "ARRAY["
                        + longValues
                        + "]::bigint[], "
                        + "ARRAY["
                        + doubleValues
                        + "]::decimal[], "
                        + "ARRAY["
                        + jsonValues
                        + "]::jsonb[], "
                        + "ARRAY["
                        + modifiedTimeArr
                        + "]::timestamp[], "
                        + "'gravity'::varchar)");

        return sql.toString();
    }
}
