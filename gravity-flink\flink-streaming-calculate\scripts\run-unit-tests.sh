#!/bin/bash

# ReCalc Unit Tests Runner Script
# This script runs all unit tests for the ReCalc components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${YELLOW}Starting ReCalc Unit Tests...${NC}"
echo "Project directory: $PROJECT_DIR"

# Change to project directory
cd "$PROJECT_DIR"

# Function to run a specific test class
run_test_class() {
    local test_class=$1
    local test_name=$2
    
    echo -e "\n${YELLOW}Running $test_name...${NC}"
    
    if mvn test -Dtest="$test_class" -q; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_name failed${NC}"
        return 1
    fi
}

# Function to run all tests in a package
run_package_tests() {
    local package=$1
    local package_name=$2
    
    echo -e "\n${YELLOW}Running $package_name tests...${NC}"
    
    if mvn test -Dtest="$package.**" -q; then
        echo -e "${GREEN}✓ $package_name tests passed${NC}"
        return 0
    else
        echo -e "${RED}✗ $package_name tests failed${NC}"
        return 1
    fi
}

# Initialize test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test classes to run
declare -a TEST_CLASSES=(
    "ReCalcCdcProcessorTest:ReCalc CDC Processor"
    "ReCalcJobGeneratorTest:ReCalc Job Generator"
    "ReCalcJobPgWriterTest:ReCalc Job PG Writer"
    "ReCalcJobTriggerTest:ReCalc Job Trigger"
    "FlinkJobSubmitterTest:Flink Job Submitter"
)

echo -e "\n${YELLOW}Running individual test classes...${NC}"

# Run each test class
for test_entry in "${TEST_CLASSES[@]}"; do
    IFS=':' read -r test_class test_name <<< "$test_entry"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if run_test_class "$test_class" "$test_name"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
done

# Run all ReCalc tests together
echo -e "\n${YELLOW}Running all ReCalc tests together...${NC}"
TOTAL_TESTS=$((TOTAL_TESTS + 1))

if run_package_tests "com.envision.gravity.flink.streaming.calculate.recalc" "All ReCalc"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# Generate test report
echo -e "\n${YELLOW}Generating test report...${NC}"
mvn surefire-report:report -q || echo "Warning: Could not generate surefire report"

# Print summary
echo -e "\n${YELLOW}=== Test Summary ===${NC}"
echo -e "Total tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Check the output above for details.${NC}"
    echo -e "\nTo run a specific test:"
    echo -e "  mvn test -Dtest=<TestClassName>"
    echo -e "\nTo run tests with more verbose output:"
    echo -e "  mvn test -Dtest=<TestClassName> -X"
    echo -e "\nTo run tests and generate coverage report:"
    echo -e "  mvn clean test jacoco:report"
    exit 1
fi
