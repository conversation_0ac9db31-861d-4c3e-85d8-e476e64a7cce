package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/4/26
 * @description
 */
public class TblPrefSqlProvider {
    public String selectModelGroupList(String schemaName, List<String> prefIdList) {
        String prefIds =
                prefIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tbm.model_id = tbmc.model_id");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_component_pref tcp on tbmc.comp_id = tcp.comp_id");
                        WHERE("tcp.pref_id in ( " + prefIds + " )");
                    }
                };

        return sql.toString();
    }

    public String selectModelGroupByPref(String schemaName, List<String> prefIdList) {
        String prefIds =
                prefIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tbm.model_id as model_id, tbm.group_id as group_id");
                        FROM(schemaName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_bo_model_comp tbmc on tbm.model_id = tbmc.model_id");
                        INNER_JOIN(
                                schemaName
                                        + ".tbl_component_pref tcp on tbmc.comp_id = tcp.comp_id");
                        INNER_JOIN(schemaName + ".tbl_pref tp on tcp.pref_id = tp.pref_id");
                        WHERE("tp.pref_id in ( " + prefIds + " )");
                    }
                };

        return sql.toString();
    }
}
