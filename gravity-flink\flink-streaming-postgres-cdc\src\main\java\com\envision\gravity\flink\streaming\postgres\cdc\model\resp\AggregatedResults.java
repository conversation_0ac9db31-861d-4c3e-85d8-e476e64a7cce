package com.envision.gravity.flink.streaming.postgres.cdc.model.resp;

import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregatedResults {
    private RefreshModelReq refreshModelReq;
    private RefreshObjectReq refreshObjectReq;
    private NebulaGraphReq nebulaGraphReq;
}
