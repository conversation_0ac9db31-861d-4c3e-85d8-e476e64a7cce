package com.envision.gravity.flink.streaming.calculate.integration;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Integration test for ReCalc Job Generation Flow Tests the complete flow from CDC event to ReCalc
 * batch job execution
 *
 * <p>Test Flow: 1. CDC Event: Update tbl_property_upstream_rule.modified_time for
 * pref_rule_id='test002' 2. Expected Result: Generate job info in public.tbl_calc_job_info table 3.
 * Expected Result: Submit and execute ReCalcBatchJob.java
 *
 * <AUTHOR>
 */
public class ReCalcJobGenFlowIntegrationTest {

    private static final Logger logger =
            LoggerFactory.getLogger(ReCalcJobGenFlowIntegrationTest.class);

    // Test constants
    private static final String TEST_SCHEMA = "o17186913277371853";
    private static final String TEST_PREF_RULE_ID = "test002";
    private static final String TEST_ORG_ID = TEST_SCHEMA;
    private static final int TEST_TIMEOUT_SECONDS = 60;

    // Database connections
    private Connection pgConnection;
    private SqlSessionFactory sqlSessionFactory;

    // Test runner for ReCalc Job Generation Flow
    private ReCalcJobGenFlowTestRunner flowRunner;

    @BeforeEach
    void setUp(TestInfo testInfo) throws Exception {
        logger.info("=== Starting Integration Test: {} ===", testInfo.getDisplayName());

        // Set arch.path system property for configuration loading
        System.setProperty("arch.path", "./deploy/apps");
        System.setProperty("java.security.auth.login.config", "./deploy/zk_client_jaas.conf");
        System.setProperty("LOG_LEVEL", "DEBUG");

        // Initialize database connections
        initializeDatabaseConnections();

        // Clean up any existing test data
        cleanupTestData();

        // Prepare test data
        prepareTestData();

        // Initialize and start ReCalc Job Generation Flow
        initializeFlowRunner();

        logger.info("Test setup completed successfully");
    }

    @AfterEach
    void tearDown(TestInfo testInfo) throws Exception {
        logger.info("=== Cleaning up Integration Test: {} ===", testInfo.getDisplayName());

        try {
            // Stop ReCalc Job Generation Flow
            if (flowRunner != null) {
                flowRunner.stopFlow();
            }

            // Clean up test data
            cleanupTestData();

            // Close database connections
            if (pgConnection != null && !pgConnection.isClosed()) {
                pgConnection.close();
            }

            logger.info("Test cleanup completed successfully");
        } catch (Exception e) {
            logger.warn("Error during test cleanup: {}", e.getMessage());
        }
    }

    @Test
    void testReCalcJobGenFlow_CdcEventToJobExecution_Success() throws Exception {
        logger.info("Starting ReCalc Job Generation Flow Integration Test");

        // Step 1: Trigger CDC event by updating tbl_property_upstream_rule
        logger.info("Step 1: Triggering CDC event for pref_rule_id={}", TEST_PREF_RULE_ID);
        triggerCdcEvent();

        // Step 2: Wait for job generation and verify job info in database
        logger.info("Step 2: Waiting for job generation...");
        TblCalcJobInfo generatedJob = waitForJobGeneration();
        assertNotNull(generatedJob, "ReCalc job should be generated");

        // Step 3: Verify job details
        logger.info("Step 3: Verifying generated job details");
        verifyGeneratedJob(generatedJob);

        // Step 4: Wait for job execution and verify status updates
        logger.info("Step 4: Waiting for job execution...");
        verifyJobExecution(generatedJob.getJobId());

        logger.info("ReCalc Job Generation Flow Integration Test completed successfully");
    }

    /** Initialize database connections for testing */
    private void initializeDatabaseConnections() throws Exception {
        // Initialize PostgreSQL connection using CalcLionConfig
        String jdbcUrl = CalcLionConfig.getPgJdbcUrl();
        String username = CalcLionConfig.getPgUsername();
        String password = CalcLionConfig.getPgPassword();

        logger.info("Connecting to PostgreSQL: {}", jdbcUrl);

        pgConnection = DriverManager.getConnection(jdbcUrl, username, password);
        pgConnection.setAutoCommit(true);

        // Initialize MyBatis SqlSessionFactory
        sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();

        logger.info("Database connections initialized successfully");
    }

    /** Initialize and start ReCalc Job Generation Flow for testing */
    private void initializeFlowRunner() throws Exception {
        flowRunner = new ReCalcJobGenFlowTestRunner();

        // Validate configuration
        flowRunner.validateConfiguration();

        // Start the flow
        flowRunner.startFlow();

        // Wait for the flow to be ready
        flowRunner.waitForReady(30);

        logger.info("ReCalc Job Generation Flow is ready for testing");
    }

    /** Clean up test data from both test schema and public schema */
    private void cleanupTestData() throws Exception {
        // Clean up job info from public.tbl_calc_job_info
        try (SqlSession sqlSession = sqlSessionFactory.openSession(true)) {
            TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);

            // Find and delete test jobs
            List<TblCalcJobInfo> testJobs = mapper.findByPrefRuleId(TEST_PREF_RULE_ID, 100);
            for (TblCalcJobInfo job : testJobs) {
                mapper.deleteByJobId(job.getJobId());
                logger.debug("Deleted test job: {}", job.getJobId());
            }
        }

        logger.debug("Test data cleanup completed");
    }

    /** Prepare test data in the test schema */
    private void prepareTestData() throws Exception {
        // Ensure test rule exists in tbl_property_upstream_rule
        String checkSql =
                "SELECT COUNT(*) FROM "
                        + TEST_SCHEMA
                        + ".tbl_property_upstream_rule WHERE pref_rule_id = ?";
        try (PreparedStatement ps = pgConnection.prepareStatement(checkSql)) {
            ps.setString(1, TEST_PREF_RULE_ID);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next() && rs.getInt(1) == 0) {
                    // Insert test rule if it doesn't exist
                    insertTestRule();
                }
            }
        }

        logger.debug("Test data preparation completed");
    }

    /** Insert test rule into tbl_property_upstream_rule */
    private void insertTestRule() throws Exception {
        String insertSql =
                "INSERT INTO "
                        + TEST_SCHEMA
                        + ".tbl_property_upstream_rule "
                        + "(pref_rule_id, comp_id, pref_id, calc_type, calc_expr, is_valid_expr, "
                        + "target_category, target_comp_id, target_pref_id, src_category, "
                        + "created_time, modified_time, created_user, modified_user) "
                        + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, ?)";

        try (PreparedStatement ps = pgConnection.prepareStatement(insertSql)) {
            ps.setString(1, TEST_PREF_RULE_ID);
            ps.setString(2, "test_comp_001");
            ps.setString(3, "test_pref_001");
            ps.setInt(4, 1); // NORMAL calc type
            // Use a complex expression that is NOT direct mapping
            ps.setString(5, "sourcePoint1 + sourcePoint2 * 2"); // Non-direct mapping expression
            ps.setBoolean(6, true);
            ps.setString(7, "test_target_category");
            ps.setString(8, "test_target_comp");
            ps.setString(9, "test_target_pref");
            ps.setString(10, "test_src_category");
            ps.setString(11, "test_user");
            ps.setString(12, "test_user");

            int result = ps.executeUpdate();
            if (result > 0) {
                logger.info("Inserted test rule: pref_rule_id={}", TEST_PREF_RULE_ID);
            }
        }
    }

    /** Trigger CDC event by updating the modified_time of test rule */
    private void triggerCdcEvent() throws Exception {
        String updateSql =
                "UPDATE "
                        + TEST_SCHEMA
                        + ".tbl_property_upstream_rule "
                        + "SET modified_time = CURRENT_TIMESTAMP WHERE pref_rule_id = ?";

        try (PreparedStatement ps = pgConnection.prepareStatement(updateSql)) {
            ps.setString(1, TEST_PREF_RULE_ID);

            int result = ps.executeUpdate();
            if (result > 0) {
                logger.info(
                        "CDC event triggered: updated modified_time=CURRENT_TIMESTAMP for pref_rule_id={}",
                        TEST_PREF_RULE_ID);
            } else {
                throw new RuntimeException("Failed to trigger CDC event: no rows updated");
            }
        }
    }

    /** Wait for job generation and return the generated job */
    private TblCalcJobInfo waitForJobGeneration() throws Exception {
        int attempts = 0;
        int maxAttempts = TEST_TIMEOUT_SECONDS;

        while (attempts < maxAttempts) {
            try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);
                List<TblCalcJobInfo> jobs = mapper.findByPrefRuleId(TEST_PREF_RULE_ID, 10);

                if (!jobs.isEmpty()) {
                    TblCalcJobInfo latestJob = jobs.get(jobs.size() - 1); // Get the latest job
                    logger.info(
                            "Job generation detected: jobId={}, status={}",
                            latestJob.getJobId(),
                            latestJob.getStatus());
                    return latestJob;
                }
            }

            Thread.sleep(1000); // Wait 1 second
            attempts++;

            if (attempts % 10 == 0) {
                logger.debug("Still waiting for job generation... ({}s)", attempts);
            }
        }

        throw new RuntimeException(
                "Timeout waiting for job generation after " + maxAttempts + " seconds");
    }

    /** Verify the generated job details */
    private void verifyGeneratedJob(TblCalcJobInfo job) {
        assertNotNull(job.getJobId(), "Job ID should not be null");
        assertEquals(TEST_PREF_RULE_ID, job.getPrefRuleId(), "Pref rule ID should match");
        assertEquals(TEST_ORG_ID, job.getSrcOrgId(), "Source org ID should match");
        assertEquals(
                ReCalcJobStatusEnum.INIT.getCode(),
                job.getStatus(),
                "Initial status should be INIT");
        assertNotNull(job.getRuleInfo(), "Rule info should not be null");
        assertTrue(job.getCalcStartTime() > 0, "Calc start time should be set");
        assertTrue(job.getCalcEndTime() > 0, "Calc end time should be set");
        assertTrue(
                job.getCalcEndTime() > job.getCalcStartTime(),
                "End time should be after start time");

        logger.info("Generated job verification passed: {}", job.getJobId());
    }

    /** Verify job execution by monitoring status changes */
    private void verifyJobExecution(String jobId) throws Exception {
        // Wait for job to start (status = RUNNING)
        waitForJobStatus(jobId, ReCalcJobStatusEnum.RUNNING, 30);

        // Wait for job to complete (status = FINISHED or FAILED)
        TblCalcJobInfo completedJob = waitForJobCompletion(jobId, TEST_TIMEOUT_SECONDS);

        // Verify final status
        assertTrue(
                completedJob.getStatus() == ReCalcJobStatusEnum.FINISHED.getCode()
                        || completedJob.getStatus() == ReCalcJobStatusEnum.FAILED.getCode(),
                "Job should be in FINISHED or FAILED status");

        if (completedJob.getStatus() == ReCalcJobStatusEnum.FINISHED.getCode()) {
            logger.info("Job execution completed successfully: jobId={}", jobId);
        } else {
            logger.warn(
                    "Job execution failed: jobId={}, status={}", jobId, completedJob.getStatus());
        }
    }

    /** Wait for specific job status */
    private void waitForJobStatus(
            String jobId, ReCalcJobStatusEnum expectedStatus, int timeoutSeconds) throws Exception {
        int attempts = 0;

        while (attempts < timeoutSeconds) {
            try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);
                TblCalcJobInfo job = mapper.findByJobId(jobId);

                if (job != null && job.getStatus() == expectedStatus.getCode()) {
                    logger.info("Job status reached: jobId={}, status={}", jobId, expectedStatus);
                    return;
                }
            }

            Thread.sleep(1000);
            attempts++;
        }

        throw new RuntimeException(
                "Timeout waiting for job status "
                        + expectedStatus
                        + " after "
                        + timeoutSeconds
                        + " seconds");
    }

    /** Wait for job completion (FINISHED or FAILED status) */
    private TblCalcJobInfo waitForJobCompletion(String jobId, int timeoutSeconds) throws Exception {
        int attempts = 0;

        while (attempts < timeoutSeconds) {
            try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);
                TblCalcJobInfo job = mapper.findByJobId(jobId);

                if (job != null
                        && (job.getStatus() == ReCalcJobStatusEnum.FINISHED.getCode()
                                || job.getStatus() == ReCalcJobStatusEnum.FAILED.getCode())) {
                    logger.info(
                            "Job completion detected: jobId={}, status={}", jobId, job.getStatus());
                    return job;
                }
            }

            Thread.sleep(1000);
            attempts++;

            if (attempts % 10 == 0) {
                logger.debug("Still waiting for job completion... ({}s)", attempts);
            }
        }

        throw new RuntimeException(
                "Timeout waiting for job completion after " + timeoutSeconds + " seconds");
    }
}
