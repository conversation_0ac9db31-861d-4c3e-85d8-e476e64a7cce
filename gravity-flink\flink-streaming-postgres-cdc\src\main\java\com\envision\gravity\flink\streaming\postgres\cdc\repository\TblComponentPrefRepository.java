package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblComponentPrefMapper;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
@Slf4j
public class TblComponentPrefRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblComponentPrefRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<ModelGroup> selectAttrModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (compIdList.isEmpty() || prefIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblComponentPrefMapper tblComponentPrefMapper =
                    session.getMapper(TblComponentPrefMapper.class);
            return tblComponentPrefMapper.selectAttrModelGroupList(
                    schemaName, compIdList, prefIdList);
        } catch (Exception e) {
            log.error("Select attr model group list error.", e);
            throw new GravityRuntimeException("Select attr model group list error.", e);
        }
    }

    public List<ModelGroup> selectModelGroupList(
            String schemaName, List<String> compIdList, List<String> prefIdList) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (compIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblComponentPrefMapper tblComponentPrefMapper =
                    session.getMapper(TblComponentPrefMapper.class);
            return tblComponentPrefMapper.selectModelGroupList(schemaName, compIdList, prefIdList);
        } catch (Exception e) {
            log.error("Select model group list error.", e);
            throw new GravityRuntimeException("Select model group list error.", e);
        }
    }

    public List<ModelGroup> selectModelGroupListByCompId(
            String schemaName, List<String> compIdList) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (compIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblComponentPrefMapper tblComponentPrefMapper =
                    session.getMapper(TblComponentPrefMapper.class);
            return tblComponentPrefMapper.selectModelGroupListByCompId(schemaName, compIdList);
        } catch (Exception e) {
            log.error("Select model group list by comp ids error.", e);
            throw new GravityRuntimeException("Select model group list by comp ids error.", e);
        }
    }
}
