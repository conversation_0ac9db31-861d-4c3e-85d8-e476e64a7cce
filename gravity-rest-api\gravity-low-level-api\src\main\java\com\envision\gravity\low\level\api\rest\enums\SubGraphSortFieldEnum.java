package com.envision.gravity.low.level.api.rest.enums;

/** @Author: qi.jiang2 @Date: 2024/04/17 17:21 @Description: */
public enum SubGraphSortFieldEnum {

    // CREATED_TIME
    CREATED_TIME("createdTime", "created_time"),

    // MODIFIED_TIME
    MODIFIED_TIME("modifiedTime", "modified_time"),

    // SUB_GRAPH_ID
    SUB_GRAPH_ID("subGraphId", "sub_graph_id");

    private final String supportField;

    private final String sqlField;

    SubGraphSortFieldEnum(String supportField, String sqlField) {
        this.supportField = supportField;
        this.sqlField = sqlField;
    }

    public String getSupportField() {
        return supportField;
    }

    public String getSqlField() {
        return sqlField;
    }
}
