package com.envision.gravity.flink.steaming.bo.event.config;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description
 */
@Slf4j
public class LionConfig {

    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.pg.cdc.database", "gravity");
    }

    public static String getPgSchemaList() {
        return getStringValue("gravity-flink.bo-event.pg.cdc.schema.list", "^(o|sysenos2018).*");
    }

    public static String getPgTableList() {
        return getStringValue(
                "gravity-flink.bo-event.pg.cdc.table.list",
                "^(o|sysenos2018).*\\.tbl_edge$,^(o|sysenos2018).*\\.tbl_sub_graph$,^(o|sysenos2018).*\\.tbl_start_vid$,^(o|sysenos2018).*\\.object_detail_origin$");
    }

    public static String getSlotName() {
        return getStringValue("gravity-flink.bo-event.pg.cdc.slot.name", "bo_event_pg_cdc");
    }

    public static String getMaxBatchSize() {
        return getStringValue("gravity-flink.bo-event.source.max.batch.size", "1000");
    }

    public static String getMaxQueueSize() {
        return getStringValue("gravity-flink.bo-event.source.max.queue.size", "2000");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static int getPgMaxPoolSize() {
        return getIntValue("gravity-flink.bo-event.pg.datasource.max.pool.size", 8);
    }

    public static String getPgJdbcUrl() {
        return getStringValue("gravity-common.postgresql.jdbc-url", null);
    }

    public static String getKafkaServers() {
        return getStringValue("Camel.kafka-common", null);
    }

    public static String getEventKafkaTopic() {
        return getStringValue("gravity-flink.bo-event.kafka.event.topic", "GRAVITY_BO_EVENT_TOPIC");
    }
}
