SELECT
/*+ ENGINE('bo') */
/*+ ORG('$orgId') */
/*+ TIME_RANGE(${StartTime}, ${EndTime}) */
#foreach($property in $allProperties)
/*+ SET(`$property.prefName`=TS(`$property.prefName`)) */
#end
asset_id#if($allProperties.size() > 0),#end
#foreach($property in $allProperties)
`${property.prefName}.time` AS `${property.prefName}_time`,
`${property.prefName}.value` AS `${property.prefName}_value`#if($foreach.hasNext),#end
#end
FROM `$modelId`
WHERE asset_id IN (#foreach($assetId in $assetIds)'$assetId'#if($foreach.hasNext), #end#end)
