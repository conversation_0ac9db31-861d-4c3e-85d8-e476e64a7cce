# CalculateProcessor Operator Complete Design Document

## 1. Overview

This document describes the complete technical design of the `CalculateProcessor` operator, including both Direct Mapping and Non-Direct Mapping calculation modes implementation.

## 2. Background

### 2.1 Business Scenario

`CalculateProcessor` is a core operator in Flink streaming computation, responsible for real-time calculation and transformation of device measurement point data. It supports two main calculation modes:

- **Direct Mapping**: Simple one-to-one mapping where target properties directly copy source measurement point values
- **Non-Direct Mapping**: Complex expression-based calculations where target properties are computed using mathematical expressions

### 2.2 Technical Architecture

```
Input Stream -> CalculateProcessor -> Output Stream
    ↓              ↓                    ↓
LegacyMsgList -> Calculation Logic -> CalcResultMsg
```

## 3. Overall Architecture Design

### 3.1 Core Component Architecture

```mermaid
graph TB
    A[CalculateProcessor] --> B[Message Preprocessing Module]
    A --> C[Asset Mapping Query Module]
    A --> D[Calculation Rule Processing Module]
    
    D --> E[Direct Mapping Processor]
    D --> F[Non-Direct Mapping Processor]
    
    F --> G[Data Query Module]
    F --> H[Expression Calculation Module]
    
    G --> I[SQL Gateway Query]
    H --> J[ExpressionUtil Calculation Engine]
    
    E --> K[Result Construction Module]
    F --> K
    
    K --> L[Output Message Generation]
```

### 3.2 Data Flow Processing Pipeline

```mermaid
flowchart TD
    A[Receive LegacyMsgList] --> B[Message Type Conversion]
    B --> C[Extract Source Asset Info]
    C --> D[Query Target Asset Mapping]
    D --> E[Iterate Source Measurement Points]
    
    E --> F[Find Related Target Properties]
    F --> G{Determine Mapping Type}
    
    G -->|Direct Mapping| H[Direct Value Copy]
    G -->|Non-Direct Mapping| I[Data Source Analysis]
    
    I --> J[Build Query Request]
    J --> K[SQL Gateway Query]
    K --> L[Expression Data Preparation]
    L --> M[Expression Calculation]
    
    H --> N[Build Output Message]
    M --> N
    N --> O[Return Calculation Result]
```

## 4. Direct Mapping Design

### 4.1 Implementation Principle

Direct mapping is the simplest calculation mode, implementing one-to-one data copying:

```java
// Core logic: Direct value copying
targetPayload.getPoints().put(targetProp.getPrefName(), srcPointEntry.getValue());
```

### 4.2 Processing Flow

1. **Rule Matching**: Find related target properties based on source measurement point name and model ID
2. **Asset Filtering**: Filter applicable calculation rules based on target asset information
3. **Direct Mapping**: Copy source measurement point values directly to target properties
4. **Message Construction**: Build output messages containing calculation results

### 4.3 Data Structure

```java
// Input data structure
LegacyPayload srcPayload = {
    assetId: "asset001",
    time: 1640995200000,
    points: {
        "temperature": 25.5,
        "pressure": 101.3
    }
}

// Output data structure  
LegacyPayload targetPayload = {
    assetId: "target_asset001",
    time: 1640995200000,
    points: {
        "temp_calc": 25.5  // Direct copy of temperature value
    }
}
```

### 4.4 Performance Characteristics

- **High Performance**: No additional data queries or complex calculations required
- **Low Latency**: Direct memory operations with minimal latency
- **High Reliability**: Simple logic with low failure rate

## 5. Non-Direct Mapping Design

### 5.1 Implementation Principle

Non-direct mapping supports complex expression calculations that can reference multiple data sources:

```java
// Expression examples
"model1.temperature * 1.8 + 32"  // Temperature unit conversion
"model1.voltage * model2.current"  // Power calculation
"this.speed * 3.6"  // Current model speed conversion
```

### 5.2 Data Source Classification

| Data Source Type | Condition | Retrieval Method | Example |
|------------------|-----------|------------------|---------|
| Current Model MeasurePoint | `modelId == current && prefType == MEASUREPOINT` | Direct from LegacyPayload | Temperature value in current message |
| Current Model Attribute | `modelId == current && prefType == ATTRIBUTE` | SQL Gateway query | Device rated power attribute |
| Other Model Data | `modelId != current` | SQL Gateway query | Related device status information |

### 5.3 SQL Gateway Query Design

#### 5.3.1 Query Interface Design

```java
/**
 * Query latest values for assets from SQL Gateway
 * @param orgId Organization ID
 * @param modelId Model ID
 * @param assetPropertyInfoMap Asset property info mapping Map<assetId, List<PropertyInfo>>
 * @return Query result Map<assetId, LegacyPayload>
 */
public Map<String, LegacyPayload> queryLatestValues(
    String orgId, 
    String modelId, 
    Map<String, List<PropertyInfo>> assetPropertyInfoMap
);
```

#### 5.3.2 Dynamic SQL Template Design

Using Velocity template engine for dynamic SQL construction, directly iterating through `assetPropertyInfoMap` values:

**Velocity Template File (query-latest-values.vm):**
```sql
SELECT /*+ ORG('$orgId') */
#foreach($property in $allProperties)
    /*+ SET(`$property.prefName`=TS(`$property.prefName`)) */
#end
    asset_id#if($allProperties.size() > 0),#end
#foreach($property in $allProperties)
    #if($property.prefType.name() == "ATTRIBUTE")
    `$property.prefName` AS `$property.prefName`#if($foreach.hasNext),#end
    #elseif($property.prefType.name() == "MEASUREPOINT")
    `${property.prefName}.time` AS `${property.prefName}_time`,
    `${property.prefName}.value` AS `${property.prefName}_value`#if($foreach.hasNext),#end
    #end
#end
FROM `$modelId`
WHERE asset_id IN (#foreach($assetId in $assetIds)'$assetId'#if($foreach.hasNext), #end#end)
```

**Java Template Processing Logic:**
```java
/**
 * Build dynamic SQL query statement
 * @param orgId Organization ID
 * @param modelId Model ID
 * @param assetPropertyInfoMap Asset property info mapping
 * @return Dynamically generated SQL statement
 */
private String buildDynamicSql(String orgId, String modelId,
                              Map<String, List<PropertyInfo>> assetPropertyInfoMap) {

    if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
        logger.warn("assetPropertyInfoMap is null or empty, skipping query");
        return null;
    }

    // Collect all unique PropertyInfo by iterating through assetPropertyInfoMap values
    Set<PropertyInfo> allProperties = new HashSet<>();
    for (List<PropertyInfo> propertyList : assetPropertyInfoMap.values()) {
        if (propertyList != null) {
            allProperties.addAll(propertyList);
        }
    }

    if (allProperties.isEmpty()) {
        logger.warn("No properties found in assetPropertyInfoMap, skipping query");
        return null;
    }

    // Prepare Velocity context
    VelocityContext context = new VelocityContext();
    context.put("orgId", orgId);
    context.put("modelId", modelId);
    context.put("allProperties", allProperties);
    context.put("assetIds", assetPropertyInfoMap.keySet());

    // Render template
    StringWriter writer = new StringWriter();
    Template template = velocityEngine.getTemplate("query-latest-values.vm");
    template.merge(context, writer);

    return writer.toString();
}
```

**Key Improvements:**
1. **Direct Iteration Processing**: Iterate through each `PropertyInfo` in `assetPropertyInfoMap.values()`
2. **PrefType-based Judgment**: Determine measurement points vs attributes based on `PropertyInfo.prefType`
3. **Correct SQL Construction**:
   - Attributes: `attrName` AS `attrName`
   - Measurement Points: `pointName.time` AS `pointName_time`, `pointName.value` AS `pointName_value`
4. **Comprehensive Null Checking**: Complete null value validation for `assetPropertyInfoMap`

#### 5.3.3 Complete Query Method Implementation

```java
/**
 * Query latest values for assets from SQL Gateway
 * @param orgId Organization ID
 * @param modelId Model ID
 * @param assetPropertyInfoMap Asset property info mapping
 * @return Query result
 */
public Map<String, LegacyPayload> queryLatestValues(
        String orgId,
        String modelId,
        Map<String, List<PropertyInfo>> assetPropertyInfoMap) {

    if (assetPropertyInfoMap == null || assetPropertyInfoMap.isEmpty()) {
        logger.debug("assetPropertyInfoMap is null or empty, returning empty result");
        return new HashMap<>();
    }

    // Build dynamic SQL
    String sql = buildDynamicSql(orgId, modelId, assetPropertyInfoMap);
    if (sql == null) {
        logger.warn("Failed to build SQL, returning empty result");
        return new HashMap<>();
    }

    logger.debug("Executing SQL Gateway query: {}", sql);

    Map<String, LegacyPayload> result = new HashMap<>();

    try (Connection connection = sqlGatewayDataSource.getConnection();
         PreparedStatement statement = connection.prepareStatement(sql);
         ResultSet resultSet = statement.executeQuery()) {

        result = parseResultSetToLegacyPayload(resultSet, assetPropertyInfoMap);

    } catch (SQLException e) {
        logger.error("SQL Gateway query failed: {}", e.getMessage(), e);
        // Return empty result instead of throwing exception to allow partial processing
        return new HashMap<>();
    }

    logger.debug("SQL Gateway query completed, returned {} assets", result.size());
    return result;
}

/**
 * Parse ResultSet to LegacyPayload map
 * @param resultSet SQL query result set
 * @param assetPropertyInfoMap Original property info map for reference
 * @return Map<assetId, LegacyPayload>
 */
private Map<String, LegacyPayload> parseResultSetToLegacyPayload(
        ResultSet resultSet,
        Map<String, List<PropertyInfo>> assetPropertyInfoMap) throws SQLException {

    Map<String, LegacyPayload> result = new HashMap<>();

    while (resultSet.next()) {
        String assetId = resultSet.getString("asset_id");

        LegacyPayload payload = result.computeIfAbsent(assetId, k -> {
            LegacyPayload newPayload = new LegacyPayload();
            newPayload.setAssetId(assetId);
            newPayload.setPoints(new HashMap<>());
            newPayload.setTime(System.currentTimeMillis()); // Default timestamp
            return newPayload;
        });

        // Get property list for this asset
        List<PropertyInfo> propertyList = assetPropertyInfoMap.get(assetId);
        if (propertyList == null) {
            continue;
        }

        long maxTimestamp = payload.getTime();

        // Process each property
        for (PropertyInfo property : propertyList) {
            String prefName = property.getPrefName();
            PrefType prefType = property.getPrefType();

            try {
                if (prefType == PrefType.ATTRIBUTE) {
                    // For attributes: get value directly
                    Object value = resultSet.getObject(prefName);
                    if (value != null) {
                        payload.getPoints().put(prefName, value);
                    }

                } else if (prefType == PrefType.MEASUREPOINT) {
                    // For measurement points: get time and value
                    String timeColumn = prefName + "_time";
                    String valueColumn = prefName + "_value";

                    Object value = resultSet.getObject(valueColumn);
                    Object timeObj = resultSet.getObject(timeColumn);

                    if (value != null) {
                        payload.getPoints().put(prefName, value);

                        // Update timestamp if available
                        if (timeObj != null) {
                            long timestamp = convertToTimestamp(timeObj);
                            maxTimestamp = Math.max(maxTimestamp, timestamp);
                        }
                    }
                }
            } catch (SQLException e) {
                logger.warn("Failed to parse property {}: {}", prefName, e.getMessage());
                // Continue processing other properties
            }
        }

        // Update payload timestamp to maximum timestamp found
        payload.setTime(maxTimestamp);
    }

    return result;
}

/**
 * Convert various time objects to timestamp
 */
private long convertToTimestamp(Object timeObj) {
    if (timeObj instanceof Long) {
        return (Long) timeObj;
    } else if (timeObj instanceof Timestamp) {
        return ((Timestamp) timeObj).getTime();
    } else if (timeObj instanceof java.util.Date) {
        return ((java.util.Date) timeObj).getTime();
    } else {
        // Try to parse as string
        try {
            return Long.parseLong(timeObj.toString());
        } catch (NumberFormatException e) {
            logger.warn("Unable to parse timestamp: {}", timeObj);
            return System.currentTimeMillis();
        }
    }
}
```

#### 5.3.4 SQL Generation Example

**Input Data Example:**
```java
assetPropertyInfoMap = {
    "asset001": [
        PropertyInfo{prefName="temperature", prefType=MEASUREPOINT},
        PropertyInfo{prefName="rated_power", prefType=ATTRIBUTE}
    ],
    "asset002": [
        PropertyInfo{prefName="temperature", prefType=MEASUREPOINT},
        PropertyInfo{prefName="pressure", prefType=MEASUREPOINT}
    ]
}
```

**Generated SQL:**
```sql
SELECT /*+ ORG('o123456') */
    /*+ SET(`temperature`=TS(`temperature`)) */
    /*+ SET(`rated_power`=TS(`rated_power`)) */
    /*+ SET(`pressure`=TS(`pressure`)) */
    asset_id,
    `rated_power` AS `rated_power`,
    `temperature.time` AS `temperature_time`,
    `temperature.value` AS `temperature_value`,
    `pressure.time` AS `pressure_time`,
    `pressure.value` AS `pressure_value`
FROM `modelId`
WHERE asset_id IN ('asset001', 'asset002')
```

**Key Improvement Notes:**
1. **Simplified SET Hint Logic**: All attributes and measurement points use the same `/*+ SET(\`prefName\`=TS(\`prefName\`)) */` syntax
2. **Preserved SELECT Field Distinction**: In the SELECT section, still distinguish between attributes and measurement points based on `prefType`

**Query Result Parsing:**
```java
// Example return result
Map<String, LegacyPayload> result = {
    "asset001": LegacyPayload{
        assetId: "asset001",
        time: 1640995200000,  // Use maximum timestamp
        points: {
            "temperature": 25.5,
            "rated_power": 1000.0
        }
    },
    "asset002": LegacyPayload{
        assetId: "asset002",
        time: 1640995200000,
        points: {
            "temperature": 23.8,
            "pressure": 101.3
        }
    }
}
```

### 5.4 Expression Calculation Flow

#### 5.4.1 Data Preparation Strategy

```java
Map<String, Object> calcData = new HashMap<>();

// Strategy 1: Current model measurement point data
// Key: LegacyMsg.modelId + "." + prefName
// Value: srcPayload.points.get(prefName)

// Strategy 2: Queried data
// Key: SrcPrefItem.modelId + "." + prefName  
// Value: queryResult.points.get(prefName)

// Strategy 3: Current model expressions (isExprUseCurrentModel = true)
// Key: "this." + prefName
// Value: Corresponding measurement point value
```

#### 5.4.2 Expression Calculation Invocation

```java
CalcExpressionRequest calcRequest = new CalcExpressionRequest();
List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);

calcInputs.add(
    ExpressionCalculatorInput.builder()
        .expression(targetProp.getExpression())
        .data(calcData)
        .build());
calcRequest.setCalcInputs(calcInputs);

List<CalcExpressionResponse> calcResult = ExpressionUtil.calcExpression(calcRequest);
ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();
```

## 6. Resource Management Design

### 6.1 Connection Pool Management

```java
// SQL Gateway connection pool configuration
private transient HikariDataSource sqlGatewayDataSource;

@Override
public void open(Configuration parameters) throws Exception {
    super.open(parameters);
    initSqlGatewayDataSource();
}

@Override
public void close() throws Exception {
    if (sqlGatewayDataSource != null) {
        sqlGatewayDataSource.close();
    }
    super.close();
}
```

### 6.2 Cache Management

```java
// Calculation rule cache
private CalcPrefCache calcPrefCache;

@Override
public void open(Configuration parameters) throws Exception {
    this.calcPrefCache = CacheFactory.getCalcPrefCache();
}
```

## 7. Error Handling Strategy

### 7.1 Error Classification and Handling

| Error Type | Handling Strategy | Log Level | Impact Scope |
|------------|------------------|-----------|--------------|
| SQL Gateway Connection Failure | Skip calculation, log error | ERROR | Single calculation task |
| SQL Execution Failure | Skip calculation, log error | ERROR | Single query |
| Expression Calculation Failure | Throw exception | ERROR | Entire message processing |
| Data Type Conversion Failure | Use default value, log warning | WARN | Single data point |

### 7.2 Fault Tolerance Mechanism

```java
try {
    // Expression calculation
    calcResult = ExpressionUtil.calcExpression(calcRequest);
} catch (Exception e) {
    logger.error(
        "Calculate failed, orgId: {}, param: {}, reason {}",
        orgId, calcRequest, e.getMessage());
    throw new CalcFailedException(e);
}
```

## 8. Performance Optimization Strategy

### 8.1 Batch Processing Optimization

- **Data Grouping**: Group by modelId to reduce query count
- **Batch Queries**: Support multiple assetIds in single query
- **Connection Reuse**: Use connection pooling to avoid frequent connection establishment

### 8.2 Memory Optimization

- **Timely Release**: Release large object references promptly after processing
- **Streaming Processing**: Avoid accumulating large amounts of data in memory
- **Reasonable Configuration**: Set appropriate connection pool sizes

### 8.3 Caching Strategy

- **Rule Caching**: `calcPrefCache` is maintained and updated in real-time by CDC module, no additional configuration needed
- **Template Caching**: Cache compiled Velocity SQL templates to improve template rendering performance
- **Result Caching**: Consider short-term caching for frequently queried attribute values (optional optimization)

## 9. Monitoring and Operations

### 9.1 Key Metrics

- **Processing Latency**: End-to-end message processing latency
- **Throughput**: Number of messages processed per second
- **Error Rate**: Percentage of calculation failures
- **Connection Pool Status**: Active connections, waiting connections

### 9.2 Alerting Mechanism

- **High Latency Alert**: Processing latency exceeds threshold
- **High Error Rate Alert**: Error rate exceeds threshold
- **Connection Pool Alert**: Connection pool resource shortage
- **SQL Gateway Unavailable Alert**: Query service exception

## 10. Configuration Management

### 10.1 Required Configuration

```properties
# SQL Gateway connection configuration
gravity-common.sql-gateway.jdbc-url=*************************
gravity-common.sql-gateway.calculate.username=username
gravity-common.sql-gateway.calculate.password=password

# Connection pool configuration
gravity-flink.calc-stream.sql-gateway.pool.max-size=10
gravity-flink.calc-stream.sql-gateway.pool.timeout=30000
```

### 10.2 Optional Configuration

```properties
# Performance tuning configuration
gravity-flink.calc-stream.batch.size=1000
gravity-flink.calc-stream.query.timeout=5000
# Note: calcPrefCache is maintained by CDC module, no TTL configuration needed
```

## 11. Implementation Plan

### 11.1 Development Phases

#### Phase 1: Infrastructure Completion (Completed)
- ✅ Direct mapping functionality implementation
- ✅ Basic operator framework setup
- ✅ Cache integration and resource management

#### Phase 2: Non-Direct Mapping Core Features (In Progress)
- 🚧 SQL Gateway connection pool management
- 🚧 Data query module implementation
- 🚧 Expression calculation integration

#### Phase 3: Feature Enhancement and Optimization
- ⏳ Error handling mechanism improvement
- ⏳ Performance optimization and tuning
- ⏳ Monitoring metrics integration

#### Phase 4: Testing and Deployment
- ⏳ Unit testing and integration testing
- ⏳ Performance benchmark testing
- ⏳ Production environment deployment

### 11.2 Testing Strategy

#### 11.2.1 Unit Testing

```java
// Direct mapping tests
@Test
public void testDirectMapping() {
    // Test simple one-to-one mapping
}

// Non-direct mapping tests
@Test
public void testNonDirectMapping() {
    // Test complex expression calculations
}

// SQL Gateway query tests
@Test
public void testQueryLatestValues() {
    // Test data query functionality
}
```

#### 11.2.2 Integration Testing

```java
// End-to-end tests
@Test
public void testEndToEndProcessing() {
    // Test complete message processing flow
}

// Performance tests
@Test
public void testPerformanceBenchmark() {
    // Test processing performance and resource usage
}
```

#### 11.2.3 Test Data Preparation

- **Mock Data Sources**: Prepare various types of test messages
- **Rule Configuration**: Configure calculation rules with different complexity levels
- **Environment Setup**: Set up SQL Gateway for test environment

## 12. Technical Dependencies

### 12.1 Core Dependencies

```xml
<!-- Flink streaming framework -->
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-streaming-java_2.12</artifactId>
</dependency>

<!-- Expression calculation engine -->
<dependency>
    <groupId>com.univers.business.object</groupId>
    <artifactId>calc-util</artifactId>
</dependency>

<!-- Velocity template engine -->
<dependency>
    <groupId>org.apache.velocity</groupId>
    <artifactId>velocity-engine-core</artifactId>
</dependency>

<!-- HikariCP connection pool -->
<dependency>
    <groupId>com.zaxxer</groupId>
    <artifactId>HikariCP</artifactId>
</dependency>

<!-- MySQL JDBC driver -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>
```

### 12.2 Internal Dependencies

- `gravity-common`: Common utilities and configuration management
- `gravity-cache`: Calculation rule cache
- `flink-common`: Flink common components

## 13. Deployment and Operations

### 13.1 Deployment Requirements

#### 13.1.1 Environment Requirements

- **Flink Version**: 1.14+
- **Java Version**: JDK 8+
- **Memory Requirements**: Minimum 2GB, recommended 4GB+
- **Network Requirements**: Access to SQL Gateway service

#### 13.1.2 Configuration Checklist

- [ ] SQL Gateway connection configuration is correct
- [ ] Calculation rule cache is available
- [ ] Log level configuration is reasonable
- [ ] Monitoring metrics configuration is complete

### 13.2 Operations Monitoring

#### 13.2.1 Key Monitoring Metrics

```java
// Processing performance metrics
- Message processing latency (ms)
- Message processing throughput (msg/s)
- Calculation success rate (%)

// Resource usage metrics
- CPU utilization (%)
- Memory utilization (%)
- Connection pool active connections

// Error monitoring metrics
- SQL Gateway query failure rate (%)
- Expression calculation failure rate (%)
- System exception count
```

#### 13.2.2 Alert Rules

```yaml
# Performance alerts
- Message processing latency > 5000ms
- Message processing throughput < 100 msg/s
- Calculation success rate < 95%

# Resource alerts
- CPU utilization > 80%
- Memory utilization > 85%
- Connection pool utilization > 90%

# Error alerts
- SQL Gateway query failure rate > 5%
- Expression calculation failure rate > 1%
- System exception count > 10/min
```

## 14. Common Issues and Solutions

### 14.1 Performance Issues

#### Q1: High Processing Latency
**Cause**: Slow SQL Gateway queries, complex expression calculations
**Solutions**:
- Optimize SQL query statements
- Increase connection pool size
- Simplify complex expressions

#### Q2: High Memory Usage
**Cause**: Large data caching, improper connection pool configuration
**Solutions**:
- Release large objects promptly
- Adjust connection pool configuration
- Increase JVM heap memory

### 14.2 Functional Issues

#### Q1: Incorrect Calculation Results
**Cause**: Expression errors, data type mismatches
**Solutions**:
- Validate expression syntax
- Check data type conversions
- Add data validation logic

#### Q2: SQL Gateway Connection Failures
**Cause**: Network issues, configuration errors, service unavailability
**Solutions**:
- Check network connectivity
- Verify connection configuration
- Confirm service status

## 15. Best Practices

### 15.1 Development Best Practices

1. **Expression Design**: Keep expressions concise, avoid overly complex calculations
2. **Error Handling**: Add exception handling for all external calls
3. **Logging**: Record key processing steps and error information
4. **Resource Management**: Ensure proper opening and closing of resources

### 15.2 Operations Best Practices

1. **Monitoring Coverage**: Establish comprehensive monitoring system
2. **Capacity Planning**: Configure resources reasonably based on business volume
3. **Disaster Recovery**: Conduct regular disaster recovery drills
4. **Version Management**: Establish standardized version release process

## 16. Key Implementation Methods

### 16.1 Core Methods to Implement

1. **`queryLatestValues()`** - SQL Gateway query interface
2. **`buildSqlTemplate()`** - SQL template construction
3. **`groupSrcPrefItems()`** - SrcPrefItem grouping
4. **`buildCalcData()`** - Calculation data construction
5. **`parseResultSetToLegacyPayload()`** - ResultSet parsing
6. **`initSqlGatewayDataSource()`** - Connection pool initialization
7. **`processNonDirectMapping()`** - Main non-direct mapping process

### 16.2 Method Signatures

```java
// SQL Gateway query method
private Map<String, LegacyPayload> queryLatestValues(
    String orgId,
    String modelId,
    Map<String, List<PropertyInfo>> assetPropertyInfoMap
) throws Exception;

// Data grouping method
private Map<String, Map<String, List<PropertyInfo>>> groupSrcPrefItems(
    List<SrcPrefItem> srcPrefItems,
    String currentModelId,
    Set<String> targetAssetIds
);

// Calculation data construction method
private Map<String, Object> buildCalcData(
    LegacyPayload srcPayload,
    Map<String, LegacyPayload> queryResults,
    CalcPropertyMeta targetProp,
    String currentModelId
);
```

## 17. Data Consistency and Reliability

### 17.1 Data Consistency Strategy

- **Timestamp Handling**: Use maximum timestamp when multiple measurement points have different timestamps
- **Data Freshness**: Define acceptable data staleness thresholds
- **Transaction Boundaries**: Ensure atomicity of calculation operations

### 17.2 Reliability Mechanisms

- **Retry Logic**: Implement retry for transient failures
- **Circuit Breaker**: Prevent cascade failures when SQL Gateway is unavailable
- **Graceful Degradation**: Continue processing with available data when partial queries fail

## 18. Security Considerations

### 18.1 SQL Injection Prevention

- **Parameterized Queries**: Use prepared statements for dynamic SQL
- **Input Validation**: Validate all user inputs and configuration values
- **Template Security**: Ensure Velocity templates are secure against injection

### 18.2 Access Control

- **Authentication**: Secure SQL Gateway access with proper credentials
- **Authorization**: Implement role-based access control for different operations
- **Audit Logging**: Log all security-relevant operations

---

**Document Version**: 1.0
**Last Updated**: 2024-12-19
**Author**: System Architecture Team
