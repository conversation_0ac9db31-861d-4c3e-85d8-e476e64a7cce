package com.envision.gravity.flink.streaming.bo.view.operator.side;

import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;


import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
public class SideOutputs {
    public static final OutputTag<ModelDetailOriginCdcRecord> CREATE_OR_REPLACE_VIEW_TAG =
            new OutputTag<ModelDetailOriginCdcRecord>("CreateOrReplaceView") {
                private static final long serialVersionUID = 3794307174742639598L;
            };
    public static final OutputTag<ModelDetailOriginCdcRecord> DROP_VIEW_TAG =
            new OutputTag<ModelDetailOriginCdcRecord>("DropView") {
                private static final long serialVersionUID = 4491967475671815531L;
            };
}
