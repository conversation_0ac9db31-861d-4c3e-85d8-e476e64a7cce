package com.envision.gravity.common.service.id;

import com.envision.gravity.common.enums.IDType;

/**
 * <AUTHOR>
 * @date 2024/2/19
 * @description
 */
public interface IDGen {
    /**
     * @param idType {@link IDType}
     * @return generated id
     */
    String getGeneratedId(IDType idType);

    /**
     * @param idType {@link IDType}
     * @return Id Prefix
     */
    String getIdPrefix(IDType idType);

    /**
     * @param idType {@link IDType}
     * @return Id Suffix
     */
    String getIdSuffix(IDType idType);
}
