package com.envision.gravity.obj.entity;


import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
@AllArgsConstructor
public class LatestMeasurePointEntity implements Comparable<LatestMeasurePointEntity> {
    private String rowFieldId;
    private Integer fieldIndex;
    private Object value;
    private Long time;
    private Long quality;
    private Boolean horizontal;
    private Boolean alreadyChanged;
    private Long prePointTime;

    public LatestMeasurePointEntity(
            String rowFieldId, int fieldIndex, Object value, Long time, boolean horizontal) {
        if (rowFieldId == null || value == null || time == null) {
            throw new IllegalArgumentException("Arguments could not be null");
        }
        this.rowFieldId = rowFieldId;
        this.fieldIndex = fieldIndex;
        this.value = value;
        this.time = time;
        this.horizontal = horizontal;
    }

    public LatestMeasurePointEntity(
            String rowFieldId,
            int fieldIndex,
            Object value,
            Long time,
            Long quality,
            boolean horizontal) {
        if (rowFieldId == null || value == null || time == null || quality == null) {
            throw new IllegalArgumentException("Arguments could not be null");
        }
        this.rowFieldId = rowFieldId;
        this.fieldIndex = fieldIndex;
        this.value = value;
        this.time = time;
        this.quality = quality;
        this.horizontal = horizontal;
    }

    @Override
    public int compareTo(@NotNull LatestMeasurePointEntity o) {
        if (rowFieldId != null) {
            return rowFieldId.compareTo(o.getRowFieldId());
        }
        return 0;
    }
}
