package com.envision.gravity.common.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;


import org.apache.commons.lang3.tuple.Pair;

/** @Author: qi.jiang2 @Date: 2024/03/27 14:09 @Description: */
public enum IgniteDataTypeEnum {

    // BOOLEAN
    BOOLEAN("boolean", "boolean", false),

    // DATE
    DATE("date", "varchar", false),

    // DATETIME
    DATETIME("datetime", "bigint", false),

    // TIMESTAMP
    TIMESTAMP("timestamp", "bigint", false),

    // DOUBLE
    DOUBLE("double", "double", false),

    // DURATION
    DURATION("duration", "double", false),

    // FLOAT
    FLOAT("float", "double", false),

    // INTEGER
    INTEGER("integer", "bigint", false),

    // LONG
    LONG("long", "bigint", false),

    // STRING
    STRING("string", "varchar", false),

    // TIME
    TIME("time", "varchar", false),

    // OBJECT
    OBJECT("object", "varchar", true),

    // MAP
    MAP("map", "varchar", true),

    // ENUM
    ENUM("enum", "varchar", false),

    // ARRAY
    ARRAY("array", "varchar", true);

    private final String supportDataType;

    private final String igniteDataType;

    private final boolean isJsonb;

    IgniteDataTypeEnum(String supportDataType, String igniteDataType, boolean isJsonb) {
        this.supportDataType = supportDataType;
        this.igniteDataType = igniteDataType;
        this.isJsonb = isJsonb;
    }

    public String getSupportDataType() {
        return supportDataType;
    }

    public String getIgniteDataType() {
        return igniteDataType;
    }

    public boolean isJsonb() {
        return isJsonb;
    }

    private static Map<String, IgniteDataTypeEnum> getAllTypes() {
        return Arrays.stream(IgniteDataTypeEnum.values())
                .collect(
                        Collectors.toMap(
                                IgniteDataTypeEnum::getSupportDataType,
                                igniteDataTypeEnum -> igniteDataTypeEnum));
    }

    // Pref DataType or Field DataType
    public static IgniteDataTypeEnum findByDataType(String fieldDataType) {
        // modelDataType => Enum
        Map<String, IgniteDataTypeEnum> allTypes = getAllTypes();
        IgniteDataTypeEnum result = allTypes.get(fieldDataType.toLowerCase());
        if (result == null) {
            throw new IllegalArgumentException(
                    "Unsupported data type: " + fieldDataType + ", supported types: " + allTypes);
        }
        return result;
    }

    public static Pair<String, String> getHighTableColumnName(String dataType) {
        IgniteDataTypeEnum dataTypeEnum = findByDataType(dataType);
        switch (dataTypeEnum.getIgniteDataType()) {
            case "bigint":
                return Pair.of("VALUE_LONG", "%d");
            case "double":
                return Pair.of("VALUE_DOUBLE", "%f");
            case "boolean":
                return Pair.of("VALUE_BOOL", "%b");
            default:
                String column = dataTypeEnum.isJsonb ? "VALUE_JSON" : "VALUE_STRING";
                return Pair.of(column, "%s");
        }
    }
}
