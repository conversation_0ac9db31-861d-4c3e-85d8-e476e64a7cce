package com.envision.gravity.common.util;

import java.util.List;
import java.util.concurrent.TimeUnit;


import com.google.common.base.Stopwatch;
import org.apache.commons.lang3.StringUtils;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.ClientException;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;
import org.apache.ignite.internal.processors.query.IgniteSQLException;
import org.apache.ignite.marshaller.MarshallerExclusions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IgniteUtil {
    private static final Logger logger = LoggerFactory.getLogger(IgniteUtil.class);
    private static final int maxRetries = 3;
    private static IgniteClient igniteClient;
    private static long lastUpdated = 0;

    static {
        try {
            igniteClient = createIgniteClient();
            lastUpdated = System.currentTimeMillis();
        } catch (Exception e) {
            logger.warn("Eager init ignite failed. Try initial it lazy");
        }
    }

    public static IgniteClient getIgniteClient() {
        if (igniteClient == null) {
            synchronized (IgniteUtil.class) {
                igniteClient = createIgniteClient();
                lastUpdated = System.currentTimeMillis();
            }
        }
        return igniteClient;
    }

    public static synchronized void resetIgniteClient() {
        if ((System.currentTimeMillis() - lastUpdated) > 1000 * 5) {
            close();
            igniteClient = createIgniteClient();
            lastUpdated = System.currentTimeMillis();
        }
    }

    public static <K, V> ClientCache<K, V> getCache(String name) {
        return getIgniteClient().cache(name);
    }

    public static List<List<?>> query(String schema, String sql) {
        if (StringUtils.isEmpty(sql)) {
            return null;
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        SqlFieldsQuery query = new SqlFieldsQuery(sql);

        if (!StringUtils.isEmpty(schema)) {
            query.setSchema(schema.toUpperCase());
        }
        List<List<?>> results = null;
        try (FieldsQueryCursor<List<?>> cursor = getIgniteClient().query(query)) {
            results = cursor.getAll();
        } catch (IgniteSQLException igniteSQLException) {
            logger.error("Query ignite got igniteSQLException", igniteSQLException);
            throw igniteSQLException;
        } catch (Throwable t) {
            if (t instanceof ClientException) {
                if (t.getMessage().contains("Failed to parse query")
                        || t.getMessage().contains("Duplicate key during INSERT")) {
                    logger.error("Query ignite got parse error, do not retry.", t);
                    throw t;
                } else if (t.getMessage().contains("Channel is closed")) {
                    try {
                        Thread.sleep(200);
                    } catch (Exception ignore) {
                    }
                    resetIgniteClient();
                }
            }
            logger.warn("Query ignite failed, reason: {}", t.getMessage());
        }

        if (results == null || results.isEmpty() || results.get(0).isEmpty()) {
            return null;
        }
        logger.debug(
                "Query ignite success, Schema: [{}], SQL: [{}]," + "Cost: [{}] ms",
                schema,
                sql,
                stopwatch.elapsed(TimeUnit.MILLISECONDS));

        return results;
    }

    public static List<List<?>> query(String sql) {
        return query("", sql);
    }

    private static IgniteClient createIgniteClient() {
        String igniteAddress = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_ADDRESS);
        String igniteUser = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_USERNAME);
        String ignitePassword = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_PASSWORD);

        try {
            ClientConfiguration conf =
                    new ClientConfiguration()
                            .setAddresses(igniteAddress.split(","))
                            .setPartitionAwarenessEnabled(true)
                            .setTimeout(55000)
                            .setUserName(igniteUser)
                            .setUserPassword(ignitePassword);
            return Ignition.startClient(conf);
        } catch (Exception e) {
            throw new RuntimeException("Create Ignite Client Error", e);
        }
    }

    public static void close() {
        if (igniteClient != null) {
            try {
                logger.info("closing ignite client");
                igniteClient.close();
                synchronized (IgniteUtil.class) {
                    igniteClient = null;
                }
            } catch (Exception e) {

            }
        }
        MarshallerExclusions.clearCache();
    }
}
