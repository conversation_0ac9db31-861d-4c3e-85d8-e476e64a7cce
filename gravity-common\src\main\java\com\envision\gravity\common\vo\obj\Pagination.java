package com.envision.gravity.common.vo.obj;

import javax.validation.constraints.Min;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/21 11:27 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Pagination {

    @Min(value = 1, message = "pageNo can not less than 1")
    private int pageNo;

    @Min(value = 1, message = "pageSize can not less than 1")
    private int pageSize;

    /** Calculate offset for pagination */
    public int getOffset() {
        return (pageNo - 1) * pageSize;
    }
}
