# StreamFlow Test Suite

This directory contains a comprehensive test suite for StreamFlow, including unit tests, integration tests, and end-to-end tests.

## 🧪 Test File Overview

### Core Test Files

#### 1. StreamFlowEndToEndTest.java ⭐
- **Complete end-to-end integration test**: Validates the complete message processing flow of StreamFlow
- **Features**:
  - Automatically creates test Kafka Topics
  - Starts StreamFlow background process
  - Sends test messages and validates output
  - Validates message format and directMapping headers
  - Automatically cleans up test environment

#### 2. CalculateProcessorTest.java
- **Core calculation processor unit test**: Validates core functionality of CalculateProcessor
- **Features**:
  - Tests direct mapping processing
  - Tests message parsing and transformation
  - Tests asset mapping logic

#### 3. TestMessageFactory.java
- **Test message factory**: Provides standardized test messages
- **Features**:
  - Generates standard Kafka JSON messages
  - Generates multi-point messages
  - Provides test Topic names

#### 4. KafkaTopicManager.java
- **Kafka Topic management tool**: Manages Kafka Topics in test environment
- **Features**:
  - Creates test Topics
  - Checks Topic existence
  - Cleans up test Topics

### Configuration and Tool Tests

#### 5. ConfigurationTest.java
- **Configuration test**: Validates Lion configuration loading and system properties
- **Features**:
  - Tests Lion configuration loading
  - Validates system property settings
  - Checks configuration file existence

#### 6. TestMessageFactoryTest.java
- **Test factory validation**: Validates correctness of test message factory
- **Features**:
  - Validates message format
  - Validates Topic name generation
  - Validates multi-point message format

### Unit Tests

#### 7. CalcMetaProcessorTest.java
- **Metadata processor test**: Validates calculation metadata processing logic

#### 8. CalcCommonUtilsTest.java
- **Utility class test**: Validates calculation utility class functionality
- **Features**:
  - Expression parsing tests
  - Direct mapping tests
  - Source property tests

#### 9. IgniteConfigTest.java
- **Ignite configuration test**: Validates Ignite cache configuration

#### 10. SimpleBuilderTest.java
- **Builder test**: Validates stream builder functionality

## 📋 Test Message Formats

### Input Message Format (MEASURE_POINT_ORIGIN_GRAVITY_o17186913277371853)
```json
{
    "orgId": "o17186913277371853",
    "modelId": "wind_general_001",
    "modelIdPath": "/wind_general_001",
    "payload": [{
        "assetId": "05b8z0deq502bf",
        "time": 1749052800000,
        "points": {
            "WGEN.GenSpd": 121.0
        }
    }]
}
```

### Expected Output Message Format (MEASURE_POINT_CAL_o17186913277371853)
```json
{
    "f0": {
        "orgId": "o17186913277371853",
        "modelId": "gravityMultiModelTest02",
        "modelIdPath": "/gravityMultiModelTest02",
        "payload": [{
            "assetId": "05b8z0fcj401c5",
            "time": 1749052800000,
            "points": {
                "WGEN.GenSpd": 121.0
            }
        }]
    },
    "f1": true,
    "arity": 2
}
```

**Notes**:
- Output messages contain `directMapping` flag in Kafka message headers
- `f1: true` indicates successful direct mapping
- Source asset `05b8z0deq502bf` maps to target asset `05b8z0fcj401c5`
- Model transforms from `wind_general_001` to `gravityMultiModelTest02`

## ⚙️ Pre-execution Setup

### 1. Test Environment Configuration
Tests use test environment Kafka and other services, no need to start local services.

**Test Environment Information**:
- **Kafka Server**: `*************:9092`
- **Configuration File**: `deploy/apps/config/lionconfig/ext/gravity-calc.properties`
- **Authentication Config**: `deploy/zk_client_jaas.conf`

### 2. Required Configuration Files
Ensure the following files exist:
```
gravity-flink/flink-streaming-calculate/
├── deploy/
│   ├── apps/
│   │   └── config/
│   │       └── lionconfig/
│   │           └── ext/
│   │               └── gravity-calc.properties
│   └── zk_client_jaas.conf
```

### 3. JVM Parameter Configuration
The following JVM parameters are automatically set during test execution:
- `-ea` (enable assertions)
- `-Darch.path=./deploy/apps`
- `-Djava.security.auth.login.config=./deploy/zk_client_jaas.conf`
- `-DLOG_LEVEL=DEBUG`

### 4. Test Environment Kafka Configuration
Configuration read from `gravity-calc.properties`:
- `gravity-flink.calc-stream.kafka.server.address=*************:9092`
- `gravity-flink.calc-stream.kafka.topic.pattern=MEASURE_POINT_ORIGIN_GRAVITY_.*`
- `gravity-flink.calc-stream.kafka.consumer.group=gravity_calc_stream`
- `gravity-flink.calc-stream.kafka.source.batch.size=10`
- `gravity-flink.calc-stream.kafka.source.batch.timeout.seconds=5`

## 🚀 Running Tests

### 1. Run Complete End-to-End Test (Recommended)
```bash
mvn test -Dtest=StreamFlowEndToEndTest -pl gravity-flink/flink-streaming-calculate
```

### 2. Run Unit Tests
```bash
# Run calculation processor test
mvn test -Dtest=CalculateProcessorTest -pl gravity-flink/flink-streaming-calculate

# Run configuration test
mvn test -Dtest=ConfigurationTest -pl gravity-flink/flink-streaming-calculate

# Run test factory test
mvn test -Dtest=TestMessageFactoryTest -pl gravity-flink/flink-streaming-calculate
```

### 3. Run All Tests
```bash
mvn test -pl gravity-flink/flink-streaming-calculate
```

### 4. Run Specific Test Methods
```bash
# Run specific methods of end-to-end test
mvn test -Dtest=StreamFlowEndToEndTest#testTopicCreation -pl gravity-flink/flink-streaming-calculate
mvn test -Dtest=StreamFlowEndToEndTest#testMessageSending -pl gravity-flink/flink-streaming-calculate
mvn test -Dtest=StreamFlowEndToEndTest#testEndToEndProcessing -pl gravity-flink/flink-streaming-calculate
```

## 📊 Test Process Overview

### End-to-End Test Flow
1. **Environment Initialization**:
   - Initialize Kafka Topic manager
   - Create test Topics (input and output)

2. **Start StreamFlow**:
   - Start StreamFlow in local mode in background thread
   - Wait for StreamFlow to fully start (approximately 10 seconds)

3. **Execute Tests**:
   - Test 1: Validate Topic creation
   - Test 2: Send test messages
   - Test 3: End-to-end message processing validation

4. **Environment Cleanup**:
   - Stop StreamFlow
   - Close Kafka connections
   - Clean up test resources

### Unit Tests
- **CalculateProcessor Test**: Validates core calculation logic
- **Configuration Test**: Validates Lion configuration loading
- **Utility Class Test**: Validates auxiliary tool functionality

## 🔧 故障排除

### 1. Lion 配置加载失败
- 确保 `arch.path` 指向正确的配置目录
- 检查 `deploy/apps/config/lionconfig/ext/gravity-calc.properties` 文件是否存在
- 确认 JVM 参数设置正确

### 2. Kafka 连接失败
- 检查测试环境 Kafka 服务是否可用 (`*************:9092`)
- 确认网络连接到测试环境
- 检查 Kafka 认证配置

### 3. Zookeeper 认证失败
- 确保 `zk_client_jaas.conf` 文件存在且配置正确
- 检查 `java.security.auth.login.config` 参数设置

### 4. StreamFlow 启动失败
- 检查测试环境依赖服务（PostgreSQL、Ignite 等）是否可用
- 查看 StreamFlow 日志了解具体错误
- 确认配置文件中的服务地址正确

### 5. 消息格式不匹配
- 检查输入消息格式是否正确
- 确认 StreamFlow 配置是否正确
- 验证测试环境的 topic 配置

### 6. 类型转换异常
- 确保 Flink 类型定义与实际数据匹配
- 检查 `PojoFactory` 中的类型定义
- 验证序列化配置

## ⚠️ 注意事项

1. **测试超时**：端到端测试设置了 2 分钟超时，如果 StreamFlow 启动较慢可能需要调整
2. **并发测试**：避免同时运行多个集成测试，可能会导致端口冲突
3. **数据清理**：测试使用唯一的 consumer group，避免数据污染
4. **日志级别**：可以调整日志级别查看更详细的测试信息
5. **资源清理**：测试完成后会自动清理资源，但异常退出时可能需要手动清理

## 📈 测试覆盖范围

### 功能测试
- ✅ Kafka 消息接收和发送
- ✅ 消息解析和格式转换
- ✅ 资产映射和模型转换
- ✅ 直接映射计算
- ✅ 批次处理和窗口收集
- ✅ 消息序列化和头部处理

### 集成测试
- ✅ 端到端消息流处理
- ✅ 多组件协作
- ✅ 配置加载和环境设置
- ✅ 错误处理和异常恢复

### 性能测试
- ✅ 批次处理性能
- ✅ 消息吞吐量
- ✅ 内存使用情况

## 🔄 持续改进

### 已完成的改进
- ✅ 修复了 Flink 类型系统兼容性问题
- ✅ 优化了批次处理逻辑
- ✅ 改进了错误处理和日志记录
- ✅ 简化了测试代码结构

### 未来改进计划
- 🔄 添加更多错误场景测试
- 🔄 增加性能基准测试
- 🔄 添加监控和指标收集
- 🔄 支持更多消息格式

## 📚 相关文档

- [StreamFlow 架构文档](../../../../../../../README.md)
- [Flink 流处理文档](https://flink.apache.org/)
- [Kafka 集成文档](https://kafka.apache.org/)
- [Lion 配置系统文档](https://lion.envision-digital.com/)

## 故障排除

### 1. Lion 配置加载失败
- 确保 `arch.path` 指向正确的配置目录
- 检查 `deploy/apps/config/lionconfig/ext/gravity-calc.properties` 文件是否存在
- 确认 JVM 参数设置正确

### 2. Kafka 连接失败
- 检查测试环境 Kafka 服务是否可用 (`*************:9092`)
- 确认网络连接到测试环境
- 检查 Kafka 认证配置

### 3. Zookeeper 认证失败
- 确保 `zk_client_jaas.conf` 文件存在且配置正确
- 检查 `java.security.auth.login.config` 参数设置

### 4. StreamFlow 启动失败
- 检查测试环境依赖服务（PostgreSQL、Ignite 等）是否可用
- 查看 StreamFlow 日志了解具体错误
- 确认配置文件中的服务地址正确

### 5. 消息格式不匹配
- 检查输入消息格式是否正确
- 确认 StreamFlow 配置是否正确
- 验证测试环境的 topic 配置

## 注意事项

1. **测试超时**：集成测试设置了 2 分钟超时，如果 StreamFlow 启动较慢可能需要调整
2. **并发测试**：避免同时运行多个集成测试，可能会导致端口冲突
3. **数据清理**：测试使用唯一的 consumer group，避免数据污染
4. **日志级别**：可以调整日志级别查看更详细的测试信息

## 扩展测试

可以基于现有测试框架扩展更多测试场景：
- 不同的输入消息格式
- 错误消息处理
- 性能测试
- 故障恢复测试
