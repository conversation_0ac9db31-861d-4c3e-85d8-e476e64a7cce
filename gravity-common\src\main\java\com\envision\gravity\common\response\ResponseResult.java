package com.envision.gravity.common.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/9
 * @description:
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> {

    @JsonProperty private int code;
    @JsonProperty private String message;
    @JsonProperty private T data;

    public ResponseResult(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <T> ResponseResult<T> createResult(int code, String message, T data) {
        return new ResponseResult<T>(code, message, data);
    }

    public static <T> ResponseResult<T> createResult(int code, String message) {
        return new ResponseResult<T>(code, message);
    }
}
