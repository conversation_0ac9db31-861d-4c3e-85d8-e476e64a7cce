package com.envision.gravity.ignite.tsdb.loader;

import java.util.Optional;


import io.eniot.tsdb.common.Retention;

/** <AUTHOR> 2024/3/19 */
public enum CDSTimeGroup {
    RAW_NULL(null, null), // When timeGroup is null, execute raw query
    RAW("RAW", null),
    DURATION_1m("1m", Retention.valueOf("1m")),
    DURATION_5m("5m", Retention.valueOf("5m")),
    DURATION_10m("10m", Retention.valueOf("10m")),
    DURATION_15m("15m", Retention.valueOf("15m")),
    DURATION_30m("30m", Retention.valueOf("30m")),
    DURATION_H("H", Retention.valueOf("1h"));

    private final String value;

    private final Retention retention;

    CDSTimeGroup(String value, Retention retention) {
        this.value = value;
        this.retention = retention;
    }

    public static Optional<CDSTimeGroup> match(String expr) {
        if (expr == null) {
            return Optional.of(RAW_NULL);
        }
        for (CDSTimeGroup t : CDSTimeGroup.values()) {
            if (expr.equalsIgnoreCase(t.getValue())) {
                return Optional.of(t);
            }
        }
        return Optional.empty();
    }

    public String getValue() {
        return value;
    }

    public Retention getRetention() {
        return retention;
    }
}
