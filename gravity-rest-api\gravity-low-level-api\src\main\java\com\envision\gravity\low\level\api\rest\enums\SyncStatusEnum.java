package com.envision.gravity.low.level.api.rest.enums;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @description
 */
@Slf4j
public enum SyncStatusEnum {
    SUCCESS,
    PARTIAL_SUCCESS,
    WARING,
    FAILED;

    /**
     * Convert string to SyncStatusEnum (case-insensitive) Returns FAILED if the input is invalid
     */
    public static SyncStatusEnum fromString(String status) {
        if (status == null) {
            log.warn("Null status provided, defaulting to FAILED");
            return FAILED;
        }

        try {
            return valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid status: {}, defaulting to FAILED", status);
            return FAILED;
        }
    }
}
