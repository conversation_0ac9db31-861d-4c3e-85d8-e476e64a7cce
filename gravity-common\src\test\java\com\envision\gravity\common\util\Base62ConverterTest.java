package com.envision.gravity.common.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/4/9
 * @description
 */
class Base62ConverterTest {

    @Test
    void localDateTest() {
        LocalDate dateEnd = LocalDate.of(2050, 12, 1);
        LocalDate dateStart = LocalDate.of(2024, 1, 1);
        long daysDiff = ChronoUnit.DAYS.between(dateStart, dateEnd);
        System.out.println("Days difference: " + daysDiff);

        LocalDateTime dateTimeEnd = LocalDateTime.of(2050, 12, 1, 0, 0);
        LocalDateTime dateTimeStart = LocalDateTime.of(2024, 1, 1, 0, 0);
        long minutesDiff = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
        System.out.println("Minutes difference: " + minutesDiff);
    }
}
