package com.envision.gravity.low.level.api.rest.dao.sg;

import com.envision.gravity.common.po.TblBOModel;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18
 * @description
 */
@Mapper
public interface SGModelMapper {
    List<TblBOModel> queryByModelIds(
            @Param("orgId") String orgId, @Param("modelIds") List<String> modelIdList);
}
