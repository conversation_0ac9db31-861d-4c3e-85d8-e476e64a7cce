package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblBoModel implements CdcTableEntity {
    private static final long serialVersionUID = 4385560697338318753L;
    private String modelId;
    private String modelDisplayName;
    private String description;
    private String comment;
    private String groupId;
    private String modelPath;
    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
