package com.envision.gravity.flink.streaming.postgres.cdc.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28
 * @description
 */
public class ListUtils {
    public static <T> List<List<T>> splitList(List<T> list, int batchSize) {
        List<List<T>> subLists = new ArrayList<>();
        int numberOfSubLists = (int) Math.ceil((double) list.size() / batchSize);

        for (int i = 0; i < numberOfSubLists; i++) {
            int start = i * batchSize;
            int end = Math.min((i + 1) * batchSize, list.size());
            subLists.add(list.subList(start, end));
        }

        return subLists;
    }
}
