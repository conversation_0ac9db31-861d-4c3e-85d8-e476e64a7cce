package com.envision.gravity.ignite.tsdb.loader.bo;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/** <AUTHOR> 2024/3/26 */
public class BOMultiFieldMapping {

    private final Map<String, _FieldsInfo> fieldMap = new LinkedHashMap<>();

    public void put(
            String fieldName,
            String fieldId,
            BOModelFieldType fieldType,
            boolean hasQuality,
            BOModelFieldDataType dataType) {
        if (fieldMap.containsKey(fieldName)) {
            fieldMap.get(fieldName).addField(fieldName, fieldId, fieldType, hasQuality, dataType);
        } else {
            HashSet<String> fieldIds = new HashSet<>(4);
            fieldIds.add(fieldId);
            fieldMap.put(fieldName, new _FieldsInfo(fieldIds, fieldType, hasQuality, dataType));
        }
    }

    public void put(
            String fieldName,
            Set<String> fieldIds,
            BOModelFieldType fieldType,
            boolean hasQuality,
            BOModelFieldDataType dataType) {
        fieldMap.put(fieldName, new _FieldsInfo(fieldIds, fieldType, hasQuality, dataType));
    }

    public boolean contains(String fieldName) {
        return fieldMap.containsKey(fieldName);
    }

    public void remove(String fieldName) {
        fieldMap.remove(fieldName);
    }

    // pt1 => Set<field1,field2>, pt2 => List<field1>
    public Map<String, Set<String>> getFieldIds(Set<String> fieldNames) {
        Map<String, Set<String>> res = new HashMap<>(fieldNames.size());
        for (String ref : fieldNames) {
            Set<String> fieldIds = getFieldIds(ref);
            if (fieldIds != null) {
                res.put(ref, fieldIds);
            }
        }
        return res;
    }

    public Set<String> getFieldIds(String fieldName) {
        if (!fieldMap.containsKey(fieldName)) {
            return null;
        }
        return fieldMap.get(fieldName).fieldIds;
    }

    public boolean hasQuality(String fieldName) {
        return fieldMap.get(fieldName).hasQuality;
    }

    public BOModelFieldType getFieldType(String fieldName) {
        if (!fieldMap.containsKey(fieldName)) {
            return BOModelFieldType.UNKNOWN;
        }
        return fieldMap.get(fieldName).fieldType;
    }

    public BOModelFieldDataType getDataType(String fieldName) {
        if (!fieldMap.containsKey(fieldName)) {
            return BOModelFieldDataType.UNKNOWN;
        }
        return fieldMap.get(fieldName).dataType;
    }

    public String toFieldMappingString() {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, _FieldsInfo> entry : fieldMap.entrySet()) {
            if (BOModelFieldType.ATTRIBUTE == entry.getValue().fieldType) {
                // attr
                handleFieldIds(sb, entry.getValue().fieldIds, "");
                sb.append(" AS ");
                sb.append("`");
                sb.append(entry.getKey());
                sb.append("`, ");
            } else if (BOModelFieldType.TELEMETRY == entry.getValue().fieldType) {
                // point value
                handleFieldIds(sb, entry.getValue().fieldIds, "_value");
                sb.append(" AS ");
                sb.append("`");
                sb.append(entry.getKey());
                sb.append(".value`, ");
                // point time
                handleFieldIds(sb, entry.getValue().fieldIds, "_time");
                sb.append(" AS ");
                sb.append("`");
                sb.append(entry.getKey());
                sb.append(".time`, ");
                // point quality
                if (entry.getValue().hasQuality) {
                    handleFieldIds(sb, entry.getValue().fieldIds, "_quality");
                    sb.append("`");
                    sb.append(entry.getKey());
                    sb.append(".quality`, ");
                }
            }
        }
        return sb.toString();
    }

    private void handleFieldIds(StringBuilder sb, Set<String> fieldIds, String suffix) {
        if (fieldIds.size() > 1) {
            sb.append("COALESCE(");
        }
        Iterator<String> fieldIdItr = fieldIds.iterator();
        sb.append(fieldIdItr.next());
        sb.append(suffix);
        while (fieldIdItr.hasNext()) {
            sb.append(",");
            sb.append(fieldIdItr.next());
            sb.append(suffix);
        }
        if (fieldIds.size() > 1) {
            sb.append(")");
        }
    }

    private static class _FieldsInfo {
        Set<String> fieldIds;
        BOModelFieldType fieldType;
        boolean hasQuality;
        BOModelFieldDataType dataType;

        public _FieldsInfo(
                Set<String> fieldIds,
                BOModelFieldType fieldType,
                boolean hasQuality,
                BOModelFieldDataType dataType) {
            this.fieldIds = fieldIds;
            this.fieldType = fieldType;
            this.hasQuality = hasQuality;
            this.dataType = dataType;
        }

        public void addField(
                String fieldName,
                String fieldId,
                BOModelFieldType fieldType,
                boolean hasQuality,
                BOModelFieldDataType dataType) {
            if (this.fieldType != fieldType
                    || this.hasQuality != hasQuality
                    || this.dataType != dataType) {
                throw new IllegalArgumentException("BO_MODEL_FILED_MAPPING_CONFLICT");
            }
            this.fieldIds.add(fieldId);
        }
    }
}
