package com.envision.gravity.flink.streaming.postgres.cdc.side;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;

import java.util.List;


import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
public class SideOutputs {

    public static final OutputTag<ParsedCdcRecord> REFRESH_TAG =
            new OutputTag<ParsedCdcRecord>("refreshDetailOrigin") {
                private static final long serialVersionUID = -7986351001272695872L;
            };

    public static final OutputTag<Tuple2<String, List<RefreshModelReq>>> REFRESH_MODEL_TAG =
            new OutputTag<Tuple2<String, List<RefreshModelReq>>>("refreshModel") {
                private static final long serialVersionUID = 6402194940289679082L;
            };

    public static final OutputTag<Tuple2<String, List<RefreshObjectReq>>> REFRESH_OBJECT_TAG =
            new OutputTag<Tuple2<String, List<RefreshObjectReq>>>("refreshObject") {
                private static final long serialVersionUID = -7321397456506989701L;
            };

    public static final OutputTag<Tuple2<String, List<NebulaGraphReq>>> NEBULA_GRAPH_TAG =
            new OutputTag<Tuple2<String, List<NebulaGraphReq>>>("nebulaGraphOperator") {
                private static final long serialVersionUID = 2012547943223897761L;
            };
}
