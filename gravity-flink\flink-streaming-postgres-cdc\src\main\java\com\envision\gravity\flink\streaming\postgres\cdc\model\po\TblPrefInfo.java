package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblPrefInfo implements CDCTableEntity {
    private static final long serialVersionUID = -4786998861533577919L;
    private String prefId;
    private String prefName;
    private String prefDisplayName;
    private String prefType;
    private String description;
    private String comment;
    private boolean writable;
    private boolean required;
    private String defaultValue;
    private boolean hasQuality;
    private String prefDataType;
    private String prefSignalType;
    private String dataDefinition;
    private String request;
    private String response;
    private String unit;
    private String lowerLimit;
    private String upperLimit;
    private String dimensions;
    private String intervals;
    private String defaultAggMethod;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
