package com.envision.gravity.flink.streaming.virtual.attr.sync.model.cdc;

import java.sql.Timestamp;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/11
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CdcRecord {
    @JsonIgnore private Object before;
    @JsonIgnore private Object after;
    private Source source;
    private String op;
    private Timestamp tsMs;
    @JsonIgnore private Object transaction;
}
