// Generated from
// C:/Users/<USER>/IdeaProjects/gravity-all/gravity-rest-api/gravity-low-level-api/src/main/java/com/envision/gravity/low/level/api/rest/antlr/Condition.g4 by ANTLR 4.13.2
package com.envision.gravity.low.level.api.rest.antlr;

import org.antlr.v4.runtime.tree.ParseTreeListener;

/**
 * This interface defines a complete listener for a parse tree produced by {@link ConditionParser}.
 */
public interface ConditionListener extends ParseTreeListener {
    /**
     * Enter a parse tree produced by {@link ConditionParser#parse}.
     *
     * @param ctx the parse tree
     */
    void enterParse(ConditionParser.ParseContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#parse}.
     *
     * @param ctx the parse tree
     */
    void exitParse(ConditionParser.ParseContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphAssetInRelatedModelsExpr} labeled alternative
     * in {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphAssetInRelatedModelsExpr(
            ConditionParser.GraphAssetInRelatedModelsExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphAssetInRelatedModelsExpr} labeled alternative
     * in {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphAssetInRelatedModelsExpr(
            ConditionParser.GraphAssetInRelatedModelsExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphAndExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphAndExpr(ConditionParser.GraphAndExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphAndExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphAndExpr(ConditionParser.GraphAndExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphAssetInModelsExpr} labeled alternative in
     * {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphAssetInModelsExpr} labeled alternative in
     * {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphParenExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphParenExpr(ConditionParser.GraphParenExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphParenExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphParenExpr(ConditionParser.GraphParenExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphIsInExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphIsInExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code graphComparatorExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code graphComparatorExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#joinModelGraphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#joinModelGraphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#joinModelTagEqExpr}.
     *
     * @param ctx the parse tree
     */
    void enterJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#joinModelTagEqExpr}.
     *
     * @param ctx the parse tree
     */
    void exitJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#joinModelTagExistsExpr}.
     *
     * @param ctx the parse tree
     */
    void enterJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#joinModelTagExistsExpr}.
     *
     * @param ctx the parse tree
     */
    void exitJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code i18nLikeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code i18nLikeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code comparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterComparatorExpr(ConditionParser.ComparatorExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code comparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitComparatorExpr(ConditionParser.ComparatorExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code joinModelExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterJoinModelExpr(ConditionParser.JoinModelExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code joinModelExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitJoinModelExpr(ConditionParser.JoinModelExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code isExistsExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterIsExistsExpr(ConditionParser.IsExistsExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code isExistsExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitIsExistsExpr(ConditionParser.IsExistsExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code joinGraphExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code joinGraphExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code i18nComparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code i18nComparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code inExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterInExpr(ConditionParser.InExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code inExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitInExpr(ConditionParser.InExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code likeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterLikeExpr(ConditionParser.LikeExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code likeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitLikeExpr(ConditionParser.LikeExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code fuzzySearchExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code fuzzySearchExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code binaryExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterBinaryExpr(ConditionParser.BinaryExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code binaryExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitBinaryExpr(ConditionParser.BinaryExprContext ctx);
    /**
     * Enter a parse tree produced by the {@code parenExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void enterParenExpr(ConditionParser.ParenExprContext ctx);
    /**
     * Exit a parse tree produced by the {@code parenExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     */
    void exitParenExpr(ConditionParser.ParenExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#fuzzySearchField}.
     *
     * @param ctx the parse tree
     */
    void enterFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#fuzzySearchField}.
     *
     * @param ctx the parse tree
     */
    void exitFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#field}.
     *
     * @param ctx the parse tree
     */
    void enterField(ConditionParser.FieldContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#field}.
     *
     * @param ctx the parse tree
     */
    void exitField(ConditionParser.FieldContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#fields}.
     *
     * @param ctx the parse tree
     */
    void enterFields(ConditionParser.FieldsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#fields}.
     *
     * @param ctx the parse tree
     */
    void exitFields(ConditionParser.FieldsContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#parenFields}.
     *
     * @param ctx the parse tree
     */
    void enterParenFields(ConditionParser.ParenFieldsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#parenFields}.
     *
     * @param ctx the parse tree
     */
    void exitParenFields(ConditionParser.ParenFieldsContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#parenValues}.
     *
     * @param ctx the parse tree
     */
    void enterParenValues(ConditionParser.ParenValuesContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#parenValues}.
     *
     * @param ctx the parse tree
     */
    void exitParenValues(ConditionParser.ParenValuesContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#joinGraph}.
     *
     * @param ctx the parse tree
     */
    void enterJoinGraph(ConditionParser.JoinGraphContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#joinGraph}.
     *
     * @param ctx the parse tree
     */
    void exitJoinGraph(ConditionParser.JoinGraphContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#parenGraphExpr}.
     *
     * @param ctx the parse tree
     */
    void enterParenGraphExpr(ConditionParser.ParenGraphExprContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#parenGraphExpr}.
     *
     * @param ctx the parse tree
     */
    void exitParenGraphExpr(ConditionParser.ParenGraphExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#joinModel}.
     *
     * @param ctx the parse tree
     */
    void enterJoinModel(ConditionParser.JoinModelContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#joinModel}.
     *
     * @param ctx the parse tree
     */
    void exitJoinModel(ConditionParser.JoinModelContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#parenJoinModelExpr}.
     *
     * @param ctx the parse tree
     */
    void enterParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#parenJoinModelExpr}.
     *
     * @param ctx the parse tree
     */
    void exitParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#modelEdgeType}.
     *
     * @param ctx the parse tree
     */
    void enterModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#modelEdgeType}.
     *
     * @param ctx the parse tree
     */
    void exitModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#modelParams}.
     *
     * @param ctx the parse tree
     */
    void enterModelParams(ConditionParser.ModelParamsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#modelParams}.
     *
     * @param ctx the parse tree
     */
    void exitModelParams(ConditionParser.ModelParamsContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#assetInModels}.
     *
     * @param ctx the parse tree
     */
    void enterAssetInModels(ConditionParser.AssetInModelsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#assetInModels}.
     *
     * @param ctx the parse tree
     */
    void exitAssetInModels(ConditionParser.AssetInModelsContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#assetInRelatedModels}.
     *
     * @param ctx the parse tree
     */
    void enterAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#assetInRelatedModels}.
     *
     * @param ctx the parse tree
     */
    void exitAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx);
    /**
     * Enter a parse tree produced by the {@code values} labeled alternative in {@link
     * ConditionParser#graphExprgraphExprgraphExprgraphExprgraphExprgraphExprexprexprexprexprexprexprexprexprexprexprexpr}.
     *
     * @param ctx the parse tree
     */
    void enterValues(ConditionParser.ValuesContext ctx);
    /**
     * Exit a parse tree produced by the {@code values} labeled alternative in {@link
     * ConditionParser#graphExprgraphExprgraphExprgraphExprgraphExprgraphExprexprexprexprexprexprexprexprexprexprexprexpr}.
     *
     * @param ctx the parse tree
     */
    void exitValues(ConditionParser.ValuesContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#value}.
     *
     * @param ctx the parse tree
     */
    void enterValue(ConditionParser.ValueContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#value}.
     *
     * @param ctx the parse tree
     */
    void exitValue(ConditionParser.ValueContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#stringValue}.
     *
     * @param ctx the parse tree
     */
    void enterStringValue(ConditionParser.StringValueContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#stringValue}.
     *
     * @param ctx the parse tree
     */
    void exitStringValue(ConditionParser.StringValueContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#like}.
     *
     * @param ctx the parse tree
     */
    void enterLike(ConditionParser.LikeContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#like}.
     *
     * @param ctx the parse tree
     */
    void exitLike(ConditionParser.LikeContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#and}.
     *
     * @param ctx the parse tree
     */
    void enterAnd(ConditionParser.AndContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#and}.
     *
     * @param ctx the parse tree
     */
    void exitAnd(ConditionParser.AndContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#comparator}.
     *
     * @param ctx the parse tree
     */
    void enterComparator(ConditionParser.ComparatorContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#comparator}.
     *
     * @param ctx the parse tree
     */
    void exitComparator(ConditionParser.ComparatorContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#booleanValue}.
     *
     * @param ctx the parse tree
     */
    void enterBooleanValue(ConditionParser.BooleanValueContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#booleanValue}.
     *
     * @param ctx the parse tree
     */
    void exitBooleanValue(ConditionParser.BooleanValueContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#isExists}.
     *
     * @param ctx the parse tree
     */
    void enterIsExists(ConditionParser.IsExistsContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#isExists}.
     *
     * @param ctx the parse tree
     */
    void exitIsExists(ConditionParser.IsExistsContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#isIn}.
     *
     * @param ctx the parse tree
     */
    void enterIsIn(ConditionParser.IsInContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#isIn}.
     *
     * @param ctx the parse tree
     */
    void exitIsIn(ConditionParser.IsInContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#binary}.
     *
     * @param ctx the parse tree
     */
    void enterBinary(ConditionParser.BinaryContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#binary}.
     *
     * @param ctx the parse tree
     */
    void exitBinary(ConditionParser.BinaryContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#timestamp}.
     *
     * @param ctx the parse tree
     */
    void enterTimestamp(ConditionParser.TimestampContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#timestamp}.
     *
     * @param ctx the parse tree
     */
    void exitTimestamp(ConditionParser.TimestampContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#leftParen}.
     *
     * @param ctx the parse tree
     */
    void enterLeftParen(ConditionParser.LeftParenContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#leftParen}.
     *
     * @param ctx the parse tree
     */
    void exitLeftParen(ConditionParser.LeftParenContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#rightParen}.
     *
     * @param ctx the parse tree
     */
    void enterRightParen(ConditionParser.RightParenContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#rightParen}.
     *
     * @param ctx the parse tree
     */
    void exitRightParen(ConditionParser.RightParenContext ctx);
    /**
     * Enter a parse tree produced by {@link ConditionParser#i18n}.
     *
     * @param ctx the parse tree
     */
    void enterI18n(ConditionParser.I18nContext ctx);
    /**
     * Exit a parse tree produced by {@link ConditionParser#i18n}.
     *
     * @param ctx the parse tree
     */
    void exitI18n(ConditionParser.I18nContext ctx);
}
