package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.model.InnerComponentPref;
import com.envision.gravity.low.level.api.rest.util.RestQueryUtils;

import com.google.common.collect.Multimap;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public class TblComponentPrefSqlProvider {

    public String findPrefByFieldIds(Set<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("tcp.field_id AS field_id");
        sql.SELECT("tp.pref_id AS pref_id");
        sql.SELECT(
                "CASE\n"
                        + "                WHEN tc.anonymous THEN tp.pref_name\n"
                        + "                ELSE CONCAT(tc.comp_name, ':', tp.pref_name)\n"
                        + "            END AS pref_name");
        sql.SELECT("tp.pref_type AS pref_type");
        sql.SELECT("tp.pref_data_type AS pref_data_type");
        sql.FROM(orgId + ".tbl_component_pref tcp");
        sql.INNER_JOIN(orgId + ".tbl_component tc ON tcp.comp_id = tc.comp_id");
        sql.INNER_JOIN(orgId + ".tbl_pref tp ON tcp.pref_id = tp.pref_id");
        sql.WHERE(String.format("tcp.field_id IN (%s)", RestQueryUtils.concatStr(fieldIds)));
        return sql.toString();
    }

    public String findCompPrefByFieldIds(Set<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("comp_id");
        sql.SELECT("pref_id");
        sql.SELECT("field_id");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("raw_field_id");
        sql.SELECT("field_index");
        sql.SELECT("horizontal");
        sql.FROM(orgId + ".tbl_component_pref");
        sql.WHERE(String.format("field_id IN (%s)", RestQueryUtils.concatStr(fieldIds)));
        return sql.toString();
    }

    public String findCompPrefByPrefName(String compName, String prefName, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("tcp.field_id AS field_id");
        sql.SELECT("tcp.raw_field_id AS raw_field_id");
        sql.SELECT("tcp.field_index AS field_index");
        sql.SELECT("tcp.horizontal AS horizontal");
        sql.FROM(orgId + ".tbl_component_pref tcp");
        sql.INNER_JOIN(orgId + ".tbl_component tc ON tcp.comp_id = tc.comp_id");
        sql.INNER_JOIN(orgId + ".tbl_pref tp ON tcp.pref_id = tp.pref_id");
        sql.WHERE(String.format("tp.pref_name = '%s'", prefName));
        if (StringUtils.isNotEmpty(compName)) {
            sql.WHERE(String.format("tc.comp_name = '%s'", compName));
        }
        return sql.toString();
    }

    public String selectFieldIdsByPropertyUniqueKey(String uniqueKey, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("tcp.raw_field_id");
        sql.FROM(orgId + ".tbl_component_pref tcp");
        sql.INNER_JOIN(orgId + ".tbl_pref tp on tcp.pref_id = tp.pref_id");
        sql.WHERE("tp.pref_name = '" + uniqueKey + "'");
        return sql.toString();
    }

    public String selectFieldIdsByPrefNameAndCompName(
            String prefName, String compName, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("tcp.raw_field_id");
        sql.FROM(orgId + ".tbl_component_pref tcp");
        sql.INNER_JOIN(orgId + ".tbl_pref tp on tcp.pref_id = tp.pref_id");
        sql.INNER_JOIN(orgId + ".tbl_component tc on tcp.comp_id = tc.comp_id");
        sql.WHERE("tp.pref_name = '" + prefName + "'");

        if (compName != null && !compName.isEmpty()) {
            sql.WHERE("tc.comp_name = '" + compName + "'");
        }

        return sql.toString();
    }

    public String selectByFieldIds(String fieldIdExpr, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("tp.field_id AS field_id");
        sql.SELECT("tp.pref_id AS pref_id");
        sql.SELECT("tp.pref_name AS pref_name");
        sql.SELECT("tp.pref_type AS pref_type");
        sql.SELECT("tp.pref_data_type AS pref_data_type");
        sql.FROM(orgId + ".tbl_component_pref tcp");
        sql.INNER_JOIN(orgId + ".tbl_pref tp on tcp.pref_id = tp.pref_id");
        sql.WHERE("tcp.field_id IN (#{fieldIdExpr})");

        return sql.toString();
    }

    public String selectComponentPrefByFieldIds(List<String> fieldIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("comp_id");
        sql.SELECT("pref_id");
        sql.SELECT("field_id");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.SELECT("raw_field_id");
        sql.FROM(orgId + Constants.TBL_COMPONENT_PREF_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String fieldId : fieldIds) {
            placeHolder.add("'" + fieldId + "'");
        }
        sql.WHERE("field_id in (" + String.join(", ", placeHolder) + ")");
        return sql.toString();
    }

    public String selectAssetModelMap(String schemeName, Multimap<String, String> compPrefMap) {
        String compIds =
                compPrefMap.keySet().stream()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        String prefIds =
                compPrefMap.values().stream()
                        .distinct()
                        .map(value -> "'" + value + "'")
                        .collect(Collectors.joining(", "));

        SQL sql =
                new SQL() {
                    {
                        SELECT("distinct tb.asset_id, tbm.model_id");
                        FROM(schemeName + ".tbl_pref tp");
                        INNER_JOIN(
                                schemeName + ".tbl_component_pref tcp on tp.pref_id = tcp.pref_id");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_bo_model_comp tbmc on tcp.comp_id = tbmc.comp_id");
                        INNER_JOIN(
                                schemeName + ".tbl_bo_model tbm on tbmc.model_id = tbm.model_id");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_bo_group_relation tbgr on tbm.group_id = tbgr.group_id");
                        INNER_JOIN(schemeName + ".tbl_bo tb on tbgr.asset_id = tb.asset_id");
                        WHERE(
                                "tp.pref_type = 'ATTRIBUTE' and tcp.comp_id in ( "
                                        + compIds
                                        + " ) and tcp.pref_id in ( "
                                        + prefIds
                                        + " )");
                    }
                };

        return sql.toString();
    }

    public String batchReplaceRawFieldId(
            List<InnerComponentPref> innerComponentPrefList, String orgId) {
        String values =
                innerComponentPrefList.stream()
                        .map(
                                componentProperty ->
                                        String.format(
                                                "('%s', '%s', '%s', '%s', '%s', %d, %b)",
                                                componentProperty.getCompId(),
                                                componentProperty.getPrefId(),
                                                componentProperty.getFieldId(),
                                                componentProperty.getModifiedUser(),
                                                componentProperty.getRawFieldId(),
                                                componentProperty.getFieldIndex(),
                                                componentProperty.getHorizontal()))
                        .collect(Collectors.joining(","));

        return String.format(
                "INSERT INTO "
                        + orgId
                        + ".TBL_COMPONENT_PREF (COMP_ID, PREF_ID, FIELD_ID, MODIFIED_USER, RAW_FIELD_ID, FIELD_INDEX, HORIZONTAL) VALUES %s ON CONFLICT (comp_id, pref_id)\n"
                        + "DO UPDATE \n"
                        + "SET raw_field_id=EXCLUDED.raw_field_id, field_index=EXCLUDED.field_index, horizontal=EXCLUDED.horizontal, modified_user=EXCLUDED.modified_user, modified_time=CURRENT_TIMESTAMP;",
                values);
    }
}
