package com.envision.gravity.flink.streaming.postgres.cdc.utils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2024/10/24
 * @description
 */
public class TimeUtils {
    public static String getUTCTimeString(Timestamp timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(timestamp);
    }
}
