package com.envision.gravity.flink.streaming.virtual.attr.sync.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblPrefExtMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.VirtualAttrInfo;

import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
@Slf4j
public class TblPrefExtRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblPrefExtRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<VirtualAttrInfo> queryVirtualAttrInfo(String schemeName) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemeName, "Scheme name cannot be null.");

            TblPrefExtMapper tblPrefExtMapper = session.getMapper(TblPrefExtMapper.class);
            return tblPrefExtMapper.queryVirtualAttrInfo(schemeName);
        } catch (Exception e) {
            log.error("Query virtual attr info error.", e);
            throw new GravityRuntimeException("Query virtual attr info error.", e);
        }
    }
}
