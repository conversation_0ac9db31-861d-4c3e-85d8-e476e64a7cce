package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblField;
import com.envision.gravity.common.vo.field.FieldReq;
import com.envision.gravity.low.level.api.rest.dto.RawFieldIdDto;
import com.envision.gravity.low.level.api.rest.dto.SystemIdFieldDto;

import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Set;

/** @Author: qi.jiang2 @Date: 2024/03/04 17:21 @Description: */
public interface TblFieldMapper {

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryByFieldIndex")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.INTEGER),
    })
    List<TblField> queryByFieldIndex(Set<Integer> fieldIndexSet, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldsByFieldIds")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "raw_field_id", property = "rawFieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.INTEGER),
        @Result(column = "horizontal", property = "horizontal", jdbcType = JdbcType.BOOLEAN)
    })
    List<TblField> queryFieldsByFieldIds(Set<String> fieldIds, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryRawFieldIdByFieldIds")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "raw_field_id", property = "rawFieldId", jdbcType = JdbcType.VARCHAR)
    })
    List<RawFieldIdDto> queryRawFieldIdByFieldIds(List<String> fieldIds, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldByFieldNameAndCategory")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    TblField queryFieldByFieldNameAndCategory(String fieldName, String category, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldsByFieldNameAndCategory")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblField> queryFieldsByFieldNameAndCategory(List<FieldReq> fieldReqs, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldsByCategoryId")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.INTEGER)
    })
    List<TblField> queryFieldsByCategoryId(String categoryId, String orgId);

    @SelectProvider(
            type = TblFieldSqlProvider.class,
            method = "queryFieldIdsByCategoryIdAndFieldIds")
    @Results({
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "field_display_name",
                property = "fieldDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "data_type", property = "dataType", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unit", property = "unit", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_index", property = "fieldIndex", jdbcType = JdbcType.INTEGER)
    })
    List<TblField> queryFieldIdsByCategoryIdAndFieldIds(
            String categoryId, List<String> fieldIds, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldHasData")
    int queryFieldHasData(String rawFieldId, String orgId);

    @SelectProvider(type = TblFieldSqlProvider.class, method = "queryFieldsBySystemIds")
    @Results({
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_path", property = "modelPath", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_name", property = "prefName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_id", property = "prefId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR)
    })
    List<SystemIdFieldDto> queryFieldsBySystemIds(Set<String> systemIdList, String orgId);
}
