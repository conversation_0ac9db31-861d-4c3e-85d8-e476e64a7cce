package com.envision.gravity.common.vo.search.asset;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/23 10:26 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchAssetWithGraphParam {

    private List<String> graphBaseCondition;

    private List<String> graphFromCondition;

    private List<String> graphToCondition;

    private List<String> graphSubGraphIdCondition;

    private boolean excludeFromVid;

    private boolean excludeToVid;

    private int maxStep;
}
