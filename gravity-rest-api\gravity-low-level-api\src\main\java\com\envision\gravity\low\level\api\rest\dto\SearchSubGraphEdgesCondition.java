package com.envision.gravity.low.level.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/05/23 11:33 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchSubGraphEdgesCondition {

    private String baseCondition;

    private List<String> fromVidConditions;

    private List<String> toVidConditions;

    private int maxStep;

    private boolean hasOrder;

    private boolean fromVidOnlyOne;
}
