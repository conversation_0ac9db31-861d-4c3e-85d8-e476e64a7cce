package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/19
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblBOModelCompInfo implements CDCTableEntity {
    private static final long serialVersionUID = 8998195709704614429L;
    private String modelId;
    private String compId;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
