package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblPgAsset;
import com.envision.gravity.common.vo.search.Sorter;
import com.envision.gravity.common.vo.search.asset.*;
import com.envision.gravity.low.level.api.rest.dto.SearchAssetCondition;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.enums.GroupByKeyEnum;

import org.apache.ibatis.jdbc.SQL;

import java.util.*;
import java.util.stream.Collectors;

/** @Author: qi.jiang2 @Date: 2024/03/18 17:59 @Description: */
public class SearchAssetSqlProvider {

    public String selectAssetPathByAssetIds(
            List<String> assetIds, SearchAssetWithGraphParam condition, String orgId) {
        List<String> replaceAssetIds = new ArrayList<>();
        for (String assetId : assetIds) {
            replaceAssetIds.add(String.format("'%s'", assetId));
        }
        List<String> graphSubGraphIdCondition =
                new ArrayList<>(condition.getGraphSubGraphIdCondition());
        String graphSql =
                "with recursive edge_finder_reverse as ("
                        + "select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps, "
                        + gpathSelectField(true)
                        + String.format(
                                " from %s.tbl_edge e inner join %s.tbl_sub_graph g on e.sub_graph_id = g.sub_graph_id where e.to_vid in (%s) and (%s) ",
                                orgId,
                                orgId,
                                String.join(", ", replaceAssetIds),
                                String.join(" and ", graphSubGraphIdCondition))
                        + "union "
                        + "select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps, "
                        + gpathSelectField(false)
                        + String.format(
                                " from %s.tbl_edge e, edge_finder_reverse f where e.to_vid = f.from_vid and e.sub_graph_id = f.sub_graph_id and f.steps < %s) ",
                                orgId, condition.getMaxStep())
                        + "select e.to_vid, e.gpath as graph_path from edge_finder_reverse e union select e.from_vid, e.gpath as graph_path from edge_finder_reverse e";
        return String.format(
                "select g.to_vid as asset_id, jsonb_agg(g.graph_path) as graph_path from (%s) as g group by g.to_vid",
                graphSql);
    }

    private String getAttributeKeys(AttributeProjection attributeProjection, String orgId) {
        String attributeKeys = "attributes";
        List<String> validProjections = new ArrayList<>();
        if (attributeProjection != null && attributeProjection.getAttributeProjection() != null) {
            validProjections =
                    attributeProjection.getAttributeProjection().stream()
                            .filter(s -> s != null && !s.isEmpty())
                            .collect(Collectors.toList());
        }

        if (validProjections.isEmpty()) {
            return attributeKeys;
        }

        List<String> replaceProjection = new ArrayList<>();
        boolean hasDefaultAttributes = false;
        for (String key : validProjections) {
            if (key.equals(Constants.ATTRIBUTES_DEFAULT_KEY)) {
                hasDefaultAttributes = true;
            } else {
                replaceProjection.add(
                        "'"
                                + key
                                + "', "
                                + "COALESCE(attributes->'"
                                + key
                                + "', attributes_lob->'"
                                + key
                                + "')");
            }
        }
        attributeKeys = "jsonb_build_object(" + String.join(", ", replaceProjection) + ")";
        if (hasDefaultAttributes) {
            attributeKeys += " || odo.attributes";
        }
        attributeKeys += " as attributes";

        return attributeKeys;
    }

    public String countByExpression(SearchAssetCondition condition, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("asset_id");
        sql.FROM(orgId + ".object_detail_origin");
        if (condition.isBaseConditionExist()) {
            sql.WHERE(condition.getBaseCondition());
        }
        if (condition.isWithGraph()) {
            String graphSql = getGraphSql(condition, orgId, false);
            return "select count(1) from ("
                    + sql
                    + ") as b inner join ("
                    + graphSql
                    + ") as g on b.asset_id = g.to_vid";
        }
        return "select count(1) from (" + sql + ") as b";
    }

    public String selectByExpression(
            SearchAssetCondition condition,
            List<Sorter> sorters,
            String orgId,
            boolean needAssetPath) {
        StringBuilder sqlBuilder = new StringBuilder();

        StringBuilder selectFieldsBuilder =
                new StringBuilder(
                        "select odo.asset_id, odo.unique_id, odo.asset_modified_time, odo.asset_created_time, count(*) over () as total_size");
        if (condition.isWithGraph()) {
            List<String> graphFromCondition =
                    new ArrayList<>(
                            condition.getSearchAssetWithGraphParam().getGraphFromCondition());
            // fromVid,toVid都没有 和 只有toVid 这两种pattern , assetPath在得到assetIds之后再取
            if (needAssetPath && (!graphFromCondition.isEmpty())) {
                selectFieldsBuilder.append(", jsonb_agg(g.graph_path) as graph_path");
            }
        }

        StringBuilder fromTablesBuilder = new StringBuilder();
        fromTablesBuilder.append(" from ").append(orgId).append(".object_detail_origin odo");
        if (condition.isWithGraph()) {
            String graphSql = getGraphSql(condition, orgId, needAssetPath);
            fromTablesBuilder
                    .append(" inner join (")
                    .append(graphSql)
                    .append(") as g on odo.asset_id = g.to_vid");
        }
        if (condition.getSearchAssetJoinModelParam() != null) {
            fromTablesBuilder
                    .append(" inner join ")
                    .append(orgId)
                    .append(".tbl_tag tt ON odo.model_id = tt.data_id");
        }

        StringBuilder whereBuilder = new StringBuilder(" where 1=1");
        if (condition.isBaseConditionExist()) {
            whereBuilder.append(" and ").append(condition.getBaseCondition());
        }

        if (condition.getSearchAssetJoinModelParam() != null) {
            SearchAssetJoinModelParam joinModelParam = condition.getSearchAssetJoinModelParam();
            if (joinModelParam.getTagsOpType() == JoinModelTagsOpType.EXISTS) {
                whereBuilder
                        .append(" and tt.tag_key = '")
                        .append(joinModelParam.getTagKey())
                        .append("'");
            } else { // EQ
                whereBuilder
                        .append(" and tt.tag_key = '")
                        .append(joinModelParam.getTagKey())
                        .append("' and tt.tag_value = '")
                        .append(joinModelParam.getTagVal())
                        .append("'");
            }
        }

        String groupByBuilder =
                " group by odo.asset_id, odo.unique_id, odo.asset_modified_time, odo.asset_created_time";

        StringBuilder sortBuilder = new StringBuilder();
        if (sorters != null) {
            sortBuilder.append(" order by ");
            for (int i = 0; i < sorters.size(); i++) {
                Sorter sorter = sorters.get(i);
                sortBuilder.append(sorter.getField()).append(" ").append(sorter.getOrder());
                if (i < sorters.size() - 1) {
                    sortBuilder.append(", ");
                }
            }
        }

        return sqlBuilder
                .append(selectFieldsBuilder)
                .append(fromTablesBuilder)
                .append(whereBuilder)
                .append(groupByBuilder)
                .append(sortBuilder)
                .toString();
    }

    public String selectByExpressionWithPagination(
            SearchAssetCondition condition,
            List<Sorter> sorters,
            int limit,
            int offset,
            boolean needAssetPath,
            String orgId) {
        return selectByExpression(condition, sorters, orgId, needAssetPath)
                + " limit "
                + limit
                + " offset "
                + offset;
    }

    private String getGraphSql(
            SearchAssetCondition searchAssetCondition, String orgId, boolean needAssetPath) {
        SearchAssetWithGraphParam searchAssetWithGraphParam =
                searchAssetCondition.getSearchAssetWithGraphParam();
        List<String> graphBaseCondition =
                new ArrayList<>(searchAssetWithGraphParam.getGraphBaseCondition());
        List<String> graphFromCondition =
                new ArrayList<>(searchAssetWithGraphParam.getGraphFromCondition());
        List<String> graphToCondition =
                new ArrayList<>(searchAssetWithGraphParam.getGraphToCondition());
        List<String> graphSubGraphIdCondition =
                new ArrayList<>(searchAssetWithGraphParam.getGraphSubGraphIdCondition());
        List<String> graphSubGraphIdConditionNotWithTable = new ArrayList<>();
        for (String condition : graphSubGraphIdCondition) {
            graphSubGraphIdConditionNotWithTable.add(
                    condition.replaceAll("g.sub_graph_id", "sub_graph_id"));
        }
        List<String> graphBaseConditionNotWithTable = new ArrayList<>();
        for (String condition : graphBaseCondition) {
            graphBaseConditionNotWithTable.add(
                    condition
                            .replaceAll("g.sub_graph_id", "sub_graph_id")
                            .replaceAll("e.edge_type_id", "edge_type_id"));
        }
        String graphSql;
        String getStartVidSql =
                String.format(
                        "select start_vid from %s.tbl_start_vid where (%s)",
                        orgId, String.join(" and ", graphSubGraphIdConditionNotWithTable));
        String toAssetPath = needAssetPath ? ",e.gpath as graph_path" : "";
        String fromAssetPath = needAssetPath ? ",e.gpath - -1 as graph_path" : "";
        String startVidPath = needAssetPath ? ", '[]'::jsonb as graph_path" : "";

        if (graphToCondition.isEmpty() && graphFromCondition.isEmpty()) {
            graphSql =
                    String.format(
                            "select distinct to_vid from %s.tbl_edge where (%s) union select from_vid from %s.tbl_edge where (%s) union %s",
                            orgId,
                            String.join(" and ", graphBaseConditionNotWithTable),
                            orgId,
                            String.join(" and ", graphBaseConditionNotWithTable),
                            getStartVidSql);
        } else if (!graphToCondition.isEmpty() && !graphFromCondition.isEmpty()) {
            graphToCondition.addAll(graphBaseCondition);
            graphFromCondition.addAll(graphBaseCondition);
            graphSql =
                    "with recursive edge_finder_reverse as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as reverse_steps, "
                            + gpathSelectField(true)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + String.join(" and ", graphToCondition)
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.reverse_steps+1 as reverse_steps, "
                            + gpathSelectField(false)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder_reverse f where f.from_vid = e.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.reverse_steps < "
                            + searchAssetWithGraphParam.getMaxStep()
                            + "), edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.reverse_steps, 1 as steps, "
                            + gpathSelectField(true)
                            + " from edge_finder_reverse e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + String.join(" and ", graphFromCondition)
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.reverse_steps, f.steps+1 as steps, "
                            + gpathSelectField(false)
                            + " from edge_finder f, edge_finder_reverse e where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.steps < "
                            + searchAssetWithGraphParam.getMaxStep()
                            + ")";
            if (searchAssetWithGraphParam.isExcludeFromVid()) {
                graphSql +=
                        String.format(
                                "select e.to_vid %s from edge_finder e union select e.from_vid %s from edge_finder e where e.steps!=1",
                                toAssetPath, fromAssetPath);
            } else if (searchAssetWithGraphParam.isExcludeToVid()) {
                graphSql +=
                        String.format(
                                "select e.to_vid %s from edge_finder e where e.reverse_steps!= 1 union select e.from_vid %s from edge_finder e",
                                toAssetPath, fromAssetPath);
            } else {
                graphSql +=
                        String.format(
                                "select e.to_vid %s from edge_finder e union select e.from_vid %s from edge_finder e",
                                toAssetPath, fromAssetPath);
            }
        } else if (!graphToCondition.isEmpty()) {
            graphToCondition.addAll(graphBaseCondition);
            graphSql =
                    "with recursive edge_finder_reverse as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps, "
                            + gpathSelectField(true)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + String.join(" and ", graphToCondition)
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps, "
                            + gpathSelectField(false)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder_reverse f where e.to_vid = f.from_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.steps < "
                            + searchAssetWithGraphParam.getMaxStep()
                            + ")";
            if (searchAssetWithGraphParam.isExcludeToVid()) {
                graphSql +=
                        "select e.to_vid from edge_finder_reverse e where e.steps!=1 union select e.from_vid from edge_finder_reverse e";
            } else {
                graphSql +=
                        "select e.to_vid from edge_finder_reverse e union select e.from_vid from edge_finder_reverse e";
            }
        } else {
            // 只有from pattern, 根据from条件筛选start_vid表中的起始点, 并入递归查询结果集
            List<String> startVidCondition = new ArrayList<>();
            for (String condition : graphFromCondition) {
                startVidCondition.add(condition.replaceFirst("e.from_vid", "start_vid"));
            }
            graphFromCondition.addAll(graphBaseCondition);
            graphSql =
                    "with recursive edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps, "
                            + gpathSelectField(true)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + String.join(" and ", graphFromCondition)
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps, "
                            + gpathSelectField(false)
                            + " from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder f where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.steps < "
                            + searchAssetWithGraphParam.getMaxStep()
                            + ")";

            if (searchAssetWithGraphParam.isExcludeFromVid()) {
                graphSql +=
                        String.format(
                                "select e.to_vid %s from edge_finder e union select e.from_vid %s from edge_finder e where e.steps!=1",
                                toAssetPath, fromAssetPath);
            } else {
                graphSql +=
                        String.format(
                                "select e.to_vid %s from edge_finder e union select e.from_vid %s from edge_finder e union select start_vid %s from %s.tbl_start_vid where (%s) and (%s)",
                                toAssetPath,
                                fromAssetPath,
                                startVidPath,
                                orgId,
                                String.join(" and ", startVidCondition),
                                String.join(" and ", graphSubGraphIdConditionNotWithTable));
            }
        }

        return graphSql;
    }

    private String gpathSelectField(boolean isRecursiveInit) {
        return isRecursiveInit
                ? "concat('[{\"fromAssetId\":\"', e.from_vid, '\", \"toAssetId\":\"', e.to_vid, '\", \"subGraphId\":\"', e.sub_graph_id, '\", \"edgeType\":\"', e.edge_type_id, '\", \"order\":\"', e.prop_value->>'order', '\"}]')::jsonb gpath"
                : "f.gpath || concat('{\"fromAssetId\":\"', e.from_vid, '\", \"toAssetId\":\"', e.to_vid, '\", \"subGraphId\":\"', e.sub_graph_id, '\", \"edgeType\":\"', e.edge_type_id, '\", \"order\":\"', e.prop_value->>'order', '\"}')::jsonb gpath";
    }

    public String selectByExpressionWithGroupBy(
            SearchAssetCondition condition, GroupBy groupBy, String orgId) {
        Map<String, GroupByKeyEnum> groupByKeyEnumMap = new HashMap<>(8);
        for (GroupByKeyEnum keyEnum : GroupByKeyEnum.values()) {
            groupByKeyEnumMap.put(keyEnum.getSupportField().toLowerCase(), keyEnum);
        }
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        List<String> fields = new ArrayList<>();
        List<String> groupByFields = new ArrayList<>();
        for (String groupByKey : groupBy.getGroupByKey()) {
            if (groupByKey.contains(Constants.DOT)) {
                int dotIndex = groupByKey.indexOf(Constants.DOT);
                String field = groupByKey.substring(0, dotIndex);
                String key = groupByKey.substring(dotIndex + 1);
                fields.add(
                        groupByKeyEnumMap.get(field.toLowerCase()).getSqlField()
                                + " -> '"
                                + key
                                + "' as \""
                                + groupByKey
                                + "\"");
                groupByFields.add("\"" + groupByKey + "\"");
            } else {
                fields.add(
                        groupByKeyEnumMap.get(groupByKey.toLowerCase()).getSqlField()
                                + " as "
                                + groupByKey);
                groupByFields.add(groupByKey);
            }
        }
        sql.SELECT(String.join(", ", fields));
        sql.FROM(orgId + ".object_detail_origin");
        sql.WHERE(
                " asset_id in (select asset_id from ("
                        + selectByExpression(condition, null, orgId, false)
                        + ") as temp)");
        sql.GROUP_BY(String.join(", ", groupByFields));
        return sql.toString();
    }

    public String selectByAssetIds(
            List<String> assetIds, AttributeProjection attributeProjection, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("odo.asset_id AS asset_id");
        sql.SELECT("system_id");
        sql.SELECT("model_id");
        sql.SELECT("org_id");
        sql.SELECT("odo.asset_display_name AS asset_display_name");
        sql.SELECT("description");
        sql.SELECT("asset_tags");
        sql.SELECT(getAttributeKeys(attributeProjection, orgId));
        sql.SELECT("unique_id");
        sql.SELECT("asset_created_time");
        sql.SELECT("asset_created_user");
        sql.SELECT("asset_modified_time");
        sql.SELECT("asset_modified_user");
        sql.FROM(orgId + ".object_detail_origin odo");
        sql.INNER_JOIN(orgId + ".tbl_bo bo ON odo.asset_id = bo.asset_id");

        StringBuilder expression = new StringBuilder("odo.asset_id in (");
        for (int i = 0; i < assetIds.size(); i++) {
            String assetId = assetIds.get(i);
            expression.append("'").append(assetId).append("'");
            if (i < assetIds.size() - 1) {
                expression.append(", ");
            } else {
                expression.append(")");
            }
        }
        sql.WHERE(expression.toString());
        return sql.toString();
    }

    public String selectByAssetIdTest(List<String> assetIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("asset_id");
        sql.SELECT("model_id");
        sql.SELECT("org_id");
        sql.SELECT("asset_display_name");
        sql.SELECT("description");
        sql.SELECT("asset_tags");
        sql.SELECT("attributes");
        sql.SELECT("unique_id");
        sql.SELECT("asset_created_time");
        sql.SELECT("asset_created_user");
        sql.SELECT("asset_modified_time");
        sql.SELECT("asset_modified_user");
        sql.SELECT(
                "jsonb_path_query_array(attributes, '$.distances[*].towards') as \"test.distances\"");
        //        sql.SELECT("jsonb_path_query(attributes, '$.distances[1]') as
        // \"bigFieldMap[1]\"");
        sql.FROM(orgId + ".object_detail_origin");

        StringBuilder expression = new StringBuilder("asset_id in (");
        for (int i = 0; i < assetIds.size(); i++) {
            String assetId = assetIds.get(i);
            expression.append("'").append(assetId).append("'");
            if (i < assetIds.size() - 1) {
                expression.append(", ");
            } else {
                expression.append(")");
            }
        }
        sql.WHERE(expression.toString());
        return sql.toString();
    }

    public String insertToPgAsset(List<TblPgAsset> pgAssets, String orgId) {
        StringBuilder placeholders = new StringBuilder();
        Set<String> assetIdSet = new HashSet<>();
        Set<String> modelIdSet = new HashSet<>();
        for (TblPgAsset pgAsset : pgAssets) {
            assetIdSet.add(pgAsset.getAssetId());
            modelIdSet.add(pgAsset.getModelId());
            placeholders
                    .append("when asset_id='")
                    .append(pgAsset.getAssetId())
                    .append("' and model_id='")
                    .append(pgAsset.getModelId())
                    .append("' then (COALESCE(attributes, '{}'::jsonb) || '")
                    .append(pgAsset.getAttributes())
                    .append("') ");
        }
        List<String> replaceAssetIds = new ArrayList<>();
        List<String> replaceModelIds = new ArrayList<>();
        for (String assetId : assetIdSet) {
            replaceAssetIds.add("'" + assetId + "'");
        }
        for (String modelId : modelIdSet) {
            replaceModelIds.add("'" + modelId + "'");
        }
        placeholders
                .append("else attributes end where asset_id in (")
                .append(String.join(",", replaceAssetIds))
                .append(")")
                .append(" and model_id in (")
                .append(String.join(",", replaceModelIds))
                .append(")");
        return "update " + orgId + ".object_detail_origin set attributes = case " + placeholders;
    }
}
