package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
public class TblBOModelSqlProvider {
    public String selectAssetTotalCount(String schemeName, String modelId) {
        SQL sql =
                new SQL() {
                    {
                        SELECT("count(tbgr.asset_id)");
                        FROM(schemeName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_bo_group_relation tbgr on tbm.model_id = tbgr.group_id");
                        INNER_JOIN(schemeName + ".tbl_bo tb on tbgr.asset_id = tb.asset_id");
                        WHERE("tbm.model_id = '" + modelId + "'");
                    }
                };

        return sql.toString();
    }

    public String queryAsset(String schemeName, String modelId, int limit, int offset) {
        SQL sql =
                new SQL() {
                    {
                        SELECT("tbm.model_id, tb.asset_id, tb.system_id");
                        FROM(schemeName + ".tbl_bo_model tbm");
                        INNER_JOIN(
                                schemeName
                                        + ".tbl_bo_group_relation tbgr on tbm.model_id = tbgr.group_id");
                        INNER_JOIN(schemeName + ".tbl_bo tb on tbgr.asset_id = tb.asset_id");
                        WHERE("tbm.model_id = '" + modelId + "'");
                        LIMIT(limit);
                        OFFSET(offset);
                    }
                };

        return sql.toString();
    }
}
