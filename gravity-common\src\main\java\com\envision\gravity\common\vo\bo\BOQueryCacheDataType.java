package com.envision.gravity.common.vo.bo;

import com.envision.gravity.common.enums.PrefDataType;

public enum BOQueryCacheDataType {
    LONG("LONG"),
    DOUBLE("DOUBLE"),
    STRING("STRING");

    private final String name;

    BOQueryCacheDataType(String name) {
        this.name = name;
    }

    public static BOQueryCacheDataType fromPrefDataType(PrefDataType prefDataType) {
        switch (prefDataType) {
            case BOOLEAN:
            case INTEGER:
            case LONG:
                return LONG;
            case FLOAT:
            case DOUBLE:
            case DURATION:
                return DOUBLE;
            case STRING:
            case TIME:
            case DATE:
            case DATETIME:
            case OBJECT:
            case MAP:
            case ARRAY:
            case ENUM:
            default:
                return STRING;
        }
    }
}
