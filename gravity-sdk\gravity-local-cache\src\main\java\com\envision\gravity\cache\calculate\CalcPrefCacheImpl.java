package com.envision.gravity.cache.calculate;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.InvalidCalcMetaType;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.util.GTCommonUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcPrefCacheImpl implements CalcPrefCache {

    private static final Logger logger = LoggerFactory.getLogger(CalcPrefCacheImpl.class);

    private static volatile CalcPrefCacheImpl instance;
    private static final ReadWriteLock instanceLock = new ReentrantReadWriteLock();

    // orgId => <compId + prefId, modelId => <srcCategoryId, exprIndex>>
    private final Map<String, ConcurrentHashMap<String, Map<String, Map<String, Integer>>>>
            targetCacheByOu = new ConcurrentHashMap<>();

    // orgId => <exprIndex, CalcPropertyMeta>
    private final Map<String, ConcurrentHashMap<Integer, CalcPropertyMeta>> targetIndexCacheByOu =
            new ConcurrentHashMap<>();

    // orgId => <srcPrefName, srcModelId => list of related target pref>
    private final Map<String, ConcurrentHashMap<String, Map<String, Set<String>>>> srcCacheByOu =
            new ConcurrentHashMap<>();

    // Memory size tracking
    private final AtomicLong targetCacheMemorySize = new AtomicLong(0);

    private final AtomicLong srcCacheMemorySize = new AtomicLong(0);

    public static CalcPrefCacheImpl getInstance() {
        if (instance == null) {
            instanceLock.writeLock().lock();
            try {
                if (instance == null) {
                    instance = new CalcPrefCacheImpl();
                }
            } finally {
                instanceLock.writeLock().unlock();
            }
        }
        return instance;
    }

    private CalcPrefCacheImpl() {}

    private ConcurrentHashMap<String, Map<String, Map<String, Integer>>> getTargetCacheByOu(
            String orgId, boolean createIfAbsent) {
        if (createIfAbsent) {
            return targetCacheByOu.computeIfAbsent(orgId, k -> new ConcurrentHashMap<>());
        } else {
            ConcurrentHashMap<String, Map<String, Map<String, Integer>>> res =
                    targetCacheByOu.get(orgId);
            return res != null ? res : new ConcurrentHashMap<>();
        }
    }

    private ConcurrentHashMap<Integer, CalcPropertyMeta> getTargetIndexCacheByOu(String orgId) {
        return targetIndexCacheByOu.computeIfAbsent(orgId, k -> new ConcurrentHashMap<>());
    }

    private Map<String, Map<String, Set<String>>> getSrcCacheByOu(
            String orgId, boolean createIfAbsent) {
        Map<String, Map<String, Set<String>>> res = srcCacheByOu.get(orgId);
        if (res != null) {
            return res;
        }

        return createIfAbsent
                ? srcCacheByOu.computeIfAbsent(orgId, k -> new ConcurrentHashMap<>())
                : new ConcurrentHashMap<>();
    }

    private Map<String, Set<String>> getSrcCacheByPrefName(
            String orgId, String srcPrefName, boolean createIfAbsent) {
        Map<String, Map<String, Set<String>>> ouCache = getSrcCacheByOu(orgId, createIfAbsent);
        if (ouCache.get(srcPrefName) != null) {
            return ouCache.get(srcPrefName);
        } else {
            return ouCache.computeIfAbsent(srcPrefName, k -> new HashMap<>());
        }
    }

    private Set<String> getSrcCacheByModelPrefName(
            String orgId, String srcPrefName, String srcModelId, boolean createIfAbsent) {
        Map<String, Set<String>> prefCache =
                getSrcCacheByPrefName(orgId, srcPrefName, createIfAbsent);
        if (prefCache.get(srcModelId) != null) {
            return prefCache.get(srcModelId);
        } else {
            return prefCache.computeIfAbsent(srcModelId, k -> new HashSet<>());
        }
    }

    @Override
    public Map<SrcPrefItem, Set<PropertyId>> batchGetTargetBySrcPref(
            String orgId, List<SrcPrefItem> srcPrefItems) {
        return srcPrefItems.stream()
                .map(
                        srcPrefItem -> {
                            Set<PropertyId> targetPrefs =
                                    getTargetBySrcPref(
                                            orgId,
                                            srcPrefItem.getPrefName(),
                                            srcPrefItem.getModelId());
                            return new AbstractMap.SimpleEntry<>(srcPrefItem, targetPrefs);
                        })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public Set<PropertyId> getTargetBySrcPref(String orgId, String srcPrefName, String srcModelId) {
        Set<String> targetPrefs = getSrcCacheByModelPrefName(orgId, srcPrefName, srcModelId, true);
        return targetPrefs.stream()
                .map(
                        tp -> {
                            String[] parts = splitTargetPrefKey(tp);
                            return new PropertyId(parts[0], parts[1], parts[2]);
                        })
                .collect(Collectors.toSet());
    }

    @Override
    public Set<PropertyId> getTargetBySrcModelId(String orgId, String srcModelId) {
        Map<String, Map<String, Set<String>>> ouCache = getSrcCacheByOu(orgId, true);
        Set<PropertyId> relatedTargets = new HashSet<>();
        for (Map.Entry<String, Map<String, Set<String>>> srcPrefNameEntry : ouCache.entrySet()) {
            for (Map.Entry<String, Set<String>> srcModelIdEntry :
                    srcPrefNameEntry.getValue().entrySet()) {
                if (!srcModelId.equals(srcModelIdEntry.getKey())) {
                    continue;
                }
                srcModelIdEntry
                        .getValue()
                        .forEach(
                                targetPrefKey -> {
                                    String[] parts = splitTargetPrefKey(targetPrefKey);
                                    relatedTargets.add(
                                            new PropertyId(parts[0], parts[1], parts[2]));
                                });
            }
        }
        return relatedTargets;
    }

    @Override
    public void batchUpdateSrcPref(
            String orgId, String srcPrefName, Map<String, Set<PropertyId>> data) {
        for (Map.Entry<String, Set<PropertyId>> entry : data.entrySet()) {
            for (PropertyId tp : entry.getValue()) {
                updateSrcPref(orgId, srcPrefName, entry.getKey(), tp);
            }
        }
    }

    @Override
    public void updateSrcPref(
            String orgId, String srcPrefName, String srcModelId, PropertyId data) {
        Set<String> targetPrefs = getSrcCacheByModelPrefName(orgId, srcPrefName, srcModelId, true);
        String targetPrefKey = getTargetPrefKey(data).intern();
        if (targetPrefs.isEmpty() || !targetPrefs.contains(targetPrefKey)) {
            // Record memory size for new target pref key
            srcCacheMemorySize.addAndGet(CalcPrefCacheUtils.estimateSrcCacheSize(targetPrefKey));
        }
        targetPrefs.add(targetPrefKey);
    }

    @Override
    public void deleteBySrcModelId(String orgId, String srcModelId) {
        if (StringUtils.isEmpty(srcModelId)) {
            logger.error("Source model is required ...");
            return;
        }

        Map<String, String> srcModelId2Category =
                ModelMetaQueryHandler.getInstance()
                        .getModelCategory(orgId, Collections.singleton(srcModelId));
        if (GTCommonUtils.isEmpty(srcModelId2Category)
                || StringUtils.isEmpty(srcModelId2Category.get(srcModelId))) {
            logger.error("Category not found by modelId {}", srcModelId);
            return;
        }

        String srcCategoryId = srcModelId2Category.get(srcModelId);

        // </p> NOTICE: when calculate, can't get source pref with specified modelId, process as
        // exception case
        // </p> srcPrefName => <srcModelId, set of target properties>
        Map<String, Map<String, Set<String>>> srcOuCache = getSrcCacheByOu(orgId, false);
        for (Map.Entry<String, Map<String, Set<String>>> srcPrefNameEntry : srcOuCache.entrySet()) {
            for (Map.Entry<String, Set<String>> srcModelIdEntry :
                    srcPrefNameEntry.getValue().entrySet()) {
                String cacheSrcModelId = srcModelIdEntry.getKey();
                if (!srcModelId.equals(cacheSrcModelId)) {
                    continue;
                }

                Set<String> relatedTargetProperties = srcModelIdEntry.getValue();
                for (String targetPrefKey : relatedTargetProperties) {
                    String[] parts = splitTargetPrefKey(targetPrefKey);
                    String targetModelId = parts[0];
                    String targetCompId = parts[1];
                    String targetPrefId = parts[2];

                    Optional<Integer> targetMetaIdxInfo =
                            getTargetPrefIndex(
                                    orgId,
                                    targetModelId,
                                    targetCompId,
                                    targetPrefId,
                                    srcCategoryId);
                    if (!targetMetaIdxInfo.isPresent()) {
                        continue;
                    }

                    CalcPropertyMeta targetMeta =
                            getTargetIndexCacheByOu(orgId).get(targetMetaIdxInfo.get());
                    targetMeta.setValidExpr(false);
                    targetMeta.setInvalidType(InvalidCalcMetaType.SRC_MODEL_DELETED.getCode());

                    // update idx cache
                    getTargetIndexCacheByOu(orgId).put(targetMetaIdxInfo.get(), targetMeta);
                    logger.warn(
                            "WARN: orgId=[{}], mark target property invalid, because source model [{}] is deleted, "
                                    + "targetModelId=[{}], targetCompId=[{}], targetPrefId=[{}], srcCategoryId=[{}]",
                            orgId,
                            srcModelId,
                            targetModelId,
                            targetCompId,
                            targetPrefId,
                            srcCategoryId);
                }

                srcPrefNameEntry.getValue().remove(srcModelId);
                logger.warn(
                        "WARN: Source property [{}] => modelId [{}] entry deleted, because this model deleted",
                        srcPrefNameEntry.getKey(),
                        cacheSrcModelId);
            }
        }
    }

    @Override
    public Optional<CalcPropertyMeta> getByTargetPref(
            String orgId, String compId, String prefId, String srcCategoryId) {
        String key = getMetaKey(compId, prefId);
        Map<String, Map<String, Integer>> model2PrefExprIndex =
                getTargetCacheByOu(orgId, true).get(key);
        if (model2PrefExprIndex == null) {
            return Optional.empty();
        }

        // Get any model's pref expression index
        Map.Entry<String, Map<String, Integer>> anyModelEntry =
                model2PrefExprIndex.entrySet().iterator().next();
        Integer exprIndex = anyModelEntry.getValue().get(srcCategoryId);
        if (exprIndex == null) {
            return Optional.empty();
        }

        return Optional.of(getTargetIndexCacheByOu(orgId).get(exprIndex));
    }

    private Optional<Integer> getTargetPrefIndex(
            String orgId, String modelId, String compId, String prefId, String srcCategoryId) {
        String key = getMetaKey(compId, prefId);
        Map<String, Map<String, Integer>> model2PrefExprIndex =
                getTargetCacheByOu(orgId, true).get(key);
        if (model2PrefExprIndex == null) {
            logger.warn(
                    "Target property cache record not found: orgId: {}, targetCompId: {}, targetPrefId: {}",
                    orgId,
                    compId,
                    prefId);
            return Optional.empty();
        }

        Map<String, Integer> srcCategory2ExprIndex = model2PrefExprIndex.get(modelId);
        if (srcCategory2ExprIndex == null) {
            logger.warn(
                    "Target model cache record not found: orgId: {}, targetModelId: {}, targetCompId: {}, targetPrefId: {}",
                    orgId,
                    modelId,
                    compId,
                    prefId);
            return Optional.empty();
        }

        Integer exprIndex = srcCategory2ExprIndex.get(srcCategoryId);
        if (exprIndex == null) {
            logger.warn(
                    "Target src category cache record not found: orgId: {}, targetModelId: {}, targetCompId: {}, targetPrefId: {}, srcCategory: {}",
                    orgId,
                    modelId,
                    compId,
                    prefId,
                    srcCategoryId);
            return Optional.empty();
        }
        return Optional.of(exprIndex);
    }

    @Override
    public Optional<CalcPropertyMeta> getByTargetPref(
            String orgId, String modelId, String compId, String prefId, String srcCategoryId) {
        Optional<Integer> exprIndexInfo =
                getTargetPrefIndex(orgId, modelId, compId, prefId, srcCategoryId);
        return exprIndexInfo.map(idx -> getTargetIndexCacheByOu(orgId).get(idx));
    }

    @Override
    public List<PropertyId> getByTargetPrefId(String orgId, String targetPrefId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, true);

        List<PropertyId> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            String[] parts = splitTargetPrefKey(targetEntry.getKey());
            String compId = parts[0];
            String prefId = parts[1];
            if (!prefId.equals(targetPrefId)) {
                continue;
            }
            result.add(new PropertyId(compId, prefId));
        }
        return result;
    }

    @Override
    public List<PropertyId> getByTargetCompId(String orgId, String targetCompId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, true);

        List<PropertyId> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            String[] parts = splitTargetPrefKey(targetEntry.getKey());
            String compId = parts[0];
            String prefId = parts[1];
            if (!compId.equals(targetCompId)) {
                continue;
            }
            result.add(new PropertyId(compId, prefId));
        }
        return result;
    }

    @Override
    public void updateTargetPref(
            String orgId,
            String compId,
            String prefId,
            Map<String, Map<String, CalcPropertyMeta>> data) {
        String prefKey = getMetaKey(compId, prefId);
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgCache =
                getTargetCacheByOu(orgId, true);
        ConcurrentHashMap<Integer, CalcPropertyMeta> indexCache = getTargetIndexCacheByOu(orgId);

        // Remove old entries and update memory size
        // Remove target property's all models cache entry
        Map<String, Map<String, Integer>> oldVal = orgCache.remove(prefKey);
        if (oldVal != null) {
            for (Map<String, Integer> categoryMap : oldVal.values()) {
                for (Integer index : categoryMap.values()) {
                    CalcPropertyMeta oldMeta = indexCache.remove(index);
                    if (oldMeta != null) {
                        targetCacheMemorySize.addAndGet(
                                -CalcPrefCacheUtils.estimateTargetCacheSize(oldMeta));
                    }
                }
            }
        }

        // Add new entries
        Map<String, Map<String, Integer>> newVal = new HashMap<>(data.size());
        for (Map.Entry<String, Map<String, CalcPropertyMeta>> modelEntry : data.entrySet()) {
            String modelId = modelEntry.getKey();
            Map<String, Integer> category2Index = new HashMap<>();

            for (Map.Entry<String, CalcPropertyMeta> exprEntry : modelEntry.getValue().entrySet()) {
                String categoryId = exprEntry.getKey();
                CalcPropertyMeta meta = exprEntry.getValue();
                Integer index = meta.hashCode();

                indexCache.put(index, meta);
                category2Index.put(categoryId, index);
                targetCacheMemorySize.addAndGet(CalcPrefCacheUtils.estimateTargetCacheSize(meta));
            }
            newVal.put(modelId, category2Index);
        }

        orgCache.put(prefKey, newVal);
    }

    @Override
    public void deleteByTargetModelId(String orgId, String targetModelId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, false);

        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            Map<String, Map<String, Integer>> targetModel2PrefExprIndex = targetEntry.getValue();
            // Remove the entry for the target model
            // Can't remove index cache, because other model using the same index
            Map<String, Integer> oldVal = targetModel2PrefExprIndex.remove(targetModelId);
            if (oldVal != null) {
                logger.warn(
                        "WARN: Target property [{}] => modelId [{}] entry deleted, because this model deleted",
                        targetEntry.getKey(),
                        targetModelId);
            }
        }
    }

    @Override
    public void deleteByTargetCompId(String orgId, String targetCompId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, false);

        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            String[] parts = splitTargetPrefKey(targetEntry.getKey());
            String compId = parts[0];
            String prefId = parts[1];

            if (!compId.equals(targetCompId)) {
                continue;
            }

            for (Map.Entry<String, Map<String, Integer>> targetModelEntry :
                    targetEntry.getValue().entrySet()) {
                String targetModelId = targetModelEntry.getKey();
                for (Map.Entry<String, Integer> exprEntry :
                        targetModelEntry.getValue().entrySet()) {
                    String srcCategoryId = exprEntry.getKey();
                    delete(orgId, targetCompId, prefId, srcCategoryId);
                }
            }
        }
    }

    @Override
    public void deleteByTargetModelIdAndCompId(
            String orgId, String targetModelId, String targetCompId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, false);

        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            String[] parts = splitTargetPrefKey(targetEntry.getKey());
            String compId = parts[0];
            String prefId = parts[1];

            if (!compId.equals(targetCompId)) {
                continue;
            }

            for (Map.Entry<String, Map<String, Integer>> targetModelEntry :
                    targetEntry.getValue().entrySet()) {
                String modelId = targetModelEntry.getKey();
                if (!modelId.equals(targetModelId)) {
                    continue;
                }

                for (Map.Entry<String, Integer> exprEntry :
                        targetModelEntry.getValue().entrySet()) {
                    String srcCategoryId = exprEntry.getKey();
                    delete(orgId, targetCompId, prefId, srcCategoryId);
                }
            }
        }
    }

    @Override
    public void deleteByTargetPrefId(String orgId, String targetPrefId) {
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgTargetCache =
                getTargetCacheByOu(orgId, false);

        for (Map.Entry<String, Map<String, Map<String, Integer>>> targetEntry :
                orgTargetCache.entrySet()) {
            String[] parts = splitTargetPrefKey(targetEntry.getKey());
            String compId = parts[0];
            String prefId = parts[1];

            if (!prefId.equals(targetPrefId)) {
                continue;
            }

            for (Map.Entry<String, Map<String, Integer>> targetModelEntry :
                    targetEntry.getValue().entrySet()) {
                String targetModelId = targetModelEntry.getKey();
                for (Map.Entry<String, Integer> exprEntry :
                        targetModelEntry.getValue().entrySet()) {
                    String srcCategoryId = exprEntry.getKey();
                    delete(orgId, compId, targetPrefId, srcCategoryId);
                }
            }
        }
    }

    @Override
    public void delete(String orgId, String compId, String prefId, String srcCategoryId) {
        String prefKey = getMetaKey(compId, prefId);
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> orgCache =
                getTargetCacheByOu(orgId, false);
        // targetModelId => <srcCategoryId, exprIndex>
        Map<String, Map<String, Integer>> oldVal = orgCache.get(prefKey);
        if (oldVal == null) {
            return;
        }

        // Each model need to remove the specific srcCategoryId expr info
        for (Map.Entry<String, Map<String, Integer>> eachModelEntry : oldVal.entrySet()) {
            String targetModelId = eachModelEntry.getKey();
            Map<String, Integer> srcCategory2ExprIndex = eachModelEntry.getValue();
            Integer exprIndex = srcCategory2ExprIndex.remove(srcCategoryId);
            if (exprIndex == null) {
                continue;
            }

            ConcurrentHashMap<Integer, CalcPropertyMeta> indexCache =
                    getTargetIndexCacheByOu(orgId);
            CalcPropertyMeta meta = indexCache.remove(exprIndex);
            if (meta == null) {
                continue;
            }

            targetCacheMemorySize.addAndGet(-CalcPrefCacheUtils.estimateTargetCacheSize(meta));

            // Sync to delete src cache
            for (SrcPrefItem srcPrefItem : meta.getSrcPrefItems()) {
                PropertyId targetPref = new PropertyId(targetModelId, compId, prefId);
                deleteSrcCache(
                        orgId, srcPrefItem.getPrefName(), srcPrefItem.getModelId(), targetPref);
            }
        }

        logger.info(
                "Deleted cache, compId: {}, prefId: {}, srcCategory: {}",
                compId,
                prefId,
                srcCategoryId);
    }

    private void deleteSrcCache(
            String orgId, String srcPrefName, String srcModelId, PropertyId data) {
        Set<String> targetPrefs = getSrcCacheByModelPrefName(orgId, srcPrefName, srcModelId, false);
        String targetPrefKey = getTargetPrefKey(data);
        boolean isDeleted = targetPrefs.remove(targetPrefKey);
        if (isDeleted) {
            // Record memory size reduction
            srcCacheMemorySize.addAndGet(-CalcPrefCacheUtils.estimateSrcCacheSize(targetPrefKey));
        }
    }

    @Override
    public Map<String, Long> getApproximateMemorySize() {
        Map<String, Long> result = new HashMap<>();
        result.put("targetCacheSize", targetCacheMemorySize.get());
        result.put("srcCacheSize", srcCacheMemorySize.get());
        return result;
    }

    /**
     * 格式化输出缓存内容
     *
     * @param orgId 组织ID
     * @return 格式化的缓存内容字符串
     */
    public void formatCacheContent(String orgId) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Cache Content for Org: ").append(orgId).append(" ===\n\n");

        // Format Target Cache
        sb.append("--- Target Cache ---\n");
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> targetCache =
                targetCacheByOu.get(orgId);
        if (targetCache != null) {
            for (Map.Entry<String, Map<String, Map<String, Integer>>> entry :
                    targetCache.entrySet()) {
                String[] keyParts = splitMetaKey(entry.getKey());
                sb.append("CompId: ")
                        .append(keyParts[0])
                        .append(", PrefId: ")
                        .append(keyParts[1])
                        .append("\n");

                Map<String, Map<String, Integer>> modelMap = entry.getValue();
                for (Map.Entry<String, Map<String, Integer>> modelEntry : modelMap.entrySet()) {
                    sb.append("  ModelId: ").append(modelEntry.getKey()).append("\n");
                    Map<String, Integer> categoryMap = modelEntry.getValue();
                    for (Map.Entry<String, Integer> categoryEntry : categoryMap.entrySet()) {
                        Integer exprIndex = categoryEntry.getValue();
                        CalcPropertyMeta meta = targetIndexCacheByOu.get(orgId).get(exprIndex);
                        if (meta != null) {
                            sb.append("    CategoryId: ")
                                    .append(categoryEntry.getKey())
                                    .append(", Expression: ")
                                    .append(meta.getExpression())
                                    .append("\n");
                        }
                    }
                }
                sb.append("\n");
            }
        } else {
            sb.append("No target cache entries found\n");
        }

        // Format Source Cache
        sb.append("\n--- Source Cache ---\n");
        ConcurrentHashMap<String, Map<String, Set<String>>> srcCache = srcCacheByOu.get(orgId);
        if (srcCache != null) {
            for (Map.Entry<String, Map<String, Set<String>>> entry : srcCache.entrySet()) {
                sb.append("Source Pref: ").append(entry.getKey()).append("\n");

                Map<String, Set<String>> modelMap = entry.getValue();
                for (Map.Entry<String, Set<String>> modelEntry : modelMap.entrySet()) {
                    sb.append("  ModelId: ").append(modelEntry.getKey()).append("\n");
                    Set<String> targetPrefs = modelEntry.getValue();
                    for (String targetPref : targetPrefs) {
                        String[] parts = splitTargetPrefKey(targetPref);
                        sb.append("    Target: ModelId=")
                                .append(parts[0])
                                .append(", CompId=")
                                .append(parts[1])
                                .append(", PrefId=")
                                .append(parts[2])
                                .append("\n");
                    }
                }
                sb.append("\n");
            }
        } else {
            sb.append("No source cache entries found\n");
        }

        // Add memory size information
        sb.append("\n--- Memory Usage ---\n");
        Map<String, Long> memorySizes = getApproximateMemorySize();
        sb.append("Target Cache Size: ")
                .append(memorySizes.get("targetCacheSize"))
                .append(" bytes\n");
        sb.append("Source Cache Size: ").append(memorySizes.get("srcCacheSize")).append(" bytes\n");

        logger.info(sb.toString());
    }

    private String getMetaKey(String compId, String prefId) {
        return compId + "##" + prefId;
    }

    private String[] splitMetaKey(String key) {
        return key.split("##");
    }

    private String getTargetPrefKey(PropertyId tp) {
        return tp.getModelId() + "##" + tp.getCompId() + "##" + tp.getPrefId();
    }

    private String[] splitTargetPrefKey(String key) {
        return key.split("##");
    }

    // ===================================================================================
    // Deprecated methods
    // ===================================================================================
    public void deleteBySrcModelIdOld(String orgId, String srcModelId) {
        if (StringUtils.isEmpty(srcModelId)) {
            logger.error("Source model is required ...");
            return;
        }

        Set<PropertyId> targetPrefs = getTargetBySrcModelId(orgId, srcModelId);
        ConcurrentHashMap<String, Map<String, Map<String, Integer>>> targetOuCache =
                getTargetCacheByOu(orgId, false);
        Map<PropertyId, Integer> toRefreshTargetPrefIndexExprs = new HashMap<>();

        for (PropertyId tp : targetPrefs) {
            Map<String, Map<String, Integer>> targetModel2PrefExprIndex =
                    targetOuCache.get(getMetaKey(tp.getCompId(), tp.getPrefId()));
            // Skip if target model entry is empty
            if (targetModel2PrefExprIndex == null) {
                continue;
            }

            for (Map.Entry<String, Map<String, Integer>> targetModelEntry :
                    targetModel2PrefExprIndex.entrySet()) {
                for (Map.Entry<String, Integer> srcCategoryEntry :
                        targetModelEntry.getValue().entrySet()) {
                    Integer exprIndex = srcCategoryEntry.getValue();
                    if (exprIndex == null) {
                        continue;
                    }

                    CalcPropertyMeta meta = getTargetIndexCacheByOu(orgId).get(exprIndex);
                    if (meta == null) {
                        continue;
                    }

                    for (SrcPrefItem srcPrefItem : meta.getSrcPrefItems()) {
                        String cacheSrcModelId = srcPrefItem.getModelId();
                        if (!srcModelId.equals(cacheSrcModelId)) {
                            continue;
                        }

                        meta.getSrcPrefItems().remove(srcPrefItem);
                        // Refresh index
                        Integer newExprIndex = meta.hashCode();
                        getTargetIndexCacheByOu(orgId).put(newExprIndex, meta);
                        getTargetIndexCacheByOu(orgId).remove(exprIndex);

                        toRefreshTargetPrefIndexExprs.put(tp, newExprIndex);
                    }
                }
            }
        }

        for (Map.Entry<PropertyId, Integer> entry : toRefreshTargetPrefIndexExprs.entrySet()) {
            PropertyId tp = entry.getKey();
            Integer newExprIndex = entry.getValue();
            Map<String, Map<String, Integer>> targetModel2PrefExprIndex =
                    targetOuCache.get(getMetaKey(tp.getCompId(), tp.getPrefId()));
            for (Map.Entry<String, Map<String, Integer>> targetModelEntry :
                    targetModel2PrefExprIndex.entrySet()) {
                for (Map.Entry<String, Integer> srcCategoryEntry :
                        targetModelEntry.getValue().entrySet()) {
                    Integer oldExprIndex = srcCategoryEntry.getValue();
                    srcCategoryEntry.setValue(newExprIndex);
                    logger.info(
                            "orgId [{}], source model [{}] deleted, related target pref [{}] expr index updated from [{}] to [{}]",
                            orgId,
                            srcModelId,
                            tp,
                            oldExprIndex,
                            newExprIndex);
                }
            }
        }
    }
}
