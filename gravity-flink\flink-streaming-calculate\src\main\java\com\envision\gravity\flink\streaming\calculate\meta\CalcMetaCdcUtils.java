package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.cdc.*;
import com.envision.gravity.flink.streaming.calculate.dto.*;

import java.util.Optional;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcMetaCdcUtils {

    private static final Logger logger = LoggerFactory.getLogger(CalcMetaCdcUtils.class);

    public static void process(ConvertedCdcRecord value) {
        String tableName = value.getTable();
        String op = value.getOp();
        Optional<CalcCdcTable> cdcTable = CalcCdcTable.find(tableName);
        if (!cdcTable.isPresent()) {
            return;
        }

        OPEnum opType = OPEnum.valueOf(op);
        switch (cdcTable.get()) {
            case TBL_PROPERTY_UPSTREAM_RULE:
                TblPropertyUpstreamRuleCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblPropertyUpstreamRule) value.getBefore(),
                                (TblPropertyUpstreamRule) value.getAfter(),
                                opType);
                break;
            case TBL_COMPONENT_PREF:
                TblComponentPrefCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblComponentPref) value.getBefore(),
                                (TblComponentPref) value.getAfter(),
                                opType);
                break;
            case TBL_COMPONENT:
                TblComponentCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblComponent) value.getBefore(),
                                (TblComponent) value.getAfter(),
                                opType);
                break;
            case TBL_PREF:
                TblPrefCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblPref) value.getBefore(),
                                (TblPref) value.getAfter(),
                                opType);
                break;
            case TBL_BO_MODEL:
                TblBoModelCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblBoModel) value.getBefore(),
                                (TblBoModel) value.getAfter(),
                                opType);
                break;
            case TBL_BO_MODEL_COMP:
                TblBoModelCompCdcProcessor.getInstance()
                        .process(
                                value.getSchema(),
                                (TblBoModelComp) value.getBefore(),
                                (TblBoModelComp) value.getAfter(),
                                opType);
                break;
            default:
                logger.error("unknown table: {}", tableName);
                break;
        }
    }
}
