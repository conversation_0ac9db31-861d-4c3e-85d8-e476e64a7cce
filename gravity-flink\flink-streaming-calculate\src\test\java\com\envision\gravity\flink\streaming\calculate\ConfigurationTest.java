package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;


import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** 配置测试 验证 Lion 配置是否正确加载 */
public class ConfigurationTest {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurationTest.class);

    @BeforeAll
    static void setup() {
        // 设置测试环境配置
        System.setProperty("arch.path", "./deploy/apps");
        System.setProperty("java.security.auth.login.config", "./deploy/zk_client_jaas.conf");
        System.setProperty("LOG_LEVEL", "DEBUG");
    }

    @Test
    public void testLionConfigurationLoading() {
        logger.info("=== 测试 Lion 配置加载 ===");

        try {
            // 测试 Kafka 配置
            String kafkaServers = CalcLionConfig.getCalcCommonKafkaServers();
            logger.info("Kafka Servers: {}", kafkaServers);

            String kafkaSourceTopicPattern = CalcLionConfig.getCalcStreamKafkaSourceTopicPattern();
            logger.info("Kafka Source Topic Pattern: {}", kafkaSourceTopicPattern);

            String kafkaSinkTopicPattern = CalcLionConfig.getCalcCommonKafkaSinkTopicPattern();
            logger.info("Kafka Sink Topic Pattern: {}", kafkaSinkTopicPattern);

            String kafkaConsumerGroup = CalcLionConfig.getCalcStreamKafkaSourceConsumerGroup();
            logger.info("Kafka Consumer Group: {}", kafkaConsumerGroup);

            int batchSize = CalcLionConfig.getCalcStreamKafkaSourceBatchSize();
            logger.info("Kafka Batch Size: {}", batchSize);

            int batchTimeout = CalcLionConfig.getCalcStreamKafkaSourceBatchTimeoutSeconds();
            logger.info("Kafka Batch Timeout: {} seconds", batchTimeout);

            // 验证配置是否正确
            if (kafkaServers != null && !kafkaServers.isEmpty()) {
                logger.info("✅ Kafka 配置加载成功");
            } else {
                logger.warn("⚠️ Kafka 配置为空，可能配置文件未正确加载");
            }

            // 测试 PostgreSQL 配置
            logger.info("=== PostgreSQL 配置 ===");
            // 注意：这些方法可能在 CalcLionConfig 中不存在，需要根据实际情况调整
            // 这里只是示例，展示如何测试配置加载

            logger.info("✅ 配置测试完成");

        } catch (Exception e) {
            logger.error("❌ 配置加载失败", e);
            throw e;
        }
    }

    @Test
    public void testSystemProperties() {
        logger.info("=== 测试系统属性 ===");

        String archPath = System.getProperty("arch.path");
        logger.info("arch.path: {}", archPath);

        String jaasConfig = System.getProperty("java.security.auth.login.config");
        logger.info("java.security.auth.login.config: {}", jaasConfig);

        String logLevel = System.getProperty("LOG_LEVEL");
        logger.info("LOG_LEVEL: {}", logLevel);

        // 验证必要的系统属性是否设置
        if (archPath != null && jaasConfig != null) {
            logger.info("✅ 系统属性设置正确");
        } else {
            logger.warn("⚠️ 系统属性设置不完整");
        }
    }

    @Test
    public void testConfigurationFiles() {
        logger.info("=== 测试配置文件存在性 ===");

        java.io.File appsDir = new java.io.File("./deploy/apps");
        logger.info("Apps directory exists: {}", appsDir.exists());

        java.io.File configDir = new java.io.File("./deploy/apps/config");
        logger.info("Config directory exists: {}", configDir.exists());

        java.io.File lionConfigDir = new java.io.File("./deploy/apps/config/lionconfig");
        logger.info("Lion config directory exists: {}", lionConfigDir.exists());

        java.io.File gravityCalcProps =
                new java.io.File("./deploy/apps/config/lionconfig/ext/gravity-calc.properties");
        logger.info("gravity-calc.properties exists: {}", gravityCalcProps.exists());

        java.io.File jaasConf = new java.io.File("./deploy/zk_client_jaas.conf");
        logger.info("zk_client_jaas.conf exists: {}", jaasConf.exists());

        if (gravityCalcProps.exists() && jaasConf.exists()) {
            logger.info("✅ 所有必要的配置文件都存在");
        } else {
            logger.warn("⚠️ 部分配置文件缺失");
        }
    }

    @Test
    public void printTestEnvironmentInfo() {
        logger.info("=== 测试环境信息 ===");
        logger.info("Working Directory: {}", System.getProperty("user.dir"));
        logger.info("Java Version: {}", System.getProperty("java.version"));
        logger.info("OS Name: {}", System.getProperty("os.name"));
        logger.info("OS Version: {}", System.getProperty("os.version"));

        // 打印所有相关的系统属性
        System.getProperties().entrySet().stream()
                .filter(
                        entry ->
                                entry.getKey().toString().contains("arch")
                                        || entry.getKey().toString().contains("lion")
                                        || entry.getKey().toString().contains("gravity")
                                        || entry.getKey().toString().contains("LOG_LEVEL"))
                .forEach(
                        entry ->
                                logger.info(
                                        "System Property: {} = {}",
                                        entry.getKey(),
                                        entry.getValue()));
    }
}
