package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.dto.TblComponent;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;

import java.util.List;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblComponentCdcProcessor {

    private static final Logger logger = LoggerFactory.getLogger(TblComponentCdcProcessor.class);

    private static volatile TblComponentCdcProcessor uniqueInstance;

    public static TblComponentCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblComponentCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblComponentCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private final CalcPrefCache calcPrefCache;

    private final CalcMetaProcessor calcMetaProcessor;

    private TblComponentCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        this.calcMetaProcessor = CalcMetaProcessor.getInstance();
    }

    public void process(String orgId, TblComponent before, TblComponent after, OPEnum op) {
        if (op == OPEnum.c) {
            logger.info("Created component, skip it...");
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error("Updated component invalid, before or after is null");
                return;
            }

            // CompName be updated
            if (!StringUtils.isEmpty(before.getCompName())
                    && !StringUtils.isEmpty(after.getCompName())
                    && !StringUtils.equals(before.getCompName(), after.getCompName())) {
                // Updated target prefName
                List<PropertyId> toUpdateTargetPrefs =
                        calcPrefCache.getByTargetCompId(orgId, after.getCompId());
                this.calcMetaProcessor.refreshTargetPropertyByCompIdAndPrefId(
                        orgId, toUpdateTargetPrefs);

                // TODO Updated source prefName
            }
        } else if (op == OPEnum.d) {
            if (before == null) {
                logger.error("Deleted component invalid, before is null");
                return;
            }

            // Target component deleted
            calcPrefCache.deleteByTargetCompId(orgId, before.getCompId());

            // TODO Source component deleted
        }
    }
}
