package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.regex.Pattern;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Kafka record serializer that converts calculation results to Kafka ProducerRecord. Handles
 * message serialization, topic determination, and header management for streaming output.
 *
 * <AUTHOR>
 */
public class KafkaRecordSerializer
        implements KafkaRecordSerializationSchema<Tuple2<CalcResultMsg, OffsetInfo>> {
    private static final Logger logger = LoggerFactory.getLogger(KafkaRecordSerializer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Pattern topicPattern;

    public KafkaRecordSerializer(String topicPattern) {
        this.topicPattern = Pattern.compile(topicPattern);
    }

    private String determineTopic(LegacyMsg msg) {
        // Use the topic pattern to determine the target topic
        // This is a simple implementation - you may want to customize this based on your needs
        return topicPattern.pattern() + msg.getOrgId();
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            Tuple2<CalcResultMsg, OffsetInfo> element,
            KafkaSinkContext kafkaSinkContext,
            Long aLong) {
        try {
            List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> msgList = element.f0.getLegacyMsgList();
            logger.debug(
                    "Starting serialization, containing {} messages",
                    msgList == null ? 0 : msgList.size());

            if (msgList == null || msgList.isEmpty()) {
                logger.warn("Message list is empty, skipping serialization");
                return null;
            }

            // Now each CalcResultMsg contains only one message, so we process it directly
            Tuple2<LegacyMsgWithMultiAssets, Boolean> msgTuple = msgList.get(0);
            String topic = determineTopic(msgTuple.f0);
            logger.debug(
                    "Processing single message: Target topic: {}, direct mapping: {}, orgId: {}",
                    topic,
                    msgTuple.f1,
                    msgTuple.f0.getOrgId());

            // Log detailed message content
            if (msgTuple.f0.getPayload() != null && !msgTuple.f0.getPayload().isEmpty()) {
                for (int i = 0; i < msgTuple.f0.getPayload().size(); i++) {
                    LegacyPayload payload = msgTuple.f0.getPayload().get(i);
                    logger.debug(
                            "Message payload[{}]: assetId={}, points={}",
                            i,
                            payload.getAssetId(),
                            payload.getPoints());
                }
            } else {
                logger.debug("Message has no payload");
            }

            if (topic == null) {
                logger.debug("Unable to determine target topic: {}", msgTuple);
                return null;
            }

            // Serialize the message
            byte[] value = objectMapper.writeValueAsBytes(msgTuple.f0);
            logger.debug("Message serialization successful, size: {} bytes", value.length);

            // Create headers if needed
            ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(topic, value);

            // Add headers if the message is direct mapping
            if (msgTuple.f1) {
                record.headers()
                        .add("directMapping", StringUtils.getBytes("1", StandardCharsets.UTF_8));
                logger.debug("Added directMapping header");
            }

            logger.debug("Sending message to topic: {}", topic);
            return record;
        } catch (Exception e) {
            logger.error("Serialization failed: {}", e.getMessage(), e);
        }
        return null;
    }
}
