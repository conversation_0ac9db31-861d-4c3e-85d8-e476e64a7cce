package com.envision.gravity.flink.streaming.bo.view.operator;

import com.envision.gravity.flink.streaming.bo.view.operator.config.LionConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;
import com.envision.gravity.flink.streaming.bo.view.operator.function.CdcRecordRouter;
import com.envision.gravity.flink.streaming.bo.view.operator.function.GenCreateOrReplaceReq;
import com.envision.gravity.flink.streaming.bo.view.operator.function.GenDropReq;
import com.envision.gravity.flink.streaming.bo.view.operator.side.SideOutputs;
import com.envision.gravity.flink.streaming.bo.view.operator.sink.CreateOrReplaceViewSink;
import com.envision.gravity.flink.streaming.bo.view.operator.sink.DropViewSink;

import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
@Slf4j
public class FlinkStreamBOViewOperator {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getCheckpointConfig().setCheckpointTimeout(1800000L);

        int timeWindowIntervalInMs = LionConfig.getTimeWindowIntervalInMs();

        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", LionConfig.getMaxBatchSize());
        properties.setProperty("max.queue.size", LionConfig.getMaxQueueSize());

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        DataStreamSource<String> pgSource =
                env.addSource(sourceFunction)
                        .setParallelism(1); // use parallelism 1 for sink to keep message ordering

        SingleOutputStreamOperator<ModelDetailOriginCdcRecord> recordStream =
                pgSource.process(new CdcRecordRouter());
        recordStream
                .getSideOutput(SideOutputs.CREATE_OR_REPLACE_VIEW_TAG)
                .keyBy(cdcRecord -> cdcRecord.getSource().getSchema())
                .window(TumblingProcessingTimeWindows.of(Time.milliseconds(timeWindowIntervalInMs)))
                .process(new GenCreateOrReplaceReq())
                .addSink(new CreateOrReplaceViewSink());
        recordStream
                .getSideOutput(SideOutputs.DROP_VIEW_TAG)
                .process(new GenDropReq())
                .addSink(new DropViewSink());

        env.execute("Flink Streaming BO View Operator");
    }
}
