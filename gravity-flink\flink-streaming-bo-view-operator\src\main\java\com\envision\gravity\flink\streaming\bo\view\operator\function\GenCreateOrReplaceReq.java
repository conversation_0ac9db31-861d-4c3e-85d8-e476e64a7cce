package com.envision.gravity.flink.streaming.bo.view.operator.function;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.bo.view.operator.config.LionConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.entity.Constants;
import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;
import com.envision.gravity.flink.streaming.bo.view.operator.model.ModelDetailOrigin;
import com.envision.gravity.flink.streaming.bo.view.operator.model.pg.ModelProperties;
import com.envision.gravity.flink.streaming.bo.view.operator.repository.ModelDetailOriginRepository;
import com.envision.gravity.flink.streaming.bo.view.operator.util.NameUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


import com.eniot.tableengine.RegisterIgniteTableConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
@Slf4j
public class GenCreateOrReplaceReq
        extends ProcessWindowFunction<
                ModelDetailOriginCdcRecord,
                Tuple3<String, RegisterIgniteTableConfig, String>,
                String,
                TimeWindow> {
    private static final long serialVersionUID = -4574472875369858680L;

    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    private static final String ATTRIBUTE = "ATTRIBUTE";
    private static final String PREF_TYPE = "prefType";
    private static final String PREF_DATA_TYPE = "prefDataType";
    private static final String TIMESTAMP = "timestamp";
    private transient ModelDetailOriginRepository modelDetailOriginRepository;
    private transient int expirySeconds;

    private static final Map<String, String> DATA_TYPE_MAP = new HashMap<>();

    static {
        DATA_TYPE_MAP.put("BOOLEAN", "boolean");
        DATA_TYPE_MAP.put("DATE", "date");
        DATA_TYPE_MAP.put("DATETIME", "timestamp");
        DATA_TYPE_MAP.put("DOUBLE", "double precision");
        DATA_TYPE_MAP.put("DURATION", "bigint");
        DATA_TYPE_MAP.put("FLOAT", "real");
        DATA_TYPE_MAP.put("INTEGER", "integer");
        DATA_TYPE_MAP.put("LONG", "bigint");
        DATA_TYPE_MAP.put("STRING", "varchar");
        DATA_TYPE_MAP.put("TIME", "time");
        DATA_TYPE_MAP.put("OBJECT", "varchar");
        DATA_TYPE_MAP.put("MAP", "varchar");
        DATA_TYPE_MAP.put("ENUM", "varchar");
        DATA_TYPE_MAP.put("ARRAY", "varchar");
    }

    @Override
    public void open(Configuration params) throws Exception {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        modelDetailOriginRepository = new ModelDetailOriginRepository(sqlSessionFactory);
        expirySeconds = LionConfig.getTableTTLInSeconds();
    }

    @Override
    public void close() throws Exception {
        try {
            PGDataSourceConfig.closeDataSource();
        } catch (Exception e) {
            log.error("Close pg dataSource error.", e);
            throw new GravityRuntimeException("Close pg dataSource error.", e);
        }
    }

    @Override
    public void process(
            String s,
            ProcessWindowFunction<
                                    ModelDetailOriginCdcRecord,
                                    Tuple3<String, RegisterIgniteTableConfig, String>,
                                    String,
                                    TimeWindow>
                            .Context
                    context,
            Iterable<ModelDetailOriginCdcRecord> elements,
            Collector<Tuple3<String, RegisterIgniteTableConfig, String>> out)
            throws Exception {

        List<ModelDetailOriginCdcRecord> uniqueRecords =
                new ArrayList<>(
                        StreamSupport.stream(elements.spliterator(), false)
                                .collect(
                                        Collectors.toMap(
                                                record ->
                                                        record.getAfter()
                                                                .getModelId(), // Key by model id
                                                // from 'after' field
                                                record -> record, // Value is the record itself
                                                (existing, replacement) ->
                                                        existing.getTsMs()
                                                                        .after(
                                                                                replacement
                                                                                        .getTsMs())
                                                                ? existing
                                                                : replacement))
                                .values());

        uniqueRecords.forEach(
                value -> {
                    try {
                        ModelDetailOrigin after = value.getAfter();
                        String viewName = NameUtil.genViewName(after);

                        long createSqlStartTime = System.currentTimeMillis();
                        String createOrReplaceViewSql =
                                genCreateOrReplaceViewSql(s, viewName, after);
                        long createSqlEndTime = System.currentTimeMillis();

                        if (createOrReplaceViewSql != null) {
                            log.info(
                                    "Start create or replace bo view, schemaName: [{}], modelId: [{}], tsMs: [{}], gen sql time cost: [{}].",
                                    s,
                                    after.getModelId(),
                                    value.getTsMs(),
                                    (createSqlEndTime - createSqlStartTime) / 1000.0 + "s");
                            out.collect(
                                    Tuple3.of(
                                            createOrReplaceViewSql,
                                            genRegisterIgniteTableConfig(s, viewName),
                                            genDropViewSql(s, viewName)));
                        }

                    } catch (JsonProcessingException e) {
                        log.error("Parse view name error, value: {}", value, e);
                    } catch (Exception e) {
                        log.error("Unknown error, value: {}", value, e);
                        throw new GravityRuntimeException("Unknown error!", e);
                    }
                });
    }

    private String genCreateOrReplaceViewSql(
            String schemaName, String viewName, ModelDetailOrigin after) {
        String modelId = after.getModelId();
        String modelPath = after.getModelPath();
        String queryModelIdsSql = genQueryModelIdsSql(schemaName, modelId, modelPath);
        // query properties
        ModelProperties modelProperties =
                modelDetailOriginRepository.selectModelProperties(schemaName, modelId);
        if (modelProperties != null) {
            String propertiesJsonData = modelProperties.getProperties();

            if (propertiesJsonData != null) {
                try {
                    JsonNode rootNode = OBJECT_MAPPER.readTree(propertiesJsonData);

                    StringJoiner attributesJoiner = new StringJoiner(",\n       ");
                    rootNode.fields()
                            .forEachRemaining(
                                    entry -> {
                                        String key = entry.getKey();
                                        JsonNode node = entry.getValue();

                                        if (ATTRIBUTE.equalsIgnoreCase(
                                                node.get(PREF_TYPE).asText())) {
                                            String dataType =
                                                    DATA_TYPE_MAP.getOrDefault(
                                                            node.get(PREF_DATA_TYPE)
                                                                    .asText()
                                                                    .toUpperCase(),
                                                            "varchar");

                                            if (TIMESTAMP.equals(dataType)) {
                                                attributesJoiner.add(
                                                        "to_timestamp((attributes -> '"
                                                                + key
                                                                + "')::bigint / 1000.0)::"
                                                                + dataType
                                                                + " AS \""
                                                                + key
                                                                + "\"");
                                            } else {
                                                attributesJoiner.add(
                                                        "(attributes ->> '"
                                                                + key
                                                                + "')::"
                                                                + dataType
                                                                + " AS \""
                                                                + key
                                                                + "\"");
                                            }
                                        }
                                    });

                    String attributeColumns = "";
                    if (attributesJoiner.length() > 0) {
                        attributeColumns = "," + attributesJoiner + "\n";
                    }

                    return "CREATE OR REPLACE VIEW "
                            + schemaName
                            + ".\""
                            + viewName
                            + "\" AS\n"
                            + "SELECT asset_id, model_id, \n"
                            + "       asset_display_name,\n"
                            + "       asset_created_time,\n"
                            + "       asset_modified_time "
                            + attributeColumns
                            + "FROM "
                            + schemaName
                            + ".object_detail_origin\n"
                            + "WHERE model_id in ("
                            + queryModelIdsSql
                            + ");";
                } catch (JsonProcessingException e) {
                    log.error(
                            "parse model properties error, modelId: {}, properties: {}",
                            modelId,
                            propertiesJsonData,
                            e);
                }
            }
        }

        return null;
    }

    private String genQueryModelIdsSql(String schemaName, String modelId, String modelPath) {
        String parentModelPath = modelPath == null ? "/" + modelId : modelPath;
        String childrenModelPath = parentModelPath + "/%";
        return String.format(
                "select model_id from %s.tbl_bo_model where model_path = '%s' or model_path like '%s'",
                schemaName, parentModelPath, childrenModelPath);
    }

    private RegisterIgniteTableConfig genRegisterIgniteTableConfig(
            String schemaName, String viewName) {
        RegisterIgniteTableConfig cfg = new RegisterIgniteTableConfig();
        cfg.setOrgId(schemaName);
        cfg.setTargetSchema(Constants.BO);
        cfg.setTargetTable(viewName);
        cfg.setSourceType("TABLE");
        cfg.setCacheMode("REPLICATED");
        cfg.setCreateOnRead(true);
        cfg.setExpirySeconds(expirySeconds);

        Map<String, String> params = new HashMap<>();
        params.put("url", LionConfig.getPgJdbcUrl());
        params.put("user", LionConfig.getPgUsername());
        params.put("passwd", LionConfig.getPgPassword());
        params.put("fromSchema", schemaName);
        params.put("fromTable", viewName);
        params.put("quoteSchema", "false");
        params.put("quoteTable", "true");
        cfg.setParams(params);

        RegisterIgniteTableConfig.Schema schema = new RegisterIgniteTableConfig.Schema();
        List<String> primaryKey = new ArrayList<>();
        primaryKey.add("asset_id");
        primaryKey.add("model_id");
        schema.setPrimaryKey(primaryKey);
        schema.setAffinityKey("asset_id");
        cfg.setSchemaConfig(schema);

        return cfg;
    }

    private String genDropViewSql(String schemaName, String viewName) {
        // DROP VIEW IF EXISTS your_schema_name.your_view_name;
        return "DROP VIEW IF EXISTS " + schemaName + ".\"" + viewName + "\";";
    }
}
