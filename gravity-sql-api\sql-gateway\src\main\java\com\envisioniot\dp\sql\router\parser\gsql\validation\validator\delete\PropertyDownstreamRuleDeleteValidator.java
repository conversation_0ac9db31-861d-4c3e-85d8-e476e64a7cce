package com.envisioniot.dp.sql.router.parser.gsql.validation.validator.delete;

import com.envisioniot.dp.sql.router.parser.gsql.model.CallbackReq;
import com.envisioniot.dp.sql.router.parser.gsql.model.CallbackResp;
import com.envisioniot.dp.sql.router.parser.gsql.model.ValidationResp;
import com.envisioniot.dp.sql.router.parser.gsql.validation.CallbackFunction;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.statement.delete.Delete;

import java.sql.SQLException;

@Slf4j
public class PropertyDownstreamRuleDeleteValidator extends BaseDeleteValidator {
    private String databaseName;

    @Override
    protected void customValidation(String databaseName, Delete delete) {
        this.databaseName =
                databaseName == null || databaseName.isEmpty() ? "public" : databaseName;
    }

    @Override
    protected CallbackFunction getCallbackFunction() {
        return new PropertyDownstreamRuleDeleteCallback();
    }

    public class PropertyDownstreamRuleDeleteCallback implements CallbackFunction {

        @Override
        public CallbackResp exec(CallbackReq req) throws SQLException {
            String parsedSql = req.getParsedSql();
            String parsedISql = req.getParsedISql();
            log.debug("Parsed delete sql: {}, parsedISql: {}", parsedSql, parsedISql);

            int rowCount =
                    doubleDelete(
                            databaseName, "tbl_property_downstream_rule", parsedSql, parsedISql);
            return CallbackResp.builder().rowCount(rowCount).build();
        }

        @Override
        public ValidationResp getValidationResp() {
            return null;
        }
    }
}
