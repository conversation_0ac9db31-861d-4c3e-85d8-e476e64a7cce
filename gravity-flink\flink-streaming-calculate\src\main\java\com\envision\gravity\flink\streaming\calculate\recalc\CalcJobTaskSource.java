package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.CalcJobTask;


import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.connector.source.Boundedness;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算作业任务源 - 基于 Flink Source API 2.0 实现
 *
 * <AUTHOR>
 */
public class CalcJobTaskSource
        implements Source<CalcJobTask, CalcJobTaskSplit, CalcJobTaskEnumeratorState> {

    private static final long serialVersionUID = 1L;
    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSource.class);

    private final TblCalcJobInfo jobInfo;
    private final boolean isBatchMode;

    public CalcJobTaskSource(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
        this.isBatchMode = true; // 默认批处理模式
    }

    public CalcJobTaskSource(TblCalcJobInfo jobInfo, boolean isBatchMode) {
        this.jobInfo = jobInfo;
        this.isBatchMode = isBatchMode;
    }

    @Override
    public Boundedness getBoundedness() {
        return isBatchMode ? Boundedness.BOUNDED : Boundedness.CONTINUOUS_UNBOUNDED;
    }

    @Override
    public SplitEnumerator<CalcJobTaskSplit, CalcJobTaskEnumeratorState> createEnumerator(
            SplitEnumeratorContext<CalcJobTaskSplit> enumContext) {
        logger.info(
                "Creating CalcJobTaskSplitEnumerator for job: {}, mode: {}",
                jobInfo.getJobId(),
                isBatchMode ? "BATCH" : "STREAMING");
        return new CalcJobTaskSplitEnumerator(jobInfo, enumContext, isBatchMode);
    }

    @Override
    public SplitEnumerator<CalcJobTaskSplit, CalcJobTaskEnumeratorState> restoreEnumerator(
            SplitEnumeratorContext<CalcJobTaskSplit> enumContext,
            CalcJobTaskEnumeratorState checkpoint) {
        logger.info(
                "Restoring CalcJobTaskSplitEnumerator for job: {} from checkpoint",
                jobInfo.getJobId());
        return new CalcJobTaskSplitEnumerator(jobInfo, enumContext, checkpoint, isBatchMode);
    }

    @Override
    public SourceReader<CalcJobTask, CalcJobTaskSplit> createReader(
            SourceReaderContext readerContext) {
        logger.info("Creating CalcJobTaskSourceReader for job: {}", jobInfo.getJobId());
        return new CalcJobTaskSourceReader(readerContext);
    }

    public TypeInformation<CalcJobTask> getProducedType() {
        return TypeInformation.of(CalcJobTask.class);
    }

    @Override
    public SimpleVersionedSerializer<CalcJobTaskSplit> getSplitSerializer() {
        // TODO: 实现分片序列化器
        return null;
    }

    @Override
    public SimpleVersionedSerializer<CalcJobTaskEnumeratorState>
            getEnumeratorCheckpointSerializer() {
        // TODO: 实现枚举器状态序列化器
        return null;
    }

    public TblCalcJobInfo getJobInfo() {
        return jobInfo;
    }

    public boolean isBatchMode() {
        return isBatchMode;
    }

    @Override
    public String toString() {
        return "CalcJobTaskSource{"
                + "jobId='"
                + (jobInfo != null ? jobInfo.getJobId() : "null")
                + '\''
                + ", isBatchMode="
                + isBatchMode
                + '}';
    }
}
