package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.cdc.OPEnum;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.GlobalWindow;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcCdcWindowCollector
        extends ProcessWindowFunction<
                ConvertedCdcRecord, ConvertedCdcRecord, String, GlobalWindow> {

    private static final Logger LOG = LoggerFactory.getLogger(CalcCdcWindowCollector.class);

    @Override
    public void process(
            String key,
            ProcessWindowFunction<ConvertedCdcRecord, ConvertedCdcRecord, String, GlobalWindow>
                            .Context
                    context,
            Iterable<ConvertedCdcRecord> elements,
            Collector<ConvertedCdcRecord> out)
            throws Exception {
        try {
            List<ConvertedCdcRecord> records = new ArrayList<>();
            elements.forEach(records::add);

            // 按时间戳排序
            records.sort(Comparator.comparing(ConvertedCdcRecord::getTsMs));

            // 处理记录序列
            if (!records.isEmpty()) {
                // 获取最后一条记录
                ConvertedCdcRecord lastRecord = records.get(records.size() - 1);

                // 如果最后一条是删除操作，直接输出
                if (Objects.equals(lastRecord.getOp(), OPEnum.d.name())) {
                    out.collect(lastRecord);
                    LOG.info(
                            "Emitted DELETE record for table: {}, key: {}",
                            lastRecord.getTable(),
                            lastRecord.getPrimaryKey());
                    return;
                }

                // 检查是否有删除操作
                boolean hasDelete =
                        records.stream()
                                .anyMatch(
                                        record -> Objects.equals(record.getOp(), OPEnum.d.name()));

                if (hasDelete) {
                    // 如果序列中包含删除操作，需要特殊处理
                    // 1. 找到最后一个删除操作
                    ConvertedCdcRecord lastDelete =
                            records.stream()
                                    .filter(
                                            record ->
                                                    Objects.equals(record.getOp(), OPEnum.d.name()))
                                    .max(Comparator.comparing(ConvertedCdcRecord::getTsMs))
                                    .get();

                    // 2. 找到删除操作之后的所有插入/更新操作
                    List<ConvertedCdcRecord> afterDeleteRecords =
                            records.stream()
                                    .filter(record -> record.getTsMs() > lastDelete.getTsMs())
                                    .collect(Collectors.toList());

                    // 3. 输出删除操作
                    out.collect(lastDelete);
                    LOG.info(
                            "Emitted DELETE record for table: {}, key: {}",
                            lastDelete.getTable(),
                            lastDelete.getPrimaryKey());

                    // 4. 输出删除后的所有操作
                    for (ConvertedCdcRecord record : afterDeleteRecords) {
                        out.collect(record);
                        LOG.info(
                                "Emitted {} record for table: {}, key: {}",
                                record.getOp(),
                                record.getTable(),
                                record.getPrimaryKey());
                    }
                } else {
                    // 如果没有删除操作，只输出最后一条记录
                    out.collect(lastRecord);
                    LOG.info(
                            "Emitted {} record for table: {}, key: {}",
                            lastRecord.getOp(),
                            lastRecord.getTable(),
                            lastRecord.getPrimaryKey());
                }
            }
        } catch (Exception e) {
            LOG.error("Error processing CDC window", e);
        }
    }
}
