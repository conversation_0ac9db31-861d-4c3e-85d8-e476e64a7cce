package com.envision.gravity.ignite.tsdb;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


import io.eniot.tsdb.common.TSDBNodeStatus;
import io.eniot.tsdb.common.sharding.*;
import io.eniot.tsdb.common.sharding.consistenthash.ConsistentHashRouter;
import io.eniot.tsdb.common.util.CommonUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.IgniteLogger;
import org.apache.ignite.binary.BinaryField;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.resources.IgniteInstanceResource;
import org.apache.ignite.resources.LoggerResource;

/** <AUTHOR> 2024/8/14 */
public class TSDBMetaServiceImpl implements TSDBMetaIgniteService {

    private static final long serialVersionUID = -2440960644933597171L;

    private static final int CODE_OK = 0;
    private static final int CODE_EMPTY_RESULT = 400;
    private static final int CODE_INSUFFICIENT_REPLICAS = 401;
    private static final int CODE_INTERNAL_ERROR = 500;

    private static final String SHARDING_CACHE_NAME = "_TSDB_SHARDING_META";

    private static final String SHARDING_CACHE_NAME_V2 = "_TBL_TSDB_META_PART";

    @IgniteInstanceResource private Ignite ignite;

    @LoggerResource private IgniteLogger logger;

    public Ignite getIgnite() {
        return ignite;
    }

    public void setIgnite(Ignite ignite) {
        this.ignite = ignite;
    }

    public IgniteLogger getLogger() {
        return logger;
    }

    public void setLogger(IgniteLogger logger) {
        this.logger = logger;
    }

    /**
     * seriesKey: <systemId, pointId>
     *
     * @param database
     * @param table
     * @param seriesList
     * @return
     */
    @Override
    public ResponseResult<Map<Pair<String, String>, List<Long>>> getShards(
            String database,
            String table,
            Set<Pair<String, String>> seriesList,
            List<TSDBNode> allNodes,
            ShardingRule shardingRule,
            int replicaFactor,
            TSDBMode rwMode) {
        long queryStartTime = System.currentTimeMillis();
        String metaCacheName = String.format("%s%s", database, SHARDING_CACHE_NAME);
        String cacheKeyType = String.format("%s%s", metaCacheName, "_KEY");
        String cacheValueType = String.format("%s%s", metaCacheName, "_VALUE");

        IgniteCache<BinaryObject, BinaryObject> igniteCache =
                ignite.cache(metaCacheName).withKeepBinary();
        if (igniteCache == null) {
            String errorMsg = String.format("Get ignite cache %s failed ...", metaCacheName);
            logger.error(errorMsg);
            return ResponseResult.createResult(CODE_INTERNAL_ERROR, errorMsg);
        }

        Set<BinaryObject> keySets = new HashSet<>();
        for (Pair<String, String> curSeries : seriesList) {
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            shardKeyBuilder.setField("database", database);
            shardKeyBuilder.setField("table_name", table);
            shardKeyBuilder.setField("system_id", curSeries.getLeft());
            shardKeyBuilder.setField("measurement", curSeries.getRight());
            keySets.add(shardKeyBuilder.build());
        }
        Map<BinaryObject, BinaryObject> seriesShards = igniteCache.getAll(keySets);
        Map<Pair<String, String>, List<Long>> existShards = new HashMap<>(seriesShards.size());
        List<Pair<String, String>> needInitSeriesList = new ArrayList<>();

        for (Pair<String, String> curSeries : seriesList) {
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            shardKeyBuilder.setField("database", database);
            shardKeyBuilder.setField("table_name", table);
            shardKeyBuilder.setField("system_id", curSeries.getLeft());
            shardKeyBuilder.setField("measurement", curSeries.getRight());

            BinaryObject shardRecord = seriesShards.get(shardKeyBuilder.build());
            if (shardRecord == null) {
                needInitSeriesList.add(curSeries);
            } else {
                BinaryField ownersField = shardRecord.type().field("owners");
                existShards.put(curSeries, ownersField.value(shardRecord));
            }
        }

        long queryEndTime = System.currentTimeMillis();
        if (CommonUtil.emptyCollection(needInitSeriesList) || rwMode == TSDBMode.READ) {
            logger.info(
                    String.format(
                            "No series need init, query series num = %d, query time = %d ms",
                            keySets.size(), queryEndTime - queryStartTime));
            return ResponseResult.createResult(CODE_OK, "ok", existShards);
        }

        ConsistentHashRouter<TSDBNode> consistentHashRouter = new ConsistentHashRouter<>(allNodes);
        Map<Pair<String, String>, List<Long>> needInitShards =
                new HashMap<>(needInitSeriesList.size());
        Map<BinaryObject, BinaryObject> needInitRecords = new HashMap<>();

        for (Pair<String, String> initSeries : needInitSeriesList) {
            ShardingInfo seriesShardingInfo =
                    buildShardingInfo(database, table, initSeries.getLeft(), initSeries.getRight());
            TSDBNode seriesNode =
                    consistentHashRouter.routeNode(shardingRule.getKey(seriesShardingInfo));
            List<TSDBNode> seriesReplicas = initSeriesReplicas(seriesNode, allNodes, replicaFactor);
            List<Long> seriesOwners =
                    seriesReplicas.stream()
                            .map(TSDBNode::getNodeHashId)
                            .collect(Collectors.toList());
            needInitShards.put(initSeries, seriesOwners);

            Timestamp now = new Timestamp(System.currentTimeMillis());
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            BinaryObjectBuilder shardValueBuilder = ignite.binary().builder(cacheValueType);

            shardKeyBuilder.setField("database", database);
            shardKeyBuilder.setField("table_name", table);
            shardKeyBuilder.setField("system_id", initSeries.getLeft());
            shardKeyBuilder.setField("measurement", initSeries.getRight());

            shardValueBuilder.setField("database", database);
            shardValueBuilder.setField("table_name", table);
            shardValueBuilder.setField("system_id", initSeries.getLeft());
            shardValueBuilder.setField("measurement", initSeries.getRight());
            shardValueBuilder.setField("owners", seriesOwners);
            shardValueBuilder.setField("create_time", now);
            shardValueBuilder.setField("update_time", now);
            needInitRecords.put(shardKeyBuilder.build(), shardValueBuilder.build());
        }
        igniteCache.putAll(needInitRecords);
        existShards.putAll(needInitShards);

        long initEndTime = System.currentTimeMillis();
        logger.info(
                String.format(
                        "Query series num = %d, need init num = %d, query time = %d ms, init time = %d ms",
                        keySets.size(),
                        needInitShards.size(),
                        queryEndTime - queryStartTime,
                        initEndTime - queryEndTime));

        return ResponseResult.createResult(CODE_OK, "ok", existShards);
    }

    @Override
    public ResponseResult<Map<Pair<String, String>, List<Long>>> getShardsV2(
            String database,
            String table,
            Set<Pair<String, String>> seriesList,
            List<TSDBNode> allNodes,
            ShardingRule shardingRule,
            int replicaFactor,
            TSDBMode rwMode) {
        long queryStartTime = System.currentTimeMillis();
        if (replicaFactor < 1 || replicaFactor > 2) {
            String errorMsg = String.format("Unsupported replica factor: %d", replicaFactor);
            logger.error(errorMsg);
            return ResponseResult.createResult(CODE_INTERNAL_ERROR, errorMsg);
        }

        String metaCacheName = String.format("%s%s", database, SHARDING_CACHE_NAME_V2);
        String cacheKeyType = String.format("%s%s", metaCacheName, "_KEY");
        String cacheValueType = String.format("%s%s", metaCacheName, "_VALUE");

        IgniteCache<BinaryObject, BinaryObject> igniteCache =
                ignite.cache(metaCacheName).withKeepBinary();
        if (igniteCache == null) {
            String errorMsg = String.format("Get ignite cache %s failed ...", metaCacheName);
            logger.error(errorMsg);
            return ResponseResult.createResult(CODE_INTERNAL_ERROR, errorMsg);
        }

        Set<BinaryObject> keySets = new HashSet<>();
        for (Pair<String, String> curSeries : seriesList) {
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            shardKeyBuilder.setField("series_id", getSeriesId(curSeries));
            keySets.add(shardKeyBuilder.build());
        }
        Map<BinaryObject, BinaryObject> seriesShards = igniteCache.getAll(keySets);
        Map<Pair<String, String>, List<Long>> existShards = new HashMap<>(seriesShards.size());
        List<Pair<String, String>> needInitSeriesList = new ArrayList<>();

        for (Pair<String, String> curSeries : seriesList) {
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            shardKeyBuilder.setField("series_id", getSeriesId(curSeries));
            BinaryObject shardRecord = seriesShards.get(shardKeyBuilder.build());
            if (shardRecord == null) {
                needInitSeriesList.add(curSeries);
            } else {
                List<Long> nodes = new ArrayList<>(replicaFactor);
                BinaryField node1Field = shardRecord.type().field("allocated_node1");
                Long node1 = node1Field.value(shardRecord);
                if (node1 != null) {
                    nodes.add(node1Field.value(shardRecord));
                }
                if (replicaFactor == 2) {
                    BinaryField node2Field = shardRecord.type().field("allocated_node2");
                    Long node2 = node2Field.value(shardRecord);
                    if (node2 != null) {
                        nodes.add(node2);
                    }
                }
                if (!CommonUtil.emptyCollection(nodes)) {
                    existShards.put(curSeries, nodes);
                } else {
                    logger.error(
                            String.format(
                                    "getShardsV2: Series %s node1 and node2 both not exist",
                                    curSeries));
                }
            }
        }

        long queryEndTime = System.currentTimeMillis();
        if (CommonUtil.emptyCollection(needInitSeriesList) || rwMode == TSDBMode.READ) {
            logger.info(
                    String.format(
                            "getShardsV2: No series need init, query series num = %d, query time = %d ms",
                            keySets.size(), queryEndTime - queryStartTime));
            return ResponseResult.createResult(CODE_OK, "ok", existShards);
        }

        Map<Pair<String, String>, List<Long>> needInitShards =
                new HashMap<>(needInitSeriesList.size());
        Map<BinaryObject, BinaryObject> needInitRecords = new HashMap<>();

        for (Pair<String, String> initSeries : needInitSeriesList) {
            ShardingInfo seriesShardingInfo =
                    buildShardingInfo(database, table, initSeries.getLeft(), initSeries.getRight());
            List<Long> seriesOwners = nodeElection(seriesShardingInfo, allNodes, replicaFactor);
            needInitShards.put(initSeries, seriesOwners);

            Timestamp now = new Timestamp(System.currentTimeMillis());
            BinaryObjectBuilder shardKeyBuilder = ignite.binary().builder(cacheKeyType);
            BinaryObjectBuilder shardValueBuilder = ignite.binary().builder(cacheValueType);

            shardKeyBuilder.setField("series_id", getSeriesId(initSeries));

            shardValueBuilder.setField("series_id", getSeriesId(initSeries));
            shardValueBuilder.setField("allocated_node1", seriesOwners.get(0));
            if (replicaFactor == 2) {
                shardValueBuilder.setField("allocated_node2", seriesOwners.get(1));
            }

            shardValueBuilder.setField("created_time", now);
            shardValueBuilder.setField("modified_time", now);
            needInitRecords.put(shardKeyBuilder.build(), shardValueBuilder.build());
        }
        igniteCache.putAll(needInitRecords);
        existShards.putAll(needInitShards);

        long initEndTime = System.currentTimeMillis();
        logger.info(
                String.format(
                        "getShardsV2: Query series num = %d, need init num = %d, query time = %d ms, init time = %d ms",
                        keySets.size(),
                        needInitShards.size(),
                        queryEndTime - queryStartTime,
                        initEndTime - queryEndTime));

        return ResponseResult.createResult(CODE_OK, "ok", existShards);
    }

    public ShardingInfo buildShardingInfo(
            String dbName, String tableName, String systemId, String measurement) {
        return new ShardingInfo()
                .shardingMeta(
                        new ShardingMeta()
                                .database(dbName)
                                .tableName(tableName)
                                .systemId(systemId)
                                .measurement(measurement));
    }

    @Override
    public String toString() {
        return "TSDBMetaServiceImpl{" + "ignite=" + ignite + ", logger=" + logger + '}';
    }

    // ----------------------------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------------------------
    // <systemId, rawFieldId>
    private String getSeriesId(Pair<String, String> curSeries) {
        return String.join("@", new String[] {curSeries.getLeft(), curSeries.getRight()});
    }

    // 初始化series节点
    public List<TSDBNode> initSeriesReplicas(
            TSDBNode firstNode, List<TSDBNode> allNodes, int replicaFactor) {
        if (replicaFactor == 1) {
            return Collections.singletonList(firstNode);
        }

        List<TSDBNode> copiedAllNodes = TSDBMetaUtils.deepCopy(allNodes);
        List<TSDBNode> availableReplicas = new ArrayList<>(replicaFactor);
        if (firstNode.getStatus() == TSDBNodeStatus.ONLINE) {
            availableReplicas.add(firstNode);
        }

        // NOTICE: initReplicas 可能不全是 ONLINE，还需要优先选 ONLINE
        addCandidateNodes(availableReplicas, copiedAllNodes, replicaFactor, TSDBNodeStatus.ONLINE);
        if (availableReplicas.size() == replicaFactor) {
            return availableReplicas;
        }

        addCandidateNodes(
                availableReplicas, copiedAllNodes, replicaFactor, TSDBNodeStatus.UNHEALTHY);
        if (availableReplicas.size() == replicaFactor) {
            return availableReplicas;
        }

        addCandidateNodes(
                availableReplicas, copiedAllNodes, replicaFactor, TSDBNodeStatus.RECOVERY);
        if (availableReplicas.size() == replicaFactor) {
            return availableReplicas;
        }

        addCandidateNodes(availableReplicas, copiedAllNodes, replicaFactor, TSDBNodeStatus.OFFLINE);
        if (availableReplicas.size() == replicaFactor) {
            return availableReplicas;
        }

        logger.error("Insufficient available replicas when init");
        return Collections.emptyList();
    }

    public void addCandidateNodes(
            List<TSDBNode> availableReplicas,
            List<TSDBNode> clonedAllNodes,
            int replicaFactor,
            TSDBNodeStatus status) {
        // remove exist elements
        clonedAllNodes.removeAll(availableReplicas);
        int candidateNum = replicaFactor - availableReplicas.size();
        Set<TSDBNode> candidateNodes = filterNodesByStatus(clonedAllNodes, status);
        if (candidateNodes.size() > 0) {
            candidateNum = Math.min(candidateNum, candidateNodes.size());
            // Set<TSDBNode> subCandidateNodes =
            // candidateNodes.stream().limit(candidateNum).collect(Collectors.toSet());
            Set<TSDBNode> subCandidateNodes =
                    new HashSet<>(getRandomNodes(candidateNodes, candidateNum));
            availableReplicas.addAll(subCandidateNodes);
        }
    }

    public static List<TSDBNode> getRandomNodes(Set<TSDBNode> nodes, int n) {
        return getRandomNodes(new ArrayList<>(nodes), n);
    }

    public static List<TSDBNode> getRandomNodes(List<TSDBNode> nodes, int n) {
        Collections.shuffle(nodes);
        return nodes.subList(0, Math.min(n, nodes.size()));
    }

    private Set<TSDBNode> filterNodesByStatus(Collection<TSDBNode> nodes, TSDBNodeStatus status) {
        return nodes.stream().filter(n -> n.getStatus() == status).collect(Collectors.toSet());
    }

    // =============================================================================================
    // TODO 如果只有一个 weight 为 0 的，这种会集中写到一个节点，导致被写爆（还是说只扩一个？）
    // =============================================================================================
    public List<Long> nodeElection(
            ShardingInfo seriesShardingInfo, List<TSDBNode> allNodes, int num) {
        List<TSDBNode> candidateNodes =
                allNodes.stream().filter(n -> n.getWeight() > 0).collect(Collectors.toList());

        if (candidateNodes.size() < allNodes.size()) {
            List<TSDBNode> seriesExceedLimitNodes =
                    allNodes.stream().filter(n -> n.getWeight() == 0).collect(Collectors.toList());
            logger.warning("Node election, nodes exceed series limit: " + seriesExceedLimitNodes);
        }

        if (CommonUtil.emptyCollection(candidateNodes)) {
            logger.error("All nodes exceed series limit, will select nodes random ...");
            List<TSDBNode> nodes = getRandomNodes(allNodes, num);
            return nodes.stream().map(TSDBNode::getNodeHashId).collect(Collectors.toList());
        }

        List<TSDBNode> offlineNodes =
                candidateNodes.stream()
                        .filter(n -> n.getStatus() == TSDBNodeStatus.OFFLINE)
                        .collect(Collectors.toList());
        List<TSDBNode> notOfflineNodes =
                candidateNodes.stream()
                        .filter(n -> n.getStatus() != TSDBNodeStatus.OFFLINE)
                        .collect(Collectors.toList());

        if (notOfflineNodes.size() < num) {
            List<Long> selectedNodes = new ArrayList<>(num);
            notOfflineNodes.forEach(n -> selectedNodes.add(n.getNodeHashId()));
            // Select rest from offline nodes
            Map<Long, Integer> offlineNodeHashId2Weight =
                    offlineNodes.stream()
                            .collect(
                                    Collectors.toMap(TSDBNode::getNodeHashId, TSDBNode::getWeight));
            List<Long> offlineSelectedNodes =
                    doNodeElection(
                            offlineNodeHashId2Weight,
                            num - selectedNodes.size(),
                            seriesShardingInfo.getShardingMeta());
            selectedNodes.addAll(offlineSelectedNodes);
            return selectedNodes;
        } else {
            Map<Long, Integer> nodeHashId2Weight =
                    notOfflineNodes.stream()
                            .collect(
                                    Collectors.toMap(TSDBNode::getNodeHashId, TSDBNode::getWeight));
            return doNodeElection(nodeHashId2Weight, num, seriesShardingInfo.getShardingMeta());
        }
    }

    /**
     * Elects nodes based on their weights and series ID
     *
     * @param nodeHashId2Weight Map of node hash IDs to their weights
     * @param num Number of nodes to elect
     * @param shardingMeta Series identifier for consistent node selection
     * @return List of elected node hash IDs
     * @throws IllegalArgumentException if input parameters are invalid
     */
    public List<Long> doNodeElection(
            Map<Long, Integer> nodeHashId2Weight, int num, ShardingMeta shardingMeta) {
        // Validate input parameters
        if (nodeHashId2Weight == null || nodeHashId2Weight.isEmpty()) {
            throw new IllegalArgumentException("Node weight map cannot be null or empty");
        }
        if (num <= 0) {
            throw new IllegalArgumentException("Number of nodes to elect must be positive");
        }
        if (shardingMeta == null) {
            throw new IllegalArgumentException("Sharding meta cannot be null");
        }

        // If number of nodes requested is greater than or equal to available nodes, return all
        // nodes
        if (nodeHashId2Weight.size() <= num) {
            return new ArrayList<>(nodeHashId2Weight.keySet());
        }

        // Calculate total weight
        int totalWeight = nodeHashId2Weight.values().stream().mapToInt(Integer::intValue).sum();

        // Create a sorted map of cumulative weights to node IDs
        TreeMap<Double, Long> weightMap = new TreeMap<>();
        double cumulativeWeight = 0.0;

        for (Map.Entry<Long, Integer> entry : nodeHashId2Weight.entrySet()) {
            cumulativeWeight += (double) entry.getValue() / totalWeight;
            weightMap.put(cumulativeWeight, entry.getKey());
        }

        // Use deterministic random selection based on weights and shardingId
        Set<Long> selectedNodes = new LinkedHashSet<>();
        String shardingId =
                String.join(
                        "@",
                        shardingMeta.getDatabase(),
                        shardingMeta.getTableName(),
                        shardingMeta.getSystemId(),
                        shardingMeta.getMeasurement());
        Random random = new Random(createSeed(shardingId));

        while (selectedNodes.size() < num) {
            double randomValue = random.nextDouble();
            Map.Entry<Double, Long> entry = weightMap.ceilingEntry(randomValue);

            if (entry == null) {
                entry = weightMap.firstEntry();
            }

            selectedNodes.add(entry.getValue());
        }

        return new ArrayList<>(selectedNodes);
    }

    private long createSeed(String shardingId) {
        // Create a deterministic seed based on seriesId
        long seed = 0;
        for (char c : shardingId.toCharArray()) {
            seed = 31 * seed + c;
        }
        return seed;
    }
}
