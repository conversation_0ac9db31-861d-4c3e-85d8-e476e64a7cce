package com.envision.gravity.ignite.tsdb.loader;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/** <AUTHOR> 2024/3/29 */
public class TSDBLoaderUtils {

    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String flattenElements(Set<String> elements) {
        StringBuilder sb = new StringBuilder();
        for (String e : elements) {
            sb.append("'");
            sb.append(e);
            sb.append("',");
        }
        String s = sb.toString();

        return s.substring(0, s.length() - 1);
    }

    public static Long parseLocalTime(String dateString, String timeZone) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateString, DEFAULT_DATETIME_FORMATTER);
        ZoneId zoneId = ZoneId.of(timeZone);
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static String formatTimestamp(Long timestampMilli, String timeZone) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMilli), ZoneId.of(timeZone))
                .format(DEFAULT_DATETIME_FORMATTER);
    }

    public static <K, V> List<Map<K, V>> splitMap(Map<K, V> map, int n) {
        List<Map<K, V>> subMaps = new ArrayList<>();
        int size = map.size();
        int subMapSize = (int) Math.ceil((double) size / n);

        Iterator<Map.Entry<K, V>> iterator = map.entrySet().iterator();
        for (int i = 0; i < n; i++) {
            Map<K, V> subMap = new HashMap<>();
            for (int j = 0; j < subMapSize && iterator.hasNext(); j++) {
                Map.Entry<K, V> entry = iterator.next();
                subMap.put(entry.getKey(), entry.getValue());
            }
            subMaps.add(subMap);
        }

        return subMaps;
    }
}
