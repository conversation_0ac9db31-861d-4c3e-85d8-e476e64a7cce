# Non-Direct Mapping Processing Logic Technical Design

## 1. Overview

This document describes the technical design for implementing non-direct mapping processing logic in the `CalculateProcessor` class. Non-direct mapping allows target properties to be calculated using complex expressions that can reference multiple source measurement points and attributes from different models.

## 2. Background

### 2.1 Direct Mapping vs Non-Direct Mapping

- **Direct Mapping**: Simple one-to-one mapping where a target property directly copies the value from a single source measurement point. The target property expression contains only one source reference.

- **Non-Direct Mapping**: Complex mapping where a target property is calculated using an expression that can contain:
  - Multiple source measurement points
  - Mathematical operators
  - References to attributes and measurement points from different models

### 2.2 Current Implementation Status

- ✅ Direct mapping is fully implemented in `CalculateProcessor.processEachSourceAssetId()` 
- 🚧 Non-direct mapping needs to be implemented at line 262: `// TODO: Non-direct mapping processing logic to be implemented`

## 3. Architecture Design

### 3.1 Overall Processing Flow

```mermaid
flowchart TD
    A[Start Non-Direct Mapping] --> B[Analyze CalcPropertyMeta.srcPrefItems]
    B --> C[Group SrcPrefItems by modelId]
    C --> D{Determine Data Source}
    
    D -->|Current Model MeasurePoint| E[Get from LegacyPayload]
    D -->|Current Model Attribute| F[Query via SQL Gateway]
    D -->|Other Model Points/Attrs| F
    
    E --> G[Build calcData Map]
    F --> H[Call queryLatestValues]
    H --> I[Parse ResultSet to LegacyPayload]
    I --> G
    
    G --> J[Setup Expression Calculation Data]
    J --> K{isExprUseCurrentModel?}
    K -->|true| L[Add 'this.' prefix]
    K -->|false| M[Use full modelId.prefName]
    
    L --> N[Call ExpressionUtil.calcExpression]
    M --> N
    N --> O[Get Calculation Result]
    O --> P[Build LegacyMsgWithMultiAssets]
    P --> Q[Add to nonMappingTargetModel2MsgMap]
    Q --> R[End]
```

### 3.2 Key Components

1. **Data Query Module**: Handles querying attributes and measurement points from SQL Gateway
2. **Data Grouping Module**: Groups source preferences by model and data type
3. **Expression Calculation Module**: Prepares data and executes expression calculations
4. **Result Processing Module**: Constructs output messages from calculation results

## 4. Detailed Technical Design

### 4.1 SQL Gateway Query Interface

#### 4.1.1 Method Signature

```java
/**
 * Query latest values for assets from SQL Gateway
 * @param orgId Organization ID
 * @param modelId Model ID for the query
 * @param assetPropertyInfoMap Map<assetId, List<PropertyInfo>> - asset properties to query
 * @return Map<assetId, LegacyPayload> - latest values for each asset
 */
public Map<String, LegacyPayload> queryLatestValues(
    String orgId, 
    String modelId, 
    Map<String, List<PropertyInfo>> assetPropertyInfoMap
) {
    // Implementation details in section 5
}
```

#### 4.1.2 SQL Template Design

Using Velocity template engine for dynamic SQL construction:

```sql
SELECT
    /*+ ORG('$orgId') */
    #foreach($attrName in $attributeNames)
    /*+ SET(`$attrName`=TS(`$attrName`)) */
    #end
    #foreach($pointName in $measurePointNames)
    /*+ SET(`$pointName`=TS(`$pointName`)) */
    #end
    asset_id,
    #foreach($attrName in $attributeNames)
    $attrName#if($foreach.hasNext),#end
    #end
    #if($attributeNames.size() > 0 && $measurePointNames.size() > 0),#end
    #foreach($pointName in $measurePointNames)
    `${pointName}.time` AS `${pointName}_time`,
    `${pointName}.value` AS `${pointName}_value`#if($foreach.hasNext),#end
    #end
FROM `$modelId`
WHERE asset_id in (#foreach($assetId in $assetIds)'$assetId'#if($foreach.hasNext),#end#end);
```

### 4.2 Data Grouping Strategy

#### 4.2.1 SrcPrefItem Grouping Logic

```java
// Grouping Strategy:
// 1. Current model measurement points (modelId == LegacyMsg.modelId && prefType == MEASUREPOINT) 
//    -> Get directly from LegacyPayload
// 2. Current model attributes (modelId == LegacyMsg.modelId && prefType == ATTRIBUTE) 
//    -> Query via SQL Gateway
// 3. Other model points/attributes (modelId != LegacyMsg.modelId) 
//    -> Query via SQL Gateway

Map<String, Map<String, List<PropertyInfo>>> groupedSrcPrefs = new HashMap<>();
// Key1: modelId, Key2: assetId, Value: List<PropertyInfo>
```

#### 4.2.2 Data Source Classification

| Condition | Data Source | Processing Method |
|-----------|-------------|-------------------|
| `modelId == current && prefType == MEASUREPOINT` | LegacyPayload | Direct access |
| `modelId == current && prefType == ATTRIBUTE` | SQL Gateway | Query required |
| `modelId != current` | SQL Gateway | Query required |

### 4.3 Expression Calculation Data Preparation

#### 4.3.1 calcData Construction Rules

```java
Map<String, Object> calcData = new HashMap<>();

// Rule 1: Current model measurement points - from srcPayload
// Key: LegacyMsg.modelId + "." + prefName
// Value: srcPayload.points.get(prefName)

// Rule 2: Other data - from SQL Gateway query results  
// Key: SrcPrefItem.modelId + "." + prefName
// Value: queryResult.points.get(prefName)

// Rule 3: When isExprUseCurrentModel = true
// Key: "this." + prefName
// Value: corresponding measurement point value
```

#### 4.3.2 Expression Calculation Flow

```java
CalcExpressionRequest calcRequest = new CalcExpressionRequest();
List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);
Map<String, Object> calcData = new HashMap<>();

// Populate calcData with all required values
// Handle 'this.' prefix for current model expressions

calcInputs.add(
    ExpressionCalculatorInput.builder()
        .expression(targetProp.getExpression())
        .data(calcData)
        .build());
calcRequest.setCalcInputs(calcInputs);

List<CalcExpressionResponse> calcResult = ExpressionUtil.calcExpression(calcRequest);
ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();
```

### 4.4 Connection Pool Management

#### 4.4.1 MySQL JDBC Connection Pool Configuration

```java
// Add to CalculateProcessor class
private transient HikariDataSource sqlGatewayDataSource;

@Override
public void open(Configuration parameters) throws Exception {
    super.open(parameters);
    initSqlGatewayDataSource();
}

@Override
public void close() throws Exception {
    if (sqlGatewayDataSource != null) {
        sqlGatewayDataSource.close();
    }
    super.close();
}

private void initSqlGatewayDataSource() {
    HikariConfig config = new HikariConfig();
    config.setDriverClassName("com.mysql.cj.jdbc.Driver");
    config.setJdbcUrl(CalcLionConfig.getSqlGatewayJdbcUrl());
    config.setUsername(CalcLionConfig.getSqlGatewayUserName());
    config.setPassword(CalcLionConfig.getSqlGatewayPassword());
    config.setMaximumPoolSize(10);
    config.setConnectionTestQuery("SELECT 1");
    this.sqlGatewayDataSource = new HikariDataSource(config);
}
```

## 5. Implementation Modules

### 5.1 Core Methods to Implement

1. **`queryLatestValues()`** - SQL Gateway query interface
2. **`buildSqlTemplate()`** - SQL template construction
3. **`groupSrcPrefItems()`** - SrcPrefItem grouping
4. **`buildCalcData()`** - Calculation data construction
5. **`parseResultSetToLegacyPayload()`** - ResultSet parsing
6. **`initSqlGatewayDataSource()`** - Connection pool initialization
7. **`processNonDirectMapping()`** - Main non-direct mapping process

### 5.2 Error Handling Strategy

```java
// 1. SQL Gateway connection failure -> Log error, skip calculation
// 2. SQL execution failure -> Log error, skip calculation  
// 3. Expression calculation failure -> Log error, throw CalcFailedException
// 4. Data type conversion failure -> Log warning, use default value
```

### 5.3 Configuration Requirements

Required Lion configuration properties:
- `gravity-common.sql-gateway.jdbc-url`
- `gravity-common.sql-gateway.calculate.username`
- `gravity-common.sql-gateway.calculate.password`

## 6. Performance Optimization

### 6.1 Batch Query Optimization

- Group by modelId to reduce SQL Gateway query count
- Support multiple assetIds in single query for efficiency
- Use connection pooling for database connection reuse

### 6.2 Memory Optimization

- Release large object references promptly
- Use streaming processing to avoid memory accumulation
- Set reasonable connection pool size

### 6.3 Caching Strategy

- Consider short-term caching for frequently queried attribute values
- Cache compiled SQL template results

## 7. Testing Strategy

### 7.1 Unit Tests

1. **`queryLatestValues()` Method Tests**
   - Normal query scenarios
   - Empty result scenarios  
   - Exception scenarios

2. **SQL Template Construction Tests**
   - Different attribute/measurement point combinations
   - Special character handling
   - Template syntax correctness

3. **Expression Calculation Tests**
   - Simple mathematical operations
   - Complex expressions
   - Boundary value handling

### 7.2 Integration Tests

1. **End-to-End Tests**
   - Complete non-direct mapping flow
   - Multiple data source combinations
   - Performance benchmark tests

2. **SQL Gateway Integration Tests**
   - Real environment connection tests
   - Query result correctness verification
   - Connection pool management tests

## 8. Implementation Plan

### Phase 1: Infrastructure Setup
1. Add SQL Gateway connection pool management
2. Implement `queryLatestValues()` basic framework
3. Add Velocity template engine dependency

### Phase 2: Core Logic Implementation  
1. Implement SrcPrefItem grouping logic
2. Implement SQL template construction
3. Implement ResultSet parsing

### Phase 3: Expression Calculation Integration
1. Implement calcData construction logic
2. Integrate ExpressionUtil.calcExpression
3. Implement result processing

### Phase 4: Testing and Optimization
1. Write unit tests
2. Write integration tests
3. Performance optimization and debugging

## 9. Key Technical Details

### 9.1 Timestamp Handling
- When multiple measurement points have different timestamps, use the maximum timestamp
- Attributes typically don't have timestamps, use query time

### 9.2 Data Type Handling
- Support basic types: numeric, string, boolean
- Handle null values and exceptional data

### 9.3 Expression Compatibility
- Support `this.` prefix syntax
- Support full `modelId.prefName` syntax
- Handle special characters in expressions

## 10. Dependencies

### 10.1 Required Libraries
- Velocity Template Engine for SQL generation
- HikariCP for connection pooling
- MySQL JDBC Driver for SQL Gateway connectivity

### 10.2 Existing Components
- `ExpressionUtil.calcExpression()` for expression evaluation
- `CalcLionConfig` for configuration management
- `PropertyInfo` and `SrcPrefItem` data structures

---

**Document Version**: 1.0  
**Last Updated**: 2024-12-19  
**Author**: System Architecture Team
