package com.envision.gravity.flink.streaming.calculate.aspect.sink;

import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import com.envision.gravity.flink.streaming.calculate.utils.CalcLionConfig;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * AspectCalc Kafka记录序列化器
 * 
 * 功能：
 * 1. 将LegacyMsgWithMultiAssets序列化为Kafka ProducerRecord
 * 2. 动态构建Topic名称（基于orgId）
 * 3. 处理direct_mapping header
 * 4. 设置消息Key和Headers
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class AspectCalcKafkaRecordSerializer implements KafkaRecordSerializationSchema<LegacyMsgWithMultiAssets> {
    
    private static final Logger logger = LoggerFactory.getLogger(AspectCalcKafkaRecordSerializer.class);
    
    @Override
    public ProducerRecord<byte[], byte[]> serialize(LegacyMsgWithMultiAssets message, 
                                                   KafkaSinkContext context, 
                                                   Long timestamp) {
        
        try {
            // ✅ 1. 构建Topic名称（基于orgId）
            String topicName = buildTopicName(message);
            
            // ✅ 2. 构建消息Key（使用modelId）
            String messageKey = message.getModelId();
            byte[] keyBytes = messageKey != null ? messageKey.getBytes(StandardCharsets.UTF_8) : null;
            
            // ✅ 3. 序列化消息体
            byte[] valueBytes = serializeMessageValue(message);
            
            // ✅ 4. 创建ProducerRecord
            ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(
                topicName,
                null,  // partition (let Kafka decide)
                timestamp,
                keyBytes,
                valueBytes
            );
            
            // ✅ 5. 添加Headers
            addMessageHeaders(record, message);
            
            logger.debug("Serialized message for topic: {}, key: {}, headers: {}", 
                        topicName, messageKey, message.getHeaders());
            
            return record;
            
        } catch (Exception e) {
            logger.error("Failed to serialize message for model: {}, orgId: {}", 
                        message.getModelId(), message.getOrgId(), e);
            throw new RuntimeException("Message serialization failed", e);
        }
    }
    
    /**
     * ✅ 构建Topic名称
     */
    private String buildTopicName(LegacyMsgWithMultiAssets message) {
        String orgId = extractOrgId(message);
        String topicPattern = CalcLionConfig.getAspectCalcKafkaSinkTopicPattern();
        
        // 构建完整的Topic名称：MEASURE_POINT_CAL_ + orgId
        String topicName = topicPattern + orgId;
        
        logger.debug("Built topic name: {} for orgId: {}", topicName, orgId);
        return topicName;
    }
    
    /**
     * ✅ 提取orgId
     */
    private String extractOrgId(LegacyMsgWithMultiAssets message) {
        // 优先从Headers中获取
        String orgId = message.getHeaders().get("org_id");
        
        if (orgId == null || orgId.isEmpty()) {
            // 从消息体中获取
            orgId = message.getOrgId();
        }
        
        if (orgId == null || orgId.isEmpty()) {
            throw new IllegalArgumentException("OrgId not found in message: " + message.getModelId());
        }
        
        return orgId;
    }
    
    /**
     * ✅ 序列化消息体
     */
    private byte[] serializeMessageValue(LegacyMsgWithMultiAssets message) throws Exception {
        // 使用Jackson序列化为JSON
        com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
        return objectMapper.writeValueAsBytes(message);
    }
    
    /**
     * ✅ 添加消息Headers
     */
    private void addMessageHeaders(ProducerRecord<byte[], byte[]> record, LegacyMsgWithMultiAssets message) {
        
        // 添加消息自带的Headers
        for (java.util.Map.Entry<String, String> entry : message.getHeaders().entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            if (value != null) {
                record.headers().add(key, value.getBytes(StandardCharsets.UTF_8));
            }
        }
        
        // ✅ 添加AspectCalc特有的Headers
        record.headers().add("source", "AspectCalcFlow".getBytes(StandardCharsets.UTF_8));
        record.headers().add("timestamp", String.valueOf(System.currentTimeMillis()).getBytes(StandardCharsets.UTF_8));
        
        // ✅ 检查并添加direct_mapping header
        if (isDirectMapping(message)) {
            record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
            logger.debug("Added direct_mapping header for model: {}", message.getModelId());
        }
        
        // 添加模型信息
        if (message.getModelId() != null) {
            record.headers().add("model_id", message.getModelId().getBytes(StandardCharsets.UTF_8));
        }
        
        if (message.getModelIdPath() != null) {
            record.headers().add("model_path", message.getModelIdPath().getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * ✅ 检查是否为直白映射
     */
    private boolean isDirectMapping(LegacyMsgWithMultiAssets message) {
        // 从Headers中检查direct_mapping标记
        String directMappingFlag = message.getHeaders().get("direct_mapping");
        return "1".equals(directMappingFlag) || "true".equalsIgnoreCase(directMappingFlag);
    }
}
