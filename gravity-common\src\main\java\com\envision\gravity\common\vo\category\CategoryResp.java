package com.envision.gravity.common.vo.category;

import java.util.Date;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/02/20 10:39 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryResp {

    private String categoryId;
    private String categoryDisplayName;
    private String createdUser;
    private String modifiedUser;
    private Date createdTime;
    private Date modifiedTime;
}
