package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

@Data
@Builder
@NoArgsConstructor
public class TblPropertyDownstreamRuleCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(
                                Types.VARCHAR, "pref_rule_id", String.class, "pref_rule_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(
                                Types.VARCHAR, "pref_rule_id", String.class, "pref_rule_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "target_category", String.class, "target_category"),
                        new JdbcTypeField(
                                Types.VARCHAR, "target_comp_id", String.class, "target_comp_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "target_pref_id", String.class, "target_pref_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "src_category", String.class, "src_category"),
                        new JdbcTypeField(
                                Types.VARCHAR, "src_comp_id", String.class, "src_comp_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "src_pref_id", String.class, "src_pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "expression", String.class, "expression"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("pref_rule_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("pref_rule_id");
        NOT_NULL_FIELDS.add("target_category");
        NOT_NULL_FIELDS.add("target_comp_id");
        NOT_NULL_FIELDS.add("target_pref_id");
        NOT_NULL_FIELDS.add("src_category");
        NOT_NULL_FIELDS.add("src_comp_id");
        NOT_NULL_FIELDS.add("src_pref_id");
        NOT_NULL_FIELDS.add("expression");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("src_comp_id"),
                        new QueryIndex("src_pref_id"),
                        new QueryIndex("src_category"));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("pref_rule_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("target_category", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("target_comp_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("target_pref_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("src_category", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("src_comp_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("src_pref_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("expression", "java.lang.String");

        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
