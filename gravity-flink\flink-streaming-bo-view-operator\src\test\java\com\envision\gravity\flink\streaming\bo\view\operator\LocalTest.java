package com.envision.gravity.flink.streaming.bo.view.operator;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @description
 */
class LocalTest {

    @Test
    void testGenQueryModelIdsSql() {
        System.out.println(genQueryModelIdsSql("o17231990549791976", "EnOS_Wind_Farm", null));
        System.out.println(
                genQueryModelIdsSql("o17231990549791976", "EnOS_Wind_Farm", "/EnOS_Wind_Farm"));
    }

    private String genQueryModelIdsSql(String schemaName, String modelId, String modelPath) {
        String parentModelPath = modelPath == null ? "/" + modelId : modelPath;
        String childrenModelPath = parentModelPath + "/%";
        return String.format(
                "select model_id from %s.tbl_bo_model where model_path = '%s' or model_path like '%s'",
                schemaName, parentModelPath, childrenModelPath);
    }
}
