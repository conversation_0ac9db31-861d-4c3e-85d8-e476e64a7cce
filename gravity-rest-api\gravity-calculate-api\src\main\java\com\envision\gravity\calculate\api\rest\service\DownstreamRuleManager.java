package com.envision.gravity.calculate.api.rest.service;

import com.envision.gravity.calculate.api.rest.dto.DownstreamAssetInfo;
import com.envision.gravity.calculate.api.rest.dto.DownstreamPropertyKey;
import com.envision.gravity.calculate.api.rest.dto.DownstreamRule;
import com.envision.gravity.calculate.api.rest.web.CalcRestConfig;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.calculate.PropertyInfo;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Stopwatch;
import com.univers.business.object.calc.dto.ExpressionParserInput;
import com.univers.business.object.calc.dto.ExpressionParserOutput;
import com.univers.business.object.calc.request.ParseExpressionRequest;
import com.univers.business.object.calc.response.ParseExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facebook.airlift.concurrent.Threads.threadsNamed;
import static java.util.concurrent.Executors.newScheduledThreadPool;

@Component
public class DownstreamRuleManager {

    private static final Logger logger = LoggerFactory.getLogger(DownstreamRuleManager.class);

    private static final String DOWNSTREAM_TABLE_NAME = "TBL_PROPERTY_DOWNSTREAM_RULE";

    private static final int PAGE_SIZE = CalcRestConfig.getCalcDownstreamRuleLoadPageSize();

    private final ModelMetaQueryHandler modelMetaQueryHandler;

    // 根据 sys_create_time 和 sys_modified_time 缓存，这两个时间由 bo level engine 更新
    private final Map<String, Instant> orgLastLoadTimeMap = new ConcurrentHashMap<>();

    // <SrcPrefName, <SrcModelId, SrcCompId + SrcPrefId + SrcCategory>>
    // SrcPrefName is request pointId
    // SrcModelId get by request assetId
    // SrcCompId + SrcPrefId + SrcCategoryId + TargetCategoryId => DownstreamRule
    private final Map<String, Cache<String, Map<String, DownstreamRule>>>
            srcPrefName2DownstreamRuleMap;

    private final ScheduledExecutorService ruleRefreshExecutor =
            newScheduledThreadPool(1, threadsNamed("Downstream-rule-refresher-%s"));

    public DownstreamRuleManager() {
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
        this.srcPrefName2DownstreamRuleMap = new HashMap<>();

        this.ruleRefreshExecutor.scheduleAtFixedRate(
                new RefreshRuleTask(),
                2,
                CalcRestConfig.getCalcDownstreamRuleRefreshIntervalSeconds(),
                TimeUnit.SECONDS);
    }

    class RefreshRuleTask implements Runnable {
        @Override
        public void run() {
            batchLoad();
        }
    }

    public void batchLoad() {
        List<String> schemas = getSchemas();
        for (String schema : schemas) {
            Stopwatch schemaStopwatch = Stopwatch.createStarted();
            int recordsProcessed = 0;

            try {
                if (isTableExists(schema, DOWNSTREAM_TABLE_NAME)) {
                    readTableWithPagination(schema, null);
                } else {
                    logger.info(
                            "Table [{}] not exist in database [{}]", DOWNSTREAM_TABLE_NAME, schema);
                }
            } finally {
                schemaStopwatch.stop();
                logger.info(
                        "Schema [{}] load completed - Records: {}, Time taken: {} ms",
                        schema,
                        recordsProcessed,
                        schemaStopwatch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }

    public void scheduledRefresh() {
        List<String> schemas = getSchemas();
        for (String schema : schemas) {
            if (isTableExists(schema, DOWNSTREAM_TABLE_NAME)) {
                Instant lastLoadTime = orgLastLoadTimeMap.getOrDefault(schema, Instant.EPOCH);
                readTableWithPagination(schema, lastLoadTime);
            }
        }
    }

    private List<String> getSchemas() {
        String sql = "SELECT DISTINCT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA";
        List<List<?>> results = IgniteUtil.query("PUBLIC", sql);
        if (GTCommonUtils.emptyCollection(results)) {
            logger.error("Maybe new ignite cluster, no schema init ...");
            return Collections.emptyList();
        }

        return results.stream()
                .map(row -> (String) row.get(0))
                .filter(schema -> schema.startsWith("O"))
                .map(schema -> "o" + schema.substring(1))
                .collect(Collectors.toList());
    }

    private boolean isTableExists(String schema, String tableName) {
        String sql =
                String.format(
                        "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES "
                                + "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'",
                        schema.toUpperCase(), tableName);
        List<List<?>> results = IgniteUtil.query("PUBLIC", sql);
        return !GTCommonUtils.emptyCollection(results) && ((Long) results.get(0).get(0)) > 0;
    }

    private void readTableWithPagination(String orgId, Instant lastLoadTime) {
        int offset = 0;
        boolean hasMore = true;
        int totalProcessed = 0;
        Instant maxTime = lastLoadTime != null ? lastLoadTime : Instant.EPOCH;

        while (hasMore) {
            String timeFilter =
                    lastLoadTime != null
                            ? String.format(
                                    "AND (SYS_CREATED_TIME >= '%s' OR SYS_MODIFIED_TIME >= '%s')",
                                    lastLoadTime, lastLoadTime)
                            : "";

            String sql =
                    String.format(
                            "SELECT PREF_RULE_ID, TARGET_COMP_ID, TARGET_PREF_ID, TARGET_CATEGORY, "
                                    + "SRC_CATEGORY, SRC_COMP_ID, SRC_PREF_ID, EXPRESSION, "
                                    + "SYS_CREATED_TIME, SYS_MODIFIED_TIME "
                                    + "FROM %s.%s WHERE 1=1 %s "
                                    + "ORDER BY SYS_MODIFIED_TIME "
                                    + "LIMIT %d OFFSET %d",
                            orgId, DOWNSTREAM_TABLE_NAME, timeFilter, PAGE_SIZE, offset);

            List<List<?>> results = IgniteUtil.query(orgId, sql);
            if (GTCommonUtils.emptyCollection(results)) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
                continue;
            }

            Map<DownstreamPropertyKey, DownstreamRule> ruleMap = new HashMap<>(results.size());
            for (List<?> row : results) {
                String prefRuleId = (String) row.get(0);
                String targetCompId = (String) row.get(1);
                String targetPrefId = (String) row.get(2);
                String targetCategory = (String) row.get(3);
                String srcCategory = (String) row.get(4);
                String srcCompId = (String) row.get(5);
                String srcPrefId = (String) row.get(6);
                String expression = (String) row.get(7);

                DownstreamPropertyKey key =
                        DownstreamPropertyKey.builder()
                                .srcCategory(srcCategory)
                                .srcCompId(srcCompId)
                                .srcPrefId(srcPrefId)
                                .build();

                DownstreamRule rule =
                        DownstreamRule.builder()
                                .targetCompId(targetCompId)
                                .targetPrefId(targetPrefId)
                                .targetCategory(targetCategory)
                                .srcCategory(srcCategory)
                                .srcCompId(srcCompId)
                                .srcPrefId(srcPrefId)
                                .expression(expression)
                                .build();

                boolean isValidExpr = isValidExpr(orgId, prefRuleId, rule);
                rule.setValidExpr(isValidExpr);
                ruleMap.put(key, rule);

                // 更新最大时间
                maxTime =
                        Collections.max(
                                Arrays.asList(
                                        maxTime,
                                        Instant.ofEpochMilli(rule.getSysCreatedTime()),
                                        Instant.ofEpochMilli(rule.getSysModifiedTime())));
            }

            Set<PropertyId> srcPropertyIds =
                    ruleMap.keySet().stream()
                            .map(rule -> new PropertyId(rule.getSrcCompId(), rule.getSrcPrefId()))
                            .collect(Collectors.toSet());
            Map<PropertyId, PropertyInfo> srcPropertyInfoMap =
                    modelMetaQueryHandler.getPropertyByCompIdAndPrefId(orgId, srcPropertyIds);

            Set<PropertyId> targetPropertyIds =
                    ruleMap.values().stream()
                            .map(
                                    rule ->
                                            new PropertyId(
                                                    rule.getTargetCompId(), rule.getTargetPrefId()))
                            .collect(Collectors.toSet());
            Map<PropertyId, PropertyInfo> targetPropertyInfoMap =
                    modelMetaQueryHandler.getPropertyByCompIdAndPrefId(orgId, targetPropertyIds);

            Set<String> modelIds =
                    srcPropertyInfoMap.values().stream()
                            .filter(Objects::nonNull)
                            .map(PropertyInfo::getModelId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            Map<String, String> modelCategoryMap =
                    modelMetaQueryHandler.getModelCategory(orgId, modelIds);

            // prefName(with compName prefix) => <modelId, Rule>
            Map<String, Map<String, DownstreamRule>> ruleCacheMap = new HashMap<>();
            for (PropertyInfo srcPropInfo : srcPropertyInfoMap.values()) {
                DownstreamPropertyKey dk =
                        DownstreamPropertyKey.builder()
                                .srcCompId(srcPropInfo.getCompId())
                                .srcPrefId(srcPropInfo.getPrefId())
                                .srcCategory(modelCategoryMap.get(srcPropInfo.getModelId()))
                                .build();

                // Set target prefName
                DownstreamRule rule = ruleMap.get(dk);
                rule.setPrefType(srcPropInfo.getPrefType());
                Optional<String> targetPrefNameInfo =
                        getTargetPrefName(
                                rule.getTargetCompId(),
                                rule.getTargetPrefId(),
                                targetPropertyInfoMap);
                if (targetPrefNameInfo.isPresent()) {
                    rule.setTargetPrefName(targetPrefNameInfo.get());
                } else {
                    logger.error("Target prefName not found for rule: {}", rule);
                }

                ruleCacheMap
                        .computeIfAbsent(srcPropInfo.getPrefName(), k -> new HashMap<>())
                        .put(srcPropInfo.getModelId(), ruleMap.get(dk));
            }

            // Update cache
            if (GTCommonUtils.nonEmptyMap(ruleCacheMap)) {
                Cache<String, Map<String, DownstreamRule>> ouRuleMetaCache =
                        this.srcPrefName2DownstreamRuleMap.computeIfAbsent(
                                orgId,
                                k -> {
                                    logger.info("Init orgId {} rule cache", orgId);
                                    return Caffeine.newBuilder()
                                            .maximumSize(
                                                    CalcRestConfig
                                                            .getCalcDownstreamRuleCacheMaxSize())
                                            .expireAfterWrite(
                                                    CalcRestConfig
                                                            .getCalcDownstreamRuleCacheExpireTimeSeconds(),
                                                    TimeUnit.SECONDS)
                                            .build();
                                });
                ouRuleMetaCache.putAll(ruleCacheMap);
            }

            totalProcessed += ruleMap.size();
            offset += PAGE_SIZE;
            if (results.size() < PAGE_SIZE) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
            }
        }

        // Update last load time
        orgLastLoadTimeMap.put(orgId, maxTime);
    }

    // 更新获取缓存数据的方法
    public Optional<DownstreamRule> getDownstreamRule(
            String orgId, String srcPrefName, DownstreamAssetInfo assetInfo) {
        String srcModelId = assetInfo.getSrcModelId();
        String srcCategoryId = assetInfo.getSrcCategory();
        String targetCategoryId = assetInfo.getTargetCategory();
        Cache<String, Map<String, DownstreamRule>> orgRuleCache =
                srcPrefName2DownstreamRuleMap.get(orgId);
        Map<String, DownstreamRule> srcModelRules = orgRuleCache.getIfPresent(srcPrefName);
        if (GTCommonUtils.isEmpty(srcModelRules)) {
            logger.warn("orgId {}, srcPrefName {} rules not found", orgId, srcPrefName);
            return Optional.empty();
        }

        DownstreamRule rule = srcModelRules.get(srcModelId);
        if (rule == null) {
            logger.warn(
                    "orgId {}, srcPrefName {}, srcModelId {} rule not found",
                    orgId,
                    srcPrefName,
                    srcModelId);
            return Optional.empty();
        }

        if (!rule.getSrcCategory().equals(srcCategoryId)
                || !rule.getTargetCategory().equals(targetCategoryId)) {
            logger.error(
                    "orgId {}, srcPrefName {}, srcModelId {} rule's info srcCategoryId {}, targetCategoryId {} "
                            + " not match input param srcCategoryId {}, targetCategoryId {}",
                    orgId,
                    srcPrefName,
                    srcModelId,
                    rule.getSrcCategory(),
                    rule.getTargetCategory(),
                    srcCategoryId,
                    targetCategoryId);
            return Optional.empty();
        }

        return Optional.of(rule);
    }

    private Optional<String> getTargetPrefName(
            String compId, String prefId, Map<PropertyId, PropertyInfo> propertyInfoMap) {
        for (Map.Entry<PropertyId, PropertyInfo> entry : propertyInfoMap.entrySet()) {
            if (compId.equals(entry.getKey().getCompId())
                    && prefId.equals(entry.getKey().getPrefId())) {
                return Optional.of(entry.getValue().getPrefName());
            }
        }
        return Optional.empty();
    }

    private boolean isValidExpr(String orgId, String prefRuleId, DownstreamRule rule) {
        ParseExpressionRequest parseExprReq = new ParseExpressionRequest();
        List<ExpressionParserInput> inputs =
                Collections.singletonList(
                        ExpressionParserInput.builder().expression(rule.getExpression()).build());
        parseExprReq.setParseInputs(inputs);

        try {
            List<ParseExpressionResponse> parseExprRes =
                    ExpressionUtil.parseExpression(parseExprReq);
            if (GTCommonUtils.emptyCollection(parseExprRes)) {
                logger.error(
                        "Downstream rule parse result is empty, orgId:{}, prefRuleId: {}, record: {}",
                        orgId,
                        prefRuleId,
                        rule);
                return false;
            }

            ExpressionParserOutput output = parseExprRes.get(0).getExpressionParsedResult();
            if (GTCommonUtils.emptyCollection(output.getSrcProperties())) {
                logger.error(
                        "Downstream rule target property is empty, orgId:{}, prefRuleId: {}, record: {}",
                        orgId,
                        prefRuleId,
                        rule);
                return false;
            }

            if (output.getSrcProperties().size() > 1) {
                logger.error(
                        "Downstream rule target property size is more than one, orgId:{}, prefRuleId: {}, record: {}",
                        orgId,
                        prefRuleId,
                        rule);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.error(
                    "Downstream rule parse failed, orgId:{}, prefRuleId: {}, record: {}, detail: {}",
                    orgId,
                    prefRuleId,
                    rule,
                    e.getMessage());
            return false;
        }
    }

    /**
     * 获取 cacheView 结构
     *
     * @param orgId 组织ID
     * @return Map，包含 rules 和 lastLoadTime
     */
    public Map<String, Object> getCacheView(String orgId) {
        Map<String, Object> data = new HashMap<>();
        ArrayList<Object> rules = new ArrayList<>();
        Cache<String, Map<String, DownstreamRule>> orgRuleCache =
                srcPrefName2DownstreamRuleMap.get(orgId);
        if (orgRuleCache != null) {
            for (String prefName : orgRuleCache.asMap().keySet()) {
                Map<String, DownstreamRule> model2ruleMap = orgRuleCache.getIfPresent(prefName);
                if (model2ruleMap != null) {
                    ArrayList<Object> model2rule = new ArrayList<>();
                    for (Map.Entry<String, DownstreamRule> entry : model2ruleMap.entrySet()) {
                        Map<String, Object> modelRule = new HashMap<>();
                        modelRule.put("modelId", entry.getKey());
                        modelRule.put("info", entry.getValue());
                        model2rule.add(modelRule);
                    }
                    Map<String, Object> ruleObj = new HashMap<>();
                    ruleObj.put("prefName", prefName);
                    ruleObj.put("model2rule", model2rule);
                    rules.add(ruleObj);
                }
            }
        }
        data.put("rules", rules);
        Instant lastLoadTime = orgLastLoadTimeMap.get(orgId);
        if (lastLoadTime != null) {
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                            .withZone(ZoneId.systemDefault());
            data.put("lastLoadTime", formatter.format(lastLoadTime));
        } else {
            data.put("lastLoadTime", null);
        }
        return data;
    }
}
