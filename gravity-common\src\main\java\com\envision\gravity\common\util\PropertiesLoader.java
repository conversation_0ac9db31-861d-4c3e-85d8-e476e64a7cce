package com.envision.gravity.common.util;

import com.envision.gravity.common.exception.GravityRuntimeException;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;


import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/1/17
 * @description
 */
@Slf4j
public class PropertiesLoader {
    public static Properties load(String path) {
        try (InputStream inputStream =
                PropertiesLoader.class.getClassLoader().getResourceAsStream(path)) {
            Properties props = new Properties();
            props.load(inputStream);
            return props;
        } catch (IOException e) {
            log.warn("Load Properties Ex", e);
            throw new GravityRuntimeException("Load Properties Ex", e);
        }
    }
}
