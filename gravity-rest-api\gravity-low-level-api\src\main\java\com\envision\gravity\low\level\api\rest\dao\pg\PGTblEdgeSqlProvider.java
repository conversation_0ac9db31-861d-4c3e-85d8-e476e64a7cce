package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblEdge;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.util.AuditUtil;
import com.envision.gravity.low.level.api.rest.util.RestTimeUtils;

import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/3
 * @description
 */
public class PGTblEdgeSqlProvider {

    public String selectGraphIdByAssetIds(Set<String> assetIds, String orgId) {
        String sql =
                String.format(
                        "SELECT start_vid as asset_id, sub_graph_id FROM %1$s.tbl_start_vid WHERE start_vid IN (%2$s)\n"
                                + "UNION\n"
                                + "SELECT to_vid as asset_id, sub_graph_id FROM %1$s.tbl_edge WHERE to_vid IN (%2$s);",
                        orgId, assetIdExpr(assetIds));
        return sql;
    }

    private String assetIdExpr(Set<String> assetIds) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (String id : assetIds) {
            sb.append("'").append(id).append("'");
            if (i != assetIds.size() - 1) {
                sb.append(",");
            }
            i++;
        }
        return sb.toString();
    }

    public String insertUpdateEdges(List<TblEdge> tblEdges, String orgId, AuditHeader auditHeader) {
        String currentUTCTimeString = RestTimeUtils.getCurrentUTCTimeString();
        String updPart =
                tblEdges.stream()
                        .map(
                                tblEdge -> {
                                    String auditUsername = AuditUtil.getAuditUsername(auditHeader);
                                    String propertyName =
                                            auditUsername != null
                                                    ? String.format("'%s'", auditUsername)
                                                    : null;

                                    return String.format(
                                            "('%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, '%s', %s, '%s', '%s')",
                                            tblEdge.getFromVid(),
                                            tblEdge.getToVid(),
                                            tblEdge.getEdgeTypeId(),
                                            tblEdge.getSubGraphId(),
                                            tblEdge.getPropValue(),
                                            tblEdge.getTreeNodeId(),
                                            currentUTCTimeString,
                                            propertyName,
                                            currentUTCTimeString,
                                            propertyName,
                                            currentUTCTimeString,
                                            currentUTCTimeString);
                                })
                        .collect(Collectors.joining(","));

        return String.format(
                "INSERT INTO "
                        + orgId
                        + ".tbl_edge (from_vid, to_vid, edge_type_id, sub_graph_id, prop_value, tree_node_id, "
                        + "created_time, created_user, modified_time, modified_user, sys_created_time, sys_modified_time)"
                        + " VALUES %s ON CONFLICT (from_vid, to_vid, edge_type_id, sub_graph_id)\n"
                        + "DO UPDATE \n"
                        + "SET from_vid=EXCLUDED.from_vid, "
                        + "to_vid=EXCLUDED.to_vid, "
                        + "edge_type_id=EXCLUDED.edge_type_id, "
                        + "sub_graph_id=EXCLUDED.sub_graph_id, "
                        + "prop_value=EXCLUDED.prop_value, "
                        + "modified_time=EXCLUDED.modified_time, "
                        + "modified_user=EXCLUDED.modified_user, "
                        + "sys_modified_time=EXCLUDED.sys_modified_time;",
                updPart);
    }

    public String batchDeleteByPrimaryKey(List<TblEdge> tblEdges, String orgId) {
        String delPart =
                tblEdges.stream()
                        .map(
                                tblEdge ->
                                        String.format(
                                                "(from_vid = '%s' and to_vid = '%s' and edge_type_id = '%s' and sub_graph_id = '%s')",
                                                tblEdge.getFromVid(),
                                                tblEdge.getToVid(),
                                                tblEdge.getEdgeTypeId(),
                                                tblEdge.getSubGraphId()))
                        .collect(Collectors.joining(" or "));

        SQL sql = new SQL();
        sql.DELETE_FROM(orgId + ".tbl_edge");
        sql.WHERE(delPart);

        return sql.toString();
    }
}
