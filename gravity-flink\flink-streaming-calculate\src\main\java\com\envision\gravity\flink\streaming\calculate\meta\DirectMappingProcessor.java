package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.PropertyFieldInfo;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingKey;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingRecord;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.IgniteClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DirectMappingProcessor {

    private static final Logger logger = LoggerFactory.getLogger(DirectMappingProcessor.class);

    private static final String FIELD_MAPPING_TABLE_NAME = "TBL_COMPONENT_PREF_FIELD_MAPPING";

    private static final String FIELD_MAPPING_CACHE_NAME = "_TBL_COMPONENT_PREF_FIELD_MAPPING";

    private static final String FIELD_MAPPING_CACHE_KEY = FIELD_MAPPING_CACHE_NAME + "_KEY";
    private static final String FIELD_MAPPING_CACHE_VALUE = FIELD_MAPPING_CACHE_NAME + "_VALUE";

    private final SqlSessionFactory sqlSessionFactory;

    private static volatile DirectMappingProcessor uniqueInstance;

    public static DirectMappingProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (DirectMappingProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new DirectMappingProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private DirectMappingProcessor() {
        System.out.println("DirectMappingProcessor init ...");
        this.sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
        System.out.println("DirectMappingProcessor init done...");
    }

    public Set<FieldMappingKey> getFieldMappingKeys(String orgId, Set<String> fieldIds) {
        if (GTCommonUtils.emptyCollection(fieldIds)) {
            logger.error("FieldIds is required ...");
            return Collections.emptySet();
        }

        String sql =
                String.format(
                        "SELECT COMP_ID, PREF_ID, FIELD_ID FROM %s.%s WHERE field_id IN (%s)",
                        orgId, FIELD_MAPPING_TABLE_NAME, GTCommonUtils.concatStr(fieldIds));
        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            logger.error("No results found for orgId: [{}], fieldIds: [{}]", orgId, fieldIds);
            return Collections.emptySet();
        }

        return results.stream()
                .map(
                        row ->
                                FieldMappingKey.builder()
                                        .compId((String) row.get(0))
                                        .prefId((String) row.get(1))
                                        .fieldId((String) row.get(2))
                                        .build())
                .collect(Collectors.toSet());
    }

    public Map<FieldMappingKey, FieldMappingRecord> getFieldMappings(
            String orgId,
            List<CalcPropertyMeta> calcPropertyMetas,
            Map<Pair<String, String>, PropertyFieldInfo> srcPrefFieldInfos) {
        if (GTCommonUtils.emptyCollection(calcPropertyMetas)) {
            logger.error("Target property params is required ...");
            return Collections.emptyMap();
        }

        List<CalcPropertyMeta> directMappingMetas =
                calcPropertyMetas.stream()
                        .filter(CalcPropertyMeta::isDirectMapping)
                        .collect(Collectors.toList());
        if (GTCommonUtils.emptyCollection(directMappingMetas)) {
            logger.warn("Direct mapping property not found ...");
            return Collections.emptyMap();
        }

        // One source property's data field used by multiple target property
        Map<SrcPrefItem, Set<FieldMappingRecord>> srcPref2FieldMappings =
                new HashMap<>(directMappingMetas.size());
        for (CalcPropertyMeta calcPrefMeta : calcPropertyMetas) {
            // Direct mapping only one src pref
            SrcPrefItem srcPrefItem = calcPrefMeta.getSrcPrefItems().get(0);
            FieldMappingRecord fieldMappingRecord =
                    FieldMappingRecord.builder()
                            .compId(calcPrefMeta.getTargetCompId())
                            .prefId(calcPrefMeta.getTargetPrefId())
                            .build();

            srcPref2FieldMappings
                    .computeIfAbsent(srcPrefItem, k -> new HashSet<>())
                    .add(fieldMappingRecord);
        }

        // Only return source field info exist records
        Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap = new HashMap<>();
        for (Map.Entry<SrcPrefItem, Set<FieldMappingRecord>> entry :
                srcPref2FieldMappings.entrySet()) {
            SrcPrefItem srcPref = entry.getKey();
            PropertyFieldInfo srcPrefInfo =
                    srcPrefFieldInfos.get(Pair.of(srcPref.getModelId(), srcPref.getPrefName()));
            if (srcPrefInfo == null) {
                logger.error("Source property field info not found, skip: {}", srcPref);
                continue;
            }

            for (FieldMappingRecord fmr : entry.getValue()) {
                fmr.setFieldId(srcPrefInfo.getFieldId());
                fmr.setFieldIndex(srcPrefInfo.getFieldIndex());
                fmr.setHorizontal(srcPrefInfo.getHorizontal());
                fmr.setRawFieldId(srcPrefInfo.getRawFieldId());

                FieldMappingKey fmk =
                        FieldMappingKey.builder()
                                .compId(fmr.getCompId())
                                .prefId(fmr.getPrefId())
                                .fieldId(fmr.getFieldId())
                                .build();
                fieldMappingRecordMap.put(fmk, fmr);
            }
        }

        return fieldMappingRecordMap;
    }

    public void deleteCompPrefFieldMapping(String orgId, Set<FieldMappingKey> fieldMappingKeys) {
        if (GTCommonUtils.emptyCollection(fieldMappingKeys)) {
            logger.warn("To delete field mapping records is empty...");
            return;
        }

        deleteIgniteCompPrefFieldMapping(orgId, fieldMappingKeys);
        deletePgCompPrefFieldMapping(orgId, fieldMappingKeys);
    }

    private void deleteIgniteCompPrefFieldMapping(
            String orgId, Set<FieldMappingKey> fieldMappingKeys) {
        long start = System.currentTimeMillis();
        StringJoiner conditions = new StringJoiner(" OR ");
        for (FieldMappingKey key : fieldMappingKeys) {
            String compId = key.getCompId();
            String prefId = key.getPrefId();
            String fieldId = key.getFieldId();
            conditions.add(
                    String.format(
                            "(comp_id = '%s' AND pref_id = '%s' AND field_id = '%s')",
                            compId, prefId, fieldId));
        }

        String sql =
                String.format(
                        "DELETE FROM %s.%s WHERE (%s)",
                        orgId.toUpperCase(), FIELD_MAPPING_TABLE_NAME, conditions);
        IgniteUtil.query(orgId, sql);
        logger.info(
                ">>>>> [NOTICE] Delete ignite field mapping records {}, cost: {} ms",
                fieldMappingKeys,
                System.currentTimeMillis() - start);
    }

    private void deletePgCompPrefFieldMapping(String orgId, Set<FieldMappingKey> fieldMappingKeys) {
        long start = System.currentTimeMillis();
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblFieldMappingMapper fieldMappingMapper =
                    session.getMapper(TblFieldMappingMapper.class);
            fieldMappingMapper.batchDelete(orgId, fieldMappingKeys);
        }
        logger.info(
                ">>>>> [NOTICE] Delete pg field mapping records {}, cost: {} ms",
                fieldMappingKeys,
                System.currentTimeMillis() - start);
    }

    /**
     * Both ignite and pg to update
     *
     * @param orgId
     * @param fieldMappingRecordMap
     */
    public void updateCompPrefFieldMapping(
            String orgId, Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap) {
        if (GTCommonUtils.isEmpty(fieldMappingRecordMap)) {
            return;
        }

        boolean isUpdate = false;
        try {
            isUpdate = updateIgniteCompPrefFieldMapping(orgId, fieldMappingRecordMap);
        } catch (Exception e) {
            logger.error(
                    "Update ignite {}.tbl_comp_pref_field_mapping failed: {}",
                    orgId,
                    e.getMessage());
        }

        if (isUpdate) {
            try {
                updatePgCompPrefFieldMapping(orgId, fieldMappingRecordMap);
            } catch (Exception e) {
                logger.error(
                        "Update pg {}.tbl_comp_pref_field_mapping failed: {}",
                        orgId,
                        e.getMessage());
            }
        }
    }

    private void updatePgCompPrefFieldMapping(
            String orgId, Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblFieldMappingMapper fieldMappingMapper =
                    session.getMapper(TblFieldMappingMapper.class);
            fieldMappingMapper.batchUpdate(orgId, fieldMappingRecordMap);
        } catch (Exception e) {
            logger.error(
                    "Update pg {}.tbl_comp_pref_field_mapping failed: {}", orgId, e.getMessage());
        }
    }

    private boolean updateIgniteCompPrefFieldMapping(
            String orgId, Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap) {
        IgniteClient igniteClient = IgniteUtil.getIgniteClient();
        ClientCache<BinaryObject, BinaryObject> cache =
                IgniteUtil.getCache(orgId + FIELD_MAPPING_CACHE_NAME).withKeepBinary();
        Map<BinaryObject, BinaryObject> toUpdateFieldMappings = new HashMap<>();

        Set<BinaryObject> keySets = new HashSet<>();
        for (Map.Entry<FieldMappingKey, FieldMappingRecord> entry :
                fieldMappingRecordMap.entrySet()) {
            BinaryObjectBuilder keyBuilder =
                    igniteClient.binary().builder(orgId + FIELD_MAPPING_CACHE_KEY);
            FieldMappingKey fieldMappingKey = entry.getKey();

            keyBuilder.setField("comp_id", fieldMappingKey.getCompId());
            keyBuilder.setField("pref_id", fieldMappingKey.getPrefId());
            keyBuilder.setField("field_id", fieldMappingKey.getFieldId());
            keySets.add(keyBuilder.build());
        }

        Map<BinaryObject, BinaryObject> existFieldMappings = cache.getAll(keySets);

        for (Map.Entry<FieldMappingKey, FieldMappingRecord> newEntry :
                fieldMappingRecordMap.entrySet()) {
            FieldMappingKey fieldMappingKey = newEntry.getKey();
            FieldMappingRecord newRecord = newEntry.getValue();

            BinaryObjectBuilder keyBuilder =
                    igniteClient.binary().builder(orgId + FIELD_MAPPING_CACHE_KEY);
            BinaryObjectBuilder curValueBuilder =
                    igniteClient.binary().builder(orgId + FIELD_MAPPING_CACHE_VALUE);

            keyBuilder.setField("comp_id", fieldMappingKey.getCompId());
            keyBuilder.setField("pref_id", fieldMappingKey.getPrefId());
            keyBuilder.setField("field_id", fieldMappingKey.getFieldId());
            BinaryObject existValue = existFieldMappings.get(keyBuilder.build());

            if (existValue == null) {
                logger.info("Insert new field mapping record: [{}]", newRecord);

                curValueBuilder.setField("created_time", new Timestamp(System.currentTimeMillis()));
                curValueBuilder.setField("created_user", "gravity");

                if (StringUtils.isNotEmpty(newRecord.getRawFieldId())) {
                    curValueBuilder.setField("raw_field_id", newRecord.getRawFieldId());
                }
                if (newRecord.getFieldIndex() != null) {
                    curValueBuilder.setField("field_index", newRecord.getFieldIndex());
                }
                if (newRecord.getHorizontal() != null) {
                    curValueBuilder.setField("horizontal", newRecord.getHorizontal());
                }

                toUpdateFieldMappings.put(keyBuilder.build(), curValueBuilder.build());
            } else {
                curValueBuilder = existValue.toBuilder();
                FieldMappingRecord existRecord =
                        FieldMappingRecord.builder()
                                .compId(curValueBuilder.getField("comp_id"))
                                .prefId(curValueBuilder.getField("pref_id"))
                                .fieldId(curValueBuilder.getField("field_id"))
                                .rawFieldId(curValueBuilder.getField("raw_field_id"))
                                .fieldIndex(curValueBuilder.getField("field_index"))
                                .horizontal(curValueBuilder.getField("horizontal"))
                                .build();

                boolean needUpdate = false;
                if (StringUtils.isNotEmpty(newRecord.getRawFieldId())) {
                    if (!Objects.equals(
                            curValueBuilder.getField("raw_field_id"), newRecord.getRawFieldId())) {
                        curValueBuilder.setField("raw_field_id", newRecord.getRawFieldId());
                        needUpdate = true;
                    }
                }
                if (newRecord.getFieldIndex() != null) {
                    if (!Objects.equals(
                            curValueBuilder.getField("field_index"), newRecord.getFieldIndex())) {
                        curValueBuilder.setField("field_index", newRecord.getFieldIndex());
                        needUpdate = true;
                    }
                }
                if (newRecord.getHorizontal() != null) {
                    if (!Objects.equals(
                            curValueBuilder.getField("horizontal"), newRecord.getHorizontal())) {
                        curValueBuilder.setField("horizontal", newRecord.getHorizontal());
                        needUpdate = true;
                    }
                }

                if (needUpdate) {
                    logger.info(
                            "Update field mapping record: before [{}], after [{}]",
                            existRecord,
                            newRecord);
                    curValueBuilder.setField(
                            "modified_time", new Timestamp(System.currentTimeMillis()));
                    curValueBuilder.setField("modified_user", "gravity");
                    toUpdateFieldMappings.put(keyBuilder.build(), curValueBuilder.build());
                }
            }
        }

        if (!toUpdateFieldMappings.isEmpty()) {
            cache.putAll(toUpdateFieldMappings);
            return true;
        }

        return false;
    }
}
