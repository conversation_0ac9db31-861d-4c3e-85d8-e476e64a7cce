package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.common.util.GravityCommonLionConfigs;
import com.envision.gravity.low.level.api.rest.util.LionUtil;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2025/2/18
 * @description
 */
@Configuration
@MapperScan(
        basePackages = "com.envision.gravity.low.level.api.rest.dao.sg",
        sqlSessionFactoryRef = "sqlSessionFactorySg")
public class SqlGatewayDataSourceCfg {
    @Bean(name = "sgDataSource")
    public HikariDataSource sgDataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
        //        ds.setJdbcUrl(
        //
        // "jdbc:mysql://*************:13306?connectionTimeZone=UTC&useLocalSessionState=true&rewriteBatchedStatements=true&connectTimeout=30000&socketTimeout=120000");
        ds.setJdbcUrl(LionUtil.getStringValue(GravityCommonLionConfigs.SQL_GATEWAY_JDBC_URL));
        ds.setUsername(
                LionUtil.getStringValue(GravityCommonLionConfigs.SQL_GATEWAY_BO_SYNC_USERNAME));
        ds.setPassword(
                LionUtil.getStringValue(GravityCommonLionConfigs.SQL_GATEWAY_BO_SYNC_PASSWORD));
        ds.setMaxLifetime(60000);
        ds.setMaximumPoolSize(
                LionUtil.getIntValue(
                        GTRestLionConfig.SQL_GATEWAY_MAX_POOL_SIZE,
                        GTRestLionConfig.SQL_GATEWAY_MAX_POOL_SIZE_DEFAULT));
        return ds;
    }

    @Bean(name = "sqlSessionFactorySg")
    public SqlSessionFactory sqlSessionFactorySg(@Qualifier("sgDataSource") DataSource datasource)
            throws Exception {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factory.setMapperLocations(resolver.getResources("classpath*:/sg-mapper/*.xml"));
        factory.setDataSource(datasource);
        return factory.getObject();
    }

    @Bean("sqlSessionTemplateSg")
    public SqlSessionTemplate sqlSessionTemplateCatalog(
            @Qualifier("sqlSessionFactorySg") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "transactionManagerSg")
    public PlatformTransactionManager transactionManagerCatalog() {
        return new DataSourceTransactionManager(sgDataSource());
    }
}
