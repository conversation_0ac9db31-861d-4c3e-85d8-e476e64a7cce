package com.envision.gravity.flink.streaming.calculate.stream;

import java.util.HashMap;
import java.util.LinkedHashSet;


import org.apache.flink.runtime.state.KeyGroupRangeAssignment;

public class KeySelectUtil {

    public static int[] createRebalanceKeys(int parallelism) {
        HashMap<Integer, LinkedHashSet<Integer>> groupRanges = new HashMap<>();
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        int maxRandomKey = parallelism * 10;
        for (int randomKey = 0; randomKey < maxRandomKey; randomKey++) {
            int subtaskIndex =
                    KeyGroupRangeAssignment.assignKeyToParallelOperator(
                            randomKey, maxParallelism, parallelism);
            LinkedHashSet<Integer> randomKeys =
                    groupRanges.computeIfAbsent(subtaskIndex, k -> new LinkedHashSet<>());
            randomKeys.add(randomKey);
        }
        int[] result = new int[parallelism];
        for (int i = 0; i < parallelism; i++) {
            LinkedHashSet<Integer> ranges = groupRanges.get(i);
            if (ranges == null || ranges.isEmpty()) {
                throw new RuntimeException("create rebalance keys error");
            }
            result[i] = ranges.stream().findFirst().get();
        }
        return result;
    }
}
