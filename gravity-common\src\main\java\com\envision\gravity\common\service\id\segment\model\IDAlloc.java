package com.envision.gravity.common.service.id.segment.model;

import java.sql.Timestamp;


import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/2/19
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class IDAlloc {
    private String idType;
    private String idPrefix;
    private String idSuffix;
    private long segmentMaxId;
    private int step;
    private String description;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
