package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/09 16:57 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblSubGraph {

    private String subGraphId;

    private String subGraphDisplayName;

    private Long rank;

    private boolean tree;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
