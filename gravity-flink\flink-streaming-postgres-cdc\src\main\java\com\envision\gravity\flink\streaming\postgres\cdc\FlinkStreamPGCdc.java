package com.envision.gravity.flink.streaming.postgres.cdc;

import com.envision.gravity.flink.streaming.postgres.cdc.config.LionConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.function.*;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.side.SideOutputs;
import com.envision.gravity.flink.streaming.postgres.cdc.sink.*;

import java.util.List;
import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;

/**
 * <AUTHOR>
 * @date 2024/5/9
 * @description
 */
@Slf4j
public class FlinkStreamPGCdc {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        int refreshTimeWindowIntervalInSeconds = LionConfig.getRefreshTimeWindowIntervalInMs();

        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", LionConfig.getMaxBatchSize());
        properties.setProperty("max.queue.size", LionConfig.getMaxQueueSize());
        //        properties.setProperty("publication.name", LionConfig.getPublicationName());
        //        properties.setProperty("publication.autocreate.mode", "filtered");

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        DataStreamSource<String> pgSource =
                env.addSource(sourceFunction)
                        .setParallelism(1); // use parallelism 1 for sink to keep message ordering

        SingleOutputStreamOperator<ParsedCdcRecord> recordStream =
                pgSource.process(new CdcRecordRouter());

        // refresh model and object data
        SingleOutputStreamOperator<List<AggregatedResults>> refreshDataOperator =
                recordStream
                        .getSideOutput(SideOutputs.REFRESH_TAG)
                        .keyBy(ParsedCdcRecord::getDb)
                        .window(
                                TumblingProcessingTimeWindows.of(
                                        Time.milliseconds(refreshTimeWindowIntervalInSeconds)))
                        .process(new AggregationDispatcher());

        // refresh model data
        refreshDataOperator
                .getSideOutput(SideOutputs.REFRESH_MODEL_TAG)
                .addSink(new RefreshModelSink());
        // refresh object data
        refreshDataOperator
                .getSideOutput(SideOutputs.REFRESH_OBJECT_TAG)
                .addSink(new RefreshObjectSink());
        // refresh nebula graph data
        refreshDataOperator
                .getSideOutput(SideOutputs.NEBULA_GRAPH_TAG)
                .addSink(new NebulaGraphSink());

        env.execute("Flink Streaming Gravity Core PG CDC");
    }
}
