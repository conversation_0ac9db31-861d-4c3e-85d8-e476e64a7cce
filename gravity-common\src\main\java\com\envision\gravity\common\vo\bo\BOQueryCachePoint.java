package com.envision.gravity.common.vo.bo;

import com.envision.gravity.common.enums.PrefDataType;

import java.util.Objects;

public class BOQueryCachePoint {
    private final String name;
    private final BOQueryCacheDataType dataType; // LONG/DOUBLE/STRING

    public BOQueryCachePoint(String name, PrefDataType dataType) {
        this.name = name;
        this.dataType = BOQueryCacheDataType.fromPrefDataType(dataType);
    }

    public String getName() {
        return name;
    }

    public BOQueryCacheDataType getDataType() {
        return dataType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BOQueryCachePoint that = (BOQueryCachePoint) o;

        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }
}
