package com.envision.gravity.flink.streaming.bo.sync.function;

import com.envision.gravity.common.definition.DefinitionEntity;
import com.envision.gravity.common.definition.bo.BODefinition;
import com.envision.gravity.common.definition.graph.BORelationDataDefinition;
import com.envision.gravity.flink.streaming.bo.sync.entity.Msg;
import com.envision.gravity.flink.streaming.bo.sync.entity.MsgMeta;
import com.envision.gravity.flink.streaming.bo.sync.entity.Payload;
import com.envision.gravity.flink.streaming.bo.sync.enums.SourceEnum;

import java.nio.charset.StandardCharsets;
import java.util.Optional;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
@Slf4j
public class ParseMsgFunction
        extends ProcessFunction<ConsumerRecord<byte[], byte[]>, Tuple2<Msg, MsgMeta>> {
    private static final long serialVersionUID = 8073370095831494310L;

    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processElement(
            ConsumerRecord<byte[], byte[]> value,
            ProcessFunction<ConsumerRecord<byte[], byte[]>, Tuple2<Msg, MsgMeta>>.Context ctx,
            Collector<Tuple2<Msg, MsgMeta>> out) {
        if (value == null || value.key() == null || value.headers() == null) {
            log.warn("Received null sync record or its key/headers. value: {}", value);
            return;
        }

        String recordValue = new String(value.value(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isNotEmpty(recordValue)) {
                parseKafkaRecordValue(recordValue)
                        .ifPresent(
                                payload -> {
                                    Msg msg =
                                            Msg.builder()
                                                    .headers(value.headers())
                                                    .key(
                                                            new String(
                                                                    value.key(),
                                                                    StandardCharsets.UTF_8))
                                                    .value(payload)
                                                    .timestamp(value.timestamp())
                                                    .timestampType(value.timestampType())
                                                    .build();

                                    MsgMeta msgMeta =
                                            MsgMeta.builder()
                                                    .topic(value.topic())
                                                    .partition(value.partition())
                                                    .offset(value.offset())
                                                    .build();

                                    out.collect(Tuple2.of(msg, msgMeta));
                                });
            } else {
                log.warn("Sync record value is null.");
            }

        } catch (Exception e) {
            log.error("Unknown exception, record:{}.", recordValue, e);
        }
    }

    private Optional<Payload> parseKafkaRecordValue(String recordValue) {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(recordValue);

            if (checkSyncRecord(recordValue, jsonNode)) {
                String source = jsonNode.get("source").asText();
                JsonNode payload = jsonNode.get("payload");

                Optional<SourceEnum> sourceEnum = SourceEnum.find(source);
                if (sourceEnum.isPresent()) {
                    DefinitionEntity dataEntity;
                    switch (sourceEnum.get()) {
                        case BO:
                            dataEntity = OBJECT_MAPPER.treeToValue(payload, BODefinition.class);
                            break;
                        case BO_RELATION:
                            dataEntity =
                                    OBJECT_MAPPER.treeToValue(
                                            payload, BORelationDataDefinition.class);
                            break;
                        default:
                            log.warn("Unsupported source {}.", source);
                            return Optional.empty();
                    }

                    return Optional.of(
                            Payload.builder()
                                    .tsMs(jsonNode.get("tsMs").asLong())
                                    .op(jsonNode.get("op").asText())
                                    .forceUpdate(
                                            jsonNode.has("forceUpdate")
                                                    && jsonNode.get("forceUpdate").asBoolean())
                                    .source(source)
                                    .orgId(jsonNode.get("orgId").asText())
                                    .syncModelFirst(
                                            jsonNode.has("syncModelFirst")
                                                    && jsonNode.get("syncModelFirst").asBoolean())
                                    .payload(dataEntity)
                                    .version(jsonNode.get("version").asText())
                                    .retryCount(
                                            jsonNode.has("retryCount")
                                                    ? jsonNode.get("retryCount").asLong() + 1
                                                    : 1)
                                    .build());
                } else {
                    log.warn("Unrecognized source {}.", source);
                    return Optional.empty();
                }
            }

            return Optional.empty();
        } catch (JsonProcessingException e) {
            log.error("Parse sync record error, record: {}.", recordValue, e);
            return Optional.empty();
        }
    }

    private Boolean checkSyncRecord(String recordValue, JsonNode jsonNode) {
        String[] requiredFields = {"op", "tsMs", "source", "payload", "orgId", "version"};

        for (String field : requiredFields) {
            if (jsonNode.get(field) == null) {
                log.warn("Invalid sync record, {} is null, record: {}.", field, recordValue);
                return false;
            }
        }

        return true;
    }
}
