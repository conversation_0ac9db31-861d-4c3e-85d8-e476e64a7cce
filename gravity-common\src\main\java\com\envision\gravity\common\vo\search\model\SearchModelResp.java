package com.envision.gravity.common.vo.search.model;

import com.envision.gravity.common.po.TblPgModel;
import com.envision.gravity.common.vo.search.SearchPaginationResp;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/14 18:33 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchModelResp {

    private List<TblPgModel> models;

    private SearchPaginationResp pagination;
}
