package com.envision.gravity.common.service.id.segment.repository;

import com.envision.gravity.common.service.id.segment.model.IDAlloc;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/15
 * @description
 */
public interface IDAllocRepository {
    /** */
    IDAlloc updateSegmentMaxIdAndGetIDAlloc(String idType);

    /** */
    IDAlloc updateSegmentMaxIdByCustomStepAndGetIDAlloc(IDAlloc idAlloc);

    /** */
    List<String> getAllIDTypes();

    IDAlloc getIDAllocByIDType(String idType);
}
