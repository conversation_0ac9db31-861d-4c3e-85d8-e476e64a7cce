package com.envision.gravity.flink.warm.cdc;

import com.envision.gravity.flink.warm.util.AssetIdToSystemIdCache;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

@Slf4j
public class WarmRecordFilter extends ProcessFunction<String, ObjectWarmRecord> {

    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private static final AssetIdToSystemIdCache ASSET_ID_TO_SYSTEM_ID_CACHE =
            new AssetIdToSystemIdCache();

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, ObjectWarmRecord>.Context ctx,
            Collector<ObjectWarmRecord> out)
            throws Exception {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(value);
            JsonNode sourceNode = jsonNode.get("source");
            JsonNode afterNode = jsonNode.get("after");
            String op = jsonNode.get("op").asText();
            // 只处理create场景
            if (!"c".equals(op)) {
                return;
            }

            String table = sourceNode.get("table").asText();
            if (!"object_detail_origin".equals(table)) {
                log.warn("Unrecognized table {}", table);
                return;
            }

            String orgId = sourceNode.get("schema").asText();
            String assetId = afterNode.get("asset_id").asText();
            String modelId = afterNode.get("model_id").asText();

            if (orgId == null || assetId == null || modelId == null) {
                log.warn("Invalid cdc record, value: {}", value);
                return;
            }
            String systemId = ASSET_ID_TO_SYSTEM_ID_CACHE.getSystemId(orgId, assetId);
            if (systemId == null) {
                log.warn("Get systemId for assetId {} fail.", assetId);
                return;
            }

            out.collect(new ObjectWarmRecord(orgId, systemId, modelId));

        } catch (Exception e) {
            log.error("Parse cdc record failed, value: {}, cause: {}", value, e.getMessage(), e);
        }
    }
}
