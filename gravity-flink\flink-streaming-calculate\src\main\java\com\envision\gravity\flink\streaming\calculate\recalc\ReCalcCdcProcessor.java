package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.common.enums.PrefType;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcTable;
import com.envision.gravity.flink.streaming.calculate.dto.CalcType;
import com.envision.gravity.flink.streaming.calculate.dto.TargetPropertyMetaInfo;
import com.envision.gravity.flink.streaming.calculate.dto.TblPropertyUpstreamRule;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcMetaInfo;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;

import java.util.*;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReCalcCdcProcessor extends ProcessFunction<ConvertedCdcRecord, RecCalcMetaInfo> {

    private static final long serialVersionUID = -2011037810160530958L;

    private static final Logger logger = LoggerFactory.getLogger(ReCalcCdcProcessor.class);

    private CalcMetaProcessor calcMetaProcessor;

    @Override
    public void open(Configuration parameters) throws Exception {
        this.calcMetaProcessor = CalcMetaProcessor.getInstance();
    }

    @Override
    public void processElement(
            ConvertedCdcRecord value,
            ProcessFunction<ConvertedCdcRecord, RecCalcMetaInfo>.Context ctx,
            Collector<RecCalcMetaInfo> out)
            throws Exception {
        String tableName = value.getTable();
        Optional<CalcCdcTable> cdcTable = CalcCdcTable.find(tableName);
        if (!cdcTable.isPresent()) {
            return;
        }

        switch (cdcTable.get()) {
            case TBL_PROPERTY_UPSTREAM_RULE:
                OPEnum opType = OPEnum.valueOf(value.getOp());
                if (opType == OPEnum.c || opType == OPEnum.d) {
                    logger.warn(
                            "ReCalc ignore TBL_PROPERTY_UPSTREAM_RULE create or delete operation event ...");
                    return;
                }

                String orgId = value.getSchema();
                TblPropertyUpstreamRule before = (TblPropertyUpstreamRule) value.getBefore();
                TblPropertyUpstreamRule after = (TblPropertyUpstreamRule) value.getAfter();
                if (after.getCalcType() == CalcType.ADHOC.getType()) {
                    logger.warn("ReCalc ignore adhoc type rule ...");
                    return;
                }

                Map<PropertyId, List<BaseCalcPropertyMeta>> updatedTargetMetas = new HashMap<>(1);
                List<BaseCalcPropertyMeta> targetPropertyInfoList = new ArrayList<>(1);
                targetPropertyInfoList.add(
                        BaseCalcPropertyMeta.builder()
                                .targetCategoryId(after.getTargetCategory())
                                .targetCompId(after.getTargetCompId())
                                .targetPrefId(after.getTargetPrefId())
                                .srcCategoryId(after.getSrcCategory())
                                .expression(after.getExpression())
                                .calcType(after.getCalcType())
                                .build());
                updatedTargetMetas.put(
                        new PropertyId(after.getTargetCompId(), after.getTargetPrefId()),
                        targetPropertyInfoList);
                Optional<TargetPropertyMetaInfo> targetPropertyMetaInfo =
                        this.calcMetaProcessor.queryTargetPropertyMeta(orgId, updatedTargetMetas);

                if (!targetPropertyMetaInfo.isPresent()) {
                    logger.error("Failed to query target property meta, orgId: [{}]", orgId);
                    return;
                }

                // Process one target property
                // targetModelId => <srcCategoryId, meta>
                Map<String, Map<String, CalcPropertyMeta>> targetModel2CategoryExprMap =
                        targetPropertyMetaInfo
                                .get()
                                .getTargetPropertyMetaMap()
                                .get(
                                        new PropertyId(
                                                after.getTargetCompId(), after.getTargetPrefId()));
                if (GTCommonUtils.isEmpty(targetModel2CategoryExprMap)) {
                    logger.error(
                            "Failed to query target property meta, orgId: [{}], targetProperty: [{}], map: [{}]",
                            orgId,
                            new PropertyId(after.getTargetCompId(), after.getTargetPrefId()),
                            targetPropertyMetaInfo.get().getTargetPropertyMetaMap());
                    return;
                }

                Set<String> targetModelIds = targetModel2CategoryExprMap.keySet();
                Map<String, CalcPropertyMeta> srcCalc2PropMetaMap =
                        targetModel2CategoryExprMap.get(targetModelIds.iterator().next());
                CalcPropertyMeta targetPropMeta = srcCalc2PropMetaMap.get(after.getSrcCategory());
                if (targetPropMeta == null) {
                    logger.error(
                            "Failed to query target property meta, orgId: [{}], targetProperty: [{}], srcCategory: [{}], map: [{}]",
                            orgId,
                            new PropertyId(after.getTargetCompId(), after.getTargetPrefId()),
                            after.getSrcCategory(),
                            srcCalc2PropMetaMap);
                    return;
                }

                // TODO 可以参与计算，只是在最终写入时不写 kafka
                if (targetPropMeta.getPrefType().equals(PrefType.ATTRIBUTE)) {
                    logger.warn(
                            "ReCalc ignore attribute type rule, orgId: [{}], pref_rule_id: [{}]",
                            orgId,
                            after.getPrefRuleId());
                    return;
                }

                // 创建 RecCalcMetaInfo 对象
                RecCalcMetaInfo recCalcMetaInfo =
                        RecCalcMetaInfo.builder()
                                .orgId(orgId)
                                .ruleInfo(after)
                                .targetModelIds(targetModelIds)
                                .targetPropertyMeta(targetPropMeta)
                                .build();

                logger.debug(
                        "ReCalc CDC processed successfully: orgId={}, prefRuleId={}, targetPropertyMeta={}",
                        orgId,
                        after.getPrefRuleId(),
                        targetPropMeta);

                out.collect(recCalcMetaInfo);
                break;
            default:
                logger.error("Unsupported table: {}", tableName);
                break;
        }
    }
}
