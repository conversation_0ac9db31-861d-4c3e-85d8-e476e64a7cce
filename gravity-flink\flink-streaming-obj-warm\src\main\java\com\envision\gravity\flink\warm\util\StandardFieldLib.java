package com.envision.gravity.flink.warm.util;

import com.envision.gravity.common.util.IgniteUtil;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class StandardFieldLib {
    private Set<String> rawFieldIdSet;
    private static final String SCHEMA_GRAVITY = "GRAVITY";
    public static final String SQL_QUERY_RAW_FIELD_ID_FROM_STANDARD_LIB =
            "select raw_field_id from standard_field_lib;";

    public StandardFieldLib() {
        rawFieldIdSet = new HashSet<>();
        List<List<?>> result =
                IgniteUtil.query(SCHEMA_GRAVITY, SQL_QUERY_RAW_FIELD_ID_FROM_STANDARD_LIB);
        for (List<?> row : result) {
            rawFieldIdSet.add((String) row.get(0));
        }
    }

    public boolean exists(String rawFieldId) {
        return rawFieldIdSet.contains(rawFieldId);
    }
}
