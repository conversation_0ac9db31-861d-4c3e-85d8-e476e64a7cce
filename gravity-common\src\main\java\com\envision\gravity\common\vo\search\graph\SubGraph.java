package com.envision.gravity.common.vo.search.graph;

import java.sql.Timestamp;
import java.util.List;


import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/** @Author: qi.jiang2 @Date: 2024/04/08 14:50 @Description: */
@Data
public class SubGraph {

    private String subGraphId;

    private JSONObject subGraphDisplayName;

    private List<String> vids;

    private List<String> startVids;

    private JSONObject tags;

    private String orgId;

    private boolean tree;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
