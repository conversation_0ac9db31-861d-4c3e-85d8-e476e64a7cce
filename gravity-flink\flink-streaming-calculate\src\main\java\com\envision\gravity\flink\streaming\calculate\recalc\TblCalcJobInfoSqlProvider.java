package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;

import java.util.List;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/**
 * SQL Provider for TblCalcJobInfo operations
 *
 * <AUTHOR>
 */
public class TblCalcJobInfoSqlProvider {

    private static final String TABLE_NAME = "public.tbl_calc_job_info";

    /** Insert new calc job info record */
    public String insert(TblCalcJobInfo jobInfo) {
        return new SQL() {
            {
                INSERT_INTO(TABLE_NAME);
                VALUES("job_id", "#{jobId}");
                VALUES("pref_rule_id", "#{prefRuleId}");
                VALUES(
                        "rule_info",
                        "#{ruleInfo,typeHandler=com.envision.gravity.flink.streaming.calculate.recalc.RecCalcMetaInfoTypeHandler}");
                VALUES("calc_start_time", "to_timestamp(#{calcStartTime} / 1000.0)");
                VALUES("calc_end_time", "to_timestamp(#{calcEndTime} / 1000.0)");
                VALUES("status", "#{status}");
                VALUES("type", "#{type}");
                VALUES("src_org_id", "#{srcOrgId}");
                VALUES("target_org_id", "#{targetOrgId}");
                VALUES("progress", "#{progress}");
                VALUES(
                        "started_time",
                        "CASE WHEN #{startedTime} = 0 THEN NULL ELSE to_timestamp(#{startedTime} / 1000.0) END");
                VALUES(
                        "finished_time",
                        "CASE WHEN #{finishedTime} = 0 THEN NULL ELSE to_timestamp(#{finishedTime} / 1000.0) END");
                VALUES(
                        "cancelled_time",
                        "CASE WHEN #{cancelledTime} = 0 THEN NULL ELSE to_timestamp(#{cancelledTime} / 1000.0) END");
                VALUES(
                        "created_time",
                        "CASE WHEN #{createdTime} = 0 THEN NULL ELSE to_timestamp(#{createdTime} / 1000.0) END");
                VALUES("created_user", "#{createdUser}");
                VALUES(
                        "modified_time",
                        "CASE WHEN #{modifiedTime} = 0 THEN NULL ELSE to_timestamp(#{modifiedTime} / 1000.0) END");
                VALUES("modified_user", "#{modifiedUser}");
                VALUES(
                        "sys_created_time",
                        "CASE WHEN #{sysCreatedTime} = 0 THEN NULL ELSE to_timestamp(#{sysCreatedTime} / 1000.0) END");
                VALUES(
                        "sys_modified_time",
                        "CASE WHEN #{sysModifiedTime} = 0 THEN NULL ELSE to_timestamp(#{sysModifiedTime} / 1000.0) END");
            }
        }.toString();
    }

    /** Update job status and modified time */
    public String updateStatus(
            @Param("jobId") String jobId,
            @Param("status") int status,
            @Param("modifiedTime") long modifiedTime) {
        return new SQL() {
            {
                UPDATE(TABLE_NAME);
                SET("status = #{status}");
                SET("modified_time = to_timestamp(#{modifiedTime} / 1000.0)");
                WHERE("job_id = #{jobId}");
            }
        }.toString();
    }

    /** Update job status with specific time field */
    public String updateStatusWithTime(
            @Param("jobId") String jobId,
            @Param("status") int status,
            @Param("timeField") String timeField,
            @Param("timeValue") long timeValue) {
        return new SQL() {
            {
                UPDATE(TABLE_NAME);
                SET("status = #{status}");
                SET("modified_time = to_timestamp(#{timeValue} / 1000.0)");
                // Dynamic time field update
                if ("started_time".equals(timeField)) {
                    SET("started_time = to_timestamp(#{timeValue} / 1000.0)");
                } else if ("finished_time".equals(timeField)) {
                    SET("finished_time = to_timestamp(#{timeValue} / 1000.0)");
                } else if ("cancelled_time".equals(timeField)) {
                    SET("cancelled_time = to_timestamp(#{timeValue} / 1000.0)");
                }
                WHERE("job_id = #{jobId}");
            }
        }.toString();
    }

    /** Update job progress */
    public String updateProgress(
            @Param("jobId") String jobId,
            @Param("progress") int progress,
            @Param("modifiedTime") long modifiedTime) {
        return new SQL() {
            {
                UPDATE(TABLE_NAME);
                SET("progress = #{progress}");
                SET("modified_time = to_timestamp(#{modifiedTime} / 1000.0)");
                WHERE("job_id = #{jobId}");
            }
        }.toString();
    }

    /** Find conflicting jobs by prefRuleId and status */
    public String findConflictingJobs(
            @Param("prefRuleId") String prefRuleId,
            @Param("excludeJobId") String excludeJobId,
            @Param("statusList") List<Integer> statusList) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(TABLE_NAME);
        sql.append(" WHERE pref_rule_id = #{prefRuleId}");
        sql.append(" AND job_id != #{excludeJobId}");

        if (statusList != null && !statusList.isEmpty()) {
            sql.append(" AND status IN (");
            for (int i = 0; i < statusList.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("#{statusList[").append(i).append("]}");
            }
            sql.append(")");
        }

        sql.append(" ORDER BY created_time DESC");
        return sql.toString();
    }

    /** Find job by jobId */
    public String findByJobId(@Param("jobId") String jobId) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT job_id, pref_rule_id, rule_info, ");
        sql.append("EXTRACT(EPOCH FROM calc_start_time) * 1000 AS calc_start_time, ");
        sql.append("EXTRACT(EPOCH FROM calc_end_time) * 1000 AS calc_end_time, ");
        sql.append("status, type, src_org_id, target_org_id, progress, ");
        sql.append(
                "CASE WHEN started_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM started_time) * 1000 END AS started_time, ");
        sql.append(
                "CASE WHEN finished_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM finished_time) * 1000 END AS finished_time, ");
        sql.append(
                "CASE WHEN cancelled_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM cancelled_time) * 1000 END AS cancelled_time, ");
        sql.append(
                "CASE WHEN created_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM created_time) * 1000 END AS created_time, ");
        sql.append("created_user, ");
        sql.append(
                "CASE WHEN modified_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM modified_time) * 1000 END AS modified_time, ");
        sql.append("modified_user, ");
        sql.append(
                "CASE WHEN sys_created_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM sys_created_time) * 1000 END AS sys_created_time, ");
        sql.append(
                "CASE WHEN sys_modified_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM sys_modified_time) * 1000 END AS sys_modified_time ");
        sql.append("FROM ").append(TABLE_NAME);
        sql.append(" WHERE job_id = #{jobId}");
        return sql.toString();
    }

    /** Find jobs by status */
    public String findByStatus(
            @Param("statusList") List<Integer> statusList, @Param("limit") int limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(TABLE_NAME);

        if (statusList != null && !statusList.isEmpty()) {
            sql.append(" WHERE status IN (");
            for (int i = 0; i < statusList.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("#{statusList[").append(i).append("]}");
            }
            sql.append(")");
        }

        sql.append(" ORDER BY created_time DESC");

        if (limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }

    /** Delete job by jobId */
    public String deleteByJobId(@Param("jobId") String jobId) {
        return new SQL() {
            {
                DELETE_FROM(TABLE_NAME);
                WHERE("job_id = #{jobId}");
            }
        }.toString();
    }

    /** Count jobs by status */
    public String countByStatus(@Param("statusList") List<Integer> statusList) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM ").append(TABLE_NAME);

        if (statusList != null && !statusList.isEmpty()) {
            sql.append(" WHERE status IN (");
            for (int i = 0; i < statusList.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("#{statusList[").append(i).append("]}");
            }
            sql.append(")");
        }

        return sql.toString();
    }

    /** Find jobs by prefRuleId */
    public String findByPrefRuleId(
            @Param("prefRuleId") String prefRuleId, @Param("limit") int limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT job_id, pref_rule_id, rule_info, ");
        sql.append("EXTRACT(EPOCH FROM calc_start_time) * 1000 AS calc_start_time, ");
        sql.append("EXTRACT(EPOCH FROM calc_end_time) * 1000 AS calc_end_time, ");
        sql.append("status, type, src_org_id, target_org_id, progress, ");
        sql.append(
                "CASE WHEN started_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM started_time) * 1000 END AS started_time, ");
        sql.append(
                "CASE WHEN finished_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM finished_time) * 1000 END AS finished_time, ");
        sql.append(
                "CASE WHEN cancelled_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM cancelled_time) * 1000 END AS cancelled_time, ");
        sql.append(
                "CASE WHEN created_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM created_time) * 1000 END AS created_time, ");
        sql.append("created_user, ");
        sql.append(
                "CASE WHEN modified_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM modified_time) * 1000 END AS modified_time, ");
        sql.append("modified_user, ");
        sql.append(
                "CASE WHEN sys_created_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM sys_created_time) * 1000 END AS sys_created_time, ");
        sql.append(
                "CASE WHEN sys_modified_time IS NULL THEN 0 ELSE EXTRACT(EPOCH FROM sys_modified_time) * 1000 END AS sys_modified_time ");
        sql.append("FROM ").append(TABLE_NAME);
        sql.append(" WHERE pref_rule_id = #{prefRuleId}");
        sql.append(" ORDER BY created_time DESC");

        if (limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }

    /** Find jobs by time range */
    public String findByTimeRange(
            @Param("startTime") long startTime,
            @Param("endTime") long endTime,
            @Param("limit") int limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(TABLE_NAME);
        sql.append(" WHERE created_time >= #{startTime}");
        sql.append(" AND created_time <= #{endTime}");
        sql.append(" ORDER BY created_time DESC");

        if (limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }

    /** Find jobs by orgId and status */
    public String findByOrgIdAndStatus(
            @Param("orgId") String orgId,
            @Param("statusList") List<Integer> statusList,
            @Param("limit") int limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(TABLE_NAME);
        sql.append(" WHERE src_org_id = #{orgId}");

        if (statusList != null && !statusList.isEmpty()) {
            sql.append(" AND status IN (");
            for (int i = 0; i < statusList.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("#{statusList[").append(i).append("]}");
            }
            sql.append(")");
        }

        sql.append(" ORDER BY created_time DESC");

        if (limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }
}
