package com.envision.gravity.low.level.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/** @Author: qi.jiang2 @Date: 2024/05/21 18:27 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ObjFieldData {

    private String systemId;
    private String fieldId;
    private String categoryId;
    private String fieldDisplayName;
    private String fieldType;
    private String dataType;
    private String unit;
    private String calcFieldExp;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
