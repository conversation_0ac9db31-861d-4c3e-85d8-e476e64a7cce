# CalcJobTaskSource 技术设计方案

## 1. 整体架构设计

### 1.1 核心组件
- **CalcJobTaskSource**: 主要的 Source 算子，基于 Flink Source API 2.0
- **CalcJobInfoManager**: 广播流管理器，处理作业信息共享
- **CalcJobTaskSplitEnumerator**: 分片枚举器，负责任务生成和分发
- **CalcJobTaskSourceReader**: 源读取器，负责任务读取
- **CalcJobTaskSplit**: 任务分片，包装单个计算任务

### 1.2 数据流架构
```
TblCalcJobInfo → CalcJobInfoManager(广播流) → ReCalcJobTaskProcessor(并发度=N)
              ↓
CalcJobTaskSource(并发度=1) → CalcJobTask → ReCalcJobTaskProcessor
              ↓
任务拆分逻辑 → Checkpoint状态管理 → 任务完成回调
```

## 2. 核心算法

### 2.1 任务拆分策略
1. **设备查询**: 根据 targetModelIds 分页查询所有设备
2. **设备批次拆分**: 按 CalcAssetSplitSize 配置拆分设备
3. **时间范围拆分**: 按 CalcTimeRangeSplitSeconds 配置拆分时间
4. **任务矩阵生成**: 设备优先，时间次之的拆分策略

### 2.2 状态管理
- **Checkpoint 支持**: 通过 CalcJobTaskEnumeratorState 管理状态
- **任务持久化**: 未完成任务保存到 Checkpoint
- **状态恢复**: 支持从 Checkpoint 恢复未完成任务

### 2.3 任务完成机制
- **回调通知**: 下游算子通过 SourceEvent 通知任务完成
- **状态更新**: 实时更新作业进度和状态
- **作业完成判断**: 所有任务完成后更新作业状态为 FINISHED

## 3. 流批兼容设计

### 3.1 广播流方案
- **CalcJobInfoManager**: 使用广播流共享 TblCalcJobInfo
- **流批兼容**: 同时支持批处理和流式处理模式
- **内存优化**: 避免每个 CalcJobTask 包含完整作业信息

### 3.2 执行模式适配
- **批处理模式**: Boundedness.BOUNDED，有界任务源
- **流式模式**: Boundedness.CONTINUOUS_UNBOUNDED，无界任务源

## 4. 配置参数

### 4.1 任务拆分配置
- `calc.query.asset.page.size`: 设备分页查询大小 (默认: 1000)
- `calc.asset.split.size`: 设备批次拆分大小 (默认: 100)
- `calc.time.range.split.seconds`: 时间范围拆分秒数 (默认: 3600)

### 4.2 并发度配置
- `recalc.job.task.source.parallelism`: Source 并发度 (默认: 1)
- `recalc.job.task.processor.parallelism`: Processor 并发度 (默认: 4)

### 4.3 Checkpoint 配置
- `recalc.checkpoint.interval.ms`: Checkpoint 间隔 (默认: 30000)
- `recalc.checkpoint.timeout.ms`: Checkpoint 超时 (默认: 600000)

## 5. 异常处理

### 5.1 任务生成异常
- 设备查询失败直接抛出异常
- 由 ReCalcBatchJob 统一捕获并更新作业状态为 FAILED

### 5.2 任务执行异常
- 单个任务失败不影响其他任务
- 支持任务级别的失败处理和重试

## 6. 监控指标

### 6.1 Source 监控
- 总任务数量
- 已完成任务数量
- 待处理任务数量
- 任务完成进度

### 6.2 性能监控
- 任务生成耗时
- 设备查询耗时
- 任务分发速率

## 7. 实现要点

### 7.1 关键类设计
- **CalcJobTask**: 精简的任务信息，不包含完整 TblCalcJobInfo
- **CalcJobTaskSplit**: 任务分片，支持 Checkpoint
- **CalcJobTaskEnumeratorState**: 枚举器状态，支持状态恢复

### 7.2 依赖组件
- **ModelMetaQueryHandler**: 设备查询组件
- **TblCalcJobInfoMapper**: 作业状态更新组件
- **CalcLionConfig**: 配置管理组件

### 7.3 事件机制
- **TaskCompletedEvent**: 任务完成事件
- **TaskFailedEvent**: 任务失败事件
- **SourceEvent**: 源事件基类
