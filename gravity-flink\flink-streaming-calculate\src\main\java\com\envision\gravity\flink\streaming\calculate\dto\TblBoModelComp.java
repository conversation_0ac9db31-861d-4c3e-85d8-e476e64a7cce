package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblBoModelComp implements CdcTableEntity {
    private static final long serialVersionUID = -5449627554198604935L;
    private String modelId;
    private String compId;

    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
