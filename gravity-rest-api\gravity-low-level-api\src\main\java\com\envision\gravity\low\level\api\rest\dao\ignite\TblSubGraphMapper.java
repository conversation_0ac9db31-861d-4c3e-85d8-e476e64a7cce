package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblStartVid;
import com.envision.gravity.common.po.TblSubGraph;

import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/04/12 15:48 @Description: */
public interface TblSubGraphMapper {

    @SelectProvider(type = TblSubGraphSqlProvider.class, method = "selectSubGraphBySubGraphId")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "rank", property = "rank", jdbcType = JdbcType.BIGINT),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.BIGINT)
    })
    List<TblSubGraph> selectSubGraphBySubGraphId(List<String> subGraphIds, String orgId);

    @SelectProvider(type = TblSubGraphSqlProvider.class, method = "selectStartVidBySubGraphId")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "start_vid", property = "startVid", jdbcType = JdbcType.BIGINT),
        @Result(column = "retain", property = "retain", jdbcType = JdbcType.BIGINT)
    })
    List<TblStartVid> selectStartVidBySubGraphId(List<String> subGraphIds, String orgId);

    @SelectProvider(type = TblSubGraphSqlProvider.class, method = "selectByStartVidS")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "start_vid", property = "startVid", jdbcType = JdbcType.BIGINT),
        @Result(column = "retain", property = "retain", jdbcType = JdbcType.BIGINT)
    })
    List<TblStartVid> selectByStartVidS(List<String> startVidS, String orgId);
}
