package com.envision.gravity.flink.streaming.postgres.cdc.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.*;

import java.util.List;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class RefreshModelSink extends RichSinkFunction<Tuple2<String, List<RefreshModelReq>>> {
    private static final long serialVersionUID = 2589026781548032545L;

    private transient TblBOModelRepository tblBOModelRepository;

    @Override
    public void open(Configuration params) throws Exception {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        tblBOModelRepository = new TblBOModelRepository(sqlSessionFactory);
    }

    @Override
    public void close() throws Exception {
        PGDataSourceConfig.closeDataSource();
    }

    @Override
    public void invoke(Tuple2<String, List<RefreshModelReq>> value, Context context) {
        String schemaName = value.f0;
        List<RefreshModelReq> refreshModelReqList = value.f1;
        if (schemaName == null || refreshModelReqList.isEmpty()) {
            return;
        }

        try {
            List<String> updatedModels =
                    refreshModelReqList.stream()
                            .map(RefreshModelReq::getUpdateRefresh)
                            .flatMap(List::stream)
                            .collect(Collectors.toList());

            int updatedRows =
                    tblBOModelRepository.updateSysModifiedTimeByPrimaryKeys(
                            schemaName, updatedModels);
            log.info(
                    "Refresh model success, schema: [{}], " + "total count: [{}], modelList: [{}].",
                    schemaName,
                    updatedRows,
                    updatedModels);
        } catch (Exception e) {
            log.error("Refresh model error.", e);
            throw new GravityRuntimeException("Refresh model error.", e);
        }
    }
}
