package com.envision.gravity.low.level.api.sql.util;

import com.envision.gravity.common.exception.BadRequestException;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.binary.BinaryObject;

/**
 * <AUTHOR>
 * @date 2024/6/19
 * @description
 */
@Slf4j
public class IgniteUtils {

    public static IgniteCache<BinaryObject, BinaryObject> getIgniteCache(
            Ignite ignite, String cacheName) {
        IgniteCache<BinaryObject, BinaryObject> igniteCache = ignite.cache(cacheName);
        if (igniteCache == null) {
            log.error("Cache {} is null!", cacheName);
            throw new BadRequestException("Cache " + cacheName + " is null!");
        }

        return igniteCache;
    }
}
