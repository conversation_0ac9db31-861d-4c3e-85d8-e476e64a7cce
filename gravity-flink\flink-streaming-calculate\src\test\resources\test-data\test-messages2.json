{"test_cases": [{"name": "one_source_multiple_targets", "description": "One source property WGEN.GenSpd maps to multiple target properties with different calculation types", "input": {"orgId": "o17186913277371853", "modelId": "wind_general_001", "modelIdPath": "/wind_general_001", "payload": [{"assetId": "05b8z0deq502bf", "time": 1749052800000, "points": {"WGEN.GenSpd": 121.0}}]}, "expected_outputs": {"direct_mapping": [{"orgId": "o17186913277371853", "modelId": "gravityMultiModelTest02", "modelIdPath": "/gravityMultiModelTest02", "payload": [{"assetId": "05b8z0fcj401c5", "time": 1749052800000, "points": {"WGEN.GenSpd": 121.0}}]}], "non_direct_mapping": [{"orgId": "o17186913277371853", "modelId": "gravityMultiModelTest02", "modelIdPath": "/gravityMultiModelTest02", "payload": [{"assetId": "05b8z0fcj401c5", "time": 1749052800000, "points": {"WGEN.GenSpd_Plus2": 123.0, "WGEN.GenSpd_PlusAttr1": 245.0, "WGEN.GenSpd_Plus_WindPoint": 246.12091256191724}}]}]}}]}