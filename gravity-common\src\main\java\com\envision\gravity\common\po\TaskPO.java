package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/14
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskPO {
    private String taskId;
    private String storageType;
    private String operationName;
    private String databaseName;
    private String tableName;
    private String requestParams;
    private String status;
    private int retryTimes;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
