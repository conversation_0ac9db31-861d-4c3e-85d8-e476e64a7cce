package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.low.level.api.rest.enums.Constants;

import org.apache.ibatis.jdbc.SQL;

/** @Author: qi.jiang2 @Date: 2024/04/25 19:40 @Description: */
public class TblCategorySqlProvider {

    public String countCategoryNum(String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM(orgId + Constants.TBL_CATEGORY_TABLE_NAME);
        return sql.toString();
    }

    public String queryCategoryWithPagination(int limit, int offset, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("category_id");
        sql.SELECT("category_display_name");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_CATEGORY_TABLE_NAME);
        sql.LIMIT(limit);
        sql.OFFSET(offset);
        return sql.toString();
    }

    public String queryAllCategory(String orgId) {
        SQL sql = new SQL();
        sql.SELECT("category_id");
        sql.SELECT("category_display_name");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_CATEGORY_TABLE_NAME);
        return sql.toString();
    }

    public String queryCategoryByCategoryId(String categoryId, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("category_id");
        sql.SELECT("category_display_name");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_CATEGORY_TABLE_NAME);
        sql.WHERE("category_id = '" + categoryId + "'");
        return sql.toString();
    }
}
