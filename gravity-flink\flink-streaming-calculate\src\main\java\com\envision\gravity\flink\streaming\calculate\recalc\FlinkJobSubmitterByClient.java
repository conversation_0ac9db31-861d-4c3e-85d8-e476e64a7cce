package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.JobID;
import org.apache.flink.client.deployment.StandaloneClusterDescriptor;
import org.apache.flink.client.deployment.StandaloneClusterId;
import org.apache.flink.client.program.ClusterClient;
import org.apache.flink.client.program.ClusterClientProvider;
import org.apache.flink.client.program.PackagedProgram;
import org.apache.flink.client.program.PackagedProgramUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.JobManagerOptions;
import org.apache.flink.configuration.RestOptions;
import org.apache.flink.runtime.jobgraph.JobGraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Flink Job Submitter for ReCalc Batch Jobs Uses Flink Client API with local JAR files
 *
 * <AUTHOR>
 */
public class FlinkJobSubmitterByClient implements FlinkJobSubmitter.FlinkJobSubmitterInterface {

    private static final Logger logger = LoggerFactory.getLogger(FlinkJobSubmitterByClient.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final Configuration flinkConfig;
    private final ClusterClientProvider<StandaloneClusterId> clusterClientProvider;
    private final String jobJarPath;
    private final int submitTimeoutSeconds;

    public FlinkJobSubmitterByClient() {
        this.submitTimeoutSeconds = CalcLionConfig.getFlinkJobSubmitTimeoutSeconds();
        this.jobJarPath = CalcLionConfig.getCalcFlinkJarPrefix();

        // Initialize Flink configuration
        this.flinkConfig = new Configuration();
        this.flinkConfig.setString(
                JobManagerOptions.ADDRESS, CalcLionConfig.getFlinkJobManagerHost());
        this.flinkConfig.setInteger(
                JobManagerOptions.PORT, CalcLionConfig.getFlinkJobManagerPort());
        this.flinkConfig.setInteger(RestOptions.PORT, CalcLionConfig.getFlinkJobManagerPort());

        // Initialize cluster client provider
        try {
            StandaloneClusterDescriptor clusterDescriptor =
                    new StandaloneClusterDescriptor(flinkConfig);
            this.clusterClientProvider =
                    clusterDescriptor.retrieve(StandaloneClusterId.getInstance());
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Flink cluster client provider", e);
        }

        logger.info(
                "FlinkJobSubmitterByClient initialized with JobManager: {}:{}, JAR: {}, timeout: {}s",
                CalcLionConfig.getFlinkJobManagerHost(),
                CalcLionConfig.getFlinkJobManagerPort(),
                jobJarPath,
                submitTimeoutSeconds);
    }

    /** Submit ReCalc batch job using Flink Client API */
    public String submitReCalcBatchJob(TblCalcJobInfo jobInfo) throws Exception {
        logger.info(
                "Submitting ReCalc batch job: jobId={}, prefRuleId={}",
                jobInfo.getJobId(),
                jobInfo.getPrefRuleId());

        return submitJobUsingClientAPI(jobInfo);
    }

    /** Submit job using Flink Client API with local JAR */
    private String submitJobUsingClientAPI(TblCalcJobInfo jobInfo) throws Exception {
        logger.info("Submitting job using Client API: jobId={}", jobInfo.getJobId());

        // 1. Create PackagedProgram from JAR file
        PackagedProgram packagedProgram = createPackagedProgram(jobInfo);

        // 2. Create JobGraph
        JobGraph jobGraph = createJobGraph(packagedProgram, jobInfo);

        // 3. Submit job to cluster
        String flinkJobId = submitJobGraph(jobGraph);

        logger.info(
                "Successfully submitted job using Client API: jobId={}, flinkJobId={}",
                jobInfo.getJobId(),
                flinkJobId);

        return flinkJobId;
    }

    /** Create PackagedProgram from JAR file */
    private PackagedProgram createPackagedProgram(TblCalcJobInfo jobInfo) throws Exception {
        // Find JAR file
        File jarFile = findJobJarFile();

        // Build program arguments
        String[] programArgs = buildJobArguments(jobInfo);

        // Create PackagedProgram
        PackagedProgram packagedProgram =
                PackagedProgram.newBuilder()
                        .setJarFile(jarFile)
                        .setEntryPointClassName(CalcLionConfig.getFlinkJobMainClass())
                        .setArguments(programArgs)
                        .build();

        logger.info(
                "Created PackagedProgram: jar={}, entryClass={}, args={}",
                jarFile.getAbsolutePath(),
                CalcLionConfig.getFlinkJobMainClass(),
                java.util.Arrays.toString(programArgs));

        return packagedProgram;
    }

    /** Create JobGraph from PackagedProgram */
    private JobGraph createJobGraph(PackagedProgram packagedProgram, TblCalcJobInfo jobInfo)
            throws Exception {
        // Set job configuration
        Configuration jobConfig = new Configuration(flinkConfig);
        jobConfig.setInteger("parallelism.default", CalcLionConfig.getFlinkJobParallelism());

        // Create JobGraph
        JobGraph jobGraph =
                PackagedProgramUtils.createJobGraph(
                        packagedProgram, jobConfig, CalcLionConfig.getFlinkJobParallelism(), false);

        // Set job name (JobGraph doesn't have setJobName method in this version)
        String jobName = "ReCalc-" + jobInfo.getJobId();

        logger.info(
                "Created JobGraph: jobId={}, jobName={}, parallelism={}",
                jobGraph.getJobID(),
                jobName,
                CalcLionConfig.getFlinkJobParallelism());

        return jobGraph;
    }

    /** Submit JobGraph to Flink cluster */
    private String submitJobGraph(JobGraph jobGraph) throws Exception {
        try (ClusterClient<StandaloneClusterId> clusterClient =
                clusterClientProvider.getClusterClient()) {
            logger.info("Submitting JobGraph to cluster: jobId={}", jobGraph.getJobID());

            CompletableFuture<JobID> submitFuture = clusterClient.submitJob(jobGraph);
            JobID jobId = submitFuture.get();

            logger.info("Successfully submitted job to cluster: flinkJobId={}", jobId);
            return jobId.toString();
        }
    }

    /** Find JAR file for the job */
    private File findJobJarFile() throws Exception {
        // Try different possible locations for the JAR file
        List<String> possiblePaths = new ArrayList<>();

        // 1. Absolute path if configured
        possiblePaths.add(jobJarPath);

        // 2. Relative to current directory
        possiblePaths.add("./" + jobJarPath);

        // 3. In target directory (for development)
        possiblePaths.add("target/" + jobJarPath);
        possiblePaths.add("gravity-flink/flink-streaming-calculate/target/" + jobJarPath);

        // 4. Common JAR names
        possiblePaths.add("target/flink-streaming-calculate-1.0.0-20250730-SNAPSHOT.jar");
        possiblePaths.add(
                "gravity-flink/flink-streaming-calculate/target/flink-streaming-calculate-1.0.0-20250730-SNAPSHOT.jar");

        for (String path : possiblePaths) {
            File jarFile = new File(path);
            if (jarFile.exists() && jarFile.isFile()) {
                logger.info("Found JAR file: {}", jarFile.getAbsolutePath());
                return jarFile;
            }
        }

        throw new RuntimeException(
                "JAR file not found. Tried paths: "
                        + possiblePaths
                        + ". Please ensure the JAR file is built and available.");
    }

    /** Build job arguments from TblCalcJobInfo */
    private String[] buildJobArguments(TblCalcJobInfo jobInfo) {
        Map<String, String> params = buildJobParameters(jobInfo);

        String[] args = new String[params.size() * 2];
        int index = 0;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            args[index++] = "--" + entry.getKey();
            args[index++] = entry.getValue();
        }

        logger.debug("Built job arguments: {}", java.util.Arrays.toString(args));
        return args;
    }

    /** Build job parameters map */
    private Map<String, String> buildJobParameters(TblCalcJobInfo jobInfo) {
        Map<String, String> params = new HashMap<>();
        params.put("jobId", jobInfo.getJobId());
        params.put("prefRuleId", jobInfo.getPrefRuleId());
        try {
            params.put("ruleInfo", objectMapper.writeValueAsString(jobInfo.getRuleInfo()));
        } catch (Exception e) {
            logger.error("Failed to serialize ruleInfo to JSON", e);
            throw new RuntimeException("Failed to serialize ruleInfo", e);
        }
        params.put("calcStartTime", String.valueOf(jobInfo.getCalcStartTime()));
        params.put("calcEndTime", String.valueOf(jobInfo.getCalcEndTime()));
        params.put("orgId", jobInfo.getSrcOrgId());
        params.put("parallelism", String.valueOf(CalcLionConfig.getFlinkJobParallelism()));

        // Add database configuration
        params.put("pgJdbcUrl", CalcLionConfig.getPgJdbcUrl());
        params.put("pgUsername", CalcLionConfig.getPgUsername());
        params.put("pgPassword", CalcLionConfig.getPgPassword());

        return params;
    }

    /** Check if Flink cluster is available */
    public boolean isClusterAvailable() {
        try (ClusterClient<StandaloneClusterId> clusterClient =
                clusterClientProvider.getClusterClient()) {
            // Try to get cluster status to check connectivity
            clusterClient.getWebInterfaceURL();
            logger.debug("Flink cluster is available");
            return true;
        } catch (Exception e) {
            logger.warn("Flink cluster health check failed: {}", e.getMessage());
            return false;
        }
    }

    /** Close resources */
    public void close() {
        try {
            // ClusterClientProvider doesn't need explicit closing in this version
            logger.info("FlinkJobSubmitterByClient closed");
        } catch (Exception e) {
            logger.warn("Error closing FlinkJobSubmitter: {}", e.getMessage());
        }
    }
}
