package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblComponentPrefInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblComponentPrefRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public class CompPrefAggregator implements Aggregator {

    private final TblComponentPrefRepository tblComponentPrefRepository;

    public CompPrefAggregator(SqlSessionFactory sqlSessionFactory) {
        tblComponentPrefRepository = new TblComponentPrefRepository(sqlSessionFactory);
    }

    @Override
    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        List<String> filteredModelIdList = new ArrayList<>();
        List<ModelGroup> filteredModelGroupList = new ArrayList<>();
        // 1.refresh model detail
        // 2.refresh object detail
        List<String> compIdList =
                records.stream()
                        .map(record -> ((TblComponentPrefInfo) record.getAfter()).getCompId())
                        .distinct()
                        .collect(Collectors.toList());
        List<String> prefIdList =
                records.stream()
                        .map(record -> ((TblComponentPrefInfo) record.getAfter()).getPrefId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!compIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentPrefRepository.selectModelGroupList(
                            schemaName, compIdList, prefIdList);

            filteredModelGroupList =
                    tblComponentPrefRepository.selectAttrModelGroupList(
                            schemaName, compIdList, prefIdList);

            if (!modelGroupList.isEmpty()) {
                filteredModelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());
            }
        }

        return AggregatedResults.builder()
                .refreshModelReq(
                        filteredModelIdList.isEmpty()
                                ? null
                                : RefreshModelReq.builder()
                                        .updateRefresh(filteredModelIdList)
                                        .build())
                .refreshObjectReq(
                        filteredModelGroupList.isEmpty()
                                ? null
                                : RefreshObjectReq.builder()
                                        .modelUpdateRefresh(filteredModelGroupList)
                                        .build())
                .build();
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        // 2.refresh object detail
        List<String> compIdList =
                records.stream()
                        .map(record -> ((TblComponentPrefInfo) record.getBefore()).getCompId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!compIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentPrefRepository.selectModelGroupListByCompId(schemaName, compIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> filteredModelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                return AggregatedResults.builder()
                        .refreshModelReq(
                                RefreshModelReq.builder()
                                        .updateRefresh(filteredModelIdList)
                                        .build())
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.if field_index changed, refresh object detail
        List<String> filteredCompIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblComponentPrefInfo before =
                                            (TblComponentPrefInfo) record.getBefore();
                                    TblComponentPrefInfo after =
                                            (TblComponentPrefInfo) record.getAfter();

                                    if (!Objects.equals(
                                            before.getFieldIndex(), after.getFieldIndex())) {
                                        return after.getCompId();
                                    }

                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        if (!filteredCompIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentPrefRepository.selectModelGroupListByCompId(
                            schemaName, filteredCompIdList);

            if (!modelGroupList.isEmpty()) {
                return AggregatedResults.builder()
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }
}
