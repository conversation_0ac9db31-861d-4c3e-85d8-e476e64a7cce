package com.envision.gravity.flink.streaming.postgres.cdc.model.cdc;

import java.sql.Timestamp;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/11
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Source {
    private String version;
    private String connector;
    private String name;
    private Timestamp tsMs;
    private Boolean snapshot;
    private String db;
    private String sequence;
    private String schema;
    private String table;

    @JsonProperty("txId")
    private Integer txId;

    private Long lsn;
    private Integer xMin;
}
