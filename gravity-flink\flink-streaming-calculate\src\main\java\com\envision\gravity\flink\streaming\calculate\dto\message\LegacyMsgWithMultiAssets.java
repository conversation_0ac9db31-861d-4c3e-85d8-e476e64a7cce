package com.envision.gravity.flink.streaming.calculate.dto.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多资产消息实体
 * 
 * 用于：
 * 1. 计算结果的输出格式
 * 2. Kafka消息的载体
 * 3. AspectCalcJobTaskProcessor 和 ReCalcJobTaskProcessor 的输出
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LegacyMsgWithMultiAssets implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 组织ID
     */
    private String orgId;
    
    /**
     * 模型ID
     */
    private String modelId;
    
    /**
     * 模型路径
     */
    private String modelIdPath;
    
    /**
     * 消息载体列表（多个资产的数据）
     */
    private List<LegacyPayload> payload;
    
    /**
     * 消息头信息
     */
    @Builder.Default
    private Map<String, String> headers = new HashMap<>();
    
    /**
     * 消息时间戳
     */
    private long timestamp;
    
    /**
     * 检查是否为有效的消息
     */
    public boolean isValid() {
        return orgId != null && !orgId.isEmpty() &&
               modelId != null && !modelId.isEmpty() &&
               payload != null && !payload.isEmpty();
    }
    
    /**
     * 获取资产数量
     */
    public int getAssetCount() {
        return payload != null ? payload.size() : 0;
    }
    
    /**
     * 添加Header
     */
    public void addHeader(String key, String value) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(key, value);
    }
    
    /**
     * 获取Header
     */
    public String getHeader(String key) {
        return headers != null ? headers.get(key) : null;
    }
    
    /**
     * 检查是否包含指定Header
     */
    public boolean hasHeader(String key) {
        return headers != null && headers.containsKey(key);
    }
    
    /**
     * 根据资产ID查找载体
     */
    public LegacyPayload findPayloadByAssetId(String assetId) {
        if (payload == null) {
            return null;
        }
        
        return payload.stream()
            .filter(p -> assetId.equals(p.getAssetId()))
            .findFirst()
            .orElse(null);
    }
    
    @Override
    public String toString() {
        return String.format("LegacyMsgWithMultiAssets{orgId='%s', modelId='%s', " +
                           "assetCount=%d, headers=%d, timestamp=%d}", 
                           orgId, modelId, getAssetCount(), 
                           headers != null ? headers.size() : 0, timestamp);
    }
}
