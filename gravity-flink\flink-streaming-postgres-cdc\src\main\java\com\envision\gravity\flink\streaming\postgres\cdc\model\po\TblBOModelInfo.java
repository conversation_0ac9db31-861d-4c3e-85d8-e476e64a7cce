package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/15
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblBOModelInfo implements CDCTableEntity {
    private static final long serialVersionUID = -6544049111199797234L;
    private String modelId;
    private String modelDisplayName;
    private String description;
    private String comment;
    private String groupId;
    private String modelPath;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
