package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.common.util.GravityCommonLionConfigs;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.utils.LionUtil;


import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** 测试 Ignite 配置读取 */
public class IgniteConfigTest {
    private static final Logger logger = LoggerFactory.getLogger(IgniteConfigTest.class);

    @Test
    void testIgniteConfigReading() {
        System.out.println("=== 测试 Ignite 配置读取 ===");

        // 测试 CalcLionConfig 读取
        System.out.println("--- CalcLionConfig 读取结果 ---");
        String calcIgniteAddress = CalcLionConfig.getIgniteAddress();
        String calcIgniteUsername = CalcLionConfig.getIgniteUsername();
        String calcIgnitePassword = CalcLionConfig.getIgnitePassword();
        String calcIgniteJdbcUrl = CalcLionConfig.getIgniteJdbcUrl();

        System.out.println("CalcLionConfig.getIgniteAddress(): " + calcIgniteAddress);
        System.out.println("CalcLionConfig.getIgniteUsername(): " + calcIgniteUsername);
        System.out.println("CalcLionConfig.getIgnitePassword(): " + calcIgnitePassword);
        System.out.println("CalcLionConfig.getIgniteJdbcUrl(): " + calcIgniteJdbcUrl);

        // 测试 LionUtil 直接读取
        System.out.println("\n--- LionUtil 直接读取结果 ---");
        String lionIgniteAddress =
                LionUtil.getValue(GravityCommonLionConfigs.IGNITE_ADDRESS, "NOT_FOUND");
        String lionIgniteUsername =
                LionUtil.getValue(GravityCommonLionConfigs.IGNITE_USERNAME, "NOT_FOUND");
        String lionIgnitePassword =
                LionUtil.getValue(GravityCommonLionConfigs.IGNITE_PASSWORD, "NOT_FOUND");
        String lionIgniteJdbcUrl =
                LionUtil.getValue(GravityCommonLionConfigs.IGNITE_JDBC_URL, "NOT_FOUND");

        System.out.println("LionUtil.getValue(IGNITE_ADDRESS): " + lionIgniteAddress);
        System.out.println("LionUtil.getValue(IGNITE_USERNAME): " + lionIgniteUsername);
        System.out.println("LionUtil.getValue(IGNITE_PASSWORD): " + lionIgnitePassword);
        System.out.println("LionUtil.getValue(IGNITE_JDBC_URL): " + lionIgniteJdbcUrl);

        // 测试 IgniteUtil 连接
        System.out.println("\n--- 测试 IgniteUtil 连接 ---");
        try {
            // 尝试获取 Ignite 客户端
            Object igniteClient = IgniteUtil.getIgniteClient();
            System.out.println("✅ IgniteUtil.getIgniteClient() 成功: " + igniteClient);

            // 尝试查询 schemas
            System.out.println("尝试查询数据库 schemas...");
            String sql = "SELECT DISTINCT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA LIMIT 5";
            java.util.List<java.util.List<?>> results = IgniteUtil.query("PUBLIC", sql);
            System.out.println("✅ 查询成功，返回 " + results.size() + " 条记录");

            for (int i = 0; i < Math.min(results.size(), 3); i++) {
                System.out.println("  Schema " + (i + 1) + ": " + results.get(i).get(0));
            }

        } catch (Exception e) {
            System.err.println("❌ IgniteUtil 连接失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== 配置测试完成 ===");
    }
}
