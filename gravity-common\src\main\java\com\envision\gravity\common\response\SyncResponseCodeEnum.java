package com.envision.gravity.common.response;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/02/27
 * @description
 */
@Getter
public enum SyncResponseCodeEnum {

    // SUCCESS
    SUCCESS(0, "SUCCESS", null),
    // EXCEPTION
    SYNC_BUSINESS_OBJECTS_ERROR(19001, "Sync business objects error!", null),
    SYNC_DATA_OBJECTS_ERROR(19002, "Sync data objects error!", null),
    SYNC_RELATIONS_ERROR(19003, "Sync relations error!", null),
    CLEAR_EXCEPTION(19004, "Clear old data exception!", null),
    DELETE_BUSINESS_OBJECTS_ERROR(19005, "Delete business objects error!", null),
    DELETE_DATA_OBJECTS_ERROR(19006, "Delete data objects error!", null),
    DELETE_RELATIONS_ERROR(19007, "Delete relations error!", null),
    DELETE_GRAPHS_ERROR(19008, "Delete graphs error!", null),
    REQUEST_GRAVITY_SERVICE_ERROR(19100, "Request gravity service error!", null),
    REQUEST_SQL_GATEWAY_ERROR(19200, "Request sql gateway error!", null),
    REQUEST_TAG_SERVICE_ERROR(19300, "Request tag service error!", null),
    SYNC_TAGS_ERROR(19301, "Sync tags error!", null),
    DELETE_TAGS_ERROR(19302, "Delete tags error!", null),
    MODEL_IDS_IS_EMPTY_EXCEPTION(19401, "Model Check Exception!", "ModelIds is empty."),
    MODEL_NOT_EXIST_EXCEPTION(19402, "Model Check Exception!", "Model does not exist."),
    INVALID_RELATIONS_PARAMS_EXCEPTION(
            19405, "Params invalid exception!", "Invalid relations params."),
    INVALID_BO_RELATIONS_PARAMS_EXCEPTION(
            19406, "Params invalid exception!", "Invalid bo relations params."),
    TREE_CHECK_EXCEPTION(19407, "Tree Check Exception!", null),
    UNKNOWN_EXCEPTION(19500, "Unknown Exception!", null),
    INTERNAL_ERROR(19501, "System internal error!", null),
    ROLLBACK_EXCEPTION(19502, "Rollback Exception!", null),
    // WARING
    PROPERTY_MISSING_WARING(
            19600, "Property Missing Waring!", "Model does not have this property."),
    ATTR_DATATYPE_CHANGED_WARING(
            19601,
            "Attribute DataType Changed Waring!",
            "Model datatype and the actual attribute dataType are inconsistent."),
    ATTR_MISSING_WARING(19602, "Attributes Missing Waring!", "Attributes is null or empty.");

    private final int code;
    private final String message;
    private final String detailMessage;

    SyncResponseCodeEnum(int code, String message, String detailMessage) {
        this.code = code;
        this.message = message;
        this.detailMessage = detailMessage;
    }

    public static SyncResponseCodeEnum parse(int code) {
        SyncResponseCodeEnum[] arr = values();
        for (SyncResponseCodeEnum v : arr) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }
}
