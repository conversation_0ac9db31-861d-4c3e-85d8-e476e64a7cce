package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;

public class TblPrefUpstreamSqlProvider {

    public String insert(String orgId, String ruleId, BaseCalcPropertyMeta calcPrefMeta) {
        String sql =
                String.format(
                        "insert into %s.tbl_property_upstream_rule (pref_rule_id, target_category, target_comp_id, target_pref_id, src_category, expression, calc_type, created_time) "
                                + "values ('%s', '%s', '%s', '%s', '%s', '%s', %d, CURRENT_TIMESTAMP) ",
                        orgId,
                        ruleId,
                        calcPrefMeta.getTargetCategoryId(),
                        calcPrefMeta.getTargetCompId(),
                        calcPrefMeta.getTargetPrefId(),
                        calcPrefMeta.getSrcCategoryId(),
                        calcPrefMeta.getExpression(),
                        calcPrefMeta.getCalcType());
        return sql;
    }

    public String deleteByRuleId(String orgId, String ruleId) {
        return String.format(
                "delete from %s.tbl_property_upstream_rule where pref_rule_id = '%s'",
                orgId, ruleId);
    }

    public String updateExprByRuleId(String orgId, String ruleId, String expr) {
        return String.format(
                "update %s.tbl_property_upstream_rule set expression = '%s', modified_time=CURRENT_TIMESTAMP, modified_user='gravity' where pref_rule_id = '%s'",
                orgId, expr, ruleId);
    }
}
