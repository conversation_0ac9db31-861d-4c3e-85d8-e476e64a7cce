package com.envision.gravity.common.vo.search.asset;

import com.envision.gravity.common.vo.search.ScrollResp;
import com.envision.gravity.common.vo.search.SearchPaginationResp;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/14 18:33 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchAssetResp {

    private List<TblPgAssetVo> assets;

    private SearchPaginationResp pagination;

    private ScrollResp scroll;

    private List<GroupByResp> groupBy;

    private List<AssetRelationship> assetRelationships;
}
