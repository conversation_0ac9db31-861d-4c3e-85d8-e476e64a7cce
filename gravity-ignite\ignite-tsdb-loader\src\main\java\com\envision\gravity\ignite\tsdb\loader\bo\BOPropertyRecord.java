package com.envision.gravity.ignite.tsdb.loader.bo;


import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> 2024/9/4 */
@Getter
@Setter
public class BOPropertyRecord {

    // comp:pref, raw_field_id, pref_type, has_quality, data_type
    private String fullPrefName;

    private String rawFieldId;

    private BOModelFieldType prefType;

    private Boolean hasQuality;

    private BOModelFieldDataType dataType;

    public BOPropertyRecord(
            String fullPrefName,
            String rawFieldId,
            BOModelFieldType prefType,
            Boolean hasQuality,
            BOModelFieldDataType dataType) {
        this.fullPrefName = fullPrefName;
        this.rawFieldId = rawFieldId;
        this.prefType = prefType;
        this.hasQuality = hasQuality;
        this.dataType = dataType;
    }
}
