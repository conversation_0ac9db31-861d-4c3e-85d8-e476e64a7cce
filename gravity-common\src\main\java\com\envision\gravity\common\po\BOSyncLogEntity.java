package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/18
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BOSyncLogEntity {
    private String syncId;
    private String orgId;
    private String boId;
    private String boType;
    private String operation;
    private String status;
    private String requestParams;
    private int responseCode;
    private String responseMessage;
    private String responseDetail;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
