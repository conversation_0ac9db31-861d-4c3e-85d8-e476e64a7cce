SELECT
    CASE
        WHEN tbl_component.anonymous THEN pref.pref_name
        ELSE CONCAT(tbl_component.comp_name, ':', pref.pref_name)
    END,
    tbl_component_pref.field_index,
    tbl_component_pref.horizontal,
    tbl_component_pref.raw_field_id,
    pref.pref_type,
    pref.data_type,
    pref.pref_signal_type
FROM
    (
        SELECT
            pref_id,
            pref_name,
            pref_type,
            pref_data_type,
            pref_signal_type,
            CASE
                WHEN pref_data_type = 'ENUM' AND data_definition LIKE '%"@type":["integer"]%' THEN 'INTEGER'
                ELSE pref_data_type
            END AS data_type
        FROM
            "${orgId}".tbl_pref
        WHERE
            pref_name IN (${prefNames})
    ) AS pref
    LEFT JOIN "${orgId}".tbl_component_pref_field_mapping AS tbl_component_pref ON pref.pref_id = tbl_component_pref.pref_id
    LEFT JOIN "${orgId}".tbl_component ON tbl_component_pref.comp_id = tbl_component.comp_id
WHERE
    tbl_component.anonymous = true
    OR
    (
        tbl_component.anonymous = false
        AND CONCAT(tbl_component.comp_name, ':', pref.pref_name) IN (${pointNames})
    );