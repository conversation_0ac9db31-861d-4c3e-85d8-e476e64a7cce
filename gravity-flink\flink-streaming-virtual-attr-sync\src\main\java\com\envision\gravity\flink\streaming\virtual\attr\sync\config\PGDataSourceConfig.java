package com.envision.gravity.flink.streaming.virtual.attr.sync.config;

import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.PGCatalogMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblBOModelMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblObjAttrMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblPrefExtMapper;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
@Slf4j
public class PGDataSourceConfig {
    private static volatile SqlSessionFactory SQL_SESSION_FACTORY;
    private static volatile HikariDataSource HIKARI_DATA_SOURCE;

    public static SqlSessionFactory getSqlSessionFactory() {
        if (SQL_SESSION_FACTORY == null) {
            synchronized (PGDataSourceConfig.class) {
                if (SQL_SESSION_FACTORY == null) {
                    log.info("Start initializing pg dataSource...");

                    HikariConfig config = new HikariConfig();
                    config.setDriverClassName(LionConfig.getPgDriverClassName());
                    config.setJdbcUrl(LionConfig.getPgJdbcUrl());
                    config.setUsername(LionConfig.getPgUsername());
                    config.setPassword(LionConfig.getPgPassword());
                    config.setMaximumPoolSize(LionConfig.getPgMaxPoolSize());
                    config.setConnectionTestQuery("SELECT 1;");
                    HIKARI_DATA_SOURCE = new HikariDataSource(config);

                    TransactionFactory transactionFactory = new JdbcTransactionFactory();
                    Environment environment =
                            new Environment("gravity", transactionFactory, HIKARI_DATA_SOURCE);
                    org.apache.ibatis.session.Configuration configuration =
                            new org.apache.ibatis.session.Configuration(environment);
                    configuration.addMapper(TblPrefExtMapper.class);
                    configuration.addMapper(TblBOModelMapper.class);
                    configuration.addMapper(PGCatalogMapper.class);
                    configuration.addMapper(TblObjAttrMapper.class);
                    SQL_SESSION_FACTORY = new SqlSessionFactoryBuilder().build(configuration);

                    log.info("Init pg dataSource success.");
                }
            }
        }
        return SQL_SESSION_FACTORY;
    }

    public static void closeDataSource() {
        if (SQL_SESSION_FACTORY != null || HIKARI_DATA_SOURCE != null) {
            synchronized (PGDataSourceConfig.class) {
                if (SQL_SESSION_FACTORY != null || HIKARI_DATA_SOURCE != null) {
                    HIKARI_DATA_SOURCE.close();
                    HIKARI_DATA_SOURCE = null;
                    SQL_SESSION_FACTORY = null;
                    log.info("Close pg dataSource success.");
                }
            }
        }
    }
}
