package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.common.calculate.AssetInfo;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcJobTaskStatusEnum;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcMetaInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.events.TaskCompletedEvent;
import com.envision.gravity.flink.streaming.calculate.recalc.events.TaskFailedEvent;

import javax.annotation.Nullable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


import org.apache.flink.api.connector.source.SourceEvent;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算作业任务分片枚举器
 *
 * <AUTHOR>
 */
public class CalcJobTaskSplitEnumerator
        implements SplitEnumerator<CalcJobTaskSplit, CalcJobTaskEnumeratorState> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSplitEnumerator.class);

    private final TblCalcJobInfo jobInfo;
    private final SplitEnumeratorContext<CalcJobTaskSplit> context;
    private final boolean isBatchMode;

    // 运行时状态
    private final ConcurrentLinkedQueue<CalcJobTaskSplit> pendingSplits =
            new ConcurrentLinkedQueue<>();
    private final ConcurrentHashMap<String, String> completedSplitIds = new ConcurrentHashMap<>();
    private final AtomicInteger nextTaskId = new AtomicInteger(1);
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicLong totalTaskCount = new AtomicLong(0);

    // 依赖组件
    private transient ModelMetaQueryHandler modelMetaQueryHandler;

    public CalcJobTaskSplitEnumerator(
            TblCalcJobInfo jobInfo,
            SplitEnumeratorContext<CalcJobTaskSplit> context,
            boolean isBatchMode) {
        this.jobInfo = jobInfo;
        this.context = context;
        this.isBatchMode = isBatchMode;
    }

    public CalcJobTaskSplitEnumerator(
            TblCalcJobInfo jobInfo,
            SplitEnumeratorContext<CalcJobTaskSplit> context,
            CalcJobTaskEnumeratorState state,
            boolean isBatchMode) {
        this.jobInfo = jobInfo;
        this.context = context;
        this.isBatchMode = isBatchMode;

        // 恢复状态
        restoreState(state);
    }

    @Override
    public void start() {
        try {
            // 初始化依赖组件
            initializeDependencies();

            if (!isInitialized.get()) {
                initializeTasks();
                isInitialized.set(true);
            }

            logger.info(
                    "CalcJobTaskSplitEnumerator started for job: {}, total tasks: {}",
                    jobInfo.getJobId(),
                    totalTaskCount.get());

        } catch (Exception e) {
            logger.error(
                    "Failed to start CalcJobTaskSplitEnumerator for job: {}",
                    jobInfo.getJobId(),
                    e);
            throw new RuntimeException("Task enumerator initialization failed", e);
        }
    }

    @Override
    public void addReader(int subtaskId) {
        // 新的 reader 加入时的处理逻辑
        logger.debug("Added reader with subtask ID: {}", subtaskId);
    }

    @Override
    public void addSplitsBack(List<CalcJobTaskSplit> splits, int subtaskId) {
        // 将分片添加回队列
        pendingSplits.addAll(splits);
        logger.debug("Added {} splits back from subtask {}", splits.size(), subtaskId);
    }

    @Override
    public void handleSplitRequest(int subtaskId, @Nullable String requesterHostname) {
        CalcJobTaskSplit split = pendingSplits.poll();
        if (split != null) {
            context.assignSplit(split, subtaskId);
            logger.debug("Assigned split {} to subtask {}", split.splitId(), subtaskId);
        } else {
            context.signalNoMoreSplits(subtaskId);
            logger.debug("No more splits available for subtask {}", subtaskId);
        }
    }

    @Override
    public void handleSourceEvent(int subtaskId, SourceEvent sourceEvent) {
        if (sourceEvent instanceof TaskCompletedEvent) {
            TaskCompletedEvent event = (TaskCompletedEvent) sourceEvent;
            handleTaskCompleted(event.getTaskId(), subtaskId);
        } else if (sourceEvent instanceof TaskFailedEvent) {
            TaskFailedEvent event = (TaskFailedEvent) sourceEvent;
            handleTaskFailed(event.getTaskId(), event.getErrorMessage(), subtaskId);
        }
    }

    @Override
    public CalcJobTaskEnumeratorState snapshotState(long checkpointId) throws Exception {
        List<CalcJobTaskSplit> pendingSplitsList = new ArrayList<>(pendingSplits);

        CalcJobTaskEnumeratorState state =
                new CalcJobTaskEnumeratorState(
                        pendingSplitsList,
                        completedSplitIds.keySet(),
                        nextTaskId.get(),
                        isInitialized.get());

        logger.debug(
                "Snapshotted state for checkpoint {}: {} pending, {} completed",
                checkpointId,
                pendingSplitsList.size(),
                completedSplitIds.size());

        return state;
    }

    @Override
    public void close() throws IOException {
        logger.info("CalcJobTaskSplitEnumerator closed for job: {}", jobInfo.getJobId());
    }

    /** 初始化依赖组件 */
    private void initializeDependencies() throws Exception {
        // 初始化 ModelMetaQueryHandler
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
    }

    /** 初始化任务 */
    private void initializeTasks() throws Exception {
        logger.info("Initializing tasks for job: {}", jobInfo.getJobId());

        // 1. 查询所有设备
        List<AssetInfo> allAssets = queryAllAssets();
        logger.info("Found {} assets for job: {}", allAssets.size(), jobInfo.getJobId());

        // 2. 生成任务
        List<CalcJobTask> tasks = generateTasks(allAssets);
        logger.info("Generated {} tasks for job: {}", tasks.size(), jobInfo.getJobId());

        // 3. 创建分片
        for (CalcJobTask task : tasks) {
            CalcJobTaskSplit split = new CalcJobTaskSplit(task.getUniqueId(), task);
            pendingSplits.offer(split);
        }

        totalTaskCount.set(tasks.size());
        logger.info("Initialized {} task splits for job: {}", tasks.size(), jobInfo.getJobId());
    }

    /** 查询所有设备 */
    private List<AssetInfo> queryAllAssets() throws Exception {
        RecCalcMetaInfo ruleInfo = jobInfo.getRuleInfo();

        Map<String, List<AssetInfo>> assetInfosByModel =
                modelMetaQueryHandler.getAssetInfoByModelIdsWithPagination(
                        ruleInfo.getOrgId(),
                        ruleInfo.getTargetModelIds(),
                        CalcLionConfig.getCalcQueryAssetPageSize());

        // 将按 modelId 分组的结果合并为一个列表
        List<AssetInfo> allAssets = new ArrayList<>();
        for (List<AssetInfo> assetInfos : assetInfosByModel.values()) {
            allAssets.addAll(assetInfos);
        }

        logger.info(
                "Queried assets by models: {}, total assets: {}",
                assetInfosByModel.keySet(),
                allAssets.size());

        return allAssets;
    }

    /** 生成计算任务 */
    private List<CalcJobTask> generateTasks(List<AssetInfo> allAssets) {
        List<CalcJobTask> tasks = new ArrayList<>();

        // 1. 设备批次拆分
        List<List<AssetInfo>> assetBatches = splitAssetsToBatches(allAssets);

        // 2. 时间范围拆分
        List<TimeRange> timeRanges = splitTimeRange();

        // 3. 生成任务矩阵（设备优先，时间次之）
        int taskIdCounter = 1;
        for (List<AssetInfo> assetBatch : assetBatches) {
            // modelId => <assetId, info>
            Map<String, Map<String, AssetInfo>> modelId2AssetMap = new HashMap<>();
            for (AssetInfo assetInfo : assetBatch) {
                modelId2AssetMap
                        .computeIfAbsent(assetInfo.getModelId(), k -> new HashMap<>())
                        .put(assetInfo.getAssetId(), assetInfo);
            }

            for (TimeRange timeRange : timeRanges) {
                CalcJobTask task =
                        CalcJobTask.builder()
                                .jobId(jobInfo.getJobId())
                                .taskId(String.valueOf(taskIdCounter++))
                                .targetPrefName(
                                        jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefName())
                                .targetAssetIds(modelId2AssetMap)
                                .startTime(timeRange.getStartTime())
                                .endTime(timeRange.getEndTime())
                                .status(RecCalcJobTaskStatusEnum.INIT)
                                .build();

                tasks.add(task);
            }
        }

        return tasks;
    }

    /** 设备批次拆分 */
    private List<List<AssetInfo>> splitAssetsToBatches(List<AssetInfo> allAssets) {
        int batchSize = CalcLionConfig.getCalcAssetSplitSize();
        List<List<AssetInfo>> batches = new ArrayList<>();

        for (int i = 0; i < allAssets.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, allAssets.size());
            batches.add(allAssets.subList(i, endIndex));
        }

        logger.info(
                "Split {} assets into {} batches with batch size {}",
                allAssets.size(),
                batches.size(),
                batchSize);

        return batches;
    }

    /** 时间范围拆分 */
    private List<TimeRange> splitTimeRange() {
        long startTime = jobInfo.getCalcStartTime();
        long endTime = jobInfo.getCalcEndTime();
        long splitSeconds = CalcLionConfig.getCalcTimeRangeSplitSeconds();
        long splitMillis = splitSeconds * 1000;

        List<TimeRange> timeRanges = new ArrayList<>();

        for (long current = startTime; current < endTime; current += splitMillis) {
            long rangeEnd = Math.min(current + splitMillis, endTime);
            timeRanges.add(new TimeRange(current, rangeEnd));
        }

        logger.info(
                "Split time range [{}, {}] into {} ranges with split size {}s",
                startTime,
                endTime,
                timeRanges.size(),
                splitSeconds);

        return timeRanges;
    }

    /** 处理任务完成 */
    private synchronized void handleTaskCompleted(String taskId, int fromSubtaskId) {
        if (completedSplitIds.put(taskId, taskId) == null) {
            long completed = completedSplitIds.size();
            long total = totalTaskCount.get();

            logger.info(
                    "Task {} completed by subtask-{}, progress: {}/{} ({:.2f}%)",
                    taskId, fromSubtaskId, completed, total, (completed * 100.0 / total));

            // 检查是否所有任务都完成
            if (completed >= total) {
                logger.info(
                        "All tasks completed for job: {}, updating status to FINISHED",
                        jobInfo.getJobId());
                // TODO: 更新作业状态为 FINISHED
            }
        }
    }

    /** 处理任务失败 */
    private void handleTaskFailed(String taskId, String errorMessage, int fromSubtaskId) {
        logger.error("Task {} failed in subtask-{}: {}", taskId, fromSubtaskId, errorMessage);
        // TODO: 处理任务失败逻辑
    }

    /** 恢复状态 */
    private void restoreState(CalcJobTaskEnumeratorState state) {
        if (state != null) {
            pendingSplits.addAll(state.getPendingSplits());
            state.getCompletedSplitIds().forEach(id -> completedSplitIds.put(id, id));
            nextTaskId.set(state.getNextTaskId());
            isInitialized.set(state.isInitialized());
            totalTaskCount.set(state.getTotalTaskCount());

            logger.info(
                    "Restored state for job: {}, {} pending, {} completed",
                    jobInfo.getJobId(),
                    pendingSplits.size(),
                    completedSplitIds.size());
        }
    }
}
