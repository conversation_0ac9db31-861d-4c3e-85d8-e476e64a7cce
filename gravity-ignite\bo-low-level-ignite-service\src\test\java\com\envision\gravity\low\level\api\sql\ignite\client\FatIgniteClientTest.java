package com.envision.gravity.low.level.api.sql.ignite.client;

import com.envision.gravity.common.enums.IDType;
import com.envision.gravity.common.ignite.service.GSqlService;
import com.envision.gravity.common.ignite.service.IDService;
import com.envision.gravity.common.ignite.service.LowLevelIgniteService;
import com.envision.gravity.common.response.ResponseResult;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.internal.IgniteEx;
import org.apache.ignite.internal.cdc.CdcEventImpl;
import org.apache.ignite.internal.util.IgniteUtils;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;
import org.apache.ignite.thread.IgniteThread;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/3/8
 * @description
 */
@Slf4j
public class FatIgniteClientTest {
    private static Ignite ignite;

    @BeforeAll
    public static void init() {
        IgniteConfiguration cfg = getIgniteConfiguration();
        ignite = Ignition.getOrStart(cfg);
    }

    @NotNull
    private static IgniteConfiguration getIgniteConfiguration() {
        IgniteConfiguration cfg = new IgniteConfiguration();
        cfg.setPeerClassLoadingEnabled(true);
        cfg.setDeploymentMode(DeploymentMode.CONTINUOUS);
        cfg.setAuthenticationEnabled(true);
        cfg.setClientMode(true);
        TcpDiscoverySpi spi = new TcpDiscoverySpi();
        TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();
        Set<String> addresses = new HashSet<>(4);
        addresses.add("*************:47500");
        ipFinder.setAddresses(addresses);
        spi.setIpFinder(ipFinder);
        cfg.setDiscoverySpi(spi);
        cfg.setNetworkTimeout(120000);
        return cfg;
    }

    @Test
    void loadExternalStorageTest() {
        long loadCacheStartTime = System.currentTimeMillis();
        ignite.cache("o20241230_TBL_COMPONENT_PREF").loadCache(null);
        long loadCacheEndTime = System.currentTimeMillis();
        log.info(
                "Load cache success, total time cost: [{}]",
                (loadCacheEndTime - loadCacheStartTime) / 1000.0 + "s");
    }

    @Test
    void externalStorageTest() {
        System.out.println(ignite.cacheNames());
    }

    @Test
    void sqlTest() {
        List<List<?>> all =
                ((IgniteEx) ignite)
                        .context()
                        .query()
                        .querySqlFields(
                                new SqlFieldsQuery(
                                                "select _key, _val, \"dtmi:custom:Tsdb_Api_Regression_Test_Model;1__measurepoint__Generic.IntTypePoint_2.1_rJ8bnMfm_value\"  \n"
                                                        + "from O16227961710541858.tbl_obj \n"
                                                        + "where system_id = '0wfV0Wb7_1yP';")
                                        .setSchema("O16227961710541858"),
                                false)
                        .getAll();
        System.out.println(all);

        //        ClientCache<Object, Object> cache =
        // igniteClient.cache("SQL_O16227961710541858_TBL_OBJ").withKeepBinary();
        //        cache.get("0wfV0Wb7_1yP");
    }

    @Test
    void instanceNameTest() {
        System.out.println(">>> " + ignite.cluster().node());
        System.out.println(">>> " + ignite.name());
    }

    @Test
    void readCDCDataValue() throws InterruptedException {
        ReadCDCDataThread readCDCDataThread = new ReadCDCDataThread(null, null);
        readCDCDataThread.start();
        Thread.sleep(10000);
    }

    static class ReadCDCDataThread extends IgniteThread {

        public ReadCDCDataThread(String igniteInstanceName, String threadName) {
            super(igniteInstanceName, threadName);
        }

        @Override
        public void run() {
            String hexString =
                    "AC ED 00 05 73 72 00 2B 6F 72 67 2E 61 70 61 63 68 65 2E 69 67 6E 69 74 65 2E 69 6E 74 65 "
                            + "72 6E 61 6C 2E 63 64 63 2E 43 64 63 45 76 65 6E 74 49 6D 70 6C 00 00 00 00 00 00 00 00 02 "
                            + "00 07 49 00 07 63 61 63 68 65 49 64 4A 00 0A 65 78 70 69 72 65 54 69 6D 65 49 00 04 70 61 "
                            + "72 74 5A 00 07 70 72 69 6D 61 72 79 4C 00 03 6B 65 79 74 00 12 4C 6A 61 76 61 2F 6C 61 6E "
                            + "67 2F 4F 62 6A 65 63 74 3B 4C 00 03 6F 72 64 74 00 2B 4C 6F 72 67 2F 61 70 61 63 68 65 2F "
                            + "69 67 6E 69 74 65 2F 63 61 63 68 65 2F 43 61 63 68 65 45 6E 74 72 79 56 65 72 73 69 6F 6E "
                            + "3B 4C 00 03 76 61 6C 71 00 7E 00 01 78 70 C9 59 FB 28 00 00 00 00 00 00 00 00 00 00 00 BD "
                            + "01 73 72 00 32 6F 72 67 2E 61 70 61 63 68 65 2E 69 67 6E 69 74 65 2E 69 6E 74 65 72 6E 61 "
                            + "6C 2E 62 69 6E 61 72 79 2E 42 69 6E 61 72 79 4F 62 6A 65 63 74 49 6D 70 6C 00 00 00 00 00 "
                            + "00 00 00 0C 00 00 78 72 00 34 6F 72 67 2E 61 70 61 63 68 65 2E 69 67 6E 69 74 65 2E 69 6E "
                            + "74 65 72 6E 61 6C 2E 62 69 6E 61 72 79 2E 42 69 6E 61 72 79 4F 62 6A 65 63 74 45 78 49 6D "
                            + "70 6C 04 8C 0E 69 79 57 B2 32 02 00 00 78 70 77 36 00 00 00 2E 67 01 2B 00 43 B2 00 7D 48 "
                            + "14 91 0C 2E 00 00 00 AC C2 60 61 2D 00 00 00 09 10 00 00 00 45 6E 4F 53 5F 53 6F 6C 61 72 "
                            + "5F 39 53 69 74 65 18 00 00 00 00 78 73 72 00 44 6F 72 67 2E 61 70 61 63 68 65 2E 69 67 6E "
                            + "69 74 65 2E 69 6E 74 65 72 6E 61 6C 2E 70 72 6F 63 65 73 73 6F 72 73 2E 63 61 63 68 65 2E "
                            + "76 65 72 73 69 6F 6E 2E 47 72 69 64 43 61 63 68 65 56 65 72 73 69 6F 6E 00 00 00 00 00 00 "
                            + "00 00 0C 00 00 78 70 77 10 13 2F B5 51 00 00 01 8E 3C 9A 01 AF 00 00 00 01 78 73 71 00 7E "
                            + "00 04 77 B5 00 00 00 AD 67 01 2B 00 55 3C 36 40 8F 96 03 45 AD 00 00 00 08 4B 92 44 A7 00 "
                            + "00 00 09 49 00 00 00 7B 22 64 65 66 61 75 6C 74 22 3A 20 22 E5 85 89 E4 BC 8F E5 9C BA E7 "
                            + "AB 99 22 2C 22 65 6E 5F 55 53 22 3A 20 22 53 6F 6C 61 72 20 53 69 74 65 22 2C 22 7A 68 5F "
                            + "43 4E 22 3A 20 22 E5 85 89 E4 BC 8F E5 9C BA E7 AB 99 22 7D 09 0A 00 00 00 30 31 39 30 30 "
                            + "30 30 30 30 32 21 71 49 A0 3C 8E 01 00 00 00 00 00 00 09 07 00 00 00 47 72 61 76 69 74 79 "
                            + "21 71 49 A0 3C 8E 01 00 00 00 00 00 00 09 07 00 00 00 47 72 61 76 69 74 79 18 66 75 82 8E "
                            + "9B 00 00 00 00 78";

            String[] hexBytes = hexString.split(" ");
            byte[] byteArray = new byte[hexBytes.length];

            for (int i = 0; i < hexBytes.length; i++) {
                byteArray[i] = (byte) Integer.parseInt(hexBytes[i], 16);
            }

            System.out.println(">>> " + (CdcEventImpl) IgniteUtils.fromBytes(byteArray));
        }
    }

    @Test
    void queryTest() {
        List<List<?>> result =
                ((IgniteEx) ignite)
                        .context()
                        .query()
                        .querySqlFields(
                                new SqlFieldsQuery("SELECT * FROM METRIC_ENGINE_CUSTOM_DB;")
                                        .setSchema("PUBLIC"),
                                false)
                        .getAll();
        System.out.println(result);
    }

    @Test
    void schemaOperator() {}

    @Test
    void serviceOperator() {
        System.out.println(ignite.services().serviceDescriptors());
        ResponseResult<?> responseResult =
                ignite.services()
                        .serviceProxy(IDService.SERVICE_NAME, IDService.class, false)
                        .get(1, IDType.OBJECT_ID);
        System.out.println(">>> " + responseResult.getData());
        System.out.println(responseResult.getMessage());
    }

    @Test
    void insertBOGroupTest() {
        String noGroupId =
                "INSERT INTO _BO_GROUP(group_name, group_display_name, created_user, modified_user) "
                        + "VALUES ('Measurement Point Attribute Set 1', '{\"default\": \"Measurement Point Attribute Set 1\",\"en_US\": \"Measurement Point Attribute Set 1\",\"zh_CN\": \"测点属性集合1\"}', 'Test', 'Test');";
        ResponseResult<?> gSqlServiceResponseResult =
                ignite.services()
                        .serviceProxy(GSqlService.SERVICE_NAME, GSqlService.class, false)
                        .execute("", "GRAVITY", noGroupId, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertBOTest() {
        String insertWithValues =
                "INSERT INTO _BO (object_display_name, system_id, created_user, modified_user) "
                        + "VALUES ('{\"default\": \"山东LCLL5.9MWp分布式项目_INV_1\"}','xxx', 'Gravity', 'Gravity'), "
                        + "('{\"default\": \"光伏场站\"}', 'xxx', 'Gravity', 'Gravity');";
        ResponseResult<?> gSqlServiceResponseResult =
                ignite.services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME,
                                LowLevelIgniteService.class,
                                false)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertPropertyTest() {
        String insertWithValues =
                "INSERT INTO _PROPERTY (pref_id, pref_name, pref_display_name, pref_type, writable, required, has_quality, pref_data_type, pref_signal_type, unit, created_user, modified_user) "
                        + "VALUES ('a', 'b', 'c', 'MEASUREPOINT', false, false, false, 'string', 'f', 'g', 'Test', 'Test'), "
                        + "('A', 'B', 'C', 'service', false, false, false, 'time', 'F', 'G', 'Test', 'Test');";
        ResponseResult<?> gSqlServiceResponseResult =
                ignite.services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME,
                                LowLevelIgniteService.class,
                                false)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }

    @Test
    void insertTagTest() {
        String insertWithValues =
                "INSERT INTO _TAG (data_id, tag_type, tag_id, created_user, modified_user) "
                        + "VALUES ('a', 'model', 'a', 'Test', 'Test'), "
                        + "('A', 'property', 'A', 'Test', 'Test');";
        ResponseResult<?> gSqlServiceResponseResult =
                ignite.services()
                        .serviceProxy(
                                LowLevelIgniteService.SERVICE_NAME,
                                LowLevelIgniteService.class,
                                false)
                        .execute("", "O16227961710541858", insertWithValues, null);
        System.out.println(gSqlServiceResponseResult.getMessage());
    }
}
