<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-flink</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>flink-common</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <spotless.version>2.27.1</spotless.version>
        <tsdb.version>1.5.8-241230-SNAPSHOT</tsdb.version>
        <flink.version>1.14.5-edh-3.1.5-4</flink.version>
        <ignite.version>2.16.0-enos-1.0.0-SNAPSHOT</ignite.version>
        <scala.version>2.12</scala.version>
        <lion.version>3.0.2-SNAPSHOT</lion.version>
        <!--<dep.scope>compile</dep.scope>-->
        <!--<dep.scope>provided</dep.scope>-->
        <dep.scope>provided</dep.scope>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.envision.arch.lion</groupId>
            <artifactId>lion-client</artifactId>
            <version>${lion.version}</version>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-kafka_${scala.version}</artifactId>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-metrics-dropwizard</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <!-- ignite -->
        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-core</artifactId>
            <version>${ignite.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.3.22</version>
            <scope>${dep.scope}</scope>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-local-cache</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
            <version>${release.version}</version>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
            <version>4.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
            <version>4.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.1</version>
            <scope>test</scope>
        </dependency>

    </dependencies>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless.version}</version>
                    <configuration>
                        <java>
                            <googleJavaFormat>
                                <version>1.7</version>
                                <style>AOSP</style>
                            </googleJavaFormat>
                            <!-- \# refers to the static imports -->
                            <importOrder>
                                <order>com.envision.gravity,javax,java,scala,\#</order>
                            </importOrder>
                            <removeUnusedImports/>
                        </java>
                    </configuration>
                    <executions>
                        <execution>
                            <id>spotless-check</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <descriptorRefs>jar-with-dependencies</descriptorRefs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.6.0.1398</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>

</project>