gravity-rest.ignite.tcpNoDelay=false
gravity-rest.ignite.client.timeout=60000

gravity-rest.nebula.session.cache.size=10
gravity-rest.nebula.session.cache.durationInMinutes=30

gravity-rest.locale=zh_CN,en_US


##############################
## Gravity common properties
##############################
gravity-common.sql-gateway.address=127.0.0.1:3307
gravity-common.sql-gateway.jdbc-url=************************************************************************************************************************************************************************

gravity-common.tsdb.url=influxdb_cluster://influxdb-shard1.apaas-ppe3.eniot.io,influxdb-shard2.apaas-ppe3.eniot.io?readTimeout=200000
gravity-common.tsdb.username=admin
gravity-common.tsdb.password=Envisi0n1234!
gravity-common.tsdb.replica-factor=1
gravity-common.tsdb.sharding-rule=SHARDING_BY_MEASUREMENT

gravity-common.ignite.address=***********:10800
gravity-common.ignite.jdbc-url=**********************************************************************************
gravity-common.ignite.jdbc-driver=org.apache.ignite.IgniteJdbcThinDriver
gravity-common.ignite.username=ignite
gravity-common.ignite.password=ignite

gravity-common.postgresql.jdbc-driver=org.postgresql.Driver
gravity-common.postgresql.jdbc-url=******************************************
gravity-common.postgresql.username=postgres
gravity-common.postgresql.password=Envisi0n4321!
gravity-common.postgresql.hostname=***********
gravity-common.postgresql.port=5432

## nebula ??????????
gravity-common.nebula.address=nebula8001:9669
gravity-common.nebula.username=root
gravity-common.nebula.password=Envisi0n4321!


Camel.kafka-common=kafka8007:9092