package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblComponentPref {
    @KeyColumn(name = "comp_id")
    @ColumnName("comp_id")
    @RequiredField(message = "comp_id field is required")
    private String compId;

    @KeyColumn(name = "pref_id")
    @ColumnName("pref_id")
    @RequiredField(message = "pref_id field is required")
    private String prefId;

    @ValueColumn(name = "field_id")
    @ColumnName("field_id")
    private String fieldId;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;

    @ValueColumn(name = "raw_field_id")
    @ColumnName("raw_field_id")
    private String rawFieldId;

    @ValueColumn(name = "field_index")
    @ColumnName("field_index")
    private Integer fieldIndex;

    @ValueColumn(name = "horizontal")
    @ColumnName("horizontal")
    private Boolean horizontal;
}
