package com.envision.gravity.flink.streaming.calculate.recalc;

import java.io.Serializable;

/**
 * 时间范围
 *
 * <AUTHOR>
 */
public class TimeRange implements Serializable {

    private static final long serialVersionUID = 1L;

    private final long startTime;
    private final long endTime;

    public TimeRange(long startTime, long endTime) {
        if (startTime >= endTime) {
            throw new IllegalArgumentException("Start time must be less than end time");
        }
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public long getDuration() {
        return endTime - startTime;
    }

    @Override
    public String toString() {
        return "TimeRange{" + "startTime=" + startTime + ", endTime=" + endTime + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TimeRange timeRange = (TimeRange) o;

        if (startTime != timeRange.startTime) return false;
        return endTime == timeRange.endTime;
    }

    @Override
    public int hashCode() {
        int result = (int) (startTime ^ (startTime >>> 32));
        result = 31 * result + (int) (endTime ^ (endTime >>> 32));
        return result;
    }
}
