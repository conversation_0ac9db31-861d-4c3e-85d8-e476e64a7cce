package com.envision.gravity.common.po;

import java.sql.Timestamp;
import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblObj {

    private String systemId;

    private String systemDisplayName;

    private String categoryId;

    private List<TblField> fields;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
