package com.envision.gravity.flink.streaming.calculate.cdc;

import java.util.Optional;

public enum CalcCdcTable {
    TBL_PROPERTY_UPSTREAM_RULE("tbl_property_upstream_rule"),
    TBL_BO_MODEL("tbl_bo_model"),
    TBL_BO_MODEL_COMP("tbl_bo_model_comp"),
    TBL_PREF("tbl_pref"),
    TBL_COMPONENT("tbl_component"),
    TBL_COMPONENT_PREF("tbl_component_pref");

    private final String name;

    CalcCdcTable(String name) {
        this.name = name;
    }

    public static Optional<CalcCdcTable> find(String expr) {
        for (CalcCdcTable t : CalcCdcTable.values()) {
            if (expr.equalsIgnoreCase(t.getName())) {
                return Optional.of(t);
            }
        }
        return Optional.empty();
    }

    public String getName() {
        return name;
    }
}
