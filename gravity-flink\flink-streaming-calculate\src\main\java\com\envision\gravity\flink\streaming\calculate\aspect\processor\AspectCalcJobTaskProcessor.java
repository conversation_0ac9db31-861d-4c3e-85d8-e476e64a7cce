package com.envision.gravity.flink.streaming.calculate.aspect.processor;

import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.message.CalcResultMsg;
import com.envision.gravity.flink.streaming.calculate.dto.meta.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.dto.query.LatestQueryEntity;
import com.envision.gravity.flink.streaming.calculate.sqlgateway.CalculationServiceHelper;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyPayload;

import java.util.*;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AspectCalc切面计算任务处理器
 *
 * <p>功能： 1. 处理CalcJobTask，执行切面计算 2. 使用最新值查询数据 3. 输出到下游KafkaSink 4. 支持任务完成通知
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class AspectCalcJobTaskProcessor
        extends ProcessFunction<CalcJobTask, LegacyMsgWithMultiAssets> {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcJobTaskProcessor.class);

    // ✅ 流式专用：全局状态管理
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient CalculationServiceHelper calculationServiceHelper;

    // ✅ Job元信息缓存（简化实现，实际可能需要更复杂的缓存机制）
    private transient Map<String, CalcJobMetaInfo> jobMetaInfoCache;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化组件
        this.taskCompletionNotifier = TaskCompletionNotifier.getGlobalInstance();
        this.calculationServiceHelper = CalculationServiceHelper.getInstance();
        this.jobMetaInfoCache = new HashMap<>();

        logger.info("AspectCalcJobTaskProcessor opened");
    }

    @Override
    public void processElement(
            CalcJobTask calcJobTask, Context ctx, Collector<LegacyMsgWithMultiAssets> out)
            throws Exception {

        String taskId = calcJobTask.getTaskId();
        String jobId = calcJobTask.getJobId();

        logger.info("Processing aspect calculation task: {} for job: {}", taskId, jobId);

        try {
            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(jobId, taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, jobId);
                return;
            }

            // ✅ 获取作业元信息（从内存状态或上下文）
            CalcJobMetaInfo jobMetaInfo = getJobMetaInfo(jobId);
            if (jobMetaInfo == null) {
                logger.error("Job meta info not found for job: {}", jobId);
                return;
            }

            // ✅ 切面计算核心：最新值计算处理
            List<LegacyMsgWithMultiAssets> calcResults =
                    executeAspectCalculation(jobMetaInfo, calcJobTask);

            // ✅ 关键差异：整个Task完成后才输出到下游KafkaSink
            for (LegacyMsgWithMultiAssets result : calcResults) {
                out.collect(result);
            }

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(jobId, taskId);

            logger.info(
                    "Aspect calculation task {} completed for job {}, output {} results",
                    taskId,
                    jobId,
                    calcResults.size());

        } catch (Exception e) {
            logger.error(
                    "Aspect calculation task {} failed for job {}: {}",
                    taskId,
                    jobId,
                    e.getMessage(),
                    e);
            taskCompletionNotifier.markTaskFailed(jobId, taskId, e.getMessage());
        }
    }

    /** ✅ 切面计算核心方法：最新值计算处理 */
    private List<LegacyMsgWithMultiAssets> executeAspectCalculation(
            CalcJobMetaInfo jobMetaInfo, CalcJobTask calcJobTask) throws Exception {

        String orgId = jobMetaInfo.getOrgId();

        // ✅ 关键差异1：不需要时间拆分，直接处理整个Task
        logger.info(
                "Aspect calculation task {} processing without time splitting",
                calcJobTask.getTaskId());

        // ✅ 直接查询最新值并计算
        CalcResultMsg calcResults = queryLatestDataAndCalculate(orgId, jobMetaInfo, calcJobTask);

        // 转换为输出格式
        return convertToOutputMessages(calcResults, jobMetaInfo);
    }

    /** ✅ 关键差异：查询最新值数据并计算 */
    private CalcResultMsg queryLatestDataAndCalculate(
            String orgId, CalcJobMetaInfo jobMetaInfo, CalcJobTask calcJobTask) throws Exception {

        // 1. 构建最新值查询实体
        Map<String, LatestQueryEntity> latestQueryEntities =
                buildLatestValueQueryEntities(jobMetaInfo, calcJobTask);

        // 2. ✅ 关键差异：统一使用最新值查询方法
        Map<String, LegacyPayload> allLatestValues = queryAllLatestValuesOnly(latestQueryEntities);

        // 3. 构建目标资产数据（基于最新值）
        Map<String, LegacyPayload> targetAssetValues =
                buildTargetAssetValuesFromLatest(calcJobTask, allLatestValues);

        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
                execTargetPropExprCalc(
                        orgId, jobMetaInfo, calcJobTask, targetAssetValues, modelPathMap);

        return CalcResultMsg.builder().targetModel2MsgMap(targetModel2MsgMap).build();
    }

    /** ✅ 关键差异：只构建最新值查询实体 */
    private Map<String, LatestQueryEntity> buildLatestValueQueryEntities(
            CalcJobMetaInfo jobMetaInfo, CalcJobTask calcJobTask) {

        List<SrcPrefItem> srcPrefItems = jobMetaInfo.getTargetPropertyMeta().getSrcPrefItems();
        Map<String, LatestQueryEntity> latestQueryEntities = new HashMap<>();

        String orgId = jobMetaInfo.getOrgId();

        // ✅ 所有依赖项都使用最新值查询
        for (SrcPrefItem srcPrefItem : srcPrefItems) {
            String srcModelId = srcPrefItem.getModelId();
            LatestQueryEntity queryEntity =
                    latestQueryEntities.computeIfAbsent(
                            srcModelId,
                            k ->
                                    LatestQueryEntity.builder()
                                            .orgId(orgId)
                                            .modelId(srcModelId)
                                            .build());

            queryEntity.getPropertyNames().add(srcPrefItem.getPrefName());

            List<String> targetAssetIds = calcJobTask.getTargetAssetIds();
            if (jobMetaInfo.getTargetModelIds().contains(srcModelId)) {
                queryEntity.getAssetIds().addAll(targetAssetIds);
            } else {
                // 通过 AssetInfo 获取依赖资产
                List<String> dependentAssetIds = getDependentAssetIds(targetAssetIds, srcModelId);
                queryEntity.getAssetIds().addAll(dependentAssetIds);
            }
        }

        return latestQueryEntities;
    }

    /** ✅ 关键差异：查询最新值（使用 queryLatestValues 方法） */
    private Map<String, LegacyPayload> queryAllLatestValuesOnly(
            Map<String, LatestQueryEntity> latestQueryEntities) throws Exception {

        Map<String, LegacyPayload> allLatestValues = new HashMap<>();

        for (Map.Entry<String, LatestQueryEntity> entry : latestQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            LatestQueryEntity queryEntity = entry.getValue();

            // ✅ 使用最新值查询方法
            Map<String, LegacyPayload> modelLatestValues =
                    calculationServiceHelper.queryLatestValues(
                            queryEntity.getOrgId(), modelId, queryEntity);

            allLatestValues.putAll(modelLatestValues);
        }

        return allLatestValues;
    }

    /** ✅ 转换为输出消息格式 */
    private List<LegacyMsgWithMultiAssets> convertToOutputMessages(
            CalcResultMsg calcResults, CalcJobMetaInfo jobMetaInfo) {

        List<LegacyMsgWithMultiAssets> outputMessages = new ArrayList<>();
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
                calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // 前置校验
            if (shouldOutputToKafka(jobMetaInfo, message)) {
                // 添加必要的header信息
                enrichMessageHeaders(message, jobMetaInfo);
                outputMessages.add(message);
            }
        }

        return outputMessages;
    }

    /** ✅ 前置检查：判断是否应该输出到Kafka */
    private boolean shouldOutputToKafka(
            CalcJobMetaInfo jobMetaInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不输出到Kafka）
        if (jobMetaInfo.getTargetPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip outputting attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /** ✅ 丰富消息头信息，包含直白映射检查 */
    private void enrichMessageHeaders(
            LegacyMsgWithMultiAssets message, CalcJobMetaInfo jobMetaInfo) {
        // 添加切面计算相关的header信息
        message.getHeaders().put("calc_type", "aspect");
        message.getHeaders().put("job_id", jobMetaInfo.getJobId());
        message.getHeaders().put("calc_time", String.valueOf(System.currentTimeMillis()));

        // ✅ 添加orgId到消息头（用于Topic名称构建）
        String orgId = jobMetaInfo.getOrgId();
        message.getHeaders().put("org_id", orgId);

        // ✅ 前置检查：判断是否为直白映射，添加 direct_mapping header
        if (jobMetaInfo.isDirectMapping()) {
            message.getHeaders().put("direct_mapping", "1");
            logger.debug(
                    "Added direct_mapping header for aspect calc model: {}", message.getModelId());
        }
    }

    /** ✅ 获取Job元信息（从内存状态或上下文） */
    private CalcJobMetaInfo getJobMetaInfo(String jobId) {
        // 这里应该从状态或上下文中获取CalcJobMetaInfo
        // 实际实现中可能需要维护一个Map<String, CalcJobMetaInfo>
        // 或者通过其他方式获取
        return jobMetaInfoCache.get(jobId);
    }

    // TODO: 实现其他辅助方法
    private Map<String, LegacyPayload> buildTargetAssetValuesFromLatest(
            CalcJobTask calcJobTask, Map<String, LegacyPayload> allLatestValues) {
        // 实现目标资产数据构建逻辑
        return new HashMap<>();
    }

    private Map<String, String> getModelPathMap(String orgId, CalcJobTask calcJobTask) {
        // 实现模型路径映射逻辑
        return new HashMap<>();
    }

    private Map<String, LegacyMsgWithMultiAssets> execTargetPropExprCalc(
            String orgId,
            CalcJobMetaInfo jobMetaInfo,
            CalcJobTask calcJobTask,
            Map<String, LegacyPayload> targetAssetValues,
            Map<String, String> modelPathMap) {
        // 实现表达式计算逻辑
        return new HashMap<>();
    }

    private List<String> getDependentAssetIds(List<String> targetAssetIds, String srcModelId) {
        // 实现依赖资产查询逻辑
        return new ArrayList<>();
    }
}
