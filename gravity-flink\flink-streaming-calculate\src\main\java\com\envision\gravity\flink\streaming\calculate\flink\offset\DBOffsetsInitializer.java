package com.envision.gravity.flink.streaming.calculate.flink.offset;

import com.envision.gravity.common.util.IgniteUtil;

import java.util.*;
import java.util.stream.Collectors;

import static org.apache.kafka.clients.consumer.OffsetResetStrategy.LATEST;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/*
 manage kafka offset by database
*/
public class DBOffsetsInitializer implements OffsetsInitializer {

    private static final long serialVersionUID = 1649702397250402000L;

    private static final String QUERY_OFFSET_PATTERN =
            "SELECT TOPIC, PARTITION_ID, OFFSET_ID FROM TBL_OFFSET_V2 "
                    + "WHERE CONSUMER = '%s' and TOPIC IN (%s)";

    private static final Logger logger = LoggerFactory.getLogger(DBOffsetsInitializer.class);
    private final String consumer;

    public DBOffsetsInitializer(String consumer) {
        this.consumer = consumer;
    }

    @Override
    public Map<TopicPartition, Long> getPartitionOffsets(
            Collection<TopicPartition> partitions,
            PartitionOffsetsRetriever partitionOffsetsRetriever) {
        Map<TopicPartition, Long> offsetsMap = new HashMap<>();

        Set<String> topics =
                partitions.stream().map(TopicPartition::topic).collect(Collectors.toSet());

        String topicStr =
                topics.stream().map(topic -> "'" + topic + "'").collect(Collectors.joining(","));
        String sql = String.format(QUERY_OFFSET_PATTERN, consumer, String.join(", ", topicStr));
        logger.info("try to query offset: " + sql);
        List<List<?>> queryResult = IgniteUtil.query("GRAVITY", sql);
        if (queryResult != null && queryResult.get(0) != null) {
            // <topic, partition> -> offset
            Set<TopicPartition> beginOffset = new HashSet<>();
            Map<Tuple2<String, Integer>, Long> resultMap =
                    queryResult.stream()
                            .collect(
                                    Collectors.toMap(
                                            (array) ->
                                                    new Tuple2<String, Integer>(
                                                            array.get(0).toString(),
                                                            Integer.parseInt(
                                                                    array.get(1).toString())),
                                            (array) -> Long.parseLong(array.get(2).toString())));
            partitions.forEach(
                    topicPartition -> {
                        Long offset =
                                resultMap.get(
                                        new Tuple2<>(
                                                topicPartition.topic(),
                                                topicPartition.partition()));
                        if (offset != null) {
                            offsetsMap.put(topicPartition, offset + 1);
                        } else {
                            beginOffset.add(topicPartition);
                        }
                    });

            offsetsMap.forEach(
                    (partition, offset) -> {
                        logger.info("initial offset from db. " + partition + " : " + offset);
                    });
            offsetsMap.putAll(partitionOffsetsRetriever.beginningOffsets(beginOffset));
            Map<TopicPartition, Long> committedPartition =
                    partitionOffsetsRetriever.committedOffsets(beginOffset);
            offsetsMap.putAll(committedPartition);

        } else {
            offsetsMap.putAll(partitionOffsetsRetriever.beginningOffsets(partitions));
            Map<TopicPartition, Long> committedPartition =
                    partitionOffsetsRetriever.committedOffsets(partitions);
            offsetsMap.putAll(committedPartition);
        }
        offsetsMap.forEach(
                (partition, offset) -> {
                    logger.info("initial offset suc. " + partition + " : " + offset);
                });
        IgniteUtil.close();
        return offsetsMap;
    }

    @Override
    public OffsetResetStrategy getAutoOffsetResetStrategy() {
        return LATEST;
    }
}
