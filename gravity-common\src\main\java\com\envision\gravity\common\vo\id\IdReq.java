package com.envision.gravity.common.vo.id;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/02/28 11:13 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdReq {

    @Min(value = 1, message = "count can not less than 1")
    @Max(value = 10000, message = "count can not more than 10000")
    private int count;
}
