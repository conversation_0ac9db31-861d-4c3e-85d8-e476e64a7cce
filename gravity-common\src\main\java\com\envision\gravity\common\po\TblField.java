package com.envision.gravity.common.po;

import com.envision.gravity.common.enums.FieldType;

import java.io.Serializable;
import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblField implements Serializable {

    private static final long serialVersionUID = -8812921664579842037L;

    private String fieldId;

    private String fieldName;

    private String fieldDisplayName;

    private String categoryId;

    private String fieldType;

    private String dataType;

    private String unit;

    private String createdUser;

    private String modifiedUser;

    private Timestamp createdTime;

    private Timestamp modifiedTime;

    private String rawFieldId;

    private Integer fieldIndex;

    private Boolean horizontal;

    public boolean isAttr() {
        return FieldType.ATTRIBUTE.name().equalsIgnoreCase(fieldType);
    }

    public boolean isPoint() {
        return FieldType.MEASUREPOINT.name().equalsIgnoreCase(fieldType);
    }
}
