package com.envision.gravity.low.level.api.rest.antlr;

import com.envision.gravity.low.level.api.rest.enums.*;
import com.envision.gravity.low.level.api.rest.exception.UnsupportSearchException;
import com.envision.gravity.low.level.api.rest.util.ExpressionUtils;
import com.envision.gravity.low.level.api.rest.util.SpecialStringProcessor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** @Author: qi.jiang2 @Date: 2024/03/14 15:14 @Description: */
public class ModelConditionEvalVisitor extends ConditionBaseVisitor<String> {

    private static final Logger logger = LoggerFactory.getLogger(ModelConditionEvalVisitor.class);

    private final Map<String, ModelSearchSupportFieldEnum> supportFieldMap = new HashMap<>(8);

    private List<String> localeList = new ArrayList<>();

    public void setLocaleList(List<String> localeList) {
        this.localeList = localeList;
    }

    public ModelConditionEvalVisitor() {
        for (ModelSearchSupportFieldEnum supportFieldEnum : ModelSearchSupportFieldEnum.values()) {
            supportFieldMap.put(supportFieldEnum.getSupportField(), supportFieldEnum);
        }
    }

    @Override
    public String visitParse(ConditionParser.ParseContext ctx) {
        return visit(ctx.getChild(0));
    }

    @Override
    public String visitParenExpr(ConditionParser.ParenExprContext ctx) {
        String left = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        return left + op + right;
    }

    @Override
    public String visitLikeExpr(ConditionParser.LikeExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String replaceRight = ExpressionUtils.likeEscape(right);
        return getLikeExpression(fieldStr, op, replaceRight, "", false);
    }

    @Override
    public String visitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        String replaceRight = ExpressionUtils.likeEscape(right);
        return getLikeExpression(fieldStr, op, replaceRight, locale, true);
    }

    private String getLikeExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left = new StringBuilder();
        String key = "";
        String supportField;
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            supportField = fieldStr.substring(0, dotIndex);
            key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
        } else {
            supportField = fieldStr;
        }
        ModelSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
        if (supportFieldEnum == null) {
            throw new UnsupportSearchException(
                    String.format("Unsupported search by field: %s", supportField));
        }
        if (key.isEmpty()) {
            left = new StringBuilder(supportFieldEnum.getSqlField() + " ");
        } else {
            if (!isI18n) {
                left = new StringBuilder(supportFieldEnum.getSqlField() + "->>'" + key + "' ");
            } else {
                if (locale.isEmpty() || "*".equals(locale)) {
                    left.append("(");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(supportFieldEnum.getSqlField())
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' ")
                                .append(op)
                                .append(" ")
                                .append(right);
                        if (i < localeList.size() - 1) {
                            left.append(" or ");
                        }
                    }
                    left.append(" or (");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(supportFieldEnum.getSqlField())
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' isnull and ");
                    }
                    left.append(supportFieldEnum.getSqlField())
                            .append(" -> '")
                            .append(key)
                            .append("' ->> '")
                            .append(Constants.DEFAULT_LANGUAGE)
                            .append("' ")
                            .append(op)
                            .append(" ")
                            .append(right)
                            .append("))");
                } else {
                    if (!localeList.contains(locale)
                            && !Constants.DEFAULT_LANGUAGE.equals(locale)) {
                        locale = Constants.DEFAULT_LANGUAGE;
                    }
                    String sqlField = supportFieldEnum.getSqlField();
                    left =
                            new StringBuilder(
                                    "("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " or ("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + Constants.DEFAULT_LANGUAGE
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " and "
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' isnull))");
                }
                return left.toString();
            }
        }
        return left + op + " " + right;
    }

    @Override
    public String visitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx) {
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        String fieldStr = visitField(ctx.left.field());
        String locale = visitStringValue(ctx.left.stringValue());
        locale = locale.substring(1, locale.length() - 1);
        return getComparatorExpression(fieldStr, op, right, locale, true);
    }

    private String getComparatorExpression(
            String fieldStr, String op, String right, String locale, boolean isI18n) {
        StringBuilder left = new StringBuilder();
        if (fieldStr.contains(Constants.DOT)) {
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            if (key.contains(Constants.DOT)) {
                int secondDotIndex = key.indexOf(Constants.DOT);
                key = key.substring(0, secondDotIndex);
            }
            ModelSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", supportField));
            }
            if (!PgDataTypeEnum.JSONB.getName().equals(supportFieldEnum.getDataType())) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s type not jsonb, can not search by field.key",
                                supportField));
            }
            if (!CalcOperatorEnum.EQ.getSymbol().equals(op)) {
                throw new UnsupportSearchException(
                        String.format(
                                "SupportField: %s not support comparator: %s", supportField, op));
            }
            String sqlField = supportFieldEnum.getSqlField();
            if (!isI18n) {
                left = new StringBuilder(sqlField + " ");
                op = "@> ";
                String defaultRight;
                String newRight;
                if (right.startsWith(Constants.APOSTROPHE)
                        && right.endsWith(Constants.APOSTROPHE)) {
                    right = right.substring(1, right.length() - 1);
                    right = ExpressionUtils.jsonCompareEscape(right);
                    //                    newRight = "'{\"" + key + "\":\"" + right + "\"}'";
                    //                    defaultRight = "'{\"default\":\"" + right + "\"}'";
                    newRight = SpecialStringProcessor.processJsonValueWithDollarChar(key, right);
                    defaultRight =
                            SpecialStringProcessor.processJsonValueWithDollarChar("default", right);
                } else {
                    newRight = "'{\"" + key + "\":" + right + "}'";
                    defaultRight = "'{\"default\":" + right + "}'";
                }
                right = newRight;
                if (supportFieldEnum
                        .getSupportField()
                        .equalsIgnoreCase(
                                ModelSearchSupportFieldEnum.MODEL_DISPLAY_NAME.getSupportField())) {
                    return "(("
                            + left
                            + op
                            + right
                            + ") or (not "
                            + left
                            + op
                            + right
                            + " and "
                            + left
                            + op
                            + defaultRight
                            + "))";
                }
            } else {
                if (locale.isEmpty() || "*".equals(locale)) {
                    left.append("(");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(sqlField)
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' ")
                                .append(op)
                                .append(" ")
                                .append(right);
                        if (i < localeList.size() - 1) {
                            left.append(" or ");
                        }
                    }
                    left.append(" or (");
                    for (int i = 0; i < localeList.size(); i++) {
                        left.append(sqlField)
                                .append(" -> '")
                                .append(key)
                                .append("' ->> '")
                                .append(localeList.get(i))
                                .append("' isnull and ");
                    }
                    left.append(supportFieldEnum.getSqlField())
                            .append(" -> '")
                            .append(key)
                            .append("' ->> '")
                            .append(Constants.DEFAULT_LANGUAGE)
                            .append("' ")
                            .append(op)
                            .append(" ")
                            .append(right)
                            .append("))");
                } else {
                    if (!localeList.contains(locale)
                            && !Constants.DEFAULT_LANGUAGE.equals(locale)) {
                        locale = Constants.DEFAULT_LANGUAGE;
                    }
                    left =
                            new StringBuilder(
                                    "("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " or ("
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + Constants.DEFAULT_LANGUAGE
                                            + "' "
                                            + op
                                            + " "
                                            + right
                                            + " and "
                                            + sqlField
                                            + " -> '"
                                            + key
                                            + "' ->> '"
                                            + locale
                                            + "' isnull))");
                }
                return left.toString();
            }
        } else {
            ModelSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            left = new StringBuilder(supportFieldEnum.getSqlField() + " ");
            op = op + " ";
        }
        return left + op + right;
    }

    @Override
    public String visitComparatorExpr(ConditionParser.ComparatorExprContext ctx) {
        String fieldStr = visit(ctx.left);
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        return getComparatorExpression(fieldStr, op, right, "", false);
    }

    @Override
    public String visitIsExistsExpr(ConditionParser.IsExistsExprContext ctx) {
        StringBuilder res = new StringBuilder();
        String op = visit(ctx.op);
        String right = visit(ctx.right);
        right = right.substring(1, right.length() - 1);
        String[] fieldStrList = right.split(",");
        for (int i = 0; i < fieldStrList.length; i++) {
            String fieldStr = fieldStrList[i];
            if (!fieldStr.contains(Constants.DOT)) {
                throw new UnsupportSearchException(
                        String.format(
                                "The exists and not exist can only be used with field.key! Field: %s",
                                fieldStr));
            }
            int dotIndex = fieldStr.indexOf(Constants.DOT);
            String supportField = fieldStr.substring(0, dotIndex);
            String key = fieldStr.substring(dotIndex + 1);
            ModelSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(supportField);
            if (supportFieldEnum == null) {
                throw new UnsupportSearchException(
                        String.format("Unsupported search by field: %s", fieldStr));
            }
            String sqlField = supportFieldEnum.getSqlField();
            if (op.equals("exists")) {
                res.append("(")
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("')");
            } else {
                res.append("((not ")
                        .append(sqlField)
                        .append(" ?? ")
                        .append("'")
                        .append(key)
                        .append("') or ")
                        .append(sqlField)
                        .append(" is null)");
            }

            if (i < fieldStrList.length - 1) {
                res.append(" and ");
            }
        }
        return res.toString();
    }

    @Override
    public String visitBinaryExpr(ConditionParser.BinaryExprContext ctx) {
        String left = visit(ctx.left) + " ";
        String op = visit(ctx.op) + " ";
        String right = visit(ctx.right);
        return left + op + right;
    }

    @Override
    public String visitInExpr(ConditionParser.InExprContext ctx) {
        String fieldStr = visit(ctx.left);
        ModelSearchSupportFieldEnum supportFieldEnum = supportFieldMap.get(fieldStr);
        if (supportFieldEnum == null) {
            throw new UnsupportSearchException(
                    String.format("Unsupported search by field: %s", fieldStr));
        }
        String left = supportFieldEnum.getSqlField() + " ";
        String op = visit(ctx.op) + " ";
        String right = visit(ctx.right);
        return left + op + right;
    }

    @Override
    public String visitI18n(ConditionParser.I18nContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLike(ConditionParser.LikeContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitIsExists(ConditionParser.IsExistsContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitBinary(ConditionParser.BinaryContext ctx) {
        return ctx.getText().toLowerCase();
    }

    @Override
    public String visitComparator(ConditionParser.ComparatorContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitField(ConditionParser.FieldContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitFields(ConditionParser.FieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValues(ConditionParser.ValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitValue(ConditionParser.ValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitStringValue(ConditionParser.StringValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitBooleanValue(ConditionParser.BooleanValueContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenFields(ConditionParser.ParenFieldsContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitParenValues(ConditionParser.ParenValuesContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitTimestamp(ConditionParser.TimestampContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitLeftParen(ConditionParser.LeftParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitRightParen(ConditionParser.RightParenContext ctx) {
        return ctx.getText();
    }

    @Override
    public String visitIsIn(ConditionParser.IsInContext ctx) {
        return ctx.getText();
    }
}
