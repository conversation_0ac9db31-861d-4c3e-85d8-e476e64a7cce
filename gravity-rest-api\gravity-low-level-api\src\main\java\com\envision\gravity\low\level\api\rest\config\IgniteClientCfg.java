package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.common.service.id.IDService;
import com.envision.gravity.common.util.GravityCommonLionConfigs;
import com.envision.gravity.low.level.api.rest.util.LionUtil;

import org.apache.ignite.Ignition;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** @Author: qi.jiang2 @Date: 2024/01/10 20:15 @Description: */
@Configuration
public class IgniteClientCfg {

    @Bean("igniteClient")
    public IgniteClient igniteInstance() {
        String igniteAddress = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_ADDRESS);
        String igniteUser = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_USERNAME);
        String ignitePassword = LionUtil.getStringValue(GravityCommonLionConfigs.IGNITE_PASSWORD);
        int igniteClientTimeout =
                LionUtil.getIntValue(
                        GTRestLionConfig.IGNITE_CLIENT_TIMEOUT,
                        GTRestLionConfig.IGNITE_CLIENT_TIMEOUT_DEFAULT);
        boolean igniteTcpNoDelay =
                LionUtil.getBooleanValue(
                        GTRestLionConfig.IGNITE_TCP_NO_DELAY,
                        GTRestLionConfig.IGNITE_TCP_NO_DELAY_DEFAULT);

        String[] igniteServers = igniteAddress.split(",");
        ClientConfiguration cfg = new ClientConfiguration();
        cfg.setAddresses(igniteServers);
        cfg.setTimeout(igniteClientTimeout);
        cfg.setTcpNoDelay(igniteTcpNoDelay);
        cfg.setUserName(igniteUser);
        cfg.setUserPassword(ignitePassword);

        return Ignition.startClient(cfg);
    }

    @Bean("generateIdService")
    public IDService idService(@Qualifier("igniteClient") IgniteClient igniteClient) {
        return IDService.getInstance(igniteClient);
    }
}
