package com.envision.gravity.flink.streaming.virtual.attr.sync;

import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.CdcRecordParser;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.QueryVirtualAttrValue;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.RefreshReqAggregator;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.ScheduledSource;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.sink.RefreshVirtualAttrValueSink;

import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
@Slf4j
class FlinkStreamVirtualAttrSyncLocalTest {
    @Test
    void localTest() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironment();

        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", "5");
        properties.setProperty("max.queue.size", "10");
        //        properties.setProperty("publication.name", LionConfig.getPublicationName());
        //        properties.setProperty("publication.autocreate.mode", "filtered");

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        Runtime.getRuntime()
                .addShutdownHook(
                        new Thread(
                                () -> {
                                    log.info("Received shutdown signal. Clean up resources ...");
                                    try {
                                        PGDataSourceConfig.closeDataSource();
                                    } catch (Exception e) {
                                        log.error("Close datasource error", e);
                                    }
                                }));

        SingleOutputStreamOperator<RefreshReq> pgCdcSource =
                env.addSource(sourceFunction).process(new CdcRecordParser());
        DataStreamSource<RefreshReq> scheduledSource = env.addSource(new ScheduledSource());

        DataStream<RefreshReq> mergedStream = pgCdcSource.union(scheduledSource);

        //        mergedStream.map(
        //                sourceRecord -> {
        //                    System.out.println(">>> " + sourceRecord.getSchemaName());
        //                    return sourceRecord;
        //                });

        mergedStream
                .keyBy(RefreshReq::getSchemaName)
                .window(TumblingProcessingTimeWindows.of(Time.seconds(10)))
                .process(new RefreshReqAggregator())
                .process(new QueryVirtualAttrValue())
                .addSink(new RefreshVirtualAttrValueSink());

        env.execute("Flink Streaming Virtual Attr Sync");
    }
}
