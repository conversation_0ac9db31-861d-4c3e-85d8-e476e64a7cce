<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/util
       http://www.springframework.org/schema/util/spring-util.xsd">

    <!-- Data source bean -->
    <bean class="org.postgresql.ds.PGSimpleDataSource" id="postgresDataSource">
        <property name="URL" value="*****************************************************************"/>
        <property name="user" value="postgres"/>
        <property name="password" value="postgres"/>
    </bean>

    <bean id="igniteSchema" class="java.lang.String">
        <constructor-arg value="o16227961710541858"/>
    </bean>

    <!-- Ignite Configuration -->
    <bean class="org.apache.ignite.configuration.IgniteConfiguration">
        <property name="cacheConfiguration">
            <list>
                <!-- Configuration for TBL_BO_MODEL -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="writeBehindEnabled" value="true" />
                    <property name="writeSynchronizationMode" value="FULL_ASYNC" />
                    <!-- Default flush size for write-behind cache store. 10K -->
                    <property name="writeBehindFlushSize" value="1024" />
                    <!-- Default flush frequency for write-behind cache store in milliseconds. 5000 -->
                    <property name="writeBehindFlushFrequency" value="5000" />
                    <!-- Default count of flush threads for write-behind cache store. 1 -->
                    <property name="writeBehindFlushThreadCount" value="1" />
                    <!-- Default batch size for write-behind cache store. 512 -->
                    <property name="writeBehindBatchSize" value="512" />
                    <!-- Default write coalescing for write-behind cache store. true-->
                    <property name="writeBehindCoalescing" value="true"/>


                    <property name="name" value="#{igniteSchema}_TBL_BO_MODEL"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_MODEL"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_MODEL"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_path"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_path"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_VALUE"/>
                                <property name="tableName" value="TBL_BO_MODEL"/>
                                <property name="keyFields">
                                    <list>
                                        <value>model_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>model_display_name</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="group_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="model_id" value="java.lang.String"/>
                                        <entry key="model_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="model_path" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_COMPONENT -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_COMPONENT"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_COMPONENT"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_COMPONENT"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="anonymous"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="anonymous"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="template"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="template"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_VALUE"/>
                                <property name="tableName" value="TBL_COMPONENT"/>
                                <property name="keyFields">
                                    <list>
                                        <value>comp_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>comp_name</value>
                                        <value>comp_display_name</value>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="comp_name" value="java.lang.String"/>
                                        <entry key="comp_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="anonymous" value="java.lang.Boolean"/>
                                        <entry key="template" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_MODEL_RELATION -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_MODEL_RELATION"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_MODEL_RELATION"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_MODEL_RELATION_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_MODEL_RELATION_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_MODEL_RELATION"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="relation_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="relation_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="level"/>
                                                    <constructor-arg value="java.lang.Integer"/>
                                                    <constructor-arg value="level"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="relation_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="relation_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="level"/>
                                                    <constructor-arg value="java.lang.Integer"/>
                                                    <constructor-arg value="level"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_MODEL_RELATION_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_MODEL_RELATION_VALUE"/>
                                <property name="tableName" value="TBL_MODEL_RELATION"/>
                                <property name="keyFields">
                                    <list>
                                        <value>from_model_id</value>
                                        <value>to_model_id</value>
                                        <value>relation_type</value>
                                        <value>level</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>from_model_id</value>
                                        <value>to_model_id</value>
                                        <value>relation_type</value>
                                        <value>level</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>from_model_id</value>
                                                    <value>relation_type</value>
                                                    <value>level</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>to_model_id</value>
                                                    <value>relation_type</value>
                                                    <value>level</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="from_model_id" value="java.lang.String"/>
                                        <entry key="to_model_id" value="java.lang.String"/>
                                        <entry key="relation_type" value="java.lang.String"/>
                                        <entry key="level" value="java.lang.Integer"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_BO_MODEL_COMPONENT -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_MODEL_COMP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_MODEL_COMP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_MODEL_COMP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="model_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="model_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_MODEL_COMP_VALUE"/>
                                <property name="tableName" value="TBL_BO_MODEL_COMP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>comp_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>model_id</value>
                                        <value>comp_id</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="model_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="model_id" value="java.lang.String"/>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_COMPONENT_PREF -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_COMPONENT_PREF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_COMPONENT_PREF"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_PREF_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_PREF_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_COMPONENT_PREF"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_COMPONENT_PREF_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_COMPONENT_PREF_VALUE"/>
                                <property name="tableName" value="TBL_COMPONENT_PREF"/>
                                <property name="keyFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>pref_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>comp_id</value>
                                        <value>pref_id</value>
                                        <value>field_id</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="field_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="field_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_PREF -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_PREF"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_PREF"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_PREF_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_PREF_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_PREF"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="writable"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="writable"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="required"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="required"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="default_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="default_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="has_quality"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="has_quality"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_signal_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_signal_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_definition"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_definition"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="request"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="request"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="response"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="response"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="unit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="unit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="lower_limit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="lower_limit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="upper_limit"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="upper_limit"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_PREF_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_PREF_VALUE"/>
                                <property name="tableName" value="TBL_PREF"/>
                                <property name="keyFields">
                                    <list>
                                        <value>pref_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>pref_name</value>
                                        <value>pref_display_name</value>
                                        <value>pref_type</value>
                                    </list>
                                </property>

                                <property name="defaultFieldValues">
                                    <map>
                                        <entry key="writable" value="false"/>
                                        <entry key="required" value="false"/>
                                        <entry key="has_quality" value="false"/>
                                    </map>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_type"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="pref_name" value="java.lang.String"/>
                                        <entry key="pref_display_name" value="java.lang.String"/>
                                        <entry key="pref_type" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="writable" value="java.lang.Boolean"/>
                                        <entry key="required" value="java.lang.Boolean"/>
                                        <entry key="default_value" value="java.lang.String"/>
                                        <entry key="has_quality" value="java.lang.Boolean"/>
                                        <entry key="pref_data_type" value="java.lang.String"/>
                                        <entry key="pref_signal_type" value="java.lang.String"/>
                                        <entry key="data_definition" value="java.lang.String"/>
                                        <entry key="request" value="java.lang.String"/>
                                        <entry key="response" value="java.lang.String"/>
                                        <entry key="unit" value="java.lang.String"/>
                                        <entry key="lower_limit" value="java.lang.String"/>
                                        <entry key="upper_limit" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_PREF_EXT -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_PREF_EXT"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_PREF_EXT"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_PREF_EXT_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_PREF_EXT_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_PREF_EXT"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="pref_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="pref_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comp_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comp_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="category"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="category"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="calc_prop_exp"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="calc_prop_exp"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_PREF_EXT_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_PREF_EXT_VALUE"/>
                                <property name="tableName" value="TBL_PREF_EXT"/>
                                <property name="keyFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>comp_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>pref_id</value>
                                        <value>comp_id</value>
                                        <value>calc_prop_exp</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="pref_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="comp_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="pref_id" value="java.lang.String"/>
                                        <entry key="comp_id" value="java.lang.String"/>
                                        <entry key="category" value="java.lang.String"/>
                                        <entry key="calc_prop_exp" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_BO_GROUP -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_GROUP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_GROUP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_GROUP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="description"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="description"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_VALUE"/>
                                <property name="tableName" value="TBL_BO_GROUP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>group_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>group_name</value>
                                        <value>group_display_name</value>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="group_name" value="java.lang.String"/>
                                        <entry key="group_display_name" value="java.lang.String"/>
                                        <entry key="description" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_BO_GROUP_RELATION -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO_GROUP_RELATION"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO_GROUP_RELATION"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_KEY"/>
                                        <property name="valueType"
                                                  value="#{igniteSchema}_TBL_BO_GROUP_RELATION_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO_GROUP_RELATION"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="group_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="group_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_GROUP_RELATION_VALUE"/>
                                <property name="tableName" value="TBL_BO_GROUP_RELATION"/>
                                <property name="keyFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>asset_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>group_id</value>
                                        <value>asset_id</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="group_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="asset_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="group_id" value="java.lang.String"/>
                                        <entry key="asset_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_BO -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_BO"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_BO"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_BO_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_BO_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_BO"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="asset_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="asset_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="attr_modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="attr_modified_time"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_BO_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_BO_VALUE"/>
                                <property name="tableName" value="TBL_BO"/>
                                <property name="keyFields">
                                    <list>
                                        <value>asset_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>asset_id</value>
                                        <value>asset_display_name</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="system_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="asset_id" value="java.lang.String"/>
                                        <entry key="asset_display_name" value="java.lang.String"/>
                                        <entry key="system_id" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                        <entry key="attr_modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_TAG -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_TAG"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_TAG"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_TAG_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_TAG_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_TAG"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_group"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_group"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="marker"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="marker"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_key"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_key"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="tag_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="tag_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_TAG_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_TAG_VALUE"/>
                                <property name="tableName" value="TBL_TAG"/>
                                <property name="keyFields">
                                    <list>
                                        <value>data_id</value>
                                        <value>tag_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>data_id</value>
                                        <value>tag_id</value>
                                        <value>tag_type</value>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="data_id" value="java.lang.String"/>
                                        <entry key="data_type" value="java.lang.String"/>
                                        <entry key="tag_type" value="java.lang.String"/>
                                        <entry key="tag_id" value="java.lang.String"/>
                                        <entry key="tag_group" value="java.lang.String"/>
                                        <entry key="marker" value="java.lang.String"/>
                                        <entry key="tag_key" value="java.lang.String"/>
                                        <entry key="tag_value" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_OBJ_ATTR -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_OBJ_ATTR"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_OBJ_ATTR"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_OBJ_ATTR_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_OBJ_ATTR_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_OBJ_ATTR"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="system_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="system_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="field_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="field_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BOOLEAN"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_bool"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="value_bool"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_string"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="value_string"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BIGINT"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_long"/>
                                                    <constructor-arg value="java.lang.Long"/>
                                                    <constructor-arg value="value_long"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.DOUBLE"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_double"/>
                                                    <constructor-arg value="java.lang.Double"/>
                                                    <constructor-arg value="value_double"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="value_json"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="value_json"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_OBJ_ATTR_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_OBJ_ATTR_VALUE"/>
                                <property name="tableName" value="TBL_OBJ_ATTR"/>
                                <property name="keyFields">
                                    <list>
                                        <value>system_id</value>
                                        <value>field_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>system_id</value>
                                        <value>field_id</value>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="system_id" value="java.lang.String"/>
                                        <entry key="field_id" value="java.lang.String"/>
                                        <entry key="value_bool" value="java.lang.Boolean"/>
                                        <entry key="value_string" value="java.lang.String"/>
                                        <entry key="value_long" value="java.lang.Long"/>
                                        <entry key="value_double" value="java.lang.Double"/>
                                        <entry key="value_json" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_SUB_GRAPH -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_SUB_GRAPH"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_SUB_GRAPH"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_SUB_GRAPH_KEY"/>
                                        <property name="valueType"
                                                  value="#{igniteSchema}_TBL_SUB_GRAPH_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_SUB_GRAPH"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="start_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="start_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_SUB_GRAPH_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_SUB_GRAPH_VALUE"/>
                                <property name="tableName" value="TBL_SUB_GRAPH"/>
                                <property name="keyFields">
                                    <list>
                                        <value>sub_graph_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>sub_graph_id</value>
                                        <value>start_vid</value>
                                        <value>sub_graph_display_name</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sub_graph_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="start_vid"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="sub_graph_id" value="java.lang.String"/>
                                        <entry key="start_vid" value="java.lang.String"/>
                                        <entry key="sub_graph_display_name" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_EDGE -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="from_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="from_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="to_vid"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="to_vid"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="sub_graph_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="sub_graph_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_VALUE"/>
                                <property name="tableName" value="TBL_EDGE"/>
                                <property name="keyFields">
                                    <list>
                                        <value>from_vid</value>
                                        <value>to_vid</value>
                                        <value>edge_type_id</value>
                                        <value>sub_graph_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>from_vid</value>
                                        <value>to_vid</value>
                                        <value>edge_type_id</value>
                                        <value>sub_graph_id</value>
                                        <value>prop_value</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="from_vid"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="to_vid"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="sub_graph_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>from_vid</value>
                                                    <value>edge_type_id</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>to_vid</value>
                                                    <value>edge_type_id</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="from_vid" value="java.lang.String"/>
                                        <entry key="to_vid" value="java.lang.String"/>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="sub_graph_id" value="java.lang.String"/>
                                        <entry key="prop_value" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_EDGE_TYPE -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE_TYPE"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE_TYPE"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE_TYPE"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_display_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_display_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_VALUE"/>
                                <property name="tableName" value="TBL_EDGE_TYPE"/>
                                <property name="keyFields">
                                    <list>
                                        <value>edge_type_id</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>edge_type_display_name</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="edge_type_display_name" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- Configuration for TBL_EDGE_TYPE_PROP -->
                <bean class="org.apache.ignite.configuration.CacheConfiguration">
                    <property name="name" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP"/>
                    <property name="cacheMode" value="REPLICATED"/>
                    <property name="atomicityMode" value="TRANSACTIONAL"/>
                    <property name="dataRegionName" value="InMemory_Region"/>
                    <property name="sqlSchema" value="#{igniteSchema}"/>
                    <property name="cacheStoreFactory">
                        <bean class="org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory">
                            <property name="dataSourceBean" value="postgresDataSource"/>
                            <property name="dialect">
                                <bean class="org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect"/>
                            </property>
                            <property name="types">
                                <list>
                                    <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                        <property name="cacheName" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP"/>
                                        <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_KEY"/>
                                        <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_VALUE"/>
                                        <property name="databaseSchema" value="#{igniteSchema}"/>
                                        <property name="databaseTable" value="TBL_EDGE_TYPE_PROP"/>
                                        <property name="keyFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_name"/>
                                                </bean>
                                            </list>
                                        </property>
                                        <property name="valueFields">
                                            <list>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="edge_type_id"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="edge_type_id"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_name"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_name"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="prop_data_type"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="prop_data_type"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.BOOLEAN"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="nullable"/>
                                                    <constructor-arg value="java.lang.Boolean"/>
                                                    <constructor-arg value="nullable"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="default_value"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="default_value"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="comment"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="comment"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="created_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.TIMESTAMP"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_time"/>
                                                    <constructor-arg value="java.sql.Timestamp"/>
                                                    <constructor-arg value="modified_time"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="created_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="created_user"/>
                                                </bean>
                                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                    <constructor-arg>
                                                        <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                    </constructor-arg>
                                                    <constructor-arg value="modified_user"/>
                                                    <constructor-arg value="java.lang.String"/>
                                                    <constructor-arg value="modified_user"/>
                                                </bean>
                                            </list>
                                        </property>
                                    </bean>
                                </list>
                            </property>
                        </bean>
                    </property>
                    <property name="readThrough" value="false"/>
                    <property name="writeThrough" value="true"/>
                    <property name="queryEntities">
                        <list>
                            <bean class="org.apache.ignite.cache.QueryEntity">
                                <property name="keyType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_KEY"/>
                                <property name="valueType" value="#{igniteSchema}_TBL_EDGE_TYPE_PROP_VALUE"/>
                                <property name="tableName" value="TBL_EDGE_TYPE_PROP"/>
                                <property name="keyFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>prop_name</value>
                                    </list>
                                </property>

                                <property name="notNullFields">
                                    <list>
                                        <value>edge_type_id</value>
                                        <value>prop_name</value>
                                        <value>prop_data_type</value>
                                    </list>
                                </property>

                                <!-- Defining indexed fields.-->
                                <property name="indexes">
                                    <list>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg value="edge_type_id"/>
                                        </bean>
                                        <bean class="org.apache.ignite.cache.QueryIndex">
                                            <constructor-arg>
                                                <list>
                                                    <value>edge_type_id</value>
                                                    <value>prop_name</value>
                                                </list>
                                            </constructor-arg>
                                            <constructor-arg value="SORTED"/>
                                        </bean>
                                    </list>
                                </property>

                                <property name="fields">
                                    <map>
                                        <entry key="edge_type_id" value="java.lang.String"/>
                                        <entry key="prop_name" value="java.lang.String"/>
                                        <entry key="prop_data_type" value="java.lang.String"/>
                                        <entry key="nullable" value="java.lang.Boolean"/>
                                        <entry key="default_value" value="java.lang.String"/>
                                        <entry key="comment" value="java.lang.String"/>
                                        <entry key="created_user" value="java.lang.String"/>
                                        <entry key="modified_user" value="java.lang.String"/>
                                        <entry key="created_time" value="java.sql.Timestamp"/>
                                        <entry key="modified_time" value="java.sql.Timestamp"/>
                                    </map>
                                </property>
                            </bean>
                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>
</beans>
