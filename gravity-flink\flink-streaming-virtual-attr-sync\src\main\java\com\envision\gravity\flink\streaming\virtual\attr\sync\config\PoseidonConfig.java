package com.envision.gravity.flink.streaming.virtual.attr.sync.config;


import com.envision.apim.poseidon.config.PConfig;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description
 */
public class PoseidonConfig {
    public static String EMPTY_UUID = "00000000-0000-0000-0000-000000000000";
    public static final PConfig POSEIDON_CONFIG =
            PConfig.init().appKey(EMPTY_UUID).appSecret(EMPTY_UUID).verifySSL(false).debug();
}
