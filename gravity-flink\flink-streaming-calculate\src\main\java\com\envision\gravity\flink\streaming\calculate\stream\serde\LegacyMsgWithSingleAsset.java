package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;


import lombok.Getter;
import lombok.Setter;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Getter
@Setter
@TypeInfo(PojoFactory.LegacyMsgWithSingleAssetType.class)
public class LegacyMsgWithSingleAsset extends LegacyMsg {
    private LegacyPayload payload;

    public LegacyPayload getPayload() {
        return payload;
    }

    // --------------------------------------------------------------------------------
    public void setPayload(LegacyPayload payload) {
        this.payload = payload;
    }
}
