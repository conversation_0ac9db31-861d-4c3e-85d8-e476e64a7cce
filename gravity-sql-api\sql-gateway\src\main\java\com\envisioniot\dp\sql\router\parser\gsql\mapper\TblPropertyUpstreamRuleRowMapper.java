package com.envisioniot.dp.sql.router.parser.gsql.mapper;

import com.envisioniot.dp.sql.router.utils.ResultSetUtils;

import com.envision.gravity.common.po.TblPropertyUpstreamRule;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblPropertyUpstreamRuleRowMapper
        implements ResultSetUtils.RowMapper<TblPropertyUpstreamRule> {

    @Override
    public TblPropertyUpstreamRule mapRow(ResultSet rs) throws SQLException {
        return TblPropertyUpstreamRule.builder()
                .prefRuleId(rs.getString("pref_rule_id"))
                .targetCategory(rs.getString("target_category"))
                .targetCompId(rs.getString("target_comp_id"))
                .targetPrefId(rs.getString("target_pref_id"))
                .srcCategory(rs.getString("src_category"))
                .expression(rs.getString("expression"))
                .calcType(rs.getInt("calc_type"))
                .createdTime(rs.getTimestamp("created_time"))
                .createdUser(rs.getString("created_user"))
                .modifiedTime(rs.getTimestamp("modified_time"))
                .modifiedUser(rs.getString("modified_user"))
                .build();
    }
}
