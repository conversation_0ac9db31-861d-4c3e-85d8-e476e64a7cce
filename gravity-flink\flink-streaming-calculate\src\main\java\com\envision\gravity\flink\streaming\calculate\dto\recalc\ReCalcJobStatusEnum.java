package com.envision.gravity.flink.streaming.calculate.dto.recalc;

import java.util.Optional;

public enum ReCalcJobStatusEnum {
    INIT(0),
    RUNNING(1),
    FAILED(2),
    CANCELLED(3),
    FINISHED(4),
    SUBMIT_FAILED(5);

    private final int code;

    ReCalcJobStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static Optional<ReCalcJobStatusEnum> getByCode(int code) {
        for (ReCalcJobStatusEnum status : ReCalcJobStatusEnum.values()) {
            if (status.getCode() == code) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }
}
