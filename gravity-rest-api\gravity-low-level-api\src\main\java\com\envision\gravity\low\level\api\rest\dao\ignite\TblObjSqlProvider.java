package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.enums.IgniteDataTypeEnum;
import com.envision.gravity.common.vo.obj.Pagination;
import com.envision.gravity.low.level.api.rest.enums.Constants;

import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/** @Author: qi.jiang2 @Date: 2024/05/20 16:00 @Description: */
public class TblObjSqlProvider {

    public String countPropertyValueNum(
            List<String> rawFieldIds,
            List<Object> values,
            List<String> systemIds,
            String dataType,
            String orgId) {
        SQL sql = new SQL();
        sql.SELECT("to.system_id");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME + " as to");
        List<String> newValues = new ArrayList<>();
        for (Object value : values) {
            if (IgniteDataTypeEnum.STRING.getIgniteDataType().equals(dataType)) {
                value = "'" + value + "'";
            } else {
                value = String.valueOf(value);
            }
            newValues.add((String) value);
        }
        String valuesStr = String.format("(%s)", String.join(", ", newValues));
        List<String> replace = new ArrayList<>();
        if (rawFieldIds != null && !rawFieldIds.isEmpty()) {
            for (String rawFieldId : rawFieldIds) {
                replace.add("\"" + rawFieldId + "\" in " + valuesStr);
            }
        }
        List<String> systemIdsReplace = new ArrayList<>();
        for (String systemId : systemIds) {
            systemIdsReplace.add("'" + systemId + "'");
        }
        sql.INNER_JOIN(orgId + ".tbl_bo_part as tb on tb.system_id = to.system_id");
        sql.WHERE(
                String.format(
                        "(%s) and tb.system_id not in (%s)",
                        String.join(" or ", replace), String.join(", ", systemIdsReplace)));
        sql.LIMIT(1);
        return sql.toString();
    }

    public String queryAllObj(Pagination pagination, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("system_id");
        sql.SELECT("system_display_name");
        sql.SELECT("category_id");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        if (pagination != null) {
            sql.LIMIT(pagination.getPageSize());
            sql.OFFSET((long) (pagination.getPageNo() - 1) * pagination.getPageSize());
        }
        return sql.toString();
    }

    public String countObjTotalSize(String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        return sql.toString();
    }

    public String countObjByCategoryId(String objCategory, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        sql.WHERE("category_id = '" + objCategory + "'");
        return sql.toString();
    }

    public String queryObjByCategoryId(String objCategory, Pagination pagination, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("system_id");
        sql.SELECT("system_display_name");
        sql.SELECT("category_id");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        sql.WHERE("category_id = '" + objCategory + "'");
        if (pagination != null) {
            sql.LIMIT(pagination.getPageSize());
            sql.OFFSET((long) (pagination.getPageNo() - 1) * pagination.getPageSize());
        }
        return sql.toString();
    }

    public String queryObjBySystemIds(Set<String> systemIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("system_id");
        sql.SELECT("system_display_name");
        sql.SELECT("category_id");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_OBJ_TABLE_NAME);
        List<String> replace = new ArrayList<>();
        for (String systemId : systemIds) {
            replace.add("'" + systemId + "'");
        }
        sql.WHERE("system_id in (" + String.join(", ", replace) + ")");
        return sql.toString();
    }
}
