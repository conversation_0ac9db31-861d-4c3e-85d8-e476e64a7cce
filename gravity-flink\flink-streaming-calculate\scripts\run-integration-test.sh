#!/bin/bash

# ReCalc Job Generation Flow Integration Test Runner
# This script runs the integration test for the complete ReCalc flow

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}=== ReCalc Job Generation Flow Integration Test ===${NC}"
echo "Project directory: $PROJECT_DIR"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if Maven is available
    if ! command -v mvn &> /dev/null; then
        print_error "Maven is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Java is available
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    # Check Java version (should be 8 or higher)
    JAVA_VERSION=$(java -version 2>&1 | grep -oP 'version "?(1\.)?\K\d+' | head -1)
    if [ "$JAVA_VERSION" -lt 8 ]; then
        print_error "Java 8 or higher is required. Current version: $JAVA_VERSION"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to check database connectivity
check_database() {
    print_info "Checking database connectivity..."
    
    # Default database configuration
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-5432}
    DB_NAME=${DB_NAME:-gravity}
    DB_USER=${DB_USER:-postgres}
    DB_PASSWORD=${DB_PASSWORD:-password}
    
    # Check if PostgreSQL is accessible
    if command -v psql &> /dev/null; then
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
            print_success "Database connectivity check passed"
        else
            print_warning "Cannot connect to PostgreSQL database"
            print_warning "Please ensure PostgreSQL is running and accessible"
            print_warning "Database: $DB_HOST:$DB_PORT/$DB_NAME (user: $DB_USER)"
        fi
    else
        print_warning "psql not found, skipping database connectivity check"
    fi
}

# Function to check Flink cluster
check_flink() {
    print_info "Checking Flink cluster..."
    
    FLINK_HOST=${FLINK_HOST:-localhost}
    FLINK_PORT=${FLINK_PORT:-8081}
    
    # Check if Flink REST API is accessible
    if command -v curl &> /dev/null; then
        if curl -s "http://$FLINK_HOST:$FLINK_PORT/overview" &> /dev/null; then
            print_success "Flink cluster connectivity check passed"
        else
            print_warning "Cannot connect to Flink cluster"
            print_warning "Please ensure Flink is running and accessible"
            print_warning "Flink: http://$FLINK_HOST:$FLINK_PORT"
        fi
    else
        print_warning "curl not found, skipping Flink connectivity check"
    fi
}

# Function to compile the project
compile_project() {
    print_info "Compiling project..."
    
    cd "$PROJECT_DIR"
    
    # Apply code formatting
    print_info "Applying code formatting..."
    mvn spotless:apply -q
    
    # Compile the project
    print_info "Compiling source code..."
    mvn compile -q
    
    # Compile test code
    print_info "Compiling test code..."
    mvn test-compile -q
    
    print_success "Project compilation completed"
}

# Function to run integration test
run_integration_test() {
    print_info "Running ReCalc Job Generation Flow Integration Test..."
    
    cd "$PROJECT_DIR"
    
    # Set test configuration
    export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"
    
    # Run the specific integration test
    mvn test \
        -Dtest=ReCalcJobGenFlowIntegrationTest \
        -Darch.path=./deploy/apps \
        -Djava.security.auth.login.config=./deploy/zk_client_jaas.conf \
        -DLOG_LEVEL=INFO \
        -Dlogback.configurationFile=logback-test.xml \
        -Dmaven.test.failure.ignore=false \
        -q
    
    TEST_RESULT=$?
    
    if [ $TEST_RESULT -eq 0 ]; then
        print_success "Integration test completed successfully"
    else
        print_error "Integration test failed with exit code: $TEST_RESULT"
        return $TEST_RESULT
    fi
}

# Function to generate test report
generate_report() {
    print_info "Generating test report..."
    
    cd "$PROJECT_DIR"
    
    # Check if test results exist
    if [ -d "target/surefire-reports" ]; then
        REPORT_DIR="target/surefire-reports"
        
        print_info "Test results available in: $REPORT_DIR"
        
        # Count test results
        TOTAL_TESTS=$(find "$REPORT_DIR" -name "TEST-*.xml" -exec grep -l "testcase" {} \; | wc -l)
        FAILED_TESTS=$(find "$REPORT_DIR" -name "TEST-*.xml" -exec grep -l "failure\|error" {} \; | wc -l)
        
        print_info "Total tests: $TOTAL_TESTS"
        if [ "$FAILED_TESTS" -eq 0 ]; then
            print_success "Failed tests: $FAILED_TESTS"
        else
            print_error "Failed tests: $FAILED_TESTS"
        fi
        
        # Show recent log files
        if [ -d "target/test-logs" ]; then
            print_info "Recent log files:"
            find "target/test-logs" -name "*.log" -mtime -1 -exec ls -la {} \;
        fi
    else
        print_warning "No test results found"
    fi
}

# Function to cleanup
cleanup() {
    print_info "Cleaning up..."
    
    # Kill any remaining Java processes related to the test
    pkill -f "ReCalcJobGenFlow" 2>/dev/null || true
    pkill -f "ReCalcBatchJob" 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Main execution
main() {
    print_info "Starting ReCalc Job Generation Flow Integration Test"
    print_info "Timestamp: $(date)"
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Run checks and tests
    check_prerequisites
    check_database
    check_flink
    compile_project
    run_integration_test
    generate_report
    
    print_success "Integration test execution completed successfully"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-checks)
            SKIP_CHECKS=true
            shift
            ;;
        --skip-compile)
            SKIP_COMPILE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --skip-checks    Skip prerequisite checks"
            echo "  --skip-compile   Skip project compilation"
            echo "  --help, -h       Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
