package com.envision.gravity.low.level.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/04/17 16:25 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchSubGraphCondition {

    private String baseCondition;

    private List<String> fromVidConditions;

    private List<String> toVidConditions;

    private List<String> vidConditions;

    private VidConditionPart vidConditionPart;

    private String baseConditionWithVid;

    private List<String> vids;

    private int maxStep;
}
