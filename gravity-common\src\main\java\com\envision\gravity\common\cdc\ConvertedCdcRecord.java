package com.envision.gravity.common.cdc;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConvertedCdcRecord {
    private String schema;
    private String db;
    private String table;
    private String primaryKey;

    // -----------------------------------------------
    private CdcTableEntity before;
    private CdcTableEntity after;
    private String op;
    private Long tsMs;
}
