package com.envision.gravity.flink.streaming.calculate.batch.source;

import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.batch.splitter.CalcJobTaskSplitEnumerator;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;

import java.util.*;


import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 批处理版本的CalcJobTaskSource
 *
 * <p>特点： 1. 单Job处理，Job级别状态隔离 2. 固定并行度=1 3. 有限生命周期，Job完成后退出 4. 使用内存状态通知机制
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class BatchCalcJobTaskSource extends RichSourceFunction<CalcJobTask>
        implements CheckpointedFunction {

    private static final Logger logger = LoggerFactory.getLogger(BatchCalcJobTaskSource.class);

    private final TblCalcJobInfo jobInfo;
    private final String jobId;
    private volatile boolean isRunning = true;

    private final SqlSessionFactory sqlSessionFactory;

    // ✅ 批处理：单Job任务状态管理
    private transient Queue<CalcJobTask> pendingTasks;
    private transient Set<String> emittedTaskIds; // 已发送的任务ID
    private transient int totalTaskCount;
    private transient int emittedTaskCount;
    private transient boolean allTasksEmitted;

    // ✅ 内存状态通知机制
    private transient TaskCompletionNotifier taskCompletionNotifier;

    // ✅ Checkpoint 状态：保存所有未确认完成的任务
    private transient ListState<CalcJobTask> checkpointedPendingTasks;
    private transient ListState<String> checkpointedEmittedTaskIds;
    private transient ListState<Integer> checkpointedTotalCount;
    private transient ListState<Integer> checkpointedEmittedCount;
    private transient ListState<Boolean> checkpointedAllTasksEmitted;

    public BatchCalcJobTaskSource(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
        this.jobId = jobInfo.getJobId();
        this.sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.pendingTasks = new LinkedList<>();
        this.emittedTaskIds = new HashSet<>();
        this.totalTaskCount = 0;
        this.emittedTaskCount = 0;
        this.allTasksEmitted = false;

        // ✅ 初始化Job级别的内存状态通知机制
        this.taskCompletionNotifier = TaskCompletionNotifier.getInstance(jobId);

        logger.info(
                "BatchCalcJobTaskSource opened for job: {} with memory-based completion tracking",
                jobId);
    }

    @Override
    public void run(SourceContext<CalcJobTask> ctx) throws Exception {
        final Object lock = ctx.getCheckpointLock();

        // 初始化任务
        synchronized (lock) {
            if (totalTaskCount == 0) {
                initializeTasks();
            }
        }

        logger.info(
                "Starting task emission for job: {}, total tasks: {}, pending: {}",
                jobId,
                totalTaskCount,
                pendingTasks.size());

        while (isRunning) {
            boolean hasWork = false;

            synchronized (lock) {
                // ✅ 1. 检查Job状态
                if (!checkJobStatusNotCancelled()) {
                    logger.warn("Job {} is CANCELLED, terminating BatchCalcJobTaskSource", jobId);
                    break;
                }

                // ✅ 2. 检查并清理已完成的任务
                cleanupCompletedTasks();

                // ✅ 3. 发送待处理的任务
                hasWork = emitPendingTasks(ctx);

                // ✅ 4. 检查是否所有任务都完成
                if (allTasksCompleted()) {
                    logger.info("All tasks completed for job: {}, marking job as FINISHED", jobId);
                    markJobAsFinished();
                    break;
                }
            }

            if (!hasWork) {
                // 没有新任务发送时，短暂休眠
                Thread.sleep(CalcLionConfig.getTaskCompletionCheckIntervalMs());
            }
        }

        logger.info("BatchCalcJobTaskSource finished for job: {}", jobId);
    }

    private void initializeTasks() throws Exception {
        logger.info("Initializing tasks for job: {}", jobId);

        // 生成所有任务
        CalcJobTaskSplitEnumerator enumerator = new CalcJobTaskSplitEnumerator(jobInfo);
        List<CalcJobTask> allTasks = enumerator.generateAllTasks();

        // 添加到待处理队列
        pendingTasks.addAll(allTasks);
        totalTaskCount = allTasks.size();

        logger.info("Initialized {} tasks for job: {}", totalTaskCount, jobId);
    }

    /** ✅ Job状态检查，防止处理已取消的Job */
    private boolean checkJobStatusNotCancelled() throws Exception {
        try {
            TblCalcJobInfo currentJobInfo;
            try (SqlSession session = sqlSessionFactory.openSession(true)) {
                TblCalcJobInfoMapper jobInfoMapper = session.getMapper(TblCalcJobInfoMapper.class);
                currentJobInfo = jobInfoMapper.findByJobId(jobId);
            }

            if (currentJobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return false;
            }

            int jobStatus = currentJobInfo.getStatus();

            // ✅ 使用枚举进行状态检查
            Optional<ReCalcJobStatusEnum> statusEnum = ReCalcJobStatusEnum.getByCode(jobStatus);

            if (statusEnum.isPresent()) {
                ReCalcJobStatusEnum status = statusEnum.get();
                logger.debug(
                        "Job {} current status: {} ({})", jobId, status.name(), status.getCode());

                // ✅ 检查是否为CANCELLED状态
                if (status == ReCalcJobStatusEnum.CANCELLED) {
                    logger.warn("Job {} status is CANCELLED, should terminate", jobId);
                    return false;
                }

                return true;
            } else {
                logger.error("Unknown job status code: {} for job: {}", jobStatus, jobId);
                return false;
            }

        } catch (Exception e) {
            logger.error("Failed to check job status for job {}: {}", jobId, e.getMessage(), e);
            throw e;
        }
    }

    private void cleanupCompletedTasks() {
        // ✅ 从Job级别的内存状态获取已完成的任务
        Set<String> completedTaskIds = taskCompletionNotifier.getCompletedTasks();

        if (!completedTaskIds.isEmpty()) {
            // 从已发送任务集合中移除已完成的任务
            int removedCount = 0;
            Iterator<String> iterator = emittedTaskIds.iterator();
            while (iterator.hasNext()) {
                String taskId = iterator.next();
                if (completedTaskIds.contains(taskId)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.info(
                        "Cleaned up {} completed tasks for job {}, remaining emitted: {}",
                        removedCount,
                        jobId,
                        emittedTaskIds.size());
            }
        }
    }

    private boolean emitPendingTasks(SourceContext<CalcJobTask> ctx) {
        boolean emittedAny = false;

        // ✅ 发送所有待处理的任务（支持下游并行处理）
        while (!pendingTasks.isEmpty()) {
            CalcJobTask task = pendingTasks.poll();

            // 记录已发送的任务
            emittedTaskIds.add(task.getTaskId());
            emittedTaskCount++;

            logger.info(
                    "Emitting task: {} for job: {} (emitted: {}/{}, pending: {})",
                    task.getTaskId(),
                    jobId,
                    emittedTaskCount,
                    totalTaskCount,
                    pendingTasks.size());

            ctx.collect(task);
            emittedAny = true;
        }

        // 检查是否所有任务都已发送
        if (pendingTasks.isEmpty() && !allTasksEmitted) {
            allTasksEmitted = true;
            logger.info("All tasks emitted for job: {}, waiting for completion", jobId);
        }

        return emittedAny;
    }

    private boolean allTasksCompleted() {
        if (!allTasksEmitted) {
            return false; // 还有任务未发送
        }

        // ✅ 检查是否所有已发送的任务都已完成
        boolean completed = emittedTaskIds.isEmpty();

        if (completed) {
            logger.info(
                    "All tasks completed for job: {}, total processed: {}", jobId, totalTaskCount);
        } else {
            logger.debug(
                    "Job {} progress: {} tasks still processing", jobId, emittedTaskIds.size());
        }

        return completed;
    }

    private void markJobAsFinished() {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            TblCalcJobInfoMapper jobInfoMapper = session.getMapper(TblCalcJobInfoMapper.class);
            jobInfoMapper.updateStatus(
                    jobId, ReCalcJobStatusEnum.FINISHED.getCode(), System.currentTimeMillis());
            logger.info("Job {} marked as FINISHED in database", jobId);
        } catch (Exception e) {
            logger.error("Failed to mark job {} as FINISHED: {}", jobId, e.getMessage(), e);
        }
    }

    @Override
    public void cancel() {
        logger.info("BatchCalcJobTaskSource cancelled for job: {}", jobId);
        isRunning = false;
    }

    @Override
    public void close() throws Exception {
        super.close();
        // ✅ 清理Job级别的内存状态
        if (taskCompletionNotifier != null) {
            taskCompletionNotifier.cleanup();
        }
        logger.info("BatchCalcJobTaskSource closed and cleaned up for job: {}", jobId);
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        logger.debug(
                "Snapshotting state for job: {}, pending: {}, emitted: {}",
                jobId,
                pendingTasks.size(),
                emittedTaskIds.size());

        // ✅ 保存待处理任务
        checkpointedPendingTasks.clear();
        for (CalcJobTask task : pendingTasks) {
            checkpointedPendingTasks.add(task);
        }

        // ✅ 保存已发送但未完成的任务ID
        checkpointedEmittedTaskIds.clear();
        for (String taskId : emittedTaskIds) {
            checkpointedEmittedTaskIds.add(taskId);
        }

        checkpointedTotalCount.clear();
        checkpointedTotalCount.add(totalTaskCount);

        checkpointedEmittedCount.clear();
        checkpointedEmittedCount.add(emittedTaskCount);

        checkpointedAllTasksEmitted.clear();
        checkpointedAllTasksEmitted.add(allTasksEmitted);

        logger.debug("State snapshot completed for job: {}", jobId);
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        logger.info("Initializing state for job: {}", jobId);

        // ✅ Job级别的状态描述符（完全隔离）
        checkpointedPendingTasks =
                context.getOperatorStateStore()
                        .getListState(
                                new ListStateDescriptor<>(
                                        "pending-tasks-" + jobId, CalcJobTask.class));

        checkpointedEmittedTaskIds =
                context.getOperatorStateStore()
                        .getListState(
                                new ListStateDescriptor<>(
                                        "emitted-task-ids-" + jobId, String.class));

        checkpointedTotalCount =
                context.getOperatorStateStore()
                        .getListState(
                                new ListStateDescriptor<>(
                                        "total-task-count-" + jobId, Integer.class));

        checkpointedEmittedCount =
                context.getOperatorStateStore()
                        .getListState(
                                new ListStateDescriptor<>(
                                        "emitted-task-count-" + jobId, Integer.class));

        checkpointedAllTasksEmitted =
                context.getOperatorStateStore()
                        .getListState(
                                new ListStateDescriptor<>(
                                        "all-tasks-emitted-" + jobId, Boolean.class));

        // ✅ 状态恢复
        if (context.isRestored()) {
            logger.info("Restoring state for job: {}", jobId);

            // 恢复待处理任务
            pendingTasks = new LinkedList<>();
            for (CalcJobTask task : checkpointedPendingTasks.get()) {
                pendingTasks.offer(task);
            }

            // ✅ 恢复已发送任务ID（这些任务需要重新处理，依赖幂等性）
            emittedTaskIds = new HashSet<>();
            for (String taskId : checkpointedEmittedTaskIds.get()) {
                emittedTaskIds.add(taskId);
            }

            // ✅ 将已发送但未完成的任务重新加入待处理队列
            if (!emittedTaskIds.isEmpty()) {
                regenerateEmittedTasks();
            }

            // 恢复其他状态
            for (Integer count : checkpointedTotalCount.get()) {
                totalTaskCount = count;
                break; // 只取第一个值
            }

            for (Integer count : checkpointedEmittedCount.get()) {
                emittedTaskCount = count;
                break; // 只取第一个值
            }

            for (Boolean emitted : checkpointedAllTasksEmitted.get()) {
                allTasksEmitted = emitted;
                break; // 只取第一个值
            }

            // ✅ 重置已发送任务集合，因为这些任务会重新处理
            emittedTaskIds.clear();

            logger.info(
                    "State restored for job {}: total={}, pending={}, will re-process emitted tasks",
                    jobId,
                    totalTaskCount,
                    pendingTasks.size());
        }
    }

    private void regenerateEmittedTasks() throws Exception {
        // ✅ 重新生成已发送但未完成的任务
        CalcJobTaskSplitEnumerator enumerator = new CalcJobTaskSplitEnumerator(jobInfo);
        List<CalcJobTask> allTasks = enumerator.generateAllTasks();

        // 将需要重新处理的任务加入队列
        for (CalcJobTask task : allTasks) {
            if (emittedTaskIds.contains(task.getTaskId())) {
                pendingTasks.offer(task);
                logger.info("Re-queued task for processing: {}", task.getTaskId());
            }
        }
    }
}
