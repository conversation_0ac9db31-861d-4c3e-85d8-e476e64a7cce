package com.envision.gravity.flink.streaming.bo.view.operator.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.bo.view.operator.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.config.TableEngineConfig;
import com.envision.gravity.flink.streaming.bo.view.operator.request.DestroyCacheReq;

import java.sql.SQLException;
import java.sql.Statement;


import com.eniot.tableengine.TableEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
@Slf4j
public class DropViewSink extends RichSinkFunction<Tuple2<String, DestroyCacheReq>> {
    private static final long serialVersionUID = -1490570486045683608L;
    private transient SqlSessionFactory sqlSessionFactory;
    private transient TableEngine tableEngine;

    public void open(Configuration parameters) throws Exception {
        sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        tableEngine = TableEngineConfig.buildTableEngine();
    }

    @Override
    public void close() throws Exception {
        try {
            PGDataSourceConfig.closeDataSource();
            TableEngineConfig.closeTableEngine();
        } catch (Exception e) {
            log.error("Close resource error.", e);
            throw new GravityRuntimeException("Close resource error.", e);
        }
    }

    @Override
    public void invoke(Tuple2<String, DestroyCacheReq> value, Context context) {
        if (value != null) {
            try (SqlSession session = sqlSessionFactory.openSession(true);
                    Statement statement = session.getConnection().createStatement()) {
                long executeStartTime = System.currentTimeMillis();
                int rowsAffected = statement.executeUpdate(value.f0);
                long dropPgViewEndTime = System.currentTimeMillis();

                DestroyCacheReq destroyCacheReq = value.f1;
                tableEngine.destroyCache(
                        destroyCacheReq.getOrgId(),
                        destroyCacheReq.getSchema(),
                        destroyCacheReq.getTable());
                long executeEndTime = System.currentTimeMillis();

                log.info(
                        "Drop bo view success, value: {}, "
                                + "drop pg view time cost: [{}], destroy ignite table time cost: [{}].",
                        value,
                        (dropPgViewEndTime - executeStartTime) / 1000.0 + "s",
                        (executeEndTime - dropPgViewEndTime) / 1000.0 + "s");
            } catch (SQLException e) {
                log.error("Drop bo view error, cause: {}, value: {} ", e.getMessage(), value, e);
            } catch (Exception e) {
                log.error("Unknown error, cause: {} ", e.getMessage(), e);
                throw new GravityRuntimeException("Unknown error!", e);
            }
        }
    }
}
