package com.envision.gravity.flink.steaming.bo.event;

import com.envision.gravity.flink.steaming.bo.event.config.LionConfig;
import com.envision.gravity.flink.steaming.bo.event.function.CdcRecordRouter;
import com.envision.gravity.flink.steaming.bo.event.function.EventGenerator;
import com.envision.gravity.flink.steaming.bo.event.sink.KafkaSink;

import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description
 */
@Slf4j
public class FlinkSteamBOEvent {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", LionConfig.getMaxBatchSize());
        properties.setProperty("max.queue.size", LionConfig.getMaxQueueSize());

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        DataStreamSource<String> pgSource =
                env.addSource(sourceFunction)
                        .setParallelism(1); // use parallelism 1 for sink to keep message ordering

        pgSource.process(new CdcRecordRouter())
                .process(new EventGenerator())
                .addSink(new KafkaSink());

        env.execute("Flink Streaming BO Event");
    }
}
