package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;


import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
public class PGCatalogSqlProvider {
    private static final String SCHEME_LIKE = LionConfig.getSchemeLike();

    public String queryScheme() {
        SQL sql =
                new SQL() {
                    {
                        SELECT("nspname");
                        FROM("pg_catalog.pg_namespace");
                        WHERE("nspname LIKE '" + SCHEME_LIKE + "'");
                    }
                };

        return sql.toString();
    }
}
