package com.envision.gravity.common.vo.search.graph;

import com.envision.gravity.common.pojo.EdgeProperty;

import javax.validation.Valid;

import java.util.List;


import lombok.Data;

/** @Author: qi.jiang2 @Date: 2024/05/09 13:52 @Description: */
@Data
public class EdgeCreateOrUpdateReq {

    @Valid private List<EdgeProperty> edges;

    private boolean checkEdgeConnected;

    private boolean syncRequest = false;
}
