package com.envision.gravity.ignite.tsdb.loader;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.apache.ignite.cache.CacheAtomicityMode.TRANSACTIONAL;

import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.cache.CacheMode;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.spi.communication.tcp.TcpCommunicationSpi;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;

/** <AUTHOR> 2024/3/20 */
public class TSDBLoaderTest {

    // SQL_${schema}_${table}
    private static final String SCHEMA = "TSDB";

    private static final String TABLE_NAME = "TBL_TSDB_METRIC";

    private static final String CACHE_NAME = String.format("SQL_%s_%s", SCHEMA, TABLE_NAME);

    private static final String KEY_TYPE = String.format("%s.%s_KEY", SCHEMA, TABLE_NAME);

    private static final String VALUE_TYPE = String.format("%s.%s_VALUE", SCHEMA, TABLE_NAME);

    public static void main(String[] args) {
        // To start ignite with desired configuration uncomment the appropriate line.
        Collection<String> serverIps = new ArrayList<>();
        // serverIps.add("*************");
        serverIps.add("127.0.0.1");
        // serverIps.add("*************");
        Ignite ignite = createIgnite(serverIps, 47500, 0);

        try {
            System.out.println(">>> Cache store example started. " + CACHE_NAME);
            CacheConfiguration<BinaryObject, BinaryObject> cacheCfg =
                    new CacheConfiguration<>(CACHE_NAME);

            QueryEntity queryEntity = new QueryEntity();
            queryEntity.setKeyType(KEY_TYPE);
            queryEntity.setValueType(VALUE_TYPE);
            Set<String> set = new HashSet<>();
            set.add(TSDBLoaderConstants.MDMID);
            set.add(TSDBLoaderConstants.KPI);
            set.add(TSDBLoaderConstants.TS);
            set.add(TSDBLoaderConstants.TIMEGROUP);
            set.add(TSDBLoaderConstants.AGGTYPE);
            queryEntity.setKeyFields(set);

            queryEntity.addQueryField(
                    TSDBLoaderConstants.MDMID, String.class.getName(), TSDBLoaderConstants.MDMID);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.KPI, String.class.getName(), TSDBLoaderConstants.KPI);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.TS, String.class.getName(), TSDBLoaderConstants.TS);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.TIMEGROUP,
                    String.class.getName(),
                    TSDBLoaderConstants.TIMEGROUP);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.AGGTYPE,
                    String.class.getName(),
                    TSDBLoaderConstants.AGGTYPE);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.VALUEINT,
                    Integer.class.getName(),
                    TSDBLoaderConstants.VALUEINT);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.VALUELONG,
                    Long.class.getName(),
                    TSDBLoaderConstants.VALUELONG);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.VALUEDOUBLE,
                    Double.class.getName(),
                    TSDBLoaderConstants.VALUEDOUBLE);
            queryEntity.addQueryField(
                    TSDBLoaderConstants.VALUESTRING,
                    String.class.getName(),
                    TSDBLoaderConstants.VALUESTRING);

            queryEntity.setTableName(TABLE_NAME);

            cacheCfg.setQueryEntities(Collections.singleton(queryEntity));

            // Set atomicity as transaction, since we are showing transactions in example.
            cacheCfg.setAtomicityMode(TRANSACTIONAL);
            cacheCfg.setCacheMode(CacheMode.REPLICATED);

            JdbcType[] jdbcTypes = new JdbcType[1];
            JdbcType jdbcType = new JdbcType();
            jdbcType.setKeyType(KEY_TYPE);
            jdbcType.setValueType(VALUE_TYPE);
            jdbcType.setCacheName(CACHE_NAME);
            jdbcType.setKeyFields(
                    new JdbcTypeField(
                            Types.VARCHAR, TSDBLoaderConstants.MDMID, String.class, "mdmId"),
                    new JdbcTypeField(Types.VARCHAR, TSDBLoaderConstants.KPI, String.class, "kpi"),
                    new JdbcTypeField(Types.VARCHAR, TSDBLoaderConstants.TS, String.class, "ts"),
                    new JdbcTypeField(
                            Types.VARCHAR,
                            TSDBLoaderConstants.TIMEGROUP,
                            String.class,
                            "timeGroup"),
                    new JdbcTypeField(
                            Types.VARCHAR, TSDBLoaderConstants.AGGTYPE, String.class, "aggType"));

            jdbcType.setValueFields(
                    new JdbcTypeField(
                            Types.VARCHAR, TSDBLoaderConstants.MDMID, String.class, "mdmId"),
                    new JdbcTypeField(Types.VARCHAR, TSDBLoaderConstants.KPI, String.class, "kpi"),
                    new JdbcTypeField(Types.VARCHAR, TSDBLoaderConstants.TS, String.class, "ts"),
                    new JdbcTypeField(
                            Types.VARCHAR,
                            TSDBLoaderConstants.TIMEGROUP,
                            String.class,
                            "timeGroup"),
                    new JdbcTypeField(
                            Types.VARCHAR, TSDBLoaderConstants.AGGTYPE, String.class, "aggType"),
                    new JdbcTypeField(
                            Types.INTEGER, TSDBLoaderConstants.VALUEINT, Integer.class, "valueInt"),
                    new JdbcTypeField(
                            Types.BIGINT, TSDBLoaderConstants.VALUELONG, Long.class, "valueLong"),
                    new JdbcTypeField(
                            Types.DOUBLE,
                            TSDBLoaderConstants.VALUEDOUBLE,
                            Double.class,
                            "valueDouble"),
                    new JdbcTypeField(
                            Types.VARCHAR,
                            TSDBLoaderConstants.VALUESTRING,
                            String.class,
                            "valueString"));
            jdbcTypes[0] = jdbcType;

            TSDBCacheStoreFactory tsdbLoaderFactory = new TSDBCacheStoreFactory();
            tsdbLoaderFactory.setTypes(jdbcTypes);
            tsdbLoaderFactory.setConnectionURL(
                    "influxdb_cluster://*************:8086,10.65.101.235:8086,10.65.101.236:8086?readTimeout=5000");
            tsdbLoaderFactory.setUserName("admin");
            tsdbLoaderFactory.setPassword("Envisi0n1234!");
            tsdbLoaderFactory.setClusterMetaURL("*************:10800");
            tsdbLoaderFactory.setClusterMetaUserName("ignite");
            tsdbLoaderFactory.setClusterMetaPassword("ignite");

            cacheCfg.setCacheStoreFactory(tsdbLoaderFactory);
            cacheCfg.setReadThrough(true);
            cacheCfg.setWriteThrough(false);
            cacheCfg.setSqlSchema(SCHEMA);
            cacheCfg.setDataRegionName("InMemory_Region");

            // Auto-close cache at the end of the example.
            try (IgniteCache<BinaryObject, BinaryObject> cache =
                    ignite.getOrCreateCache(cacheCfg)) {
                long start = System.currentTimeMillis();

                // TSDBLoaderRequest loaderRequest = aggrQueryRequest();
                //                TSDBLoaderRequest rawRequest = rawQueryRequest();
                //                cache.loadCache(null, rawRequest);
                //
                //                TSDBLoaderRequest aggrRequest = aggrQueryRequest();
                //                cache.loadCache(null, aggrRequest);

                // TSDBLoaderRequest aggrRequest = testRequest();
                TSDBLoaderRequest request = metricRequest();
                cache.loadCache(null, request);

                long end = System.currentTimeMillis();
            } finally {
                // Distributed cache could be removed from cluster only by #destroyCache() call.
                // ignite.destroyCache(CACHE_NAME);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static TSDBLoaderRequest metricRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("INV.ActPowOut", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2023-02-19 00:00:00")
                .endTime("2023-03-30 00:00:00")
                .assetIds(new HashSet<>(Arrays.asList("IiXFn7j7")))
                .timeGroup("RAW");
    }

    private static TSDBLoaderRequest prefRawRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("active_power", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2024-02-01 00:00:00")
                .endTime("2024-02-20 00:00:00")
                .assetIds(Collections.singleton("assetId1"))
                .timeGroup("RAW")
                .slimit(null);
    }

    private static TSDBLoaderRequest testRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("ActPowOut", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2023-02-19 00:00:00")
                .endTime("2023-03-30 00:00:00")
                .assetIds(Collections.singleton("IiXFn7j7"))
                .timeGroup("RAW")
                .slimit(null);
    }

    private static TSDBLoaderRequest rawQueryRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("SCADATagState", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2023-02-20 00:00:00")
                .endTime("2023-03-30 00:00:00")
                .assetIds(Collections.singleton("8B1EzpQF"))
                .timeGroup("RAW")
                .slimit(10);
    }

    private static TSDBLoaderRequest aggrQueryRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("ActPowOut", "count");
        pointWithAggrs.put("SCADATagState", "count");

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2023-02-20 00:00:00")
                .endTime("2023-03-30 00:00:00")
                .assetIds(Collections.singleton("8B1EzpQF"))
                .timeGroup("10m")
                .slimit(10);
    }

    public static Ignite createIgnite(
            Collection<String> serverIps, int serverPort, long metricsLogFreq) {
        try {
            Ignition.setClientMode(true);

            IgniteConfiguration cfg = new IgniteConfiguration();
            cfg.setPeerClassLoadingEnabled(true);
            cfg.setDeploymentMode(DeploymentMode.CONTINUOUS);
            cfg.setAuthenticationEnabled(true);
            cfg.setClientMode(true);
            TcpDiscoverySpi spi = new TcpDiscoverySpi();
            TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();

            Set<String> addrs = new HashSet<>(serverIps.size());
            for (String serverIp : serverIps) {
                addrs.add(serverIp + ":" + serverPort);
            }
            ipFinder.setAddresses(addrs);
            spi.setIpFinder(ipFinder);
            cfg.setDiscoverySpi(spi);
            cfg.setNetworkTimeout(120000);
            cfg.setAuthenticationEnabled(true);
            cfg.setMetricsLogFrequency(metricsLogFreq);
            TcpCommunicationSpi communicationSpi = new TcpCommunicationSpi();
            communicationSpi.setMessageQueueLimit(512);
            communicationSpi.setSlowClientQueueLimit(128);
            communicationSpi.setIdleConnectionTimeout(3600000);
            cfg.setCommunicationSpi(communicationSpi);

            return Ignition.getOrStart(cfg);
        } catch (Exception e) {
            throw new RuntimeException("Create Ignite Error", e);
        }
    }
}
