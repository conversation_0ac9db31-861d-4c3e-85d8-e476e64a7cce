package com.envision.gravity.common.vo.bo;

import java.util.List;
import java.util.Objects;

public class BOQueryCacheHint {
    private final String name;
    private final List<String> args;

    public BOQueryCacheHint(String name, List<String> args) {
        this.name = name;
        this.args = args;
    }

    public String getName() {
        return name;
    }

    public List<String> getArgs() {
        return args;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        BOQueryCacheHint queryHint = (BOQueryCacheHint) o;

        if (!Objects.equals(name, queryHint.name)) {
            return false;
        }
        return Objects.equals(args, queryHint.args);
    }

    @Override
    public int hashCode() {
        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (args != null ? args.hashCode() : 0);
        return result;
    }
}
