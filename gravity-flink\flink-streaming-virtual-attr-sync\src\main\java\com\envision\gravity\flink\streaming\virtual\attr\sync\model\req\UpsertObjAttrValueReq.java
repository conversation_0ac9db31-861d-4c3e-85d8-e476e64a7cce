package com.envision.gravity.flink.streaming.virtual.attr.sync.model.req;

import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/9
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpsertObjAttrValueReq {
    private String systemId;
    private Map<String, Object> attrMap;
    private Map<String, String> uniqueMap;
}
