package com.envision.gravity.flink.streaming.calculate.flink.offset;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.HashMap;


import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.flink.api.java.tuple.Tuple2;

@Data
@AllArgsConstructor
@TypeInfo(PojoFactory.OffsetInfoType.class)
public class OffsetInfo {

    private String topic;
    private int partition;
    private long offset;
    private HashMap<Tuple2<String, Integer>, Long> collector;

    public long getOffset() {
        return offset;
    }

    public void collect(OffsetInfo newOffset) {
        Tuple2<String, Integer> key = new Tuple2<>(newOffset.getTopic(), newOffset.getPartition());
        Long offset = collector.get(key);
        if (offset == null || offset < newOffset.getOffset()) {
            collector.put(key, newOffset.getOffset());
        }
    }

    public HashMap<Tuple2<String, Integer>, Long> getCollector() {
        return collector;
    }

    public int getGroupKey() {
        return partition;
    }

    public OffsetInfo(String topic, int partition, long offset) {
        this.topic = topic;
        this.partition = partition;
        this.offset = offset;
    }

    public OffsetInfo() {
        this.collector = new HashMap<>();
    }
}
