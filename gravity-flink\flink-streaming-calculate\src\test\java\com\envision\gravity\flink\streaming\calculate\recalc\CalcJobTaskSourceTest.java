package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.TblPropertyUpstreamRule;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.flink.api.connector.source.Boundedness;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.api.connector.source.SplitEnumerator;
import org.apache.flink.api.connector.source.SplitEnumeratorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * CalcJobTaskSource 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CalcJobTaskSourceTest {

    @Mock private SplitEnumeratorContext<CalcJobTaskSplit> enumContext;

    @Mock private SourceReaderContext readerContext;

    private TblCalcJobInfo testJobInfo;
    private CalcJobTaskSource batchSource;
    private CalcJobTaskSource streamSource;

    private Set<String> createTargetModelIds() {
        Set<String> targetModelIds = new HashSet<>();
        targetModelIds.add("model_001");
        targetModelIds.add("model_002");
        return targetModelIds;
    }

    @BeforeEach
    void setUp() {
        // 创建测试数据
        TblPropertyUpstreamRule ruleInfo =
                TblPropertyUpstreamRule.builder()
                        .prefRuleId("test_rule_001")
                        .targetCategory("test_category")
                        .targetCompId("test_comp_001")
                        .targetPrefId("test_pref_001")
                        .srcCategory("test_src_category")
                        .expression("test_expression")
                        .calcType(1)
                        .build();

        CalcPropertyMeta propertyMeta =
                CalcPropertyMeta.builder().prefName("test_pref_name").build();

        CalcJobMetaInfo metaInfo =
                CalcJobMetaInfo.builder()
                        .orgId("o17186913277371853")
                        .ruleInfo(ruleInfo)
                        .targetModelIds(createTargetModelIds())
                        .targetPropertyMeta(propertyMeta)
                        .build();

        testJobInfo =
                TblCalcJobInfo.builder()
                        .jobId("test_job_001")
                        .prefRuleId("test_rule_001")
                        .ruleInfo(metaInfo)
                        .calcStartTime(System.currentTimeMillis())
                        .calcEndTime(System.currentTimeMillis() + 3600000) // 1小时后
                        .build();

        batchSource = new CalcJobTaskSource(testJobInfo, true);
        streamSource = new CalcJobTaskSource(testJobInfo, false);
    }

    @Test
    void testGetBoundedness_BatchMode() {
        assertEquals(Boundedness.BOUNDED, batchSource.getBoundedness());
    }

    @Test
    void testGetBoundedness_StreamMode() {
        assertEquals(Boundedness.CONTINUOUS_UNBOUNDED, streamSource.getBoundedness());
    }

    @Test
    void testCreateEnumerator() {
        SplitEnumerator<CalcJobTaskSplit, CalcJobTaskEnumeratorState> enumerator =
                batchSource.createEnumerator(enumContext);

        assertNotNull(enumerator);
        assertInstanceOf(CalcJobTaskSplitEnumerator.class, enumerator);
    }

    @Test
    void testRestoreEnumerator() {
        CalcJobTaskEnumeratorState state = new CalcJobTaskEnumeratorState();

        SplitEnumerator<CalcJobTaskSplit, CalcJobTaskEnumeratorState> enumerator =
                batchSource.restoreEnumerator(enumContext, state);

        assertNotNull(enumerator);
        assertInstanceOf(CalcJobTaskSplitEnumerator.class, enumerator);
    }

    @Test
    void testCreateReader() {
        SourceReader<CalcJobTask, CalcJobTaskSplit> reader =
                batchSource.createReader(readerContext);

        assertNotNull(reader);
        assertInstanceOf(CalcJobTaskSourceReader.class, reader);
    }

    @Test
    void testGetProducedType() {
        assertEquals(CalcJobTask.class, batchSource.getProducedType().getTypeClass());
    }

    @Test
    void testGetJobInfo() {
        assertEquals(testJobInfo, batchSource.getJobInfo());
    }

    @Test
    void testIsBatchMode() {
        assertTrue(batchSource.isBatchMode());
        assertFalse(streamSource.isBatchMode());
    }

    @Test
    void testDefaultConstructor() {
        CalcJobTaskSource defaultSource = new CalcJobTaskSource(testJobInfo);
        assertTrue(defaultSource.isBatchMode()); // 默认为批处理模式
    }

    @Test
    void testToString() {
        String result = batchSource.toString();
        assertTrue(result.contains("CalcJobTaskSource"));
        assertTrue(result.contains("test_job_001"));
        assertTrue(result.contains("isBatchMode=true"));
    }
}
