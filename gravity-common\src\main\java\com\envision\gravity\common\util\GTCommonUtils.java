package com.envision.gravity.common.util;

import java.security.SecureRandom;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

public class GTCommonUtils {

    private static final String[] CHARS =
            new String[] {
                "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "p", "q", "r", "s", "t",
                "u", "v", "w", "x", "y", "z", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C",
                "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V",
                "W", "X", "Y", "Z"
            };

    private static final String[] CHARS_LOW_CASE =
            new String[] {
                "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "p", "q", "r", "s", "t",
                "u", "v", "w", "x", "y", "z", "2", "3", "4", "5", "6", "7", "8", "9"
            };

    public static String concatStr(Collection<?> parts) {
        return concatStr(parts, true);
    }

    private static String concatStr(Collection<?> parts, boolean isStr) {
        return parts.stream()
                .map(
                        s -> {
                            if (s != null) {
                                if (isStr) {
                                    String s1 = s.toString().replace("'", "''");
                                    return "'" + s1 + "'";
                                }
                                return s.toString();
                            } else {
                                return "null";
                            }
                        })
                .collect(Collectors.joining(", "));
    }

    public static <K, V> boolean isEmpty(Map<K, V> map) {
        return map == null || map.size() == 0;
    }

    public static <K, V> boolean nonEmptyMap(Map<K, V> map) {
        return map != null && map.size() > 0;
    }

    public static <E> boolean emptyCollection(Collection<E> c) {
        return c == null || c.size() == 0;
    }

    public static String genShortUUID(int length) {
        StringBuilder shortBuffer = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int index = new SecureRandom().nextInt(CHARS.length);
            shortBuffer.append(CHARS[index]);
        }
        return shortBuffer.toString();
    }

    public static String genLowCaseShortUUID(int length) {
        StringBuilder shortBuffer = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int index = new SecureRandom().nextInt(CHARS_LOW_CASE.length);
            shortBuffer.append(CHARS_LOW_CASE[index]);
        }
        return shortBuffer.toString();
    }
}
