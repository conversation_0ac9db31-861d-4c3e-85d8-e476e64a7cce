# ReCalc Job Generation Flow Integration Testing Guide

## 概述

本文档描述了如何运行 ReCalc Job Generation Flow 的集成测试，验证从 CDC 事件到 ReCalc 批处理作业执行的完整流程。

## 测试流程

### 测试场景
集成测试验证以下完整流程：

1. **CDC 事件触发**: 更新 `o17186913277371853.tbl_property_upstream_rule` 表中 `pref_rule_id = 'test001'` 记录的 `modified_time` 字段
2. **作业生成**: ReCalc Job Generation Flow 检测到 CDC 事件，生成重跑作业信息
3. **数据库写入**: 作业信息写入到 `public.tbl_calc_job_info` 表
4. **作业提交**: 提交 Flink 批处理任务 `ReCalcBatchJob.java`
5. **作业执行**: 验证 `ReCalcBatchJob.java` 正常执行并更新作业状态

### 测试数据
- **测试 Schema**: `o17186913277371853`
- **测试规则 ID**: `test001`
- **目标表**: `public.tbl_calc_job_info`
- **超时时间**: 60 秒

## 环境准备

### 1. 数据库配置

确保 PostgreSQL 数据库正在运行，并包含以下配置：

```properties
# PostgreSQL 配置
gravity-common.postgresql.jdbc-url=****************************************
gravity-common.postgresql.username=postgres
gravity-common.postgresql.password=password
```

### 2. Flink 集群配置

确保 Flink 集群正在运行：

```properties
# Flink 配置
gravity-flink.cluster.jobmanager.host=localhost
gravity-flink.cluster.jobmanager.port=8081
gravity-flink.cluster.jobmanager.rest-url=http://localhost:8081
```

### 3. 数据库表结构

确保以下表存在：

#### o17186913277371853.tbl_property_upstream_rule
```sql
CREATE TABLE o17186913277371853.tbl_property_upstream_rule (
    pref_rule_id VARCHAR(255) PRIMARY KEY,
    comp_id VARCHAR(255),
    pref_id VARCHAR(255),
    calc_type INTEGER,
    calc_expr TEXT,
    is_valid_expr BOOLEAN,
    created_time BIGINT,
    modified_time BIGINT,
    created_user VARCHAR(255),
    modified_user VARCHAR(255)
);
```

#### public.tbl_calc_job_info
```sql
CREATE TABLE public.tbl_calc_job_info (
    job_id VARCHAR(255) PRIMARY KEY,
    pref_rule_id VARCHAR(255),
    rule_info TEXT,
    calc_start_time BIGINT,
    calc_end_time BIGINT,
    status INTEGER,
    type INTEGER,
    src_org_id VARCHAR(255),
    target_org_id VARCHAR(255),
    progress INTEGER,
    started_time BIGINT,
    finished_time BIGINT,
    cancelled_time BIGINT,
    created_time BIGINT,
    modified_time BIGINT,
    created_user VARCHAR(255),
    modified_user VARCHAR(255),
    sys_created_time BIGINT,
    sys_modified_time BIGINT
);
```

## 运行集成测试

### 方法 1: 使用测试脚本（推荐）

```bash
# 进入项目目录
cd gravity-flink/flink-streaming-calculate

# 运行集成测试脚本
./scripts/run-integration-test.sh
```

脚本选项：
- `--skip-checks`: 跳过前置条件检查
- `--skip-compile`: 跳过项目编译
- `--help`: 显示帮助信息

### 方法 2: 使用 Maven 直接运行

```bash
# 进入项目目录
cd gravity-flink/flink-streaming-calculate

# 应用代码格式化
mvn spotless:apply

# 编译项目
mvn compile test-compile

# 运行集成测试
mvn test -Dtest=ReCalcJobGenFlowIntegrationTest \
    -Darch.path=./deploy/apps \
    -Djava.security.auth.login.config=./deploy/zk_client_jaas.conf \
    -DLOG_LEVEL=INFO
```

### 方法 3: 在 IDE 中运行

1. 在 IDE 中打开 `ReCalcJobGenFlowIntegrationTest.java`
2. 配置 JVM 参数：
   ```
   -ea
   -Darch.path=./deploy/apps
   -Djava.security.auth.login.config=./deploy/zk_client_jaas.conf
   -DLOG_LEVEL=DEBUG
   ```
3. 运行测试方法 `testReCalcJobGenFlow_CdcEventToJobExecution_Success()`

## 测试验证

### 成功标准

测试成功时应该看到以下结果：

1. **CDC 事件触发成功**:
   ```
   [INFO] CDC event triggered: updated modified_time=1671234567890 for pref_rule_id=test001
   ```

2. **作业生成成功**:
   ```
   [INFO] Job generation detected: jobId=abc123def456, status=0
   [INFO] Generated job verification passed: abc123def456
   ```

3. **作业执行成功**:
   ```
   [INFO] Job status reached: jobId=abc123def456, status=RUNNING
   [INFO] Job execution completed successfully: jobId=abc123def456
   ```

### 失败排查

#### 1. 数据库连接失败
```
[ERROR] Cannot connect to PostgreSQL database
```
**解决方案**: 检查数据库配置和连接性

#### 2. Flink 集群不可用
```
[ERROR] Cannot connect to Flink cluster
```
**解决方案**: 确保 Flink 集群正在运行

#### 3. CDC 事件未触发
```
[ERROR] Timeout waiting for job generation after 60 seconds
```
**解决方案**: 
- 检查 CDC 配置
- 确认测试数据存在
- 检查 ReCalc Job Generation Flow 是否正常启动

#### 4. 作业执行失败
```
[WARN] Job execution failed: jobId=abc123, status=FAILED
```
**解决方案**: 
- 检查 Flink 集群资源
- 查看 ReCalcBatchJob 日志
- 验证作业参数

## 配置文件

### integration-test.properties
```properties
# PostgreSQL 配置
gravity-common.postgresql.jdbc-url=****************************************
gravity-common.postgresql.username=postgres
gravity-common.postgresql.password=password

# Flink 配置
gravity-flink.cluster.jobmanager.host=localhost
gravity-flink.cluster.jobmanager.port=8081
gravity-flink.recalc.use-pre-uploaded-jar=true

# CDC 配置
gravity-flink.recalc.pg.cdc.schema-list=^(o17186913277371853).*
gravity-flink.recalc.pg.cdc.table-list=^o17186913277371853\\.(tbl_property_upstream_rule)$

# 测试配置
test.schema=o17186913277371853
test.pref-rule-id=test001
test.timeout-seconds=60
```

## 日志和调试

### 日志级别配置
```properties
# 调试级别日志
logging.level.com.envision.gravity.flink.streaming.calculate=DEBUG
logging.level.com.envision.gravity.flink.streaming.calculate.recalc=DEBUG
logging.level.com.envision.gravity.flink.streaming.calculate.integration=INFO
```

### 关键日志文件
- `target/surefire-reports/`: Maven 测试报告
- `target/test-logs/`: 测试执行日志
- Flink JobManager 日志: 查看作业提交和执行状态

## 故障排除

### 常见问题

1. **测试超时**: 增加 `test.timeout-seconds` 配置
2. **内存不足**: 增加 JVM 堆内存 `-Xmx2g`
3. **端口冲突**: 修改 Flink 或数据库端口配置
4. **权限问题**: 确保测试用户有数据库读写权限

### 调试技巧

1. **启用详细日志**: 设置 `LOG_LEVEL=DEBUG`
2. **单步调试**: 在 IDE 中设置断点
3. **数据库查询**: 手动查询测试表验证数据状态
4. **Flink Web UI**: 访问 http://localhost:8081 查看作业状态

## 扩展测试

### 添加新的测试场景

1. 创建新的测试方法
2. 准备特定的测试数据
3. 验证预期的结果
4. 更新文档

### 性能测试

可以扩展集成测试来验证性能指标：
- 作业生成延迟
- 作业执行时间
- 资源使用情况

## 总结

ReCalc Job Generation Flow 集成测试提供了端到端的验证，确保从 CDC 事件到批处理作业执行的完整流程正常工作。通过自动化测试，可以快速发现和修复集成问题，提高系统的可靠性。
