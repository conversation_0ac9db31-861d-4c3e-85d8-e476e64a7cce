package com.envision.gravity.common.vo.bo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class BOQueryCacheTSDBLoadRequest extends BOQueryCacheAbstractLoadRequest {
    private final Map<String, String> asset2systemId;

    private final boolean useLocalTimeZone;
    private final Map<String, String> assetId2timeZone;
    private final Map<String, Set<String>> pointName2fieldId;

    public BOQueryCacheTSDBLoadRequest(
            String orgId,
            String database,
            List<String> assetIds,
            List<BOQueryCachePoint> points,
            long startTime,
            long endTime,
            List<BOQueryCacheHint> hints,
            Map<String, String> asset2systemId,
            boolean useLocalTimeZone,
            Map<String, String> assetId2timeZone,
            Map<String, Set<String>> pointName2fieldId) {
        super(orgId, database, assetIds, points, false, startTime, endTime, hints);
        this.asset2systemId = asset2systemId;
        this.useLocalTimeZone = useLocalTimeZone;
        this.assetId2timeZone = assetId2timeZone;
        this.pointName2fieldId = pointName2fieldId;
    }

    public Map<String, String> getAsset2systemId() {
        return asset2systemId;
    }

    public boolean isUseLocalTimeZone() {
        return useLocalTimeZone;
    }

    public Map<String, String> getAssetId2timeZone() {
        return assetId2timeZone;
    }

    public Map<String, Set<String>> getPointName2fieldId() {
        return pointName2fieldId;
    }
}
