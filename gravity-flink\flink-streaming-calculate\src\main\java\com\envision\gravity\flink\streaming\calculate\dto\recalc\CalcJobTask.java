package com.envision.gravity.flink.streaming.calculate.dto.recalc;

import com.envision.gravity.common.calculate.AssetInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyPayload;
import com.univers.business.object.calc.dto.ExpressionCalculatorInput;
import com.univers.business.object.calc.dto.ExpressionCalculatorOutput;
import com.univers.business.object.calc.request.CalcExpressionRequest;
import com.univers.business.object.calc.response.CalcExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算作业任务
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobTask implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTask.class);

    /** 作业ID */
    private String jobId;

    /** 任务ID */
    private String taskId;

    /** 目标测量点名称 */
    private String targetPrefName;

    /** 目标资产ID列表 */
    // modelId => <assetId, info>
    private Map<String, Map<String, AssetInfo>> targetAssetIds;

    /** 开始时间 */
    private long startTime;

    /** 结束时间 */
    private long endTime;

    /** 任务状态 */
    private RecCalcJobTaskStatusEnum status;

    /** 获取任务的唯一标识 */
    public String getUniqueId() {
        return jobId + "_" + taskId;
    }

    @Override
    public String toString() {
        return "CalcJobTask{"
                + "jobId='"
                + jobId
                + '\''
                + ", taskId='"
                + taskId
                + '\''
                + ", targetPrefName='"
                + targetPrefName
                + '\''
                + ", targetAssetIds="
                + (targetAssetIds != null ? targetAssetIds.size() : 0)
                + ", startTime="
                + startTime
                + ", endTime="
                + endTime
                + ", status="
                + status
                + '}';
    }

    public Map<String, LegacyMsgWithMultiAssets> execTargetPropExprCalc(String orgId,
                                                                        TblCalcJobInfo jobInfo,
                                                                        CalcJobTask taskInfo,
                                                                        Map<String, LegacyPayload> targetAssetValues,
                                                                        Map<String, String> modelPathMap) {
        RecCalcMetaInfo calcMeta = jobInfo.getRuleInfo();
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = new HashMap<>();
        for (Map.Entry<String, Map<String, AssetInfo>> entry : taskInfo.getTargetAssetIds().entrySet()) {
            String targetModelId = entry.getKey();
            for (Map.Entry<String, AssetInfo> assetInfoEntry : entry.getValue().entrySet()) {
                String targetAssetId = assetInfoEntry.getKey();
                LegacyPayload srcPayload = targetAssetValues.get(targetAssetId);
                if (srcPayload == null) {
                    logger.warn("No src payload found for target asset: {}, orgId: {}, prefRuleId: {}",
                            targetAssetId, orgId, jobInfo.getPrefRuleId());
                    continue;
                }

                CalcExpressionRequest calcRequest = new CalcExpressionRequest();
                List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);
                calcInputs.add(
                        ExpressionCalculatorInput.builder()
                                .expression(calcMeta.getTargetPropertyMeta().getExpression())
                                .data(srcPayload.getPoints())
                                .build());
                calcRequest.setCalcInputs(calcInputs);

                logger.debug(
                        "Expression calculation request: expression={}, data={}",
                        calcMeta.getTargetPropertyMeta().getExpression(),
                        srcPayload.getPoints());

                List<CalcExpressionResponse> calcResult;
                try {
                    calcResult = ExpressionUtil.calcExpression(calcRequest);
                } catch (Exception e) {
                    logger.error(
                            "Calculate failed, orgId: {}, param: {}, reason {}",
                            orgId,
                            calcRequest,
                            e.getMessage());
                    throw new RuntimeException("Expression calculation failed", e);
                }

                ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();
                Object calculatedValue = calcOutput.getValue();

                LegacyMsgWithMultiAssets targetMsg =
                        targetModel2MsgMap.computeIfAbsent(
                                targetModelId,
                                k -> {
                                    LegacyMsgWithMultiAssets msg = new LegacyMsgWithMultiAssets();
                                    msg.setOrgId(orgId);
                                    msg.setModelId(targetModelId);
                                    msg.setModelIdPath(modelPathMap.get(targetModelId));
                                    msg.setPayload(new ArrayList<>());
                                    return msg;
                                });

                LegacyPayload targetPayload =
                        targetMsg.getPayload().stream()
                                .filter(p -> p.getAssetId().equals(targetAssetId))
                                .findFirst()
                                .orElseGet(
                                        () -> {
                                            LegacyPayload newPayload = new LegacyPayload();
                                            newPayload.setAssetId(targetAssetId);
                                            newPayload.setTime(srcPayload.getTime());
                                            newPayload.setPoints(new HashMap<>());
                                            targetMsg.getPayload().add(newPayload);
                                            return newPayload;
                                        });

                // Add calculated result
                targetPayload.getPoints().put(calcMeta.getTargetPropertyMeta().getPrefName(), calculatedValue);
            }
        }

        return targetModel2MsgMap;
    }
}
