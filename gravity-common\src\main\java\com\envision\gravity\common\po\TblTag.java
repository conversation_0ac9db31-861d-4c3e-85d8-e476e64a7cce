package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @description
 */
@Data
@Builder
public class TblTag {
    @KeyColumn(name = "data_id")
    @ColumnName("data_id")
    @RequiredField(message = "data_id field is required")
    private String dataId;

    @ValueColumn(name = "tag_type")
    @ColumnName("tag_type")
    @RequiredField(message = "tag_type field is required")
    private String tagType;

    @ValueColumn(name = "data_type")
    @ColumnName("data_type")
    private String dataType;

    @ValueColumn(name = "tag_group")
    @ColumnName("tag_group")
    private String tagGroup;

    @KeyColumn(name = "tag_id")
    @ColumnName("tag_id")
    @RequiredField(message = "tag_id field is required")
    private String tagId;

    @ValueColumn(name = "marker")
    @ColumnName("marker")
    private String marker;

    @ValueColumn(name = "tag_key")
    @ColumnName("tag_key")
    private String tagKey;

    @ValueColumn(name = "tag_value")
    @ColumnName("tag_value")
    private String tagValue;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;

    @ValueColumn(name = "data_tag_id")
    @ColumnName("data_tag_id")
    private String dataTagId;
}
