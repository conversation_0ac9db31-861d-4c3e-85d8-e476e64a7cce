package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/9
 * @description
 */
class QueryVirtualAttrValueTest {
    @Test
    void genQueryVirtualAttrValueSqlTest() {
        String attrName = "zujian-001:test";

        List<String> assetIdList = new ArrayList<>();
        assetIdList.add("0ZhG10fg_0x7");
        assetIdList.add("0ZhG10fg_0x8");
        assetIdList.add("0ZhG10gW_003");

        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        System.out.println(
                "SELECT MDMID,`"
                        + attrName
                        + "` \n"
                        + "FROM _attr\n"
                        + "WHERE MDMID IN ("
                        + assetIds
                        + ");");

        String sql =
                "SELECT /*+ ENGINE('metric'), ORG('o16227961710541858') */"
                        + "   MDMID,\n"
                        + "   `systemType`,\n"
                        + "   `capacity`,\n"
                        + "   `capacity_rated`,\n"
                        + "   `_timezone`\n"
                        + "FROM _attr\n"
                        + "WHERE MDMID IN ('0ZhG10jV_0gd', '0ZhG10jV_0ge');";
        System.out.println(sql);
    }

    @Test
    void regexTest() {
        String regex = ".*";
        String str1 = "123";
        String str2 = "ABC";
        String str3 = "%%%abc*|||";
        System.out.println(str1.matches(regex));
        System.out.println(str2.matches(regex));
        System.out.println(str3.matches(regex));

        String regex1 = "o16227961710541858";
        String str4 = "o16227961710541858";
        System.out.println(str4.matches(regex1));
    }

    @Test
    void getFieldId() {
        String[] requestArray =
                "\"UT_model_createAutoBase_modelId_Err32__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_ERROR7__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err275__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err80__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err219__DCM__attribute__label\",\"VT_tree_TreeBase_modelId_points__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err396__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err49__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err267__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err113__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err138__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err388__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err411__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err64__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err89__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err403__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err428__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err226__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err23__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err395__DCM__attribute__label\",\"AT_device_SKDev_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err266__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err112__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err96__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err387__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err410__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err233__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err104__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err63__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err88__DCM__attribute__label\",\"dcm_dev_test1720427571072__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err402__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err427__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err225__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err30__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_ERROR5__DCM__attribute__label\",\"JT_device_devBase_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err273__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err144__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err169__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err419__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err217__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err22__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err394__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err47__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err265__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err111__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err95__DCM__attribute__label\",\"VT_projection_test_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err386__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err39__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err232__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err62__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err87__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err401__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err426__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_ERROR4__DCM__attribute__label\",\"dcm_dev_test1720426811499__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err418__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err21__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err393__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err46__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err6__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err110__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err135__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_ERROR10__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err385__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err433__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err231__DCM__attribute__label\",\"JT_device_MigrationDevBase_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err329__DCM__attribute__label\",\"TMY_DCM_Model_0701_001__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err61__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err86__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err377__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err400__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err425__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err223__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_ERROR3__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err369__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err417__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err20__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err392__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err45__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err5__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err263__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err288__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err409__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err37__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err255__DCM__attribute__label\",\"dcm_dev_test1720427454789__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err328__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err60__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err85__DCM__attribute__label\",\"TMY_DCM_Model_0708_002__DCM__attribute__label\",\"AT_device_MigrationDevBase_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err376__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err29__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err424__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err222__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err52__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err368__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err416__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err214__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err239__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err391__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err69__DCM__attribute__label\",\"JT_device_Group_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err4__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err92__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err408__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err36__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err431__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err84__DCM__attribute__label\",\"TMY_DCM_Model_0708_001__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err375__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err28__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err423__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err51__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err367__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err415__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err213__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err238__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err109__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err390__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err43__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err68__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err91__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err407__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err430__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err301__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err83__DCM__attribute__label\",\"OUTofLIMITOUTofLIMITOUTofLIMITOUTofLIMITOUTofLIMITOUTofLIMITOUTof__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err374__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err399__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err422__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err220__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err116__DCM__attribute__label\",\"AT_device_devBase_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err366__DCM__attribute__label\",\"dcm_dev_test1720431161509__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err414__DCM__attribute__label\",\"VT_tree_TreeBase_modelId_1__DCM__attribute__label\",\"VT_tree_TreeBase_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err42__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err67__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err2__DCM__attribute__label\",\"dtmi:DCM:DCM;1__attribute__label\",\"UT_model_createAutoBase_modelId_Err90__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err406__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err34__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err59__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err123__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err82__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err398__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err421__DCM__attribute__label\",\"AT_device_Group_modelId_1__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err115__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err413__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err236__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err107__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err66__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err405__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err228__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err33__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err81__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err372__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err25__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err397__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err195__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err420__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err268__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err114__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err98__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err389__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err412__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err106__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err40__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err65__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err356__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err404__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err429__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err202__DCM__attribute__label\",\"UT_model_createAutoBase_modelId_Err227__DCM__attribute__label\""
                        .split(",");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < requestArray.length; i++) {
            String cleanedElement = requestArray[i].replace("\"", "");
            sb.append("'").append(cleanedElement).append("'");
            if (i < requestArray.length - 1) {
                sb.append(", ");
            }
        }

        String result = sb.toString();
        System.out.println(result);
    }

    @Test
    void duplicateTest() {
        List<RefreshReq> refreshReqList = new ArrayList<>();
        refreshReqList.add(new RefreshReq("schema1"));
        refreshReqList.add(new RefreshReq("schema1"));
        refreshReqList.add(new RefreshReq("schema2"));

        Set<String> uniqueSchemaNames = new HashSet<>();
        List<RefreshReq> deduplicatedList =
                StreamSupport.stream(((Iterable<RefreshReq>) refreshReqList).spliterator(), false)
                        .filter(element -> uniqueSchemaNames.add(element.getSchemaName()))
                        .collect(Collectors.toList());

        deduplicatedList.forEach(req -> System.out.println(req.getSchemaName()));
    }
}
