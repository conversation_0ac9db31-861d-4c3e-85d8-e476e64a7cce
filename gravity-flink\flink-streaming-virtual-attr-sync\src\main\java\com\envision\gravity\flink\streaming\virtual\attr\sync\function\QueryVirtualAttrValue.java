package com.envision.gravity.flink.streaming.virtual.attr.sync.function;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.MetricEngineConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.UpsertObjAttrValuePoseidonReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.UpsertObjAttrValueReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ModelAsset;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ObjAttrValue;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.VirtualAttrInfo;
import com.envision.gravity.flink.streaming.virtual.attr.sync.repository.TblBOModelRepository;
import com.envision.gravity.flink.streaming.virtual.attr.sync.repository.TblObjAttrRepository;
import com.envision.gravity.flink.streaming.virtual.attr.sync.repository.TblPrefExtRepository;

import java.util.*;
import java.util.stream.Collectors;


import com.eniot.metricengine.MetricEngine;
import com.eniot.metricengine.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class QueryVirtualAttrValue
        extends ProcessFunction<List<RefreshReq>, UpsertObjAttrValuePoseidonReq> {
    private static final long serialVersionUID = -6218910296533153057L;

    private static final int ASSET_PAGE_SIZE = LionConfig.getAssetPageSize();
    private static final String MODEL_REGEX = LionConfig.getModelRegex();
    private static final String ATTR_REGEX = LionConfig.getAttrRegex();
    private static final String SCHEME_REGEX = LionConfig.getSchemeRegex();
    private transient TblPrefExtRepository tblPrefExtRepository;
    private transient TblBOModelRepository tblBOModelRepository;
    private transient TblObjAttrRepository tblObjAttrRepository;
    private transient MetricEngine metricEngine;

    @Override
    public void open(Configuration params) {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        tblPrefExtRepository = new TblPrefExtRepository(sqlSessionFactory);
        tblBOModelRepository = new TblBOModelRepository(sqlSessionFactory);
        tblObjAttrRepository = new TblObjAttrRepository(sqlSessionFactory);
        metricEngine = MetricEngineConfig.buildMetricEngine();
    }

    @Override
    public void close() {
        try {
            PGDataSourceConfig.closeDataSource();
            MetricEngineConfig.closeMetricEngine();
        } catch (Exception e) {
            log.error("Close resource error.", e);
            throw new GravityRuntimeException("Close resource error.", e);
        }
    }

    @Override
    public void processElement(
            List<RefreshReq> value,
            ProcessFunction<List<RefreshReq>, UpsertObjAttrValuePoseidonReq>.Context ctx,
            Collector<UpsertObjAttrValuePoseidonReq> out)
            throws Exception {
        if (!value.isEmpty()) {
            for (RefreshReq refreshReq : value) {
                String schemaName = refreshReq.getSchemaName();
                if (schemaName.matches(SCHEME_REGEX)) {
                    // 1. query all virtual attr
                    List<VirtualAttrInfo> virtualAttrInfos =
                            tblPrefExtRepository.queryVirtualAttrInfo(schemaName);
                    // 2. refresh by attr and device
                    for (VirtualAttrInfo virtualAttrInfo : virtualAttrInfos) {
                        String modelId = virtualAttrInfo.getModelId();
                        String fieldId = virtualAttrInfo.getFieldId();
                        String rawFieldId = virtualAttrInfo.getRawFieldId();
                        String attrName = virtualAttrInfo.getAttrName();
                        if (modelId.matches(MODEL_REGEX) && attrName.matches(ATTR_REGEX)) {
                            int totalCount =
                                    tblBOModelRepository.selectAssetTotalCount(schemaName, modelId);
                            if (totalCount > 0) {
                                int totalPage = totalCount / ASSET_PAGE_SIZE + 1;
                                for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
                                    int offset = (pageNo - 1) * ASSET_PAGE_SIZE;
                                    List<ModelAsset> modelAssets =
                                            tblBOModelRepository.queryAsset(
                                                    schemaName, modelId, ASSET_PAGE_SIZE, offset);

                                    if (!modelAssets.isEmpty()) {
                                        if (rawFieldId != null && !rawFieldId.isEmpty()) {
                                            List<UpsertObjAttrValueReq> upsertObjAttrValueReq =
                                                    queryVirtualAttrValue(
                                                            schemaName,
                                                            fieldId,
                                                            rawFieldId,
                                                            attrName,
                                                            modelAssets);

                                            if (!upsertObjAttrValueReq.isEmpty()) {
                                                out.collect(
                                                        UpsertObjAttrValuePoseidonReq.Builder
                                                                .newBuilder()
                                                                .setOrgId(schemaName)
                                                                .setObjAttrs(upsertObjAttrValueReq)
                                                                .build());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private List<UpsertObjAttrValueReq> queryVirtualAttrValue(
            String schemaName,
            String fieldId,
            String rawFieldId,
            String attrName,
            List<ModelAsset> modelAssets) {
        long queryStartTime = System.currentTimeMillis();
        List<String> assetIdList =
                modelAssets.stream().map(ModelAsset::getAssetId).collect(Collectors.toList());
        List<String> systemIdList =
                modelAssets.stream().map(ModelAsset::getSystemId).collect(Collectors.toList());
        Map<String, String> assetIdSystemIdMap =
                modelAssets.stream()
                        .collect(Collectors.toMap(ModelAsset::getAssetId, ModelAsset::getSystemId));

        String querySql = genQueryVirtualAttrValueSql(attrName, assetIdList);

        try {
            Map<String, Object> systemIdObjAttrValueMap =
                    tblObjAttrRepository.queryAttrValue(schemaName, rawFieldId, systemIdList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ObjAttrValue::getSystemId, ObjAttrValue::getAttrValue));

            QueryResult queryResult = metricEngine.execute(schemaName, null, null, querySql, null);

            List<UpsertObjAttrValueReq> objAttrs = new ArrayList<>();
            queryResult
                    .getData()
                    .forEach(
                            row -> {
                                String assetId = String.valueOf(row.get(0));
                                if (assetIdSystemIdMap.get(assetId) != null) {
                                    String systemId = assetIdSystemIdMap.get(assetId);
                                    Object virtualAttrValue = row.get(1);
                                    Object objAttrValue = systemIdObjAttrValueMap.get(systemId);
                                    if (!Objects.equals(virtualAttrValue, objAttrValue)) {
                                        Map<String, Object> attrMap = new HashMap<>();
                                        attrMap.put(fieldId, virtualAttrValue);
                                        objAttrs.add(
                                                UpsertObjAttrValueReq.builder()
                                                        .systemId(systemId)
                                                        .attrMap(attrMap)
                                                        .build());
                                    }
                                }
                            });

            long queryEndTime = System.currentTimeMillis();
            log.info(
                    "Query virtual attr value success, total time cost: [{}] ms. ",
                    (queryEndTime - queryStartTime));
            return objAttrs;
        } catch (Exception e) {
            log.error(
                    "Query virtual attr value error, schemeName: [{}], querySql: [{}]",
                    schemaName,
                    querySql,
                    e);
            return Collections.emptyList();
        }
    }

    private String genQueryVirtualAttrValueSql(String attrName, List<String> assetIdList) {
        String assetIds =
                assetIdList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        return "SELECT MDMID,`"
                + attrName
                + "` \n"
                + "FROM _attr\n"
                + "WHERE MDMID IN ("
                + assetIds
                + ");";
    }
}
