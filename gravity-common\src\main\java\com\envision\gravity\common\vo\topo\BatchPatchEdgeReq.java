package com.envision.gravity.common.vo.topo;

import com.envision.gravity.common.pojo.EdgeProperty;

import javax.validation.Valid;

import java.util.List;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
public class BatchPatchEdgeReq {

    @Valid private List<EdgeProperty> upsertEdges;

    @Valid private List<EdgeProperty> deleteEdges;

    private boolean checkEdgeConnected;
}
