package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/02/20 12:14 @Description: */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TblCategory {

    private String categoryId;
    private String categoryDisplayName;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
