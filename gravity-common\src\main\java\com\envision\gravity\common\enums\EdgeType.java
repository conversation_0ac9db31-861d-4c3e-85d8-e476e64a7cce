package com.envision.gravity.common.enums;

/** @Author: qi.jiang2 @Date: 2024/04/15 14:48 @Description: */
public enum EdgeType {

    // Gravity_Asset_Relationship
    GRAVITY_ASSET_RELATIONSHIP("gravity_asset_relationship", "Gravity_Asset_Relationship"),

    // Gravity_Gateway_Asset_Relationship
    GRAVITY_GATEWAY_ASSET_RELATIONSHIP(
            "gravity_gateway_asset_relationship", "Gravity_Gateway_Asset_Relationship"),

    // Gravity_Gateway_Group_Sub_Device_Relationship
    Gravity_Gateway_Group_Sub_Device_Relationship(
            "gravity_gateway_group_sub_device_relationship",
            "Gravity_Gateway_Group_Sub_Device_Relationship"),

    // Gravity_Gateway_Group_Gateway_Relationship
    Gravity_Gateway_Group_Gateway_Relationship(
            "gravity_gateway_group_gateway_relationship",
            "Gravity_Gateway_Group_Gateway_Relationship");

    private final String name;
    private final String nebulaEdgeSchema;

    EdgeType(String name, String nebulaEdgeSchema) {
        this.name = name;
        this.nebulaEdgeSchema = nebulaEdgeSchema;
    }

    public String getName() {
        return name;
    }

    public String getNebulaEdgeSchema() {
        return nebulaEdgeSchema;
    }
}
