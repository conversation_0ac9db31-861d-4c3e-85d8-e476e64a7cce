package com.envision.gravity.ignite.core.store.jdbc.dialect;

import java.util.Arrays;

import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/6/3
 * @description
 */
class PostgreSQLDialectTest {

    @Test
    public void testMergeQuery() {
        PostgreSQLDialect dialect = new PostgreSQLDialect();

        Assertions.assertTrue(dialect.hasMerge());

        System.out.println(
                ">>>"
                        + dialect.mergeQuery(
                                "table",
                                Arrays.asList("id", "name"),
                                Arrays.asList("age", "city")));
        System.out.println(
                ">>>" + dialect.mergeQuery("table", singleton("col1"), singleton("uniqCol")));
        System.out.println(">>>" + dialect.mergeQuery("table", singleton("col1"), emptyList()));
    }
}
