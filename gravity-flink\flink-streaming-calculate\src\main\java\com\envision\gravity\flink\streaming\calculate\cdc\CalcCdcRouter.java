package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.streaming.calculate.dto.*;

import java.util.Optional;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CalcCdcRouter extends ProcessFunction<String, ConvertedCdcRecord> {
    private static final Logger logger = LoggerFactory.getLogger(CalcCdcRouter.class);

    private static final long serialVersionUID = -5177401654698191195L;

    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, ConvertedCdcRecord>.Context ctx,
            Collector<ConvertedCdcRecord> out)
            throws Exception {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(value);
            JsonNode sourceNode = jsonNode.get("source");

            String tableName = sourceNode.get("table").asText();
            Optional<CalcCdcTable> cdcTable = CalcCdcTable.find(tableName);
            if (!cdcTable.isPresent()) {
                logger.error("Meta flow unsupported table [{}]", tableName);
                return;
            }

            ConvertedCdcRecord convertedCdcRecord =
                    ConvertedCdcRecord.builder()
                            .schema(sourceNode.get("schema").asText())
                            .db(sourceNode.get("db").asText())
                            .table(cdcTable.get().getName())
                            .op(jsonNode.get("op").asText())
                            .tsMs(jsonNode.get("ts_ms").asLong())
                            .build();

            switch (cdcTable.get()) {
                case TBL_PROPERTY_UPSTREAM_RULE:
                    TblPropertyUpstreamRule beforeRule =
                            OBJECT_MAPPER.treeToValue(
                                    jsonNode.get("before"), TblPropertyUpstreamRule.class);
                    TblPropertyUpstreamRule afterRule =
                            OBJECT_MAPPER.treeToValue(
                                    jsonNode.get("after"), TblPropertyUpstreamRule.class);
                    convertedCdcRecord.setBefore(beforeRule);
                    convertedCdcRecord.setAfter(afterRule);
                    convertedCdcRecord.setPrimaryKey(
                            afterRule != null
                                    ? afterRule.getPrefRuleId()
                                    : beforeRule.getPrefRuleId());
                    break;
                case TBL_BO_MODEL:
                    TblBoModel beforeModel =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblBoModel.class);
                    TblBoModel afterModel =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblBoModel.class);
                    convertedCdcRecord.setBefore(beforeModel);
                    convertedCdcRecord.setAfter(afterModel);
                    convertedCdcRecord.setPrimaryKey(
                            afterModel != null
                                    ? afterModel.getModelId()
                                    : beforeModel.getModelId());
                    break;
                case TBL_BO_MODEL_COMP:
                    TblBoModelComp beforeModelComp =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblBoModelComp.class);
                    TblBoModelComp afterModelComp =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblBoModelComp.class);
                    convertedCdcRecord.setBefore(beforeModelComp);
                    convertedCdcRecord.setAfter(afterModelComp);
                    convertedCdcRecord.setPrimaryKey(
                            afterModelComp != null
                                    ? afterModelComp.getModelId() + "_" + afterModelComp.getCompId()
                                    : beforeModelComp.getModelId()
                                            + "_"
                                            + beforeModelComp.getCompId());
                    break;
                case TBL_COMPONENT_PREF:
                    TblComponentPref beforeCompPref =
                            OBJECT_MAPPER.treeToValue(
                                    jsonNode.get("before"), TblComponentPref.class);
                    TblComponentPref afterCompPref =
                            OBJECT_MAPPER.treeToValue(
                                    jsonNode.get("after"), TblComponentPref.class);
                    convertedCdcRecord.setBefore(beforeCompPref);
                    convertedCdcRecord.setAfter(afterCompPref);
                    convertedCdcRecord.setPrimaryKey(
                            afterCompPref != null
                                    ? afterCompPref.getCompId() + "_" + afterCompPref.getPrefId()
                                    : beforeCompPref.getCompId()
                                            + "_"
                                            + beforeCompPref.getPrefId());
                    break;
                case TBL_COMPONENT:
                    TblComponent beforeComp =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblComponent.class);
                    TblComponent afterComp =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblComponent.class);
                    convertedCdcRecord.setBefore(beforeComp);
                    convertedCdcRecord.setAfter(afterComp);
                    convertedCdcRecord.setPrimaryKey(
                            afterComp != null ? afterComp.getCompId() : beforeComp.getCompId());
                    break;
                case TBL_PREF:
                    TblPref beforePref =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblPref.class);
                    TblPref afterPref =
                            OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblPref.class);
                    convertedCdcRecord.setBefore(beforePref);
                    convertedCdcRecord.setAfter(afterPref);
                    convertedCdcRecord.setPrimaryKey(
                            afterPref != null ? afterPref.getPrefId() : beforePref.getPrefId());
                    break;
                default:
                    logger.error("Meta flow unsupported table [{}]", tableName);
                    break;
            }

            if (convertedCdcRecord.getBefore() != null || convertedCdcRecord.getAfter() != null) {
                out.collect(convertedCdcRecord);
            }
        } catch (Exception e) {
            logger.error("Failed to parse cdc record, value: {}", value, e);
        }
    }
}
