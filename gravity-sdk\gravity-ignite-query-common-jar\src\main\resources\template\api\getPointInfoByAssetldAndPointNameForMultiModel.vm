SELECT
    comp_name,
    anonymous,
    pref_name,
    field_index,
    horizontal,
    raw_field_id,
    pref_type,
    data_type
FROM
    (
        SELECT
            DISTINCT tbl_bo_model.model_id AS model_id
        FROM
            "${orgId}".tbl_bo_model
            JOIN "${orgId}".tbl_bo_group_relation_part ON tbl_bo_model.group_id = tbl_bo_group_relation_part.group_id
        WHERE
            tbl_bo_group_relation_part.asset_id IN (${assetIds})
    ) AS model
    LEFT JOIN
    (
        SELECT
            tbl_bo_model_comp.model_id,
            tbl_component.comp_name,
            tbl_component.anonymous,
            tbl_pref.pref_name,
            tbl_component_pref.field_index,
            tbl_component_pref.horizontal,
            tbl_component_pref.raw_field_id,
            tbl_pref.pref_type,
            CASE
                WHEN tbl_pref.pref_data_type = 'ENUM' AND tbl_pref.data_definition LIKE '%"@type":["integer"]%' THEN 'INTEGER'
                ELSE tbl_pref.pref_data_type
            END AS data_type
        FROM
            "${orgId}".tbl_pref
            LEFT JOIN "${orgId}".tbl_component_pref_field_mapping AS tbl_component_pref ON tbl_pref.pref_id = tbl_component_pref.pref_id
            LEFT JOIN "${orgId}".tbl_component ON tbl_component_pref.comp_id = tbl_component.comp_id
            LEFT JOIN "${orgId}".tbl_bo_model_comp ON tbl_component.comp_id = tbl_bo_model_comp.comp_id
        WHERE
            tbl_pref.pref_name IN (${prefNames}) AND
            (
                tbl_component.anonymous = true OR
                (
                    tbl_component.anonymous = false
                    AND CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name) IN (${pointNames})
                )
            )
    ) AS pref ON model.model_id = pref.model_id;