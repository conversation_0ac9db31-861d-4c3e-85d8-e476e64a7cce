package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ModelAsset;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
public interface TblBOModelMapper {

    @SelectProvider(type = TblBOModelSqlProvider.class, method = "selectAssetTotalCount")
    int selectAssetTotalCount(String schemeName, String modelId);

    @SelectProvider(type = TblBOModelSqlProvider.class, method = "queryAsset")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelAsset> queryAsset(String schemeName, String modelId, int limit, int offset);
}
