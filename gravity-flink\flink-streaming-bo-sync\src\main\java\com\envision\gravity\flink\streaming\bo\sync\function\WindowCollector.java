package com.envision.gravity.flink.streaming.bo.sync.function;

import com.envision.gravity.flink.streaming.bo.sync.entity.Msg;
import com.envision.gravity.flink.streaming.bo.sync.entity.MsgMeta;

import java.util.stream.StreamSupport;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.GlobalWindow;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2025/3/19
 * @description
 */
@Slf4j
public class WindowCollector
        extends ProcessWindowFunction<
                Tuple2<Msg, MsgMeta>, Iterable<Tuple2<Msg, MsgMeta>>, Integer, GlobalWindow> {
    private static final long serialVersionUID = -5692693495552805295L;

    @Override
    public void process(
            Integer integer,
            ProcessWindowFunction<
                                    Tuple2<Msg, MsgMeta>,
                                    Iterable<Tuple2<Msg, MsgMeta>>,
                                    Integer,
                                    GlobalWindow>
                            .Context
                    context,
            Iterable<Tuple2<Msg, MsgMeta>> elements,
            Collector<Iterable<Tuple2<Msg, MsgMeta>>> out)
            throws Exception {
        log.debug(
                "Received sync request, total request count: {}.",
                StreamSupport.stream(elements.spliterator(), false).count());
        out.collect(elements);
    }
}
