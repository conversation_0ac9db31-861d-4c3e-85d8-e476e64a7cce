package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.service.AuthService;

import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

@Api(tags = "Authorization")
@RestController
@Validated
@RequestMapping("/auth")
public class AuthController {

    @Resource private AuthService authService;

    @GravityLog
    @PostMapping(value = "/management/admin-role")
    public ResponseResult<?> grantOrRevokeAdminRole(
            @NotBlank(message = "action can not be blank") @RequestParam String action,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody Map<String, List<String>> payload,
            HttpServletRequest request) {
        if (payload == null || payload.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        List<String> userIds = payload.get("userIds");
        if (userIds == null || userIds.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        if ("grant".equals(action)) {
            return authService.grantAdminRole(userIds, orgId);
        } else if ("revoke".equals(action)) {
            return authService.revokeAdminRole(userIds, orgId);
        } else {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(ResponseCodeEnum.INVALID_PARAMETERS.getMessage())
                    .build();
        }
    }

    @GravityLog
    @PostMapping(value = "/management/resources-privilege")
    public ResponseResult<?> grantOrRevokeResourcesPrivilege(
            @NotBlank(message = "action can not be blank") @RequestParam String action,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody Map<String, List<String>> payload,
            HttpServletRequest request) {
        if (payload == null || payload.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        List<String> userIds = payload.get("userIds");
        List<String> resourceIds = payload.get("resourceIds");
        if (userIds == null || userIds.isEmpty() || resourceIds == null || resourceIds.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        if ("grant".equals(action)) {
            return authService.grantResourcesPrivilege(userIds, resourceIds, orgId);
        } else if ("revoke".equals(action)) {
            return authService.revokeResourcesPrivilege(userIds, resourceIds, orgId);
        } else {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(ResponseCodeEnum.INVALID_PARAMETERS.getMessage())
                    .build();
        }
    }

    @GravityLog
    @GetMapping(value = "/get-authorized-resources")
    public ResponseResult<?> getAuthorizedResources(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @NotBlank(message = "userId can not be blank") @RequestParam String userId,
            HttpServletRequest request) {
        return authService.getAuthorizedResources(userId, orgId);
    }

    @GravityLog
    @PostMapping(value = "/check-resource-permission")
    public ResponseResult<?> checkResourcePermission(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @NotBlank(message = "userId can not be blank") @RequestParam String userId,
            @RequestBody Map<String, List<String>> payload,
            HttpServletRequest request) {
        if (payload == null || payload.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .data(true)
                    .build();
        }
        List<String> resourceIds = payload.get("resourceIds");
        if (resourceIds == null || resourceIds.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .data(true)
                    .build();
        }
        return authService.checkResourcePermission(userId, orgId, resourceIds);
    }

    @GravityLog
    @PostMapping(value = "/check-bo-permission")
    public ResponseResult<?> checkBOPermission(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @NotBlank(message = "userId can not be blank") @RequestParam String userId,
            @RequestBody Map<String, List<String>> payload,
            HttpServletRequest request) {
        if (payload == null || payload.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .data(true)
                    .build();
        }
        List<String> assetIds = payload.get("assetIds");
        if (assetIds == null || assetIds.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .data(true)
                    .build();
        }
        return authService.checkBOPermission(userId, orgId, assetIds);
    }
}
