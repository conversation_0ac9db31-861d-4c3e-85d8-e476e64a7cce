package com.envision.gravity.common.vo.category;

import javax.validation.constraints.NotBlank;

import java.util.Objects;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/02/20 10:32 @Description: */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategoryReq {

    @NotBlank(message = "categoryId can not blank")
    private String categoryId;

    private String categoryDisplayName;
    private String createdUser;
    private String modifiedUser;

    public boolean metaEquals(CategoryReq categoryReq) {
        return Objects.equals(categoryId, categoryReq.getCategoryId())
                && Objects.equals(createdUser, categoryReq.getCreatedUser())
                && Objects.equals(modifiedUser, categoryReq.getModifiedUser())
                && Objects.equals(categoryDisplayName, categoryReq.getCategoryDisplayName());
    }
}
