package com.envision.gravity.flink.streaming.bo.sync;

import java.util.*;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025/3/3
 * @description
 */
class SendSyncEventsTest {
    private static final String TOPIC_NAME = "GRAVITY_BO_SYNC_TOPIC";
    private static final String BOOTSTRAP_SERVERS = "*************:9092";
    private static final String ORG_ID = "o17406396022821251";
    private static final String SYNC_ID = "gravity_sync_test_id";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @NotNull
    private static KafkaProducer<String, String> getKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        props.put(
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        props.put(
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");

        return new KafkaProducer<>(props);
    }

    @Test
    void sendBusinessObject() {
        try (KafkaProducer<String, String> producer = getKafkaProducer()) {
            String assetId = "gravity_sync_retry_test_asset00011";
            String systemId = "gravity_sync_retry_test_asset00011";
            String modelId = "gravity_sync_test_model";

            Map<String, Object> payload = new HashMap<>();
            Map<String, Object> businessObject = new HashMap<>();
            businessObject.put("assetId", assetId);
            businessObject.put("modelIds", Collections.singletonList(modelId));
            businessObject.put(
                    "name",
                    new HashMap<String, String>() {
                        private static final long serialVersionUID = -3596881793907248286L;

                        {
                            put("default", assetId);
                        }
                    });
            businessObject.put(
                    "tags",
                    new HashMap<String, String>() {
                        private static final long serialVersionUID = 6512525799314395015L;

                        {
                            put("k1", "v1");
                            put("k2", "v2");
                        }
                    });
            businessObject.put("systemId", systemId);
            businessObject.put("createdUser", "");
            businessObject.put("modifiedUser", "");
            payload.put("businessObject", businessObject);

            Map<String, Object> event = new HashMap<>();
            event.put("tsMs", System.currentTimeMillis());
            event.put("op", "UPSERT");
            event.put("source", "BO");
            event.put("forceUpdate", false);
            event.put("syncModelFirst", true);
            event.put("orgId", ORG_ID);
            event.put("payload", payload);
            event.put("version", "1.0");

            String value;
            try {
                value = OBJECT_MAPPER.writeValueAsString(event);
            } catch (Exception e) {
                System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                return;
            }

            List<Header> headers =
                    Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

            ProducerRecord<String, String> record =
                    new ProducerRecord<>(TOPIC_NAME, null, assetId, value, headers);

            producer.send(
                    record,
                    (metadata, exception) -> {
                        if (exception != null) {
                            System.err.println("Failed to send message: " + exception.getMessage());
                        } else {
                            System.out.println(
                                    "Message sent successfully! Topic: "
                                            + metadata.topic()
                                            + ", Partition: "
                                            + metadata.partition()
                                            + ", Offset: "
                                            + metadata.offset());
                        }
                    });
        }
    }

    @Test
    void sendBOs() {
        try (KafkaProducer<String, String> producer = getKafkaProducer()) {
            for (int i = 0; i < 1; i++) {
                String assetId = "gravity_sync_test_asset00011";
                String systemId = "gravity_sync_test_asset00011";
                String modelId = "kzg001";

                Map<String, Object> payload = new HashMap<>();
                Map<String, Object> businessObject = new HashMap<>();
                businessObject.put("assetId", assetId);
                businessObject.put("modelIds", Collections.singletonList(modelId));
                businessObject.put(
                        "name",
                        new HashMap<String, String>() {
                            private static final long serialVersionUID = -3596881793907248286L;

                            {
                                put("default", assetId);
                            }
                        });
                businessObject.put(
                        "tags",
                        new HashMap<String, String>() {
                            private static final long serialVersionUID = 6512525799314395015L;

                            {
                                put("k1", "v1");
                                put("k2", "v2");
                            }
                        });
                businessObject.put("systemId", systemId);
                businessObject.put("createdUser", "");
                businessObject.put("modifiedUser", "");
                payload.put("businessObject", businessObject);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "UPSERT");
                event.put("source", "BO");
                event.put("forceUpdate", false);
                event.put("syncModelFirst", true);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, assetId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }

            for (int i = 0; i < 2; i++) {
                String assetId = "gravity_sync_test_asset00011";
                String systemId = "gravity_sync_test_asset00011";

                Map<String, Object> payload = new HashMap<>();

                Map<String, Object> dataObject = new HashMap<>();
                dataObject.put("systemId", systemId);
                dataObject.put("systemDisplayName", new HashMap<>());
                dataObject.put(
                        "attributes",
                        new HashMap<String, String>() {
                            private static final long serialVersionUID = 5153075866112918718L;

                            {
                                put("generic_point_string_test001_1", "value1");
                            }
                        });
                payload.put("dataObject", dataObject);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "UPSERT");
                event.put("source", "BO");
                event.put("forceUpdate", false);
                event.put("syncModelFirst", true);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, assetId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }

            for (int i = 0; i < 3; i++) {
                String assetId = "gravity_sync_test_asset00012";
                String graphId = "gravity_sync_test_graph0003";

                Map<String, Object> payload = new HashMap<>();

                Map<String, Object> businessObjectRelation = new HashMap<>();
                businessObjectRelation.put("assetId", assetId);

                List<Map<String, Object>> relations = new ArrayList<>();

                Map<String, Object> relation1 = new HashMap<>();
                relation1.put("graphId", graphId);
                relation1.put("assetPath", Arrays.asList("a", "b", assetId));
                relation1.put("order", 0);
                relation1.put("edgeType", "Gravity_Asset_Relationship");
                relation1.put("tree", true);
                relations.add(relation1);

                //                Map<String, Object> relation2 = new HashMap<>();
                //                relation2.put("graphId", graphId);
                //                relation2.put("assetPath", Arrays.asList("e", assetId));
                //                relation2.put("order", 0);
                //                relation2.put("edgeType", "Gravity_Asset_Relationship");
                //                relation2.put("tree", true);
                //                relations.add(relation2);

                businessObjectRelation.put("relation", relations);

                payload.put("businessObjectRelation", businessObjectRelation);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "UPSERT");
                event.put("source", "BO");
                event.put("forceUpdate", false);
                event.put("syncModelFirst", true);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, assetId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }
        }
    }

    @Test
    void sendBORelations() {
        try (KafkaProducer<String, String> producer = getKafkaProducer()) {
            for (int i = 0; i < 4; i++) {
                String graphId = "gravity_sync_test_graph_xxx0009";

                Map<String, String> name = new HashMap<>();
                name.put("default", "gravity_sync_test_graph_xxx0009");

                Map<String, String> tags = new HashMap<>();
                tags.put("k1", "v1");
                tags.put("k2", "v2");

                Map<String, Object> payload = new HashMap<>();
                payload.put("graphId", graphId);
                payload.put("name", name);
                payload.put("tags", tags);
                payload.put("tree", true);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "UPSERT");
                event.put("source", "BO_RELATION");
                event.put("forceUpdate", true);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, graphId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }

            for (int i = 0; i < 1; i++) {
                String graphId = "gravity_sync_test_graph_xxx0009";

                Map<String, String> name = new HashMap<>();
                name.put("default", "gravity_sync_test_graph_xxx0009");

                Map<String, String> tags = new HashMap<>();
                tags.put("k1", "v1");
                tags.put("k2", "v2");

                Map<String, Object> payload = new HashMap<>();
                payload.put("graphId", graphId);
                payload.put("name", name);
                payload.put("tags", tags);
                payload.put("tree", true);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "DELETE");
                event.put("source", "BO_RELATION");
                event.put("forceUpdate", true);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, graphId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }

            for (int i = 0; i < 5; i++) {
                String graphId = "gravity_sync_test_graph_xxx0009";

                Map<String, String> name = new HashMap<>();
                name.put("default", "gravity_sync_test_graph_xxx0009");

                Map<String, String> tags = new HashMap<>();
                tags.put("k1", "v1");
                tags.put("k2", "v2");

                Map<String, Object> payload = new HashMap<>();
                payload.put("graphId", graphId);
                payload.put("name", name);
                payload.put("tags", tags);
                payload.put("tree", true);

                Map<String, Object> event = new HashMap<>();
                event.put("tsMs", System.currentTimeMillis());
                event.put("op", "UPSERT");
                event.put("source", "BO_RELATION");
                event.put("forceUpdate", false);
                event.put("orgId", ORG_ID);
                event.put("payload", payload);
                event.put("version", "1.0");

                String value;
                try {
                    value = OBJECT_MAPPER.writeValueAsString(event);
                } catch (Exception e) {
                    System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                    return;
                }

                List<Header> headers =
                        Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

                ProducerRecord<String, String> record =
                        new ProducerRecord<>(TOPIC_NAME, null, graphId, value, headers);

                producer.send(
                        record,
                        (metadata, exception) -> {
                            if (exception != null) {
                                System.err.println(
                                        "Failed to send message: " + exception.getMessage());
                            } else {
                                System.out.println(
                                        "Message sent successfully! Topic: "
                                                + metadata.topic()
                                                + ", Partition: "
                                                + metadata.partition()
                                                + ", Offset: "
                                                + metadata.offset());
                            }
                        });
            }
        }
    }

    @Test
    void dataTypeTest() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> event = new HashMap<>();
        String[] array = {"1,2,3,4,5", "string"};
        event.put("value", objectMapper.writeValueAsString(array));
        System.out.println(objectMapper.writeValueAsString(event));
    }

    @Test
    void sendDeleteBOs() {
        try (KafkaProducer<String, String> producer = getKafkaProducer()) {
            String assetId = "jennytestDev0030";

            Map<String, Object> payload = new HashMap<>();
            Map<String, Object> businessObject = new HashMap<>();
            businessObject.put("assetId", assetId);
            payload.put("businessObject", businessObject);

            Map<String, Object> event = new HashMap<>();
            event.put("tsMs", System.currentTimeMillis());
            event.put("op", "DELETE");
            event.put("source", "BO");
            event.put("orgId", ORG_ID);
            event.put("payload", payload);
            event.put("version", "1.0");

            String value;
            try {
                value = OBJECT_MAPPER.writeValueAsString(event);
            } catch (Exception e) {
                System.err.println("Failed to serialize event to JSON: " + e.getMessage());
                return;
            }

            List<Header> headers =
                    Collections.singletonList(new RecordHeader("syncId", SYNC_ID.getBytes()));

            ProducerRecord<String, String> record =
                    new ProducerRecord<>(TOPIC_NAME, null, assetId, value, headers);

            producer.send(
                    record,
                    (metadata, exception) -> {
                        if (exception != null) {
                            System.err.println("Failed to send message: " + exception.getMessage());
                        } else {
                            System.out.println(
                                    "Message sent successfully! Topic: "
                                            + metadata.topic()
                                            + ", Partition: "
                                            + metadata.partition()
                                            + ", Offset: "
                                            + metadata.offset());
                        }
                    });
        }
    }
}
