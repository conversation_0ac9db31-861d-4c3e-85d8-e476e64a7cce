package com.envision.gravity.flink.streaming.calculate.aspect.source;

import com.envision.gravity.flink.streaming.calculate.cache.CacheFactory;
import com.envision.gravity.flink.streaming.calculate.dto.calc.BaseCalcPropertyMeta;
import com.envision.gravity.flink.streaming.calculate.dto.calc.CalcPropertyMeta;
import com.envision.gravity.flink.streaming.calculate.dto.calc.PropertyId;
import com.envision.gravity.flink.streaming.calculate.dto.meta.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.meta.ModelMetaQueryHandler;
import com.envision.gravity.flink.streaming.calculate.utils.UpstreamCalcUtils;

import java.util.*;


import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * GetCalcJobInfoTimer (定时获取Job信息 - AspectCalc版本)
 *
 * <p>功能： 1. 从实时元数据获取Job信息 2. 返回CalcJobMetaInfo而不是TblCalcJobInfo 3. 支持多Schema查询 4. 集成缓存机制
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class GetCalcJobInfoTimer extends RichSourceFunction<CalcJobMetaInfo>
        implements CheckpointedFunction {

    private static final Logger logger = LoggerFactory.getLogger(GetCalcJobInfoTimer.class);
    private static final String TABLE_NAME = "tbl_upstream_rule";

    private final long checkIntervalMs;

    private volatile boolean isRunning = true;
    private transient ModelMetaQueryHandler modelMetaQueryHandler;
    private transient ValueState<Long> lastCheckTime;

    public GetCalcJobInfoTimer(long checkIntervalMs) {
        this.checkIntervalMs = checkIntervalMs;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
        logger.info(
                "GetCalcJobInfoTimer opened with interval: {}ms for AspectCalc", checkIntervalMs);
    }

    @Override
    public void run(SourceContext<CalcJobMetaInfo> ctx) throws Exception {
        while (isRunning) {
            try {
                // ✅ 从实时元数据获取Job信息
                List<CalcJobMetaInfo> calcJobs = fetchJobs();

                synchronized (ctx.getCheckpointLock()) {
                    for (CalcJobMetaInfo jobInfo : calcJobs) {
                        logger.info("Emitting calc job meta info: {}", jobInfo.getJobId());
                        ctx.collect(jobInfo);
                    }

                    if (lastCheckTime != null) {
                        lastCheckTime.update(System.currentTimeMillis());
                    }
                }

                // 使用配置的检查间隔
                Thread.sleep(checkIntervalMs);

            } catch (Exception e) {
                logger.error("Error in GetCalcJobInfoTimer: {}", e.getMessage(), e);
                Thread.sleep(5000);
            }
        }
    }

    /** ✅ 从实时元数据获取Job信息 */
    public List<CalcJobMetaInfo> fetchJobs() throws Exception {
        List<CalcJobMetaInfo> allCalcJobs = new ArrayList<>();
        List<String> schemas = this.modelMetaQueryHandler.getSchemas();
        logger.info("Found schemas: {}", schemas);

        for (String schema : schemas) {
            if (this.modelMetaQueryHandler.isTableExists(schema, TABLE_NAME)) {
                List<CalcJobMetaInfo> schemaJobs =
                        UpstreamCalcUtils.queryTblUpstreamRuleWithPagination(
                                schema, this::buildCalcJobMetaInfo);
                allCalcJobs.addAll(schemaJobs);
            } else {
                logger.info("Table [{}] not exist in database [{}]", TABLE_NAME, schema);
            }

            if (logger.isDebugEnabled()) {
                CacheFactory.getCalcPrefCache().formatCacheContent(schema);
            }
        }

        logger.info("Fetched {} calc jobs from all schemas", allCalcJobs.size());
        return allCalcJobs;
    }

    /** ✅ 构建CalcJobMetaInfo */
    public List<CalcJobMetaInfo> buildCalcJobMetaInfo(
            String orgId, Map<PropertyId, List<BaseCalcPropertyMeta>> baseTargetPropertyInfoMap) {

        List<CalcJobMetaInfo> calcJobMetaInfoList = new ArrayList<>();

        for (Map.Entry<PropertyId, List<BaseCalcPropertyMeta>> targetPropEntry :
                baseTargetPropertyInfoMap.entrySet()) {
            PropertyId propertyId = targetPropEntry.getKey();
            String targetCompId = propertyId.getCompId();
            String targetPrefId = propertyId.getPrefId();

            Set<String> targetModelIds =
                    CacheFactory.getCalcPrefCache()
                            .getModelByTargetPref(orgId, targetCompId, targetPrefId);

            for (BaseCalcPropertyMeta baseRuleInfo : targetPropEntry.getValue()) {
                Optional<CalcPropertyMeta> calcPropMeta =
                        CacheFactory.getCalcPrefCache()
                                .getByTargetPref(
                                        orgId,
                                        targetCompId,
                                        targetPrefId,
                                        baseRuleInfo.getSrcCategoryId());

                if (!calcPropMeta.isPresent()) {
                    logger.error(
                            "Failed to build calc job, target property calc meta not found, "
                                    + "org: [{}], prefRuleId: [{}], targetCompId: [{}], targetPrefId: [{}], srcCategoryId: [{}]",
                            orgId,
                            baseRuleInfo.getPrefRuleId(),
                            targetCompId,
                            targetPrefId,
                            baseRuleInfo.getSrcCategoryId());
                    continue;
                }

                String jobId = baseRuleInfo.getPrefRuleId() + "-" + System.currentTimeMillis();

                CalcJobMetaInfo calcJobMetaInfo =
                        CalcJobMetaInfo.builder()
                                .orgId(orgId)
                                .jobId(jobId)
                                .ruleInfo(baseRuleInfo)
                                .targetModelIds(new ArrayList<>(targetModelIds))
                                .targetPropertyMeta(calcPropMeta.get())
                                .build();

                calcJobMetaInfoList.add(calcJobMetaInfo);

                logger.debug("Built calc job meta info: {} for org: {}", jobId, orgId);
            }
        }

        return calcJobMetaInfoList;
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        if (lastCheckTime != null) {
            // 保存最后检查时间
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        lastCheckTime =
                context.getOperatorStateStore()
                        .getState(new ValueStateDescriptor<>("last-check-time", Long.class));
    }
}
