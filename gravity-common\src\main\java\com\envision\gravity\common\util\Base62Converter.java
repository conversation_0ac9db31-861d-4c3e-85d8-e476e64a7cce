package com.envision.gravity.common.util;

import com.envision.gravity.common.exception.InternalException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2024/2/20
 * @description
 */
public class Base62Converter {
    private static final String BASE62_DIGITS =
            "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final int BASE = 62;
    private static final LocalDate BASE_DATE = LocalDate.of(2024, 1, 1);
    private static final LocalDateTime BASE_DATE_TIME = LocalDateTime.of(2024, 1, 1, 0, 0);

    public static String convertDecimalToBase62(long decimalNumber, int length) {
        return convertToBase62(decimalNumber, length);
    }

    public static String convertDateToBase62() {
        LocalDate currentDate = LocalDate.now();
        long daysDiff = ChronoUnit.DAYS.between(BASE_DATE, currentDate);

        return convertToBase62(daysDiff, 3);
    }

    public static String convertDateTimeToBase62() {
        LocalDateTime currentLocalDateTime = LocalDateTime.now();
        long minutesDiff = ChronoUnit.MINUTES.between(BASE_DATE_TIME, currentLocalDateTime);
        return convertToBase62(minutesDiff, 4);
    }

    public static String convertDateTimeToBase62(LocalDateTime localDateTime) {
        long minutesDiff = ChronoUnit.MINUTES.between(BASE_DATE_TIME, localDateTime);
        return convertToBase62(minutesDiff, 4);
    }

    private static String convertToBase62(long number, int length) {
        StringBuilder base62String = new StringBuilder();

        while (number > 0) {
            int remainder = (int) (number % BASE);
            base62String.insert(0, BASE62_DIGITS.charAt(remainder));
            number /= BASE;
        }

        if (base62String.length() > length) {
            throw new InternalException("Exceeding digit limit!");
        }

        while (base62String.length() < length) {
            base62String.insert(0, BASE62_DIGITS.charAt(0));
        }

        return base62String.toString();
    }
}
