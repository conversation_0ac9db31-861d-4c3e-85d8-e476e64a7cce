package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReCalcJobPgWriter extends ProcessFunction<TblCalcJobInfo, TblCalcJobInfo> {
    private static final long serialVersionUID = 6400036132115890802L;
    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobPgWriter.class);

    private transient SqlSessionFactory sqlSessionFactory;
    private transient TblCalcJobInfoMapper mapper;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();

        try (SqlSession session = sqlSessionFactory.openSession()) {
            this.mapper = session.getMapper(TblCalcJobInfoMapper.class);
        }

        logger.info("ReCalcJobPgWriter initialized successfully");
    }

    @Override
    public void processElement(
            TblCalcJobInfo tblCalcJobInfo,
            ProcessFunction<TblCalcJobInfo, TblCalcJobInfo>.Context context,
            Collector<TblCalcJobInfo> collector)
            throws Exception {

        if (tblCalcJobInfo == null) {
            logger.warn("Received null TblCalcJobInfo, skipping");
            return;
        }

        try (SqlSession session = sqlSessionFactory.openSession()) {
            TblCalcJobInfoMapper sessionMapper = session.getMapper(TblCalcJobInfoMapper.class);

            // 插入作业信息到数据库
            int result = sessionMapper.insert(tblCalcJobInfo);
            session.commit();

            if (result <= 0) {
                throw new RuntimeException("Failed to insert job info: insert returned " + result);
            }

            logger.info(
                    "Successfully inserted ReCalc job: jobId={}, prefRuleId={}",
                    tblCalcJobInfo.getJobId(),
                    tblCalcJobInfo.getPrefRuleId());

            // 传递给下一个算子
            collector.collect(tblCalcJobInfo);

        } catch (Exception e) {
            logger.error(
                    "Failed to insert ReCalc job: jobId={}, prefRuleId={}, error={}",
                    tblCalcJobInfo.getJobId(),
                    tblCalcJobInfo.getPrefRuleId(),
                    e.getMessage(),
                    e);
            throw e;
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        logger.info("ReCalcJobPgWriter closed");
    }
}
