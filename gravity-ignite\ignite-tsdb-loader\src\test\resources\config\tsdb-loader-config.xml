<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!--
    Ignite configuration with all defaults and enabled p2p deployment and enabled events.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">
    <bean class="org.apache.ignite.configuration.IgniteConfiguration">
        <property name="sqlConfiguration">
            <bean class="org.apache.ignite.configuration.SqlConfiguration">
                <property name="sqlSchemas">
                    <list>
                        <value>o16227953714301175</value>
                        <value>TSDB</value>
                    </list>
                </property>

                <property name="queryEnginesConfiguration">
                    <list>
                        <bean class="org.apache.ignite.indexing.IndexingQueryEngineConfiguration">
                            <property name="default" value="true"/>
                        </bean>
<!--                        <bean class="org.apache.ignite.calcite.CalciteQueryEngineConfiguration">-->
<!--                            <property name="default" value="false"/>-->
<!--                        </bean>-->
                    </list>
                </property>
            </bean>
        </property>

        <property name="cacheConfiguration">
            <bean class="org.apache.ignite.configuration.CacheConfiguration">
                <property name="dataRegionName" value="InMemory_Region"/>
                <property name="sqlSchema" value="TSDB"/>
                <property name="name" value="SQL_TSDB_TBL_TSDB_METRIC"/>
                <property name="cacheMode" value="REPLICATED"/>
                <property name="atomicityMode" value="TRANSACTIONAL"/>
                <property name="cacheStoreFactory">
                    <bean class="com.envision.gravity.ignite.tsdb.loader.TSDBCacheStoreFactory">
                        <property name="types">
                            <list>
                                <bean class="org.apache.ignite.cache.store.jdbc.JdbcType">
                                    <property name="cacheName" value="SQL_TSDB_TBL_TSDB_METRIC"/>
                                    <property name="keyType" value="TSDB.TBL_TSDB_METRIC_KEY"/>
                                    <property name="valueType" value="TSDB.TBL_TSDB_METRIC_VALUE"/>
                                    <property name="keyFields">
                                        <list>
                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="REQUESTID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="requestId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="ORGID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="orgId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="MDMID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="mdmId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TIMEZONE"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="timeZone"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="KPI"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="kpi"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TS"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.BIGINT"/>
                                                </property>
                                                <property name="javaFieldName" value="ts"/>
                                                <property name="javaFieldType" value="java.lang.Long"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="LOCAL_TIME"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="localTime"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TIMEGROUP"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="timeGroup"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="AGGTYPE"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="aggType"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>
                                        </list>
                                    </property>
                                    <property name="valueFields">
                                        <list>
                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="REQUESTID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="requestId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="ORGID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="orgId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="MDMID"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="mdmId"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TIMEZONE"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="timeZone"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="KPI"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="kpi"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TS"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.BIGINT"/>
                                                </property>
                                                <property name="javaFieldName" value="ts"/>
                                                <property name="javaFieldType" value="java.lang.Long"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="LOCAL_TIME"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="localTime"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="TIMEGROUP"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="timeGroup"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="AGGTYPE"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="aggType"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="QUALITY"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.INTEGER"/>
                                                </property>
                                                <property name="javaFieldName" value="quality"/>
                                                <property name="javaFieldType" value="java.lang.Integer"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="VALUEINT"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.INTEGER"/>
                                                </property>
                                                <property name="javaFieldName" value="valueInt"/>
                                                <property name="javaFieldType" value="java.lang.Integer"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="VALUELONG"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.BIGINT"/>
                                                </property>
                                                <property name="javaFieldName" value="valueLong"/>
                                                <property name="javaFieldType" value="java.lang.Long"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="VALUEDOUBLE"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.DOUBLE"/>
                                                </property>
                                                <property name="javaFieldName" value="valueDouble"/>
                                                <property name="javaFieldType" value="java.lang.Double"/>
                                            </bean>

                                            <bean class="org.apache.ignite.cache.store.jdbc.JdbcTypeField">
                                                <property name="databaseFieldName" value="VALUESTRING"/>
                                                <property name="databaseFieldType">
                                                    <util:constant static-field="java.sql.Types.VARCHAR"/>
                                                </property>
                                                <property name="javaFieldName" value="valueString"/>
                                                <property name="javaFieldType" value="java.lang.String"/>
                                            </bean>
                                        </list>
                                    </property>
                                </bean>
                            </list>
                        </property>

                        <!-- influxdb config -->
                        <!-- property name="connectionURL" value="influxdb_cluster://10.65.101.234:8086,10.65.101.235:8086,10.65.101.236:8086?readTimeout=5000"/>
                        <property name="userName" value="admin"/>
                        <property name="password" value="Envisi0n1234!"/>
                        <property name="clusterMetaURL" value="10.65.101.234:10800"/>
                        <property name="clusterMetaUserName" value="ignite"/>
                        <property name="clusterMetaPassword" value="ignite"/>
                        <property name="clusterReplicaFactor" value="2"/>
                        <property name="clusterShardingRule" value="SHARDING_BY_MEASUREMENT" /-->

                        <property name="connectionURL" value="cds://ors-core-unified-service.apaas-pdc1.eniot.io"/>
                        <property name="userName" value="admin"/>
                        <property name="password" value="Envisi0n1234!"/>
                        <property name="clusterMetaURL" value="10.65.64.62:10800"/>
                        <property name="clusterMetaUserName" value="ignite"/>
                        <property name="clusterMetaPassword" value="ignite"/>
                        <property name="clusterReplicaFactor" value="2"/>
                        <property name="clusterShardingRule" value="SHARDING_BY_MEASUREMENT"/>
                    </bean>
                </property>
                <property name="readThrough" value="true"/>
                <property name="writeThrough" value="false"/>

                <!-- Configure query entities -->
                <property name="queryEntities">
                    <list>
                        <bean class="org.apache.ignite.cache.QueryEntity">
                            <property name="tableName" value="TBL_TSDB_METRIC"/>
                            <!-- Setting  the type of the key -->
                            <property name="keyType" value="TSDB.TBL_TSDB_METRIC_KEY"/>

                            <property name="keyFields">
                                <set>
                                    <value>REQUESTID</value>
                                    <value>ORGID</value>
                                    <value>MDMID</value>
                                    <value>TIMEZONE</value>
                                    <value>KPI</value>
                                    <value>TS</value>
                                    <value>LOCAL_TIME</value>
                                    <value>TIMEGROUP</value>
                                    <value>AGGTYPE</value>
                                </set>
                            </property>

                            <!-- Setting type of the value -->
                            <property name="valueType" value="TSDB.TBL_TSDB_METRIC_VALUE"/>

                            <!-- Defining fields that will be either indexed or queryable.
                                 Indexed fields are added to the 'indexes' list below.-->
                            <property name="fields">
                                <map>
                                    <entry key="REQUESTID" value="java.lang.String"/>
                                    <entry key="ORGID" value="java.lang.String"/>
                                    <entry key="MDMID" value="java.lang.String"/>
                                    <entry key="TIMEZONE" value="java.lang.String"/>
                                    <entry key="KPI" value="java.lang.String"/>
                                    <entry key="TS" value="java.lang.Long"/>
                                    <entry key="LOCAL_TIME" value="java.lang.String"/>
                                    <entry key="TIMEGROUP" value="java.lang.String"/>
                                    <entry key="AGGTYPE" value="java.lang.String"/>
                                    <entry key="QUALITY" value="java.lang.Integer"/>
                                    <entry key="VALUEINT" value="java.lang.Integer"/>
                                    <entry key="VALUELONG" value="java.lang.Long"/>
                                    <entry key="VALUEDOUBLE" value="java.lang.Double"/>
                                    <entry key="VALUESTRING" value="java.lang.String"/>
                                </map>
                            </property>

                            <!-- Defining indexed fields.-->
<!--                            <property name="indexes">-->
<!--                                <list>-->
<!--                                    &lt;!&ndash; Single field (aka. column) index &ndash;&gt;-->
<!--                                    <bean class="org.apache.ignite.cache.QueryIndex">-->
<!--                                        <constructor-arg value="name"/>-->
<!--                                    </bean>-->
<!--                                    &lt;!&ndash; Group index. &ndash;&gt;-->
<!--                                    <bean class="org.apache.ignite.cache.QueryIndex">-->
<!--                                        <constructor-arg>-->
<!--                                            <list>-->
<!--                                                <value>id</value>-->
<!--                                                <value>salary</value>-->
<!--                                            </list>-->
<!--                                        </constructor-arg>-->
<!--                                        <constructor-arg value="SORTED"/>-->
<!--                                    </bean>-->
<!--                                </list>-->
<!--                            </property>-->
                        </bean>
                    </list>
                </property>
            </bean>
        </property>

        <!-- Enabling the peer-class loading feature. -->
        <property name="peerClassLoadingEnabled" value="true"/>

<!--        <property name="serviceConfiguration">-->
<!--            <list>-->
<!--                &lt;!&ndash;-->
<!--                  Setting up IDService. The service will be deployed automatically according to the configuration-->
<!--                  below.-->
<!--                  &ndash;&gt;-->
<!--                <bean class="org.apache.ignite.services.ServiceConfiguration">-->
<!--                    &lt;!&ndash; Unique service name &ndash;&gt;-->
<!--                    <property name="name" value="TSDBMetricLoaderService"/>-->

<!--                    &lt;!&ndash; Service implementation's class &ndash;&gt;-->
<!--                    <property name="service">-->
<!--                        <bean class="com.envision.gravity.ignite.tsdb.loader.TSDBLoaderServiceImpl"/>-->
<!--                    </property>-->

<!--                    &lt;!&ndash; Only one instance of the service will be deployed cluster wide &ndash;&gt;-->
<!--                    <property name="totalCount" value="3"/>-->

<!--                    &lt;!&ndash; Only one instance of the service can be deployed on a single node. &ndash;&gt;-->
<!--                    <property name="maxPerNodeCount" value="1"/>-->
<!--                </bean>-->
<!--            </list>-->
<!--        </property>-->

<!--        <property name="dataStorageConfiguration">-->
<!--            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">-->
<!--                <property name="defaultDataRegionConfiguration">-->
<!--                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">-->
<!--                        <property name="persistenceEnabled" value="false"/>-->
<!--                    </bean>-->
<!--                </property>-->
<!--            </bean>-->
<!--        </property>-->

        <property name="dataStorageConfiguration">
            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">
                <property name="defaultDataRegionConfiguration">
                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                        <property name="name" value="Default_Region"/>
                        <property name="initialSize" value="#{ 128 * 1024L * 1024}"/>
                        <property name="maxSize" value="#{ 1 * 1024L * 1024 * 1024}"/>
                        <property name="checkpointPageBufferSize" value="#{ 64 * 1024L * 1024}"/>

                        <property name="persistenceEnabled" value="true"/>
                        <property name="metricsEnabled" value="true"/>
                    </bean>
                </property>

                <property name="dataRegionConfigurations">
                    <list>
                        <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                            <property name="name" value="InMemory_Region"/>
                            <property name="initialSize" value="#{ 128 * 1024L * 1024}"/>
                            <property name="maxSize" value="#{1 * 1024L * 1024 * 1024}"/>

                            <property name="persistenceEnabled" value="false"/>
                            <property name="metricsEnabled" value="false"/>
                            <property name="pageEvictionMode" value="RANDOM_2_LRU"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>

        <property name="discoverySpi">
            <bean class="org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi">
                <property name="ipFinder">
                    <bean class="org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder">
                        <property name="addresses">
                            <list>
                                <!-- In distributed environment, replace with actual host IP address. -->
                                <value>127.0.0.1:47500..47509</value>
                            </list>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>
    </bean>
</beans>