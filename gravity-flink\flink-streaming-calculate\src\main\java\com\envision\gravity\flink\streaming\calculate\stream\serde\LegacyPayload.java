package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.Map;


import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;

@Data
@NoArgsConstructor
@TypeInfo(PojoFactory.LegacyPayloadType.class)
public class LegacyPayload {
    private Map<String, Object> points;
    private Long time;
    private String assetId;
    // private String systemId;
}
