package com.envision.gravity.common.vo.search.asset;


import org.apache.commons.lang3.tuple.Pair;

public class SearchAssetJoinModelParam {

    private JoinModelTagsOpType tagsOpType;

    private Pair<String, String> tagKv;

    public SearchAssetJoinModelParam(JoinModelTagsOpType tagsOpType, String tagKey, String tagVal) {
        this.tagsOpType = tagsOpType;
        this.tagKv = Pair.of(tagKey, tagVal);
    }

    public JoinModelTagsOpType getTagsOpType() {
        return tagsOpType;
    }

    public String getTagKey() {
        return this.tagKv.getLeft();
    }

    public String getTagVal() {
        return this.tagKv.getRight();
    }
}
