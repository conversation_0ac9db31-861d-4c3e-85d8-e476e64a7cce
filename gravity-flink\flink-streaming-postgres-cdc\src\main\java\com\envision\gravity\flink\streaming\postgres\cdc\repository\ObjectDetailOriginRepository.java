package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.ObjectDetailOriginMapper;

import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class ObjectDetailOriginRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public ObjectDetailOriginRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public int batchDeleteByPrimaryKey(
            String schemaName, List<String> assetIdList, List<String> modelIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");
            if (assetIdList.isEmpty() || modelIdList.isEmpty()) {
                return 0;
            }

            ObjectDetailOriginMapper objectDetailOriginMapper =
                    session.getMapper(ObjectDetailOriginMapper.class);
            return objectDetailOriginMapper.batchDeleteByPrimaryKey(
                    schemaName, assetIdList, modelIdList);
        } catch (Exception e) {
            log.error("Batch delete object detail origin by assetIdList and modelIdList error.", e);
            throw new GravityRuntimeException(
                    "Batch delete object detail origin by assetIdList and modelIdList error.", e);
        }
    }

    public int batchDeleteByAssetIdList(String schemaName, List<String> assetIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {
            Objects.requireNonNull(schemaName, "Schema name cannot be null.");
            if (assetIdList.isEmpty()) {
                return 0;
            }

            ObjectDetailOriginMapper objectDetailOriginMapper =
                    session.getMapper(ObjectDetailOriginMapper.class);
            return objectDetailOriginMapper.batchDeleteByAssetIdList(schemaName, assetIdList);
        } catch (Exception e) {
            log.error("Batch delete object detail origin by assetIdList error.", e);
            throw new GravityRuntimeException(
                    "Batch delete object detail origin by assetIdList error.", e);
        }
    }
}
