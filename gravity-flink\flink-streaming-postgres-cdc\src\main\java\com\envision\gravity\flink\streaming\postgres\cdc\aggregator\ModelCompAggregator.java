package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblBOModelCompInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblBOModelCompRepository;

import java.util.List;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public class ModelCompAggregator implements Aggregator {

    private final TblBOModelCompRepository tblBOModelCompRepository;

    public ModelCompAggregator(SqlSessionFactory sqlSessionFactory) {
        tblBOModelCompRepository = new TblBOModelCompRepository(sqlSessionFactory);
    }

    @Override
    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        // 2.refresh object detail
        List<String> modelIdList =
                records.stream()
                        .map(record -> ((TblBOModelCompInfo) record.getAfter()).getModelId())
                        .distinct()
                        .collect(Collectors.toList());
        List<String> compIdList =
                records.stream()
                        .map(record -> ((TblBOModelCompInfo) record.getAfter()).getCompId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!modelIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblBOModelCompRepository.selectModelGroupList(
                            schemaName, modelIdList, compIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> filteredModelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                return AggregatedResults.builder()
                        .refreshModelReq(
                                RefreshModelReq.builder()
                                        .updateRefresh(filteredModelIdList)
                                        .build())
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        // 2.refresh object detail
        List<String> modelIdList =
                records.stream()
                        .map(record -> ((TblBOModelCompInfo) record.getBefore()).getModelId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!modelIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblBOModelCompRepository.selectModelGroupListByModelId(schemaName, modelIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> filteredModelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                return AggregatedResults.builder()
                        .refreshModelReq(
                                RefreshModelReq.builder()
                                        .updateRefresh(filteredModelIdList)
                                        .build())
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        return null;
    }
}
