package com.envision.gravity.common.enums;

import java.util.Objects;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description Attribute/Measurement Point/Metric/Service
 */
@Getter
public enum PrefType {
    ATTRIBUTE("ATTRIBUTE", (byte) 0),
    MEASUREPOINT("MEASUREPOINT", (byte) 1),
    METRIC("METRIC", (byte) 2),
    COMMAND("COMMAND", (byte) 3);

    private static final PrefType[] VALUE_LIST = new PrefType[4];

    static {
        for (PrefType type : PrefType.values()) {
            VALUE_LIST[type.id] = type;
        }
    }

    public static PrefType fromId(byte id) {
        try {
            return VALUE_LIST[id];
        } catch (Exception e) {
            return null;
        }
    }

    public static PrefType fromName(String name) {
        for (PrefType t : PrefType.values()) {
            if (Objects.equals(t.name, name)) {
                return t;
            }
        }
        return null;
    }

    private final String name;
    private final byte id;

    PrefType(String name, byte id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public byte getId() {
        return id;
    }
}
