package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.category.CategoryReq;
import com.envision.gravity.common.vo.category.QueryCategoryReq;
import com.envision.gravity.common.vo.obj.Pagination;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.repository.CategoryRepository;
import com.envision.gravity.low.level.api.rest.service.CategoryService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.*;

import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.checkCategoryReqDefine;

/** @Author: qi.jiang2 @Date: 2024/02/20 10:23 @Description: */
@Api(tags = "Category")
@Slf4j
@Validated
@RestController
@RequestMapping("/category")
public class CategoryController {

    @Resource private CategoryService categoryService;

    @Resource private CategoryRepository categoryRepository;

    @PostMapping(value = "/batch-add-or-update")
    public ResponseResult<?> batchCreateOrUpdateCategory(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody List<@Valid CategoryReq> categoryReqList,
            HttpServletRequest request) {
        log.debug("Start add or update category, orgId: {}, params: {}", orgId, categoryReqList);

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (categoryReqList.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        checkCategoryReqDefine(categoryReqList, null);

        return categoryService.batchCreateOrUpdateCategory(categoryReqList, orgId, auditHeader);
    }

    @PostMapping
    public ResponseResult<?> queryCategory(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid QueryCategoryReq query,
            HttpServletRequest request) {
        log.info("Start query category, orgId: {}, param: {}", orgId, query);
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        return categoryService.queryCategory(query.getCategoryId(), orgId, language);
    }

    @PostMapping(value = "/list")
    public ResponseResult<?> queryCategoryList(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid Pagination pagination,
            HttpServletRequest request) {

        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        return categoryService.queryCategoryList(pagination, language, orgId);
    }
}
