package com.envision.gravity.common.vo.sync;

import com.envision.gravity.common.vo.obj.Pagination;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuerySyncLogsReq {
    private String status;
    private List<String> boIds;
    private List<String> boTypes;

    @Valid
    @NotNull(message = "pagination can not be blank")
    private Pagination pagination;
}
