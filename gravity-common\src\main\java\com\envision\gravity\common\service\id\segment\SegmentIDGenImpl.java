package com.envision.gravity.common.service.id.segment;

import com.envision.gravity.common.enums.IDType;
import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.common.exception.InternalException;
import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.service.id.IDGen;
import com.envision.gravity.common.service.id.segment.model.IDAlloc;
import com.envision.gravity.common.service.id.segment.model.Segment;
import com.envision.gravity.common.service.id.segment.model.SegmentBuffer;
import com.envision.gravity.common.service.id.segment.repository.IDAllocRepository;
import com.envision.gravity.common.util.Base36Converter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;


import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.perf4j.StopWatch;
import org.perf4j.slf4j.Slf4JStopWatch;

/**
 * <AUTHOR>
 * @date 2024/2/19
 * @description
 */
@Slf4j
public class SegmentIDGenImpl implements IDGen {
    /** The maximum step does not exceed 10,000 */
    private static final int MAX_STEP = 10000;
    /** A segment has a duration of 15 minutes */
    private static final long SEGMENT_DURATION = 15 * 60 * 1000L;

    private final ExecutorService service =
            new ThreadPoolExecutor(
                    5,
                    Integer.MAX_VALUE,
                    60L,
                    TimeUnit.SECONDS,
                    new SynchronousQueue<>(),
                    new UpdateThreadFactory());
    private volatile boolean initOK = false;
    @Getter private Map<String, SegmentBuffer> cache = new ConcurrentHashMap<>();
    @Setter private IDAllocRepository idAllocRepository;

    public static class UpdateThreadFactory implements ThreadFactory {

        private static int threadInitNumber = 0;

        private static synchronized int nextThreadNum() {
            return threadInitNumber++;
        }

        @Override
        public Thread newThread(@NotNull Runnable r) {
            return new Thread(r, "Thread-Segment-Update-" + nextThreadNum());
        }
    }

    private void updateCacheFromDb() {
        log.info("Update cache from db");
        StopWatch sw = new Slf4JStopWatch();
        try {
            List<String> dbIDTypes = idAllocRepository.getAllIDTypes();
            if (dbIDTypes == null || dbIDTypes.isEmpty()) {
                return;
            }

            for (String tag : dbIDTypes) {
                SegmentBuffer buffer = new SegmentBuffer();
                buffer.setKey(tag);
                Segment segment = buffer.getCurrent();
                segment.setValue(new AtomicLong(0));
                segment.setMax(0);
                segment.setStep(0);
                cache.put(tag, buffer);
                log.info("Add tag {} from db to IdCache, SegmentBuffer {}", tag, buffer);
            }

        } catch (Exception e) {
            log.warn("Update cache from db exception!", e);
        } finally {
            sw.stop("updateCacheFromDb");
        }
    }

    @Override
    public String getGeneratedId(final IDType idType) {
        if (!initOK) {
            updateCacheFromDb();
            initOK = true;
        }

        String key = idType.getName();
        int length = idType.getGeneratedLength();
        long maxId = idType.getMaxId();
        if (key == null) {
            throw new InternalException(ResponseCodeEnum.ID_TYPE_NOT_EXISTS.getMessage());
        }

        if (cache.containsKey(key)) {
            SegmentBuffer buffer = cache.get(key);
            if (!buffer.isInitOk()) {
                synchronized (buffer) {
                    if (!buffer.isInitOk()) {
                        try {
                            updateSegmentFromDb(key, buffer.getCurrent());
                            log.info(
                                    "Init buffer. Update key {} {} from db",
                                    key,
                                    buffer.getCurrent());
                            buffer.setInitOk(true);
                        } catch (Exception e) {
                            log.warn("Init buffer {} exception!", buffer.getCurrent(), e);
                        }
                    }
                }
            }
            return getIdFromSegmentBuffer(cache.get(key), length, maxId);
        }

        throw new InternalException(ResponseCodeEnum.ID_TYPE_NOT_EXISTS.getMessage());
    }

    @Override
    public String getIdPrefix(IDType idType) {
        IDAlloc idAlloc = idAllocRepository.getIDAllocByIDType(idType.getName());
        if (idAlloc != null) {
            if (idAlloc.getIdPrefix() != null && !idAlloc.getIdPrefix().equals("null")) {
                return idAlloc.getIdPrefix();
            }
            return Base36Converter.convertDateTimeToBase36(
                    idAlloc.getCreatedTime().toLocalDateTime());
        }
        throw new InternalException(ResponseCodeEnum.ID_TYPE_NOT_EXISTS.getMessage());
    }

    @Override
    public String getIdSuffix(IDType idType) {
        IDAlloc idAlloc = idAllocRepository.getIDAllocByIDType(idType.getName());
        if (idAlloc != null) {
            return idAlloc.getIdSuffix();
        }
        throw new InternalException(ResponseCodeEnum.ID_TYPE_NOT_EXISTS.getMessage());
    }

    public void updateSegmentFromDb(String key, Segment segment) {
        StopWatch sw = new Slf4JStopWatch();
        SegmentBuffer buffer = segment.getBuffer();
        IDAlloc idAlloc;
        if (!buffer.isInitOk()) {
            idAlloc = idAllocRepository.updateSegmentMaxIdAndGetIDAlloc(key);
            buffer.setStep(idAlloc.getStep());
            buffer.setMinStep(idAlloc.getStep());
        } else if (buffer.getUpdateTimestamp() == 0) {
            idAlloc = idAllocRepository.updateSegmentMaxIdAndGetIDAlloc(key);
            buffer.setUpdateTimestamp(System.currentTimeMillis());
            buffer.setStep(idAlloc.getStep());
            buffer.setMinStep(idAlloc.getStep());
        } else {
            long duration = System.currentTimeMillis() - buffer.getUpdateTimestamp();
            int nextStep = buffer.getStep();
            if (duration < SEGMENT_DURATION) {
                if (nextStep * 2 > MAX_STEP) {
                    // do nothing
                } else {
                    nextStep = nextStep * 2;
                }
            } else if (duration < SEGMENT_DURATION * 2) {
                // do nothing with nextStep
            } else {
                nextStep = nextStep / 2 >= buffer.getMinStep() ? nextStep / 2 : nextStep;
            }
            log.info(
                    "key[{}], step[{}], duration[{}mins], nextStep[{}]",
                    key,
                    buffer.getStep(),
                    String.format("%.2f", ((double) duration / (1000 * 60))),
                    nextStep);
            IDAlloc temp = new IDAlloc();
            temp.setIdType(key);
            temp.setStep(nextStep);
            idAlloc = idAllocRepository.updateSegmentMaxIdByCustomStepAndGetIDAlloc(temp);
            buffer.setUpdateTimestamp(System.currentTimeMillis());
            buffer.setStep(nextStep);
            buffer.setMinStep(idAlloc.getStep());
        }
        // Must set value before set max
        long value = idAlloc.getSegmentMaxId() - buffer.getStep();
        segment.getValue().set(value);
        segment.setMax(idAlloc.getSegmentMaxId());
        segment.setStep(buffer.getStep());
        sw.stop("updateSegmentFromDb", key + " " + segment);
    }

    public String getIdFromSegmentBuffer(
            final SegmentBuffer buffer, final int length, final long maxId) {
        while (true) {
            buffer.rLock().lock();
            try {
                final Segment segment = buffer.getCurrent();
                if (!buffer.isNextReady()
                        && (segment.getIdle() < 0.9 * segment.getStep())
                        && buffer.getThreadRunning().compareAndSet(false, true)) {
                    service.execute(
                            () -> {
                                Segment next = buffer.getSegments()[buffer.nextPos()];
                                boolean updateOk = false;
                                try {
                                    updateSegmentFromDb(buffer.getKey(), next);
                                    updateOk = true;
                                    log.info("update segment {} from db {}", buffer.getKey(), next);
                                } catch (Exception e) {
                                    log.warn(buffer.getKey() + " updateSegmentFromDb exception", e);
                                } finally {
                                    if (updateOk) {
                                        buffer.wLock().lock();
                                        buffer.setNextReady(true);
                                        buffer.getThreadRunning().set(false);
                                        buffer.wLock().unlock();
                                    } else {
                                        buffer.getThreadRunning().set(false);
                                    }
                                }
                            });
                }
                long value = segment.getValue().getAndIncrement();
                if (value < segment.getMax()) {
                    if (value > maxId) {
                        throw new GravityRuntimeException(
                                ResponseCodeEnum.REACH_UPPER_LIMIT.getMessage());
                    }
                    return Base36Converter.convertDecimalToBase36(value, length);
                }
            } finally {
                buffer.rLock().unlock();
            }
            waitAndSleep(buffer);
            buffer.wLock().lock();
            try {
                final Segment segment = buffer.getCurrent();
                long value = segment.getValue().getAndIncrement();
                if (value < segment.getMax()) {
                    if (value > maxId) {
                        throw new GravityRuntimeException(
                                ResponseCodeEnum.REACH_UPPER_LIMIT.getMessage());
                    }
                    return Base36Converter.convertDecimalToBase36(value, length);
                }
                if (buffer.isNextReady()) {
                    buffer.switchPos();
                    buffer.setNextReady(false);
                } else {
                    log.error("Both two segments in {} are not ready!", buffer);
                    throw new GravityRuntimeException(
                            ResponseCodeEnum.ID_TWO_SEGMENTS_ARE_NULL.getMessage());
                }
            } finally {
                buffer.wLock().unlock();
            }
        }
    }

    private void waitAndSleep(SegmentBuffer buffer) {
        int roll = 0;
        while (buffer.getThreadRunning().get()) {
            roll += 1;
            if (roll > 10000) {
                try {
                    TimeUnit.MILLISECONDS.sleep(10);
                    break;
                } catch (InterruptedException e) {
                    log.warn("Thread {} interrupted!", Thread.currentThread().getName());
                    break;
                }
            }
        }
    }
}
