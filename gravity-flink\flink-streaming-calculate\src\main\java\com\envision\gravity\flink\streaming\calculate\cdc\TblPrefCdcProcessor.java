package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.dto.TblPref;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;

import java.util.List;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblPrefCdcProcessor {

    private static final Logger logger = LoggerFactory.getLogger(TblPrefCdcProcessor.class);

    private static volatile TblPrefCdcProcessor uniqueInstance;

    private final CalcPrefCache calcPrefCache;

    private final CalcMetaProcessor calcMetaProcessor;

    public static TblPrefCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblPrefCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblPrefCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private TblPrefCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        this.calcMetaProcessor = CalcMetaProcessor.getInstance();
    }

    public void process(String orgId, TblPref before, TblPref after, OPEnum op) {
        if (op == OPEnum.c) {
            logger.info("Created property, skip it...");
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error("Updated property invalid, before or after is null");
                return;
            }

            // updated prefName
            if (!StringUtils.isEmpty(before.getPrefName())
                    && !StringUtils.isEmpty(after.getPrefName())
                    && !StringUtils.equals(before.getPrefName(), after.getPrefName())) {
                List<PropertyId> toUpdateTargetPrefs =
                        calcPrefCache.getByTargetPrefId(orgId, after.getPrefId());
                this.calcMetaProcessor.refreshTargetPropertyByCompIdAndPrefId(
                        orgId, toUpdateTargetPrefs);
            }

            // TODO property type changed from 'MEASUREPOINT' TO 'ATTRIBUTE', original direct
            // mapping should be deleted
        } else if (op == OPEnum.d) {
            if (before == null) {
                logger.error("Deleted property invalid, before is null");
                return;
            }
            if (StringUtils.isEmpty(before.getPrefId())) {
                logger.error("Deleted property invalid, prefId is empty");
                return;
            }

            // Delete target cache
            this.calcPrefCache.deleteByTargetPrefId(orgId, before.getPrefId());

            // TODO Delete source cache
        }
    }
}
