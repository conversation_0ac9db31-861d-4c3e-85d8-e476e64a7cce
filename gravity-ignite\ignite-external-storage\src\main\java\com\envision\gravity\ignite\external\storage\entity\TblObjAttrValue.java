package com.envision.gravity.ignite.external.storage.entity;

import java.io.Serializable;
import java.sql.Timestamp;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/2
 * @description
 */
@Data
public class TblObjAttrValue implements Serializable {
    private static final long serialVersionUID = 2416917231986105437L;
    private String systemId;
    private String fieldId;
    private Boolean valueBool;
    private String valueString;
    private Long valueLong;
    private Double valueDouble;
    private String valueJson;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
