package com.envision.gravity.flink.streaming.postgres.cdc.model.req;

import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroupUpdateParam;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefreshObjectReq {
    private List<ModelGroup> modelInsertRefresh;
    private List<ModelGroup> modelUpdateRefresh;
    private List<ModelGroup> modelDeleteRefresh;
    private List<ModelGroupUpdateParam> modelGroupChangedRefresh;
    private List<String> boUpsertRefresh;
    private List<String> boDeleteRefresh;
}
