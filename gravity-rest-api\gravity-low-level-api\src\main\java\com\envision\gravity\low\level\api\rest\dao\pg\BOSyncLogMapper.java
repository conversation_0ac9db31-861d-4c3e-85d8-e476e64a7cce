package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.BOSyncLogEntity;
import com.envision.gravity.common.vo.sync.QuerySyncLogsReq;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @description
 */
@Mapper
public interface BOSyncLogMapper {
    int batchReplace(@Param("logs") List<BOSyncLogEntity> logs);

    long querySyncLogsCount(
            @Param("syncId") String syncId,
            @Param("orgId") String orgId,
            @Param("req") QuerySyncLogsReq querySyncLogsReq);

    List<BOSyncLogEntity> querySyncLogs(
            @Param("syncId") String syncId,
            @Param("orgId") String orgId,
            @Param("req") QuerySyncLogsReq querySyncLogsReq);
}
