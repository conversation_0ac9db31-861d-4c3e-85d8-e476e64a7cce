package com.envision.gravity.flink.streaming.calculate.batch.splitter;

import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.*;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务拆分枚举器（批处理版本）
 *
 * <p>功能： 1. 根据TblCalcJobInfo生成CalcJobTask 2. 按资产拆分任务 3. 使用分页查询获取目标资产
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class CalcJobTaskSplitEnumerator {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSplitEnumerator.class);

    private final TblCalcJobInfo jobInfo;
    private final ModelMetaQueryHandler modelMetaQueryHandler;

    public CalcJobTaskSplitEnumerator(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
    }

    /** ✅ 生成所有任务（批处理版本） */
    public List<CalcJobTask> generateAllTasks() throws Exception {
        List<CalcJobTask> allTasks = new ArrayList<>();

        String orgId = jobInfo.getSrcOrgId();
        String jobId = jobInfo.getJobId();
        long startTime = jobInfo.getStartTime();
        long endTime = jobInfo.getEndTime();

        // 1. 查询目标资产
        List<String> targetAssetIds = queryTargetAssets(orgId, jobInfo);

        // 2. 按资产拆分任务
        for (String assetId : targetAssetIds) {
            CalcJobTask task =
                    CalcJobTask.builder()
                            .taskId(generateTaskId(jobId, assetId))
                            .jobId(jobId)
                            .targetAssetIds(Arrays.asList(assetId))
                            .startTime(startTime)
                            .endTime(endTime)
                            .orgId(orgId)
                            .targetModelId(jobInfo.getRuleInfo().getTargetModelIds().get(0))
                            .build();

            allTasks.add(task);
        }

        logger.info("Generated {} tasks for job: {}", allTasks.size(), jobId);
        return allTasks;
    }

    /** ✅ 查询目标资产（与ReCalcBatchJob保持一致的实现） */
    private List<String> queryTargetAssets(String orgId, TblCalcJobInfo jobInfo) throws Exception {
        List<String> targetModelIds = jobInfo.getRuleInfo().getTargetModelIds();
        Set<String> modelIdSet = new HashSet<>(targetModelIds);

        // ✅ 使用分页查询获取所有资产信息
        int pageSize = CalcLionConfig.getCalcQueryAssetPageSize();
        Map<String, List<com.envision.gravity.common.calculate.AssetInfo>> assetInfosByModel =
                modelMetaQueryHandler.getAssetInfoByModelIdsWithPagination(
                        orgId, modelIdSet, pageSize);

        // 将按模型分组的资产信息合并为资产ID列表
        List<String> targetAssetIds = new ArrayList<>();
        for (List<com.envision.gravity.common.calculate.AssetInfo> assetInfos :
                assetInfosByModel.values()) {
            for (com.envision.gravity.common.calculate.AssetInfo assetInfo : assetInfos) {
                targetAssetIds.add(assetInfo.getAssetId());
            }
        }

        logger.info(
                "Queried {} target assets for job: {} with models: {}",
                targetAssetIds.size(),
                jobInfo.getJobId(),
                targetModelIds);

        return targetAssetIds;
    }

    private String generateTaskId(String jobId, String assetId) {
        return jobId + "_" + assetId + "_" + System.currentTimeMillis();
    }
}
