package com.envision.gravity.flink.streaming.calculate.batch.splitter;

import com.envision.gravity.common.calculate.AssetInfo;
import com.envision.gravity.common.calculate.ModelMetaQueryHandler;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.utils.CalcCommonService;

import java.util.*;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务拆分枚举器（批处理版本）
 *
 * <p>功能： 1. 根据TblCalcJobInfo生成CalcJobTask 2. 按资产拆分任务 3. 使用分页查询获取目标资产
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class CalcJobTaskSplitEnumerator {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSplitEnumerator.class);

    private final TblCalcJobInfo jobInfo;
    private final ModelMetaQueryHandler modelMetaQueryHandler;
    private final CalcCommonService calcCommonService;

    public CalcJobTaskSplitEnumerator(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();
        this.calcCommonService = CalcCommonService.getInstance();
    }

    /** ✅ 生成所有任务（批处理版本） */
    public List<CalcJobTask> generateAllTasks() throws Exception {
        List<CalcJobTask> allTasks = new ArrayList<>();

        String orgId = jobInfo.getSrcOrgId();
        String jobId = jobInfo.getJobId();
        long calcStartTime = jobInfo.getCalcStartTime();
        long calcEndTime = jobInfo.getCalcEndTime();

        // 1. 查询目标资产
        Map<String, Map<String, AssetInfo>> targetAssetIds =
                this.calcCommonService.queryTargetAssets(orgId, jobInfo.getRuleInfo());

        List<Map<String, Map<String, AssetInfo>>> splitModelAssetInfoMap =
                this.calcCommonService.splitModelAssetInfoMap(targetAssetIds);

        // 2. 按资产拆分任务
        int taskNo = 1;
        for (Map<String, Map<String, AssetInfo>> taskTargetAssets : splitModelAssetInfoMap) {
            CalcJobTask task =
                    CalcJobTask.builder()
                            .taskId(generateTaskId(jobId, taskNo))
                            .jobId(jobId)
                            .targetAssetIds(taskTargetAssets)
                            .startTime(calcStartTime)
                            .endTime(calcEndTime)
                            .build();

            allTasks.add(task);
            taskNo++;
        }

        logger.info("Generated {} tasks for job: {}", allTasks.size(), jobId);
        return allTasks;
    }

    private String generateTaskId(String jobId, int taskNo) {
        return jobId + "_task_" + taskNo;
    }
}
