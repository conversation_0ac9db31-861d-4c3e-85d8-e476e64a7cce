grammar Condition;

options {
    caseInsensitive = true;
}

parse
 : expr EOF
 ;

graphExpr
 : left=graphExpr op=and right=graphExpr                      #graphAndExpr
 | left=leftParen op=graphExpr right=rightParen               #graphParenExpr
 | left=field op=isIn right=parenValues                       #graphIsInExpr
 | left=field op=comparator right=value                       #graphComparatorExpr
 | left=field op=assetInModels right=parenValues              #graphAssetInModelsExpr
 | left=field op=assetInRelatedModels right=modelParams       #graphAssetInRelatedModelsExpr
 ;

joinModelGraphExpr : joinModelTagEqExpr | joinModelTagExistsExpr;
joinModelTagEqExpr: left=field op=EQ right=value;
joinModelTagExistsExpr: EXISTS LEFT_PAREN field RIGHT_PAREN;

expr
 : left=expr op=binary right=expr                             #binaryExpr
 | left=leftParen op=expr right=rightParen                    #parenExpr
 | left=field op=isIn right=parenValues                       #inExpr
 | left=field op=comparator right=value                       #comparatorExpr
 | left=field op=like right=stringValue                       #likeExpr
 | op=isExists right=parenFields                              #isExistsExpr
 | op=joinGraph right=parenGraphExpr                          #joinGraphExpr
 | op=joinModel right=parenJoinModelExpr                      #joinModelExpr
 | left=i18n op=comparator right=value                        #i18nComparatorExpr
 | left=i18n op=like right=stringValue                        #i18nLikeExpr
 | FUZZY_SEARCH LEFT_PAREN fuzzySearchField COMMA stringValue RIGHT_PAREN  #fuzzySearchExpr
 ;

fuzzySearchField
 : field
 | i18n
 ;

SIGNED_NUMBER
    : ('+' | '-')? (DECIMAL_DIGITS)
    ;

DECIMAL_DIGITS
    : DIGIT+ (DOT DIGIT)?
    ;
DIGIT
    : [0-9]+
    ;

TRUE                    : 'true';
FALSE                   : 'false';
field                   : FIELD+ (DOT (FIELD | SIGNED_NUMBER))*;
fields                  : field (COMMA field)*;
parenFields             : LEFT_PAREN fields RIGHT_PAREN;
parenValues             : LEFT_PAREN values RIGHT_PAREN;
joinGraph               : JOIN_GRAPH;
parenGraphExpr          : LEFT_PAREN graphExpr RIGHT_PAREN;
joinModel               : JOIN_MODEL;
parenJoinModelExpr      : LEFT_PAREN joinModelGraphExpr RIGHT_PAREN;
modelEdgeType           : FIELD;
modelParams             : LEFT_PAREN modelEdgeType COMMA values RIGHT_PAREN;
assetInModels           : ASSET_IN_MODELS;
assetInRelatedModels    : ASSET_IN_RELATED_MODELS;
values                  : value (COMMA value)*;
value                   : STRING | SIGNED_NUMBER | timestamp | booleanValue;
stringValue             : STRING;
like                    : LIKE;
and                     : AND;
comparator              : GT | GE | LT | LE | EQ | NEQ;
booleanValue            : TRUE | FALSE;
isExists                : NOT_EXISTS | EXISTS;
isIn                    : IN | NOT_IN;
binary                  : AND | OR;
timestamp               : INTEGER;
leftParen               : LEFT_PAREN;
rightParen              : RIGHT_PAREN;
i18n                    : 'i18n' LEFT_PAREN field COMMA stringValue RIGHT_PAREN;
INTEGER                 : [0-9]+;
LIKE                    : 'like';
NOT_EXISTS              : 'not exists';
EXISTS                  : 'exists';
AND                     : 'and' ;
OR                      : 'or' ;
IN                      : 'in';
NOT_IN                  : 'not in';
GT                      : '>' ;
GE                      : '>=' ;
LT                      : '<' ;
LE                      : '<=' ;
EQ                      : '=' ;
NEQ                     : '!=' ;
LEFT_PAREN              : '(';
RIGHT_PAREN             : ')';
ASSET_IN_MODELS         : 'in assetInModels';
ASSET_IN_RELATED_MODELS : 'in assetInRelatedModels';
JOIN_GRAPH              : 'join graph';
JOIN_MODEL              : 'join model';
FUZZY_SEARCH            : 'fuzzy_search';
FIELD                   : (ENGLISH_CHAR | CHINESE_CHAR | GERMAN_FRENCH_CHAR | JAPANESE_CHAR | OTHER_CHAR)+;
ENGLISH_CHAR            : [a-z0-9_];
CHINESE_CHAR            : [\u4E00-\u9FFF];
GERMAN_FRENCH_CHAR      : [\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF];
JAPANESE_CHAR           : [\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF];
OTHER_CHAR              : [:#/\\-];
DOT                     : '.';
COMMA                   : ',';
STRING
    : '\'' (~('\'' | '\\') | '\\\'' | '\\\\')*  '\''
    ;

WS : [ \t\n\r]+ -> skip ;