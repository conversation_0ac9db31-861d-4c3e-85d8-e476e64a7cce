package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.ArrayList;
import java.util.List;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
class TblBOSqlProviderTest {

    @Test
    void updateSysModifiedTimeByPrimaryKeys() {
        List<String> assetIdList = new ArrayList<>();
        assetIdList.add("asset_id1");
        assetIdList.add("asset_id2");
        assetIdList.add("asset_id3");
        TblBOSqlProvider tblBOSqlProvider = new TblBOSqlProvider();
        String sql = tblBOSqlProvider.updateSysModifiedTimeByPrimaryKeys("gravity", assetIdList);
        System.out.println(sql);
    }

    @Test
    void refreshObjectDetailByAssetIdArr() {
        List<String> assetIdList = new ArrayList<>();
        assetIdList.add("asset_id1");
        assetIdList.add("asset_id2");
        assetIdList.add("asset_id3");
        TblBOSqlProvider tblBOSqlProvider = new TblBOSqlProvider();
        String sql = tblBOSqlProvider.batchRefreshObjectDetailByAssetIds("gravity", assetIdList);
        System.out.println(sql);
    }
}
