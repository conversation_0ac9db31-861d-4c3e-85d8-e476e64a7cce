package com.envision.gravity.flink.streaming.bo.view.operator.model;

import java.sql.Timestamp;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Source {
    private String version;
    private String connector;
    private String name;
    private Timestamp tsMs;
    private Boolean snapshot;
    private String db;
    private String sequence;
    private String schema;
    private String table;

    @JsonProperty("txId")
    private Integer txId;

    private Long lsn;
    private Integer xMin;
}
