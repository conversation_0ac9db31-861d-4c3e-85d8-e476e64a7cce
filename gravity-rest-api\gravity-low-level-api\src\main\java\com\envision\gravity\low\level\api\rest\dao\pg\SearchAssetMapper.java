package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblPgAsset;
import com.envision.gravity.common.vo.search.Sorter;
import com.envision.gravity.common.vo.search.asset.AssetPath;
import com.envision.gravity.common.vo.search.asset.AttributeProjection;
import com.envision.gravity.common.vo.search.asset.GroupBy;
import com.envision.gravity.common.vo.search.asset.SearchAssetWithGraphParam;
import com.envision.gravity.low.level.api.rest.dto.SearchAssetCondition;
import com.envision.gravity.low.level.api.rest.dto.SearchAssetResultInfo;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

/** @Author: qi.jiang2 @Date: 2024/03/18 17:47 @Description: */
public interface SearchAssetMapper {

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "selectAssetPathByAssetIds")
    @Results({
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "graph_path", property = "graphPath", jdbcType = JdbcType.VARCHAR)
    })
    List<AssetPath> selectAssetPathByAssetIds(
            List<String> assetIds, SearchAssetWithGraphParam condition, String orgId);

    @SelectProvider(
            type = SearchAssetSqlProvider.class,
            method = "selectByExpressionWithPagination")
    @Results({
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "graph_path", property = "graphPath", jdbcType = JdbcType.VARCHAR),
        @Result(column = "total_size", property = "totalSize", jdbcType = JdbcType.VARCHAR)
    })
    List<SearchAssetResultInfo> selectByExpressionWithPagination(
            SearchAssetCondition condition,
            List<Sorter> sorters,
            int limit,
            int offset,
            boolean needAssetPath,
            String orgId);

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "selectByExpression")
    List<String> selectByExpression(
            SearchAssetCondition condition, List<Sorter> sorters, String orgId);

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "selectByExpressionWithGroupBy")
    List<Map<String, Object>> selectByExpressionWithGroupBy(
            SearchAssetCondition condition, GroupBy groupBy, String orgId);

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "selectByAssetIds")
    @Results({
        @Result(column = "asset_id", property = "assetId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "asset_display_name",
                property = "assetDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
        @Result(column = "org_id", property = "orgId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "unique_id", property = "uniqueId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "asset_tags",
                property = "assetTags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "attributes",
                property = "attributes",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "asset_created_time",
                property = "assetCreatedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "asset_created_user",
                property = "assetCreatedUser",
                jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "asset_modified_time",
                property = "assetModifiedTime",
                jdbcType = JdbcType.TIMESTAMP),
        @Result(
                column = "asset_modified_user",
                property = "assetModifiedUser",
                jdbcType = JdbcType.VARCHAR)
    })
    List<TblPgAsset> selectByAssetIds(
            List<String> assetIds, AttributeProjection attributeProjection, String orgId);

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "countByExpression")
    int countByExpression(SearchAssetCondition condition, String orgId);

    @InsertProvider(type = SearchAssetSqlProvider.class, method = "insertToPgAsset")
    void insertToPgAsset(List<TblPgAsset> pgAssets, String orgId);

    @SelectProvider(type = SearchAssetSqlProvider.class, method = "selectByAssetIdTest")
    List<Map<String, Object>> selectByAssetIdTest(List<String> assetIds, String orgId);
}
