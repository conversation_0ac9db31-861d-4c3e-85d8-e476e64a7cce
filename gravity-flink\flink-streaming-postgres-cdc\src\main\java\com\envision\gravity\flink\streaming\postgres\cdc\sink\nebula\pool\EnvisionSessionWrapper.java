package com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.pool;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicBoolean;


import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.InvalidSessionException;
import com.vesoft.nebula.client.graph.net.Session;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
public class EnvisionSessionWrapper implements Serializable {
    private static final long serialVersionUID = -9023875778753521296L;
    private final Session session;
    private final AtomicBoolean available = new AtomicBoolean(true);

    public EnvisionSessionWrapper(Session session) {
        this.session = session;
    }

    /**
     * Execute the query sentence.
     *
     * @param stmt The query sentence.
     * @return The ResultSet.
     */
    public ResultSet execute(String stmt) throws IOErrorException {
        if (noAvailable()) {
            throw new InvalidSessionException();
        }
        return session.execute(stmt);
    }

    void setNoAvailable() {
        this.available.set(false);
    }

    boolean noAvailable() {
        return !available.get();
    }

    public void release() {
        session.release();
    }

    Session getSession() {
        return session;
    }

    public boolean ping() {
        return session.ping();
    }
}
