package com.envision.gravity.flink.streaming.bo.view.operator.sink;

import com.envision.gravity.flink.streaming.bo.view.operator.entity.Constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.eniot.tableengine.RegisterIgniteTableConfig;
import com.eniot.tableengine.TableEngine;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/6/17
 * @description
 */
class CreateOrReplaceViewSinkTest {

    @Test
    void tableEngineTest() {
        RegisterIgniteTableConfig cfg = new RegisterIgniteTableConfig();
        cfg.setOrgId(Constants.GRAVITY);
        cfg.setTargetSchema("gravity");
        cfg.setTargetTable("dim_bo_EnOS_Solar_Inverter1");
        cfg.setSourceType("TABLE");

        Map<String, String> params = new HashMap<>();
        params.put("url", "*******************************************************");
        params.put("user", "postgres");
        params.put("passwd", "postgres");
        params.put("fromSchema", "gravity");
        params.put("fromTable", "dim_bo_EnOS_Solar_Inverter1");
        cfg.setParams(params);

        RegisterIgniteTableConfig.Schema schema = new RegisterIgniteTableConfig.Schema();
        List<String> primaryKey = new ArrayList<>();
        primaryKey.add("asset_id");
        primaryKey.add("model_id");
        schema.setPrimaryKey(primaryKey);
        cfg.setSchemaConfig(schema);

        try (TableEngine tableEngine =
                TableEngine.builder()
                        .withIgniteIp("************")
                        .withIgniteUser("ignite")
                        .withIgnitePasswd("ignite")
                        .build()) {
            tableEngine.register(cfg);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
