package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblBO {
    @KeyColumn(name = "asset_id")
    @ColumnName("asset_id")
    private String assetId;

    @ValueColumn(name = "asset_display_name")
    @ColumnName("asset_display_name")
    @RequiredField(message = "asset_display_name field is required")
    private String assetDisplayName;

    @ValueColumn(name = "system_id")
    @ColumnName("system_id")
    private String systemId;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
