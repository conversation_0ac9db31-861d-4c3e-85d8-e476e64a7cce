package com.envision.gravity.flink.streaming.calculate.batch;

import com.envision.gravity.flink.streaming.calculate.batch.processor.ReCalcJobTaskProcessor;
import com.envision.gravity.flink.streaming.calculate.batch.source.BatchCalcJobTaskSource;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;

import java.util.concurrent.TimeUnit;


import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReCalc批处理作业主类
 *
 * <p>功能： 1. 处理单个Job的历史数据重跑 2. 使用BatchCalcJobTaskSource生成任务 3. 通过ReCalcJobTaskProcessor处理任务 4.
 * 直接写入Kafka，Job完成后退出
 *
 * <p>使用方式： flink run -c com.envision.gravity.flink.streaming.calculate.batch.ReCalcBatchJob \
 * flink-streaming-calculate-1.0.jar job-001
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class ReCalcBatchJob {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcBatchJob.class);

    public static void main(String[] args) throws Exception {
        // 参数校验
        if (args.length != 1) {
            logger.error("Usage: ReCalcBatchJob <jobId>");
            System.exit(1);
        }

        String jobId = args[0];
        logger.info("Starting ReCalcBatchJob for jobId: {}", jobId);

        // 验证Job存在性
        TblCalcJobInfo jobInfo = validateAndLoadJobInfo(jobId);

        // 创建执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment();

        // 构建数据流
        buildDataStream(env, jobInfo);

        // 执行作业
        String jobName = "ReCalcBatchJob-" + jobId;
        logger.info("Executing ReCalcBatchJob: {}", jobName);
        env.execute(jobName);

        logger.info("ReCalcBatchJob completed for jobId: {}", jobId);
    }

    /** 验证并加载Job信息 */
    private static TblCalcJobInfo validateAndLoadJobInfo(String jobId) throws Exception {
        // 需要通过SqlSession获取Mapper实例
        try (org.apache.ibatis.session.SqlSession session =
                com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig
                        .getSqlSessionFactory()
                        .openSession()) {

            TblCalcJobInfoMapper mapper = session.getMapper(TblCalcJobInfoMapper.class);
            TblCalcJobInfo jobInfo = mapper.findByJobId(jobId);

            if (jobInfo == null) {
                throw new IllegalArgumentException("Job not found: " + jobId);
            }

            logger.info(
                    "Loaded job info: {}, orgId: {}, status: {}",
                    jobId,
                    jobInfo.getSrcOrgId(),
                    jobInfo.getStatus());

            return jobInfo;
        }
    }

    /** 创建执行环境 */
    private static StreamExecutionEnvironment createExecutionEnvironment() {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 基础配置
        env.setParallelism(CalcLionConfig.getReCalcJobTaskProcessorParallelism());

        // Checkpoint配置
        env.enableCheckpointing(
                CalcLionConfig.getReCalcCheckpointIntervalMs(), CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig()
                .setCheckpointTimeout(CalcLionConfig.getReCalcCheckpointTimeoutMs());
        env.getCheckpointConfig()
                .setMaxConcurrentCheckpoints(CalcLionConfig.getReCalcCheckpointMaxConcurrent());

        // 重启策略
        env.setRestartStrategy(
                RestartStrategies.fixedDelayRestart(
                        CalcLionConfig.getReCalcRestartAttempts(),
                        Time.of(CalcLionConfig.getReCalcRestartDelayMs(), TimeUnit.MILLISECONDS)));

        logger.info(
                "Created execution environment with parallelism: {}",
                CalcLionConfig.getReCalcDefaultParallelism());

        return env;
    }

    /** 构建数据流 */
    private static void buildDataStream(StreamExecutionEnvironment env, TblCalcJobInfo jobInfo) {
        // 1. 创建BatchCalcJobTaskSource（并行度=1）
        DataStream<CalcJobTask> taskStream =
                env.addSource(new BatchCalcJobTaskSource(jobInfo))
                        .name("BatchCalcJobTaskSource")
                        .uid("batch-calc-job-task-source")
                        .setParallelism(1); // 固定并行度=1

        // 2. 创建ReCalcJobTaskProcessor（可配置并行度）
        taskStream
                .process(new ReCalcJobTaskProcessor(jobInfo.getJobId()))
                .name("ReCalcJobTaskProcessor")
                .uid("recalc-job-task-processor")
                .setParallelism(CalcLionConfig.getReCalcJobTaskProcessorParallelism());

        logger.info(
                "Built data stream for job: {}, processor parallelism: {}",
                jobInfo.getJobId(),
                CalcLionConfig.getReCalcJobTaskProcessorParallelism());
    }
}
