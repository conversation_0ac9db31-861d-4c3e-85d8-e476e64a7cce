package com.envision.gravity.flink.streaming.calculate.batch;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;


import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReCalc Batch Job - Temporary Word Count Implementation This is a placeholder implementation for
 * integration testing Will be replaced with actual calculation logic later
 *
 * <AUTHOR>
 */
public class ReCalcBatchJob {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcBatchJob.class);

    public static void main(String[] args) throws Exception {
        // Parse parameters
        ParameterTool params = ParameterTool.fromArgs(args);
        String jobId = params.get("jobId");

        // Update job status to RUNNING
        updateJobStatus(jobId, ReCalcJobStatusEnum.RUNNING, "started_time");

        try {
            // Execute the batch job
            executeBatchJob(jobId);

            // Update job status to FINISHED
            updateJobStatus(jobId, ReCalcJobStatusEnum.FINISHED, "finished_time");

            logger.info("ReCalc Batch Job completed successfully: jobId={}", jobId);

        } catch (Exception e) {
            logger.error("ReCalc Batch Job failed: jobId={}, error={}", jobId, e.getMessage(), e);

            // Update job status to FAILED
            updateJobStatus(jobId, ReCalcJobStatusEnum.FAILED, "finished_time");

            throw e;
        }
    }

    /** Execute the actual batch job logic Currently implements a simple word count for testing */
    private static void executeBatchJob(String jobId) throws Exception {
        // Get execution environment
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        logger.info("Batch job parallelism set to: {}", env.getParallelism());

        // Execute the job
        env.execute("ReCalc Batch Job - " + jobId);

        // Simulate calculation processing time
        int processingTimeMs = 2000; // Fixed time for testing
        logger.info("Simulating calculation processing time: {}ms", processingTimeMs);
        Thread.sleep(processingTimeMs);

        logger.info("Batch job execution completed for jobId: {}", jobId);
    }

    /** Update job status in database */
    private static void updateJobStatus(
            String jobId, ReCalcJobStatusEnum status, String timeField) {
        try {
            SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
            try (SqlSession sqlSession = sqlSessionFactory.openSession(true)) {
                TblCalcJobInfoMapper mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);

                long currentTime = System.currentTimeMillis();
                int result =
                        mapper.updateStatusWithTime(
                                jobId, status.getCode(), timeField, currentTime);

                if (result > 0) {
                    logger.info(
                            "Updated job status: jobId={}, status={}, timeField={}",
                            jobId,
                            status,
                            timeField);
                } else {
                    logger.warn("Failed to update job status: jobId={}, status={}", jobId, status);
                }
            }
        } catch (Exception e) {
            logger.error(
                    "Database error while updating job status: jobId={}, status={}",
                    jobId,
                    status,
                    e);
        }
    }
}
