package com.envision.gravity.flink.streaming.calculate.dto.calc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 计算属性元数据
 * 
 * 用于：
 * 1. CalcJobMetaInfo 的 targetPropertyMeta 字段
 * 2. 目标属性的详细信息
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcPropertyMeta implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 属性名称
     */
    private String prefName;
    
    /**
     * 属性类型
     */
    private PrefType prefType;
    
    /**
     * 计算表达式
     */
    private String expression;
    
    /**
     * 是否为直白映射
     */
    private boolean directMapping;
    
    /**
     * 源属性项列表
     */
    private List<SrcPrefItem> srcPrefItems;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 检查是否为有效的属性元数据
     */
    public boolean isValid() {
        return prefName != null && !prefName.isEmpty() &&
               prefType != null &&
               expression != null && !expression.isEmpty() &&
               srcPrefItems != null && !srcPrefItems.isEmpty();
    }
    
    /**
     * 检查是否为测量点
     */
    public boolean isMeasurePoint() {
        return prefType != null && prefType.isMeasurePoint();
    }
    
    /**
     * 检查是否为属性
     */
    public boolean isAttribute() {
        return prefType != null && prefType.isAttribute();
    }
    
    @Override
    public String toString() {
        return String.format("CalcPropertyMeta{prefName='%s', prefType=%s, directMapping=%s, " +
                           "srcItems=%d, expression='%s'}", 
                           prefName, prefType, directMapping,
                           srcPrefItems != null ? srcPrefItems.size() : 0,
                           expression != null && expression.length() > 50 ? 
                               expression.substring(0, 50) + "..." : expression);
    }
}
