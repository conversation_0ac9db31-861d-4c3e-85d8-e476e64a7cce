package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.common.enums.PrefType;


import org.junit.jupiter.api.Test;

/** 简单的 Builder 测试，用于验证方法名称 */
public class SimpleBuilderTest {

    @Test
    public void testCalcPropertyMetaBuilder() {
        // 测试 CalcPropertyMeta 的 builder 方法
        CalcPropertyMeta meta =
                CalcPropertyMeta.builder()
                        .prefName("test")
                        .targetCompId("comp")
                        .targetPrefId("pref")
                        .srcCategoryId("src")
                        .targetCategoryId("target")
                        .isDirectMapping(true)
                        .prefType(PrefType.MEASUREPOINT)
                        .build();

        System.out.println("Builder test passed: " + meta.getPrefName());

        // 测试可用的方法
        System.out.println("Available methods:");
        for (java.lang.reflect.Method method : meta.getClass().getMethods()) {
            if (method.getName().toLowerCase().contains("valid")) {
                System.out.println("  " + method.getName() + "()");
            }
        }
    }
}
