package com.envision.gravity.common.vo.search.graph;

import com.envision.gravity.common.vo.search.SearchPaginationResp;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/08 14:47 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchSubGraphResp {

    private List<SubGraph> subGraphs;

    private SearchPaginationResp pagination;
}
