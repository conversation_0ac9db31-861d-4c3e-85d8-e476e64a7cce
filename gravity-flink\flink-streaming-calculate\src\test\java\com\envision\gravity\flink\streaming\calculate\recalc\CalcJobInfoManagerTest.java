package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.TblPropertyUpstreamRule;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobMetaInfo;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * CalcJobInfoManager 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CalcJobInfoManagerTest {

    @Mock private StreamExecutionEnvironment env;

    @Mock private ReadOnlyBroadcastState<String, TblCalcJobInfo> readOnlyBroadcastState;

    @Mock private BroadcastState<String, TblCalcJobInfo> broadcastState;

    private TblCalcJobInfo testJobInfo;

    private Set<String> createTargetModelIds() {
        Set<String> targetModelIds = new HashSet<>();
        targetModelIds.add("model_001");
        targetModelIds.add("model_002");
        return targetModelIds;
    }

    @BeforeEach
    void setUp() {
        // 创建测试数据
        TblPropertyUpstreamRule ruleInfo =
                TblPropertyUpstreamRule.builder()
                        .prefRuleId("test_rule_001")
                        .targetCategory("test_category")
                        .targetCompId("test_comp_001")
                        .targetPrefId("test_pref_001")
                        .srcCategory("test_src_category")
                        .expression("test_expression")
                        .calcType(1)
                        .build();

        CalcPropertyMeta propertyMeta =
                CalcPropertyMeta.builder().prefName("test_pref_name").build();

        CalcJobMetaInfo metaInfo =
                CalcJobMetaInfo.builder()
                        .orgId("o17186913277371853")
                        .ruleInfo(ruleInfo)
                        .targetModelIds(createTargetModelIds())
                        .targetPropertyMeta(propertyMeta)
                        .build();

        testJobInfo =
                TblCalcJobInfo.builder()
                        .jobId("test_job_001")
                        .prefRuleId("test_rule_001")
                        .ruleInfo(metaInfo)
                        .calcStartTime(System.currentTimeMillis())
                        .calcEndTime(System.currentTimeMillis() + 3600000)
                        .build();
    }

    @Test
    void testCreateCalcJobInfoBroadcast() {
        // 由于 StreamExecutionEnvironment 的复杂性，这里主要测试方法不抛异常
        assertDoesNotThrow(
                () -> {
                    // 实际测试中需要真实的 StreamExecutionEnvironment
                    // 这里只验证方法签名和基本逻辑
                    assertNotNull(CalcJobInfoManager.CALC_JOB_INFO_DESCRIPTOR);
                    assertEquals(
                            "calc-job-info-broadcast",
                            CalcJobInfoManager.CALC_JOB_INFO_DESCRIPTOR.getName());
                });
    }

    @Test
    void testGetCalcJobInfo_Success() throws Exception {
        // 模拟广播状态返回作业信息
        when(readOnlyBroadcastState.get("test_job_001")).thenReturn(testJobInfo);

        TblCalcJobInfo result =
                CalcJobInfoManager.getCalcJobInfo(readOnlyBroadcastState, "test_job_001");

        assertEquals(testJobInfo, result);
        verify(readOnlyBroadcastState).get("test_job_001");
    }

    @Test
    void testGetCalcJobInfo_NotFound() throws Exception {
        // 模拟广播状态返回 null
        when(readOnlyBroadcastState.get("non_existent_job")).thenReturn(null);

        RuntimeException exception =
                assertThrows(
                        RuntimeException.class,
                        () -> {
                            CalcJobInfoManager.getCalcJobInfo(
                                    readOnlyBroadcastState, "non_existent_job");
                        });

        assertTrue(
                exception
                        .getMessage()
                        .contains("Calc job info not found for jobId: non_existent_job"));
        verify(readOnlyBroadcastState).get("non_existent_job");
    }

    @Test
    void testUpdateCalcJobInfo_Success() throws Exception {
        // 测试更新广播状态
        CalcJobInfoManager.updateCalcJobInfo(broadcastState, testJobInfo);

        verify(broadcastState).put("test_job_001", testJobInfo);
    }

    @Test
    void testUpdateCalcJobInfo_Exception() throws Exception {
        // 模拟广播状态抛出异常
        doThrow(new RuntimeException("State update failed"))
                .when(broadcastState)
                .put("test_job_001", testJobInfo);

        assertThrows(
                RuntimeException.class,
                () -> {
                    CalcJobInfoManager.updateCalcJobInfo(broadcastState, testJobInfo);
                });

        verify(broadcastState).put("test_job_001", testJobInfo);
    }

    @Test
    void testBroadcastDescriptor() {
        assertNotNull(CalcJobInfoManager.CALC_JOB_INFO_DESCRIPTOR);
        assertEquals(
                "calc-job-info-broadcast", CalcJobInfoManager.CALC_JOB_INFO_DESCRIPTOR.getName());
        // 注意：在单元测试环境中，序列化器可能未初始化，所以不测试序列化器
    }
}
