package com.envision.gravity.flink.streaming.postgres.cdc.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.config.IgniteDataSourceConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroupUpdateParam;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.ObjectDetailOriginRepository;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblBOModelRepository;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblBORepository;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ignite.client.IgniteClient;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class RefreshObjectSink extends RichSinkFunction<Tuple2<String, List<RefreshObjectReq>>> {
    private static final long serialVersionUID = 688197415726452173L;

    private transient ObjectDetailOriginRepository objectDetailOriginRepository;
    private transient TblBOModelRepository tblBOModelRepository;
    private transient TblBORepository tblBORepository;
    private transient ExecutorService executorService;

    @Override
    public void open(Configuration params) throws Exception {
        SqlSessionFactory sqlSessionFactory = PGDataSourceConfig.getSqlSessionFactory();
        IgniteClient igniteClient = IgniteDataSourceConfig.getIgniteClient();
        executorService =
                new ThreadPoolExecutor(
                        8,
                        8,
                        60L,
                        TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(1000),
                        new ThreadFactory() {
                            private final AtomicInteger poolNumber = new AtomicInteger(1);

                            @Override
                            public Thread newThread(@NotNull Runnable r) {
                                return new Thread(
                                        r,
                                        String.format(
                                                "Refresh-Object-Thread-Pool-%d",
                                                poolNumber.getAndIncrement()));
                            }
                        },
                        new ThreadPoolExecutor.AbortPolicy());

        objectDetailOriginRepository = new ObjectDetailOriginRepository(sqlSessionFactory);
        tblBOModelRepository = new TblBOModelRepository(sqlSessionFactory);
        tblBORepository = new TblBORepository(sqlSessionFactory, igniteClient, executorService);
    }

    @Override
    public void close() throws Exception {
        PGDataSourceConfig.closeDataSource();
        IgniteDataSourceConfig.closeIgniteClient();
        executorService.shutdownNow();
    }

    @Override
    public void invoke(Tuple2<String, List<RefreshObjectReq>> value, Context context) {
        String schemaName = value.f0;
        List<RefreshObjectReq> refreshObjectReqList = value.f1;
        if (schemaName == null || refreshObjectReqList.isEmpty()) {
            return;
        }

        try {
            List<ModelGroup> modelInsOrUpdReqList = new ArrayList<>();
            List<ModelGroupUpdateParam> modelGroupChangedReqList = new ArrayList<>();
            List<String> boInsOrUpdReqList = new ArrayList<>();
            for (RefreshObjectReq refreshObjectReq : refreshObjectReqList) {
                if (refreshObjectReq.getModelInsertRefresh() != null
                        && !refreshObjectReq.getModelInsertRefresh().isEmpty()) {
                    modelInsOrUpdReqList.addAll(refreshObjectReq.getModelInsertRefresh());
                }

                if (refreshObjectReq.getModelUpdateRefresh() != null
                        && !refreshObjectReq.getModelUpdateRefresh().isEmpty()) {
                    modelInsOrUpdReqList.addAll(refreshObjectReq.getModelUpdateRefresh());
                }

                if (refreshObjectReq.getModelGroupChangedRefresh() != null
                        && !refreshObjectReq.getModelGroupChangedRefresh().isEmpty()) {
                    modelGroupChangedReqList.addAll(refreshObjectReq.getModelGroupChangedRefresh());
                }

                if (refreshObjectReq.getBoUpsertRefresh() != null
                        && !refreshObjectReq.getBoUpsertRefresh().isEmpty()) {
                    boInsOrUpdReqList.addAll(refreshObjectReq.getBoUpsertRefresh());
                    log.info(
                            "Refresh object due to bo upsert, schemaName: [{}], "
                                    + "asset count: [{}], assetIdList: [{}].",
                            schemaName,
                            boInsOrUpdReqList.size(),
                            boInsOrUpdReqList);
                }
            }

            List<String> assetIdListByModelGroup = new ArrayList<>();
            if (!modelInsOrUpdReqList.isEmpty()) {
                assetIdListByModelGroup =
                        getAssetIdListByModelGroup(schemaName, modelInsOrUpdReqList);
                log.info(
                        "Refresh object due to model update, schemaName: [{}], ModelGroup: [{}], "
                                + "asset count: [{}], assetIdList: [{}].",
                        schemaName,
                        modelInsOrUpdReqList,
                        assetIdListByModelGroup.size(),
                        assetIdListByModelGroup);
            }
            // merge asset_id list
            List<String> mergedAssetIdList =
                    Stream.concat(assetIdListByModelGroup.stream(), boInsOrUpdReqList.stream())
                            .distinct()
                            .collect(Collectors.toList());

            if (!mergedAssetIdList.isEmpty()) {
                batchRefreshObjectDetailByAssetIds(schemaName, mergedAssetIdList);
            }

            if (!modelGroupChangedReqList.isEmpty()) {
                modelGroupChangedRefresh(schemaName, modelGroupChangedReqList);
            }
        } catch (Exception e) {
            log.error("Refresh object error.", e);
            throw new GravityRuntimeException("Refresh object error.", e);
        }
    }

    private List<String> getAssetIdListByModelGroup(
            String schemaName, List<ModelGroup> modelInsOrUpdReqList) {
        // Step 1: Retrieve model_id and group_id Map
        Multimap<String, String> modelGroupMap = getModelGroupMap(modelInsOrUpdReqList);

        // Step 2: Retrieve asset_ids based on model_id and group_id
        return tblBOModelRepository.selectAssetIdList(schemaName, modelGroupMap);
    }

    private void batchRefreshObjectDetailByAssetIds(String schemaName, List<String> assetIdList) {
        if (assetIdList.isEmpty()) {
            return;
        }

        tblBORepository.batchRefreshObjectDetailByAssetIds(schemaName, assetIdList);
    }

    private void modelGroupChangedRefresh(
            String schemaName, List<ModelGroupUpdateParam> modelGroupChangedReqList) {
        Multimap<String, String> modelOldGroupMap = ArrayListMultimap.create();
        Multimap<String, String> modelNewGroupMap = ArrayListMultimap.create();
        modelGroupChangedReqList.stream()
                .filter(modelGroupUpdateParam -> modelGroupUpdateParam.getOldGroupId() != null)
                .forEach(
                        modelUpdateRefreshReq -> {
                            String modelId = modelUpdateRefreshReq.getModelId();
                            String oldGroupId = modelUpdateRefreshReq.getOldGroupId();

                            modelOldGroupMap.put(modelId, oldGroupId);
                        });

        modelGroupChangedReqList.forEach(
                modelGroupUpdateParam -> {
                    String modelId = modelGroupUpdateParam.getModelId();
                    String newGroupId = modelGroupUpdateParam.getNewGroupId();

                    modelNewGroupMap.put(modelId, newGroupId);
                });

        // Step 1: delete
        List<String> oldAssetIdList =
                tblBOModelRepository.selectAssetIdList(schemaName, modelOldGroupMap);
        int deletedRows =
                objectDetailOriginRepository.batchDeleteByPrimaryKey(
                        schemaName, oldAssetIdList, new ArrayList<>(modelOldGroupMap.keySet()));

        // Step 2: insert refresh
        List<String> newAssetIdList =
                tblBOModelRepository.selectAssetIdList(schemaName, modelNewGroupMap);
        tblBORepository.batchRefreshObjectDetailByAssetIds(schemaName, newAssetIdList);

        log.info(
                "Batch refresh object detail by modelGroupChanged success, schemaName: [{}], "
                        + "oldAsset count: [{}], oldAssetIdList: [{}], newAsset count: [{}], newAssetIdList: [{}].",
                schemaName,
                oldAssetIdList.size(),
                oldAssetIdList,
                newAssetIdList.size(),
                newAssetIdList);
    }

    private void boDeletedRefresh(String schemaName, List<String> requestParams) {
        objectDetailOriginRepository.batchDeleteByAssetIdList(schemaName, requestParams);
        log.info(
                "Batch delete object success, schemaName: [{}], asset count: [{}], assetIdList: [{}].",
                schemaName,
                requestParams.size(),
                requestParams);
    }

    private Multimap<String, String> getModelGroupMap(List<ModelGroup> requestParams) {
        Multimap<String, String> modelGroupMap = ArrayListMultimap.create();
        requestParams.forEach(
                modelGroup -> {
                    String modelId = modelGroup.getModelId();
                    String groupId = modelGroup.getGroupId();
                    modelGroupMap.put(modelId, groupId);
                });
        return modelGroupMap;
    }
}
