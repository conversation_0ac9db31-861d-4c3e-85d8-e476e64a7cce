package com.envision.gravity.ignite.tsdb;


import io.eniot.tsdb.common.exception.TSDBRuntimeException;

/** <AUTHOR> 2024/8/19 */
public class TSDBReplicaInsufficientException extends TSDBRuntimeException {

    private static final long serialVersionUID = -7561438104162654288L;

    public TSDBReplicaInsufficientException() {
        super();
    }

    public TSDBReplicaInsufficientException(String message) {
        super(message);
    }

    public TSDBReplicaInsufficientException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public TSDBReplicaInsufficientException(Throwable throwable) {
        super(throwable);
    }
}
