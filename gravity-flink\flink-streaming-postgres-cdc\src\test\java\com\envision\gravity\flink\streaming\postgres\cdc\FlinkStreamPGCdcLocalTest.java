package com.envision.gravity.flink.streaming.postgres.cdc;

import com.envision.gravity.flink.streaming.postgres.cdc.config.LionConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.config.PGDataSourceConfig;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.function.*;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.side.SideOutputs;
import com.envision.gravity.flink.streaming.postgres.cdc.sink.*;

import java.util.List;
import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/5/16
 * @description
 */
@Slf4j
class FlinkStreamPGCdcLocalTest {

    @Test
    void localTest() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.createLocalEnvironment();

        int refreshTimeWindowIntervalInMs = LionConfig.getRefreshTimeWindowIntervalInMs();

        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", "5");
        properties.setProperty("max.queue.size", "10");

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        Runtime.getRuntime()
                .addShutdownHook(
                        new Thread(
                                () -> {
                                    log.info("Received shutdown signal. Clean up resources ...");
                                    try {
                                        PGDataSourceConfig.closeDataSource();
                                    } catch (Exception e) {
                                        log.error("Close datasource error", e);
                                    }
                                }));

        //        // checkpoint的时间间隔，如果状态比较大，可以适当调大该值
        //        env.enableCheckpointing(1000);
        //        // 配置处理语义，默认是exactly-once
        //        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        //        // 两个checkpoint之间的最小时间间隔，防止因checkpoint时间过长，导致checkpoint积压
        //        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(500);
        //        // checkpoint执行的上限时间，如果超过该阈值，则会中断checkpoint
        //        env.getCheckpointConfig().setCheckpointTimeout(60000);
        //        // 最大并行执行的检查点数量，默认为1，可以指定多个，从而同时出发多个checkpoint，提升效率
        //        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        //        // 设定周期性外部检查点，将状态数据持久化到外部系统中，
        //        // 使用该方式不会在任务正常停止的过程中清理掉检查点数据
        //        env.getCheckpointConfig()
        //                .setExternalizedCheckpointCleanup(
        //
        // CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        DataStreamSource<String> pgSource =
                env.addSource(sourceFunction)
                        .setParallelism(1); // use parallelism 1 for sink to keep message ordering

        SingleOutputStreamOperator<ParsedCdcRecord> recordStream =
                pgSource.process(new CdcRecordRouter());

        //        recordStream
        //                .getSideOutput(SideOutputs.SUB_GRAPH_TAG)
        //                .keyBy(ParsedCdcRecord::getDb)
        //                .window(TumblingProcessingTimeWindows.of(Time.seconds(1)))
        //                .process(new ObjSubGraphProcessor())
        //                .addSink(new ObjSubGraphSink());

        // refresh model and object data
        SingleOutputStreamOperator<List<AggregatedResults>> refreshDataOperator =
                recordStream
                        .getSideOutput(SideOutputs.REFRESH_TAG)
                        .keyBy(ParsedCdcRecord::getDb)
                        .window(
                                TumblingProcessingTimeWindows.of(
                                        Time.milliseconds(refreshTimeWindowIntervalInMs)))
                        .process(new AggregationDispatcher());

        // refresh model data
        refreshDataOperator
                .getSideOutput(SideOutputs.REFRESH_MODEL_TAG)
                .addSink(new RefreshModelSink());
        // refresh object data
        refreshDataOperator
                .getSideOutput(SideOutputs.REFRESH_OBJECT_TAG)
                .addSink(new RefreshObjectSink());
        // refresh nebula graph data
        refreshDataOperator
                .getSideOutput(SideOutputs.NEBULA_GRAPH_TAG)
                .addSink(new NebulaGraphSink());

        env.execute("Flink Streaming Gravity Core PG CDC Local Test");
    }
}
