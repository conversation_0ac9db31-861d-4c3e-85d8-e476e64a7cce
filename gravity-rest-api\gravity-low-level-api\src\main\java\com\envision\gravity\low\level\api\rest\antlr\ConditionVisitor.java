// Generated from
// C:/Users/<USER>/IdeaProjects/gravity-all/gravity-rest-api/gravity-low-level-api/src/main/java/com/envision/gravity/low/level/api/rest/antlr/Condition.g4 by ANTLR 4.13.2
package com.envision.gravity.low.level.api.rest.antlr;

import org.antlr.v4.runtime.tree.ParseTreeVisitor;

/**
 * This interface defines a complete generic visitor for a parse tree produced by {@link
 * ConditionParser}.
 *
 * @param <T> The return type of the visit operation. Use {@link Void} for operations with no return
 *     type.
 */
public interface ConditionVisitor<T> extends ParseTreeVisitor<T> {
    /**
     * Visit a parse tree produced by {@link ConditionParser#parse}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParse(ConditionParser.ParseContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphAssetInRelatedModelsExpr} labeled alternative
     * in {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphAssetInRelatedModelsExpr(ConditionParser.GraphAssetInRelatedModelsExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphAndExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphAndExpr(ConditionParser.GraphAndExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphAssetInModelsExpr} labeled alternative in
     * {@link ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphAssetInModelsExpr(ConditionParser.GraphAssetInModelsExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphParenExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphParenExpr(ConditionParser.GraphParenExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphIsInExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphIsInExpr(ConditionParser.GraphIsInExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code graphComparatorExpr} labeled alternative in {@link
     * ConditionParser#graphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitGraphComparatorExpr(ConditionParser.GraphComparatorExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#joinModelGraphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinModelGraphExpr(ConditionParser.JoinModelGraphExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#joinModelTagEqExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinModelTagEqExpr(ConditionParser.JoinModelTagEqExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#joinModelTagExistsExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinModelTagExistsExpr(ConditionParser.JoinModelTagExistsExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code i18nLikeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitI18nLikeExpr(ConditionParser.I18nLikeExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code comparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitComparatorExpr(ConditionParser.ComparatorExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code joinModelExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinModelExpr(ConditionParser.JoinModelExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code isExistsExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitIsExistsExpr(ConditionParser.IsExistsExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code joinGraphExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinGraphExpr(ConditionParser.JoinGraphExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code i18nComparatorExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitI18nComparatorExpr(ConditionParser.I18nComparatorExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code inExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitInExpr(ConditionParser.InExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code likeExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitLikeExpr(ConditionParser.LikeExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code fuzzySearchExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitFuzzySearchExpr(ConditionParser.FuzzySearchExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code binaryExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitBinaryExpr(ConditionParser.BinaryExprContext ctx);
    /**
     * Visit a parse tree produced by the {@code parenExpr} labeled alternative in {@link
     * ConditionParser#expr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParenExpr(ConditionParser.ParenExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#fuzzySearchField}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitFuzzySearchField(ConditionParser.FuzzySearchFieldContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#field}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitField(ConditionParser.FieldContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#fields}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitFields(ConditionParser.FieldsContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#parenFields}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParenFields(ConditionParser.ParenFieldsContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#parenValues}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParenValues(ConditionParser.ParenValuesContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#joinGraph}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinGraph(ConditionParser.JoinGraphContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#parenGraphExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParenGraphExpr(ConditionParser.ParenGraphExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#joinModel}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitJoinModel(ConditionParser.JoinModelContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#parenJoinModelExpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitParenJoinModelExpr(ConditionParser.ParenJoinModelExprContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#modelEdgeType}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitModelEdgeType(ConditionParser.ModelEdgeTypeContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#modelParams}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitModelParams(ConditionParser.ModelParamsContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#assetInModels}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitAssetInModels(ConditionParser.AssetInModelsContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#assetInRelatedModels}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitAssetInRelatedModels(ConditionParser.AssetInRelatedModelsContext ctx);
    /**
     * Visit a parse tree produced by the {@code values} labeled alternative in {@link
     * ConditionParser#graphExprgraphExprgraphExprgraphExprgraphExprgraphExprexprexprexprexprexprexprexprexprexprexprexpr}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitValues(ConditionParser.ValuesContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#value}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitValue(ConditionParser.ValueContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#stringValue}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitStringValue(ConditionParser.StringValueContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#like}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitLike(ConditionParser.LikeContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#and}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitAnd(ConditionParser.AndContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#comparator}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitComparator(ConditionParser.ComparatorContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#booleanValue}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitBooleanValue(ConditionParser.BooleanValueContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#isExists}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitIsExists(ConditionParser.IsExistsContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#isIn}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitIsIn(ConditionParser.IsInContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#binary}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitBinary(ConditionParser.BinaryContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#timestamp}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitTimestamp(ConditionParser.TimestampContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#leftParen}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitLeftParen(ConditionParser.LeftParenContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#rightParen}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitRightParen(ConditionParser.RightParenContext ctx);
    /**
     * Visit a parse tree produced by {@link ConditionParser#i18n}.
     *
     * @param ctx the parse tree
     * @return the visitor result
     */
    T visitI18n(ConditionParser.I18nContext ctx);
}
