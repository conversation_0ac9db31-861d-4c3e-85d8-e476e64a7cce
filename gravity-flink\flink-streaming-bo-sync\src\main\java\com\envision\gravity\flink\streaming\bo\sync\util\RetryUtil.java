package com.envision.gravity.flink.streaming.bo.sync.util;

import com.envision.gravity.flink.streaming.bo.sync.config.LionConfig;

import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;

/**
 * <AUTHOR>
 * @date 2025/3/16
 * @description
 */
@Slf4j
public class RetryUtil {
    // Kafka configurations
    private static final String BOOTSTRAP_SERVERS = LionConfig.getKafkaServers();
    private static final String RETRY_TOPIC = LionConfig.getRetryKafkaTopic();
    private static volatile KafkaProducer<String, String> PRODUCER;

    private RetryUtil() {}

    /**
     * Initialize Kafka producer with configurations This method is thread-safe and will only
     * initialize the producer once
     */
    public static synchronized void initKafkaProducer() {
        if (PRODUCER == null) {
            log.info("Initializing Kafka producer with bootstrap servers: {}", BOOTSTRAP_SERVERS);

            Properties props = new Properties();
            props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
            props.put(
                    ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                    "org.apache.kafka.common.serialization.StringSerializer");
            props.put(
                    ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                    "org.apache.kafka.common.serialization.StringSerializer");

            try {
                PRODUCER = new KafkaProducer<>(props);
                log.info("Kafka producer initialized successfully, props: {}", props);
            } catch (Exception e) {
                log.error("Failed to initialize Kafka producer", e);
            }
        }
    }

    /** Safely close the Kafka producer This method ensures all resources are properly released */
    public static synchronized void closeKafkaProducer() {
        if (PRODUCER != null) {
            try {
                PRODUCER.flush();
                PRODUCER.close();
                log.info("Kafka producer closed successfully");
            } catch (Exception e) {
                log.error("Error closing Kafka producer", e);
            } finally {
                PRODUCER = null;
            }
        }
    }

    /**
     * Send message to retry topic This method handles the sending of messages to the retry queue
     * with headers
     *
     * @param headers additional metadata for the message
     * @param key message key used for partitioning
     * @param value message content
     */
    public static void sendToRetryQueue(List<Header> headers, String key, String value) {
        if (PRODUCER == null) {
            initKafkaProducer();
        }

        String headerInfo =
                headers.stream()
                        .map(h -> h.key() + "=" + new String(h.value()))
                        .collect(Collectors.joining(", "));

        try {
            ProducerRecord<String, String> record =
                    new ProducerRecord<>(RETRY_TOPIC, null, key, value, headers);

            PRODUCER.send(
                    record,
                    (metadata, exception) -> {
                        if (exception != null) {
                            log.error(
                                    "Send sync message to retry queue failed, Topic: {}, Headers: {}, Key: {}, Value: {}",
                                    RETRY_TOPIC,
                                    headerInfo,
                                    key,
                                    value,
                                    exception);
                        } else {
                            log.debug(
                                    "Send sync message to retry queue success, Topic: {}, Headers: {}, Key: {}, "
                                            + "Partition: {}, Offset: {}",
                                    metadata.topic(),
                                    headerInfo,
                                    key,
                                    metadata.partition(),
                                    metadata.offset());
                        }
                    });

            PRODUCER.flush();
        } catch (Exception e) {
            log.error(
                    "Error sending sync message to retry queue, Topic: {}, Headers: {}, Key: {}, Value: {}",
                    RETRY_TOPIC,
                    headerInfo,
                    key,
                    value,
                    e);
        }
    }
}
