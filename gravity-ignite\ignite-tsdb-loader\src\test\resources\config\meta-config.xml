<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!--
    Ignite configuration with all defaults and enabled p2p deployment and enabled events.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">
    <bean class="org.apache.ignite.configuration.IgniteConfiguration">
        <property name="sqlConfiguration">
            <bean class="org.apache.ignite.configuration.SqlConfiguration">
                <property name="sqlSchemas">
                    <list>
                        <value>o16227961710541858</value>
                        <value>TSDB</value>
                    </list>
                </property>

                <property name="queryEnginesConfiguration">
                    <list>
                        <bean class="org.apache.ignite.indexing.IndexingQueryEngineConfiguration">
                            <property name="default" value="false"/>
                        </bean>
                        <bean class="org.apache.ignite.calcite.CalciteQueryEngineConfiguration">
                            <property name="default" value="true"/>
                        </bean>
                    </list>
                </property>
            </bean>
        </property>

        <!-- Enabling the peer-class loading feature. -->
        <property name="peerClassLoadingEnabled" value="true"/>

<!--        <property name="serviceConfiguration">-->
<!--            <list>-->
<!--                &lt;!&ndash;-->
<!--                  Setting up IDService. The service will be deployed automatically according to the configuration-->
<!--                  below.-->
<!--                  &ndash;&gt;-->
<!--                <bean class="org.apache.ignite.services.ServiceConfiguration">-->
<!--                    &lt;!&ndash; Unique service name &ndash;&gt;-->
<!--                    <property name="name" value="TSDBMetricLoaderService"/>-->

<!--                    &lt;!&ndash; Service implementation's class &ndash;&gt;-->
<!--                    <property name="service">-->
<!--                        <bean class="com.envision.gravity.ignite.tsdb.loader.TSDBLoaderServiceImpl"/>-->
<!--                    </property>-->

<!--                    &lt;!&ndash; Only one instance of the service will be deployed cluster wide &ndash;&gt;-->
<!--                    <property name="totalCount" value="3"/>-->

<!--                    &lt;!&ndash; Only one instance of the service can be deployed on a single node. &ndash;&gt;-->
<!--                    <property name="maxPerNodeCount" value="1"/>-->
<!--                </bean>-->
<!--            </list>-->
<!--        </property>-->

        <property name="dataStorageConfiguration">
            <bean class="org.apache.ignite.configuration.DataStorageConfiguration">
                <property name="defaultDataRegionConfiguration">
                    <bean class="org.apache.ignite.configuration.DataRegionConfiguration">
                        <property name="persistenceEnabled" value="true"/>
                    </bean>
                </property>
            </bean>
        </property>

        <property name="discoverySpi">
            <bean class="org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi">
                <property name="ipFinder">
                    <bean class="org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder">
                        <property name="addresses">
                            <list>
                                <!-- In distributed environment, replace with actual host IP address. -->
                                <value>127.0.0.1:47500..47509</value>
                            </list>
                        </property>
                    </bean>
                </property>
            </bean>
        </property>
    </bean>
</beans>