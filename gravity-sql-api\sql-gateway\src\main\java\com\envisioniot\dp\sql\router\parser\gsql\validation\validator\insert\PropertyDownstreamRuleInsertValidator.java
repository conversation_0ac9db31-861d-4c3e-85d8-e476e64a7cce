package com.envisioniot.dp.sql.router.parser.gsql.validation.validator.insert;

import com.envisioniot.dp.sql.router.parser.gsql.mapper.TblPropertyDownstreamRuleRowMapper;
import com.envisioniot.dp.sql.router.parser.gsql.model.CallbackReq;
import com.envisioniot.dp.sql.router.parser.gsql.model.CallbackResp;
import com.envisioniot.dp.sql.router.parser.gsql.model.ValidationResp;
import com.envisioniot.dp.sql.router.parser.gsql.model.inner.BOLowLevelConstants;
import com.envisioniot.dp.sql.router.parser.gsql.validation.CallbackFunction;
import com.envisioniot.dp.sql.router.utils.ResultSetUtils;
import com.envisioniot.dp.sql.router.utils.TimeUtils;

import com.envision.gravity.common.ignite.service.BOLowLevelService;
import com.envision.gravity.common.po.TblPropertyDownstreamRule;
import com.envision.gravity.common.vo.api.LoadCacheReq;
import net.sf.jsqlparser.statement.insert.Insert;

import java.sql.SQLException;
import java.util.List;

public class PropertyDownstreamRuleInsertValidator extends BaseInsertValidator {

    private String databaseName;
    private String currentUTCTimeString;

    @Override
    protected Class<?> getPersistentObject() {
        return TblPropertyDownstreamRule.class;
    }

    @Override
    protected CallbackFunction getCallbackFunction() {
        return new PropertyDownstreamRuleInsertCallback();
    }

    @Override
    protected void customValidation(String databaseName, Insert insert) {
        this.databaseName = databaseName;
        this.currentUTCTimeString = TimeUtils.getCurrentUTCTimeString();
        autoInsertTime(insert, currentUTCTimeString);
    }

    public class PropertyDownstreamRuleInsertCallback implements CallbackFunction {
        @Override
        public CallbackResp exec(CallbackReq req) throws SQLException {

            // 1. query inserted data by sys_modified_Time
            String queryInsertSql =
                    String.format(
                            "%s '%s';",
                            "SELECT pref_rule_id, target_category, target_comp_id, target_pref_id, src_category, src_comp_id, src_pref_id, \"expression\", "
                                    + "created_time, modified_time, created_user, modified_user\n"
                                    + "FROM "
                                    + databaseName
                                    + ".tbl_property_downstream_rule where sys_modified_time = ",
                            currentUTCTimeString);
            List<TblPropertyDownstreamRule> tblPropertyDownstreamRuleList =
                    PG_REPOSITORY.executeQuery(
                            queryInsertSql, new TblPropertyDownstreamRuleRowMapper());

            if (tblPropertyDownstreamRuleList.isEmpty()) {
                return CallbackResp.builder().build();
            }

            // 2. load cache
            IGNITE_CLIENT
                    .services()
                    .serviceProxy(BOLowLevelService.SERVICE_NAME, BOLowLevelService.class)
                    .loadCache(
                            LoadCacheReq.builder()
                                    .cacheName(
                                            databaseName
                                                    + "_"
                                                    + BOLowLevelConstants
                                                            .TBL_PROPERTY_DOWNSTREAM_RULE_TABLE_NAME)
                                    .keyTypeName(
                                            databaseName
                                                    + "_"
                                                    + BOLowLevelConstants
                                                            .TBL_PROPERTY_DOWNSTREAM_RULE_TABLE_NAME
                                                    + "_KEY")
                                    .valueTypeName(
                                            databaseName
                                                    + "_"
                                                    + BOLowLevelConstants
                                                            .TBL_PROPERTY_DOWNSTREAM_RULE_TABLE_NAME
                                                    + "_VALUE")
                                    .cacheKeyCols(
                                            BOLowLevelConstants
                                                    .PROPERTY_DOWNSTREAM_RULE_CACHE_KEY_COLS)
                                    .cacheValueCols(
                                            BOLowLevelConstants
                                                    .PROPERTY_DOWNSTREAM_RULE_CACHE_VALUE_COLS)
                                    .keyValues(
                                            ResultSetUtils.convertToMap(
                                                    tblPropertyDownstreamRuleList))
                                    .build());

            return CallbackResp.builder().build();
        }

        @Override
        public ValidationResp getValidationResp() {
            return genDefaultValidationResp();
        }
    }
}
