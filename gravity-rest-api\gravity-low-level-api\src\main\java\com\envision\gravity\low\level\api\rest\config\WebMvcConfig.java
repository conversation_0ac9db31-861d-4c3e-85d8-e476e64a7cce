package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.low.level.api.rest.aspect.RequestBodyCachingFilter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/** <AUTHOR> 2024/9/18 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Bean
    public FilterRegistrationBean<RequestBodyCachingFilter> loggingFilter() {
        FilterRegistrationBean<RequestBodyCachingFilter> registrationBean =
                new FilterRegistrationBean<>();
        registrationBean.setFilter(new RequestBodyCachingFilter());
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }
}
