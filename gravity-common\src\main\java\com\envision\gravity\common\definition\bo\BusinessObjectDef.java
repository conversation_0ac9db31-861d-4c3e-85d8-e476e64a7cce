package com.envision.gravity.common.definition.bo;

import javax.validation.constraints.NotBlank;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessObjectDef {
    @NotBlank(message = "BusinessObject: assetId can not be blank")
    private String assetId;

    private List<String> modelIds;
    private JSONObject name;
    private Map<String, String> tags;
    private String systemId;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;

    private BusinessObjectDef before;
}
