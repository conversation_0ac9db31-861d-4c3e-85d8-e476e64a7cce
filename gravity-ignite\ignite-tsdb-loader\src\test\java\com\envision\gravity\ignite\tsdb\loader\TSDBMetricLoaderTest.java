package com.envision.gravity.ignite.tsdb.loader;

import java.util.*;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.cluster.ClusterState;
import org.apache.ignite.configuration.CacheConfiguration;

/** <AUTHOR> 2024/4/1 */
@Slf4j
public class TSDBMetricLoaderTest {

    public static void main(String[] args) {
        Ignite ignite = Ignition.start("config/tsdb-loader-config.xml");
        ignite.cluster().state(ClusterState.ACTIVE);
        System.out.println(">>> " + ignite.cacheNames());
        CacheConfiguration<BinaryObject, BinaryObject> cacheCfg =
                new CacheConfiguration<>("SQL_TSDB_TBL_TSDB_METRIC");
        cacheCfg.setSqlSchema("PUBLIC");

        IgniteCache<BinaryObject, BinaryObject> cache = ignite.getOrCreateCache(cacheCfg);
        // cache.query(new SqlFieldsQuery(initSQL())).getAll();

        // TSDBLoaderRequest loaderRequest = aggrQueryRequest();
        //                TSDBLoaderRequest rawRequest = rawQueryRequest();
        //                cache.loadCache(null, rawRequest);
        //
        //                TSDBLoaderRequest aggrRequest = aggrQueryRequest();
        //                cache.loadCache(null, aggrRequest);

        // TSDBLoaderRequest aggrRequest = testRequest();

        // ignite.destroyCache("TBL_TSDB_METRIC_CACHE");

        TSDBLoaderRequest request = cdsQueryRequest0530();
        cache.loadCache(null, request);
    }

    private static TSDBLoaderRequest rawQueryRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("active_power", null);
        pointWithAggrs.put("wind_speed", null);

        return new TSDBLoaderRequest()
                .requestId("mockRequestId668")
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2024-06-10 00:00:00")
                .endTime("2024-06-11 00:00:00")
                .assetIds(new HashSet<>(Arrays.asList("WassetId1", "WassetId2")))
                .timeGroup("RAW")
                .slimit(50);
    }

    private static TSDBLoaderRequest cdsQueryRequest0530() {
        // prefName => rawFieldIds
        Map<String, Set<String>> fieldMapping = new HashMap<>(2);
        // fieldMapping.put("SITE.CAL_tmp", Collections.singleton("SITE__CAL_TMP__DOUBLE"));
        fieldMapping.put("SITE.GenActivePW", Collections.singleton("SITE__GENACTIVEPW__DOUBLE"));

        // assetId => <systemId, timeZone>
        Map<String, Pair<String, String>> idTimezoneMapping = new HashMap<>(2);
        idTimezoneMapping.put("testmdmid01", Pair.of("testmdmid01", "+08:30"));
        idTimezoneMapping.put("testmdmid02", Pair.of("testmdmid02", ""));

        Map<String, String> pointWithAggrs = new HashMap<>();
        // pointWithAggrs.put("SITE.CAL_tmp", "SUM");
        pointWithAggrs.put("SITE.GenActivePW", "SUM");

        Map<String, String> prefDataTypes = new HashMap<>();
        // prefDataTypes.put("SITE.CAL_tmp", "DOUBLE");
        prefDataTypes.put("SITE.GenActivePW", "DOUBLE");

        return new TSDBLoaderRequest()
                .requestId("1584747875")
                .orgId("o16227953714301175")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2025-01-01 00:00:00")
                .endTime("2025-01-02 23:59:59")
                .assetIds(new HashSet<>(Arrays.asList("testmdmid02", "testmdmid01")))
                .timeGroup("H")
                .fieldMapping(fieldMapping)
                .prefDataTypes(prefDataTypes)
                .idTimezoneMapping(idTimezoneMapping);
    }

    private static TSDBLoaderRequest aggQueryRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("WNAC.WindSpeed", "FIRST");
        pointWithAggrs.put("WGEN.GenActivePW", "FIRST");
        pointWithAggrs.put("WNAC.TotalTheoryPW", "FIRST");

        return new TSDBLoaderRequest()
                .requestId("1076473744")
                .orgId("o17231990549791976")
                .pointWithAggrs(pointWithAggrs)
                .startTs(1726070400000L)
                .endTs(1726156799000L)
                .assetIds(
                        new HashSet<>(
                                Arrays.asList(
                                        "06ydp078fi02ih",
                                        "06ydp075z2000t",
                                        "06ydp078g801lx",
                                        "06ydp078f702dj",
                                        "06ydp078f302bd")))
                .timeGroup("15m")
                .slimit(42666);
    }

    private static TSDBLoaderRequest testRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("ActPowOut", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2024-02-19 00:00:00")
                .endTime("2024-03-30 00:00:00")
                .assetIds(Collections.singleton("IiXFn7j7"))
                .timeGroup("RAW")
                .slimit(null);
    }

    private static TSDBLoaderRequest rawQueryRequest1() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("SCADATagState", null);

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2024-02-20 00:00:00")
                .endTime("2024-03-30 00:00:00")
                .assetIds(Collections.singleton("8B1EzpQF"))
                .timeGroup("RAW")
                .slimit(10);
    }

    private static TSDBLoaderRequest aggrQueryRequest() {
        Map<String, String> pointWithAggrs = new HashMap<>();
        pointWithAggrs.put("ActPowOut", "count");
        pointWithAggrs.put("SCADATagState", "count");

        return new TSDBLoaderRequest()
                .orgId("o16227961710541858")
                .pointWithAggrs(pointWithAggrs)
                .startTime("2024-02-20 00:00:00")
                .endTime("2024-03-30 00:00:00")
                .assetIds(Collections.singleton("8B1EzpQF"))
                .timeGroup("10m")
                .slimit(10);
    }

    private static void print(String msg) {
        System.out.println();
        System.out.println(">>> " + msg);
    }

    private static String initSQL() {
        return "-- tbl_obj\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_obj;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_obj(\n"
                + "system_id VARCHAR, \"attr1\" VARCHAR, \"attr2\" DOUBLE, \"attr3\" DOUBLE, \"pt1_time\" TIMESTAMP, \"pt1_value\" DOUBLE, \"pt2_time\" TIMESTAMP, \"pt2_value\" DOUBLE, \"pt2_quality\" TINYINT, \"pt3_time\" TIMESTAMP, \"pt3_value\" DOUBLE, PRIMARY KEY(system_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId1', 'jiangxi', 20.38, null, '2024-04-27 18:41:00', 20, '2024-04-27 18:40:00', 12.88, 0, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId2', 'jiangxi', 21.38, null, '2024-04-27 18:41:00', 25, '2024-04-27 18:40:00', 12.88, 0, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId4', 'shanghai', 20.38, null, '2024-04-27 18:42:00', 20, '2024-04-27 18:41:00', 15.88, 1, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId3', 'shanghai', 20.38, null, '2024-04-27 18:42:00', 20, '2024-04-27 18:41:00', 13.88, 1, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId5', 'jiangxi', 20.38, null, '2024-04-27 18:42:00', 20, '2024-04-27 18:41:00', 15.88, 0, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId6', 'jiangxi', 20.38, null, '2024-04-27 18:42:00', 20, '2024-04-27 18:41:00', 16.88, 0, null, null);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId7', null, null, 20.38, null, null, null, null, null, '2024-04-27 18:41:00', 16.88);\n"
                + "INSERT INTO PUBLIC.tbl_obj(system_id, \"attr1\", \"attr2\", \"attr3\", \"pt1_time\", \"pt1_value\", \"pt2_time\", \"pt2_value\", \"pt2_quality\", \"pt3_time\", \"pt3_value\") values('assetId8', null, null, 20.38, null, null, null, null, null, '2024-04-27 18:41:00', 16.88);\n"
                + "\n"
                + "-- tbl_bo\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_bo;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_bo(asset_id VARCHAR, asset_display_name VARCHAR, system_id VARCHAR, PRIMARY KEY(asset_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId1','WindAsset1','assetId1');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId2','WindAsset2','assetId2');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId3','WindAsset3','assetId3');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId4','WindAsset4','assetId4');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId5','WindAsset5','assetId5');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('WassetId6','WindAsset6','assetId6');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('SassetId7','SolarAsset7','assetId7');\n"
                + "INSERT INTO PUBLIC.tbl_bo(asset_id, asset_display_name, system_id) values ('SassetId8','SolarAsset8','assetId8');\n"
                + "\n"
                + "-- tbl_bo_group_relation\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_bo_group_relation;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_bo_group_relation(group_id VARCHAR, asset_id VARCHAR, create_time TIMESTAMP, PRIMARY KEY(group_id, asset_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId1','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId2','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId3','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId4','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId5','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group1','WassetId6','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group2','SassetId7','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group_relation(group_id, asset_id, create_time) values ('group2','SassetId8','2024-04-27 18:42:00');\n"
                + "\n"
                + "-- tbl_bo_group\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_bo_group;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_bo_group(group_id VARCHAR, group_name VARCHAR, PRIMARY KEY(group_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_bo_group(group_id, group_name) values ('group1','风机设备组合1');\n"
                + "INSERT INTO PUBLIC.tbl_bo_group(group_id, group_name) values ('group2','光伏设备组合2');\n"
                + "\n"
                + "-- tbl_pref\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_pref;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_pref(pref_id VARCHAR, pref_name VARCHAR, pref_display_name VARCHAR, pref_type VARCHAR, pref_data_type VARCHAR, has_quality BOOLEAN, PRIMARY KEY(pref_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_pref(pref_id, pref_name, pref_display_name, pref_type, pref_data_type, has_quality) values ('pref1','location','省份','ATTRIBUTE','STRING',false);\n"
                + "INSERT INTO PUBLIC.tbl_pref(pref_id, pref_name, pref_display_name, pref_type, pref_data_type, has_quality) values ('pref2','capacity','容量','ATTRIBUTE','DOUBLE',false);\n"
                + "INSERT INTO PUBLIC.tbl_pref(pref_id, pref_name, pref_display_name, pref_type, pref_data_type, has_quality) values ('pref3','active_power','有功功率','MEASUREPOINT','DOUBLE',false);\n"
                + "INSERT INTO PUBLIC.tbl_pref(pref_id, pref_name, pref_display_name, pref_type, pref_data_type, has_quality) values ('pref4','wind_speed','风速','MEASUREPOINT','FLOAT',true);\n"
                + "\n"
                + "-- tbl_component_pref\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_component_pref;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_component_pref(comp_id VARCHAR, pref_id VARCHAR, field_id VARCHAR, PRIMARY KEY(comp_id, pref_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp1','pref1', 'attr1');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp1','pref2', 'attr2');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp1','pref3', 'pt1');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp1','pref4', 'pt2');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp2','pref1', 'attr1');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp2','pref2', 'attr2');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp2','pref3', 'pt1');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp2','pref4', 'pt2');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp3','pref2', 'attr3');\n"
                + "INSERT INTO PUBLIC.tbl_component_pref(comp_id, pref_id, field_id) values ('comp3','pref3', 'pt3');\n"
                + "\n"
                + "-- tbl_component\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_component;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_component(comp_id VARCHAR, comp_name VARCHAR, anonymous BOOLEAN, PRIMARY KEY(comp_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_component(comp_id, comp_name, anonymous) values ('comp1','风机Comp1', false);\n"
                + "INSERT INTO PUBLIC.tbl_component(comp_id, comp_name, anonymous) values ('comp2','风机Comp2', true);\n"
                + "INSERT INTO PUBLIC.tbl_component(comp_id, comp_name, anonymous) values ('comp3','光伏Comp3', true);\n"
                + "\n"
                + "-- tbl_bo_model_comp\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_bo_model_comp;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_bo_model_comp(model_id VARCHAR, comp_id VARCHAR, create_time TIMESTAMP, PRIMARY KEY(model_id, comp_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_bo_model_comp(model_id, comp_id, create_time) values ('Turbine_Access_Model','comp1','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_model_comp(model_id, comp_id, create_time) values ('Turbine_Access_Model','comp2','2024-04-27 18:42:00');\n"
                + "INSERT INTO PUBLIC.tbl_bo_model_comp(model_id, comp_id, create_time) values ('Solar_Model','comp3','2024-04-27 18:42:00');\n"
                + "\n"
                + "-- tbl_bo_model\n"
                + "-- DROP TABLE IF EXISTS PUBLIC.tbl_bo_model;\n"
                + "CREATE TABLE IF NOT EXISTS PUBLIC.tbl_bo_model(model_id VARCHAR, model_display_name VARCHAR, group_id VARCHAR, PRIMARY KEY(model_id))\n"
                + "WITH \"template=REPLICATED\";\n"
                + "INSERT INTO PUBLIC.tbl_bo_model(model_id, model_display_name, group_id) values ('Turbine_Access_Model','Turbine_Access_Model','group1');\n"
                + "INSERT INTO PUBLIC.tbl_bo_model(model_id, model_display_name, group_id) values ('Solar_Model','Solar_Model','group2');";
    }
}
