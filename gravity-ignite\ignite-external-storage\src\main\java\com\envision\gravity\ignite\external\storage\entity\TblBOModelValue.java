package com.envision.gravity.ignite.external.storage.entity;

import java.io.Serializable;
import java.sql.Timestamp;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/19
 * @description
 */
@Data
public class TblBOModelValue implements Serializable {
    private static final long serialVersionUID = 9182513785748485866L;
    private String modelId;
    private String modelDisplayName;
    private String description;
    private String comment;
    private String groupId;
    private String modelPath;
    private Timestamp createdTime;
    private String createdUser;
    private Timestamp modifiedTime;
    private String modifiedUser;
}
