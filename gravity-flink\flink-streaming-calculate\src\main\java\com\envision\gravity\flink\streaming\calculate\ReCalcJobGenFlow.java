package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.common.utils.FlinkCommonUtils;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcRouter;
import com.envision.gravity.flink.streaming.calculate.cdc.CalcCdcWindowCollector;
import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobMetaInfo;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.recalc.ReCalcCdcProcessor;
import com.envision.gravity.flink.streaming.calculate.recalc.ReCalcJobGenerator;
import com.envision.gravity.flink.streaming.calculate.recalc.ReCalcJobPgWriter;
import com.envision.gravity.flink.streaming.calculate.recalc.ReCalcJobTrigger;

import java.time.Duration;
import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.triggers.CountTrigger;
import org.apache.flink.streaming.api.windowing.triggers.ProcessingTimeoutTrigger;
import org.apache.flink.streaming.api.windowing.triggers.PurgingTrigger;

public class ReCalcJobGenFlow {

    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // env.getConfig().enableObjectReuse();
        // env.getConfig().disableGenericTypes(); // 注释掉以支持泛型类型
        FlinkCommonUtils.tryRunAsLocalModel(args, env);
        // TODO test
        env.setParallelism(2);

        // Initialize ReCalc Job Generation Flow
        initReCalcJobGenFlow(env);

        // Execute the flow
        env.execute("ReCalc Job Generation Flow");
    }

    /**
     * Initialize ReCalc Job Generation Flow
     *
     * @param env Flink StreamExecutionEnvironment
     */
    public static void initReCalcJobGenFlow(StreamExecutionEnvironment env) {
        // CDC 流独立处理，不连接到主流
        DataStream<ConvertedCdcRecord> cdcStream =
                env.addSource(initPgCdcSource())
                        .name("pgcdc")
                        .setParallelism(1)
                        .process(new CalcCdcRouter())
                        .name("cdc-router")
                        .broadcast()
                        .keyBy(record -> record.getTable() + "_" + record.getPrimaryKey())
                        .countWindow(CalcLionConfig.getReCalcPgCdcWindowBatchSize())
                        .trigger(
                                PurgingTrigger.of(
                                        ProcessingTimeoutTrigger.of(
                                                CountTrigger.of(
                                                        CalcLionConfig
                                                                .getReCalcPgCdcWindowBatchSize()),
                                                Duration.ofSeconds(
                                                        CalcLionConfig
                                                                .getReCalcPgCdcWindowTimeoutSeconds()))))
                        .process(new CalcCdcWindowCollector());

        // CDC 流处理：CDC事件 -> 元数据信息 -> 作业生成 -> 数据库写入 -> 作业触发
        DataStream<CalcJobMetaInfo> metaInfoStream =
                cdcStream.process(new ReCalcCdcProcessor()).name("recalc cdc processor");

        DataStream<TblCalcJobInfo> jobInfoStream =
                metaInfoStream.process(new ReCalcJobGenerator()).name("recalc job generator");

        DataStream<TblCalcJobInfo> savedJobStream =
                jobInfoStream.process(new ReCalcJobPgWriter()).name("recalc job pg writer");

        savedJobStream.process(new ReCalcJobTrigger()).name("recalc job trigger");
    }

    private static SourceFunction<String> initPgCdcSource() {
        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", CalcLionConfig.getReCalcPgCdcSchemaList());
        properties.setProperty("table.include.list", CalcLionConfig.getReCalcPgCdcTableList());
        //        properties.setProperty(
        //                "column.include.list", CalculateConfigOptions.getPgCdcColumnList());
        properties.setProperty("max.batch.size", CalcLionConfig.getReCalcPgCdcMaxBatchSize());
        properties.setProperty("max.queue.size", CalcLionConfig.getReCalcPgCdcMaxQueueSize());

        return PostgreSQLSource.<String>builder()
                .hostname(CalcLionConfig.getPgHostname())
                .port(CalcLionConfig.getPgPort())
                .database(CalcLionConfig.getPgDatabase()) // monitor postgres database
                .username(CalcLionConfig.getPgUsername())
                .password(CalcLionConfig.getPgPassword())
                .decodingPluginName("pgoutput")
                .slotName(CalcLionConfig.getReCalcPgCdcSlotName())
                .debeziumProperties(properties)
                .deserializer(new JsonDebeziumDeserializationSchema())
                .build();
    }
}
