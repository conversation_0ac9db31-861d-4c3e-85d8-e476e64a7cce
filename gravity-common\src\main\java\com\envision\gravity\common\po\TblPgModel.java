package com.envision.gravity.common.po;

import java.sql.Timestamp;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/18 16:48 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TblPgModel {

    private String modelId;

    private String modelDisplayName;

    private String description;

    private String orgId;

    private String modelPath;

    private JSONObject modelTags;

    private JSONObject properties;

    private Timestamp modelCreatedTime;

    private String modelCreatedUser;

    private Timestamp modelModifiedTime;

    private String modelModifiedUser;

    private Timestamp createdTime;

    private Timestamp modifiedTime;
}
