package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/4/26
 * @description
 */
public interface TblComponentMapper {
    /**
     * @param schemaName schemaName
     * @param compIdList compIdList
     * @return {@link ModelGroup} list
     */
    @SelectProvider(type = TblComponentSqlProvider.class, method = "selectModelGroupList")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupList(String schemaName, List<String> compIdList);

    /**
     * @param schemaName schemaName
     * @param compIdList compIdList
     * @return {@link ModelGroup} list
     */
    @SelectProvider(type = TblComponentSqlProvider.class, method = "selectModelGroupByComp")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupByComp(String schemaName, List<String> compIdList);
}
