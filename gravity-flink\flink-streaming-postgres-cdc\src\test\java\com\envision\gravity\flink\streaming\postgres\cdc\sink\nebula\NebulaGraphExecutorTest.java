package com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula;

import java.util.Arrays;
import java.util.List;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/12/18
 * @description
 */
class NebulaGraphExecutorTest {

    @Test
    void deleteVertices() {
        NebulaGraphExecutor nebulaGraphExecutor = new NebulaGraphExecutor();
        String spaceName = "O17186913277371853";
        List<String> vidList = Arrays.asList("a", "b", "c");
        nebulaGraphExecutor.deleteVertices(spaceName, vidList);
    }
}
