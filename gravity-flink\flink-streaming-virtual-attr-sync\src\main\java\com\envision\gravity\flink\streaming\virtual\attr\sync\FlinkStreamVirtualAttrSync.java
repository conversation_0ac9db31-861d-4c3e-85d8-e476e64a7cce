package com.envision.gravity.flink.streaming.virtual.attr.sync;

import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.CdcRecordParser;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.QueryVirtualAttrValue;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.RefreshReqAggregator;
import com.envision.gravity.flink.streaming.virtual.attr.sync.function.ScheduledSource;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.RefreshReq;
import com.envision.gravity.flink.streaming.virtual.attr.sync.sink.RefreshVirtualAttrValueSink;

import java.util.Properties;


import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class FlinkStreamVirtualAttrSync {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getCheckpointConfig().setCheckpointTimeout(10800000L);
        Properties properties = new Properties();
        properties.setProperty("snapshot.mode", "never"); // always：Full   never:Increment
        properties.setProperty("schema.include.list", LionConfig.getPgSchemaList());
        properties.setProperty("table.include.list", LionConfig.getPgTableList());
        properties.setProperty("max.batch.size", LionConfig.getMaxBatchSize());
        properties.setProperty("max.queue.size", LionConfig.getMaxQueueSize());
        //        properties.setProperty("publication.name", LionConfig.getPublicationName());
        //        properties.setProperty("publication.autocreate.mode", "filtered");

        SourceFunction<String> sourceFunction =
                PostgreSQLSource.<String>builder()
                        .hostname(LionConfig.getPgHostname())
                        .port(LionConfig.getPgPort())
                        .database(LionConfig.getPgDatabase()) // monitor postgres database
                        .username(LionConfig.getPgUsername())
                        .password(LionConfig.getPgPassword())
                        .decodingPluginName("pgoutput")
                        .slotName(LionConfig.getSlotName())
                        .debeziumProperties(properties)
                        .deserializer(
                                new JsonDebeziumDeserializationSchema()) // converts SourceRecord to
                        // JSON String
                        .build();

        SingleOutputStreamOperator<RefreshReq> pgCdcSource =
                env.addSource(sourceFunction).process(new CdcRecordParser());
        DataStreamSource<RefreshReq> scheduledSource = env.addSource(new ScheduledSource());

        DataStream<RefreshReq> mergedStream = pgCdcSource.union(scheduledSource);

        mergedStream
                .keyBy(RefreshReq::getSchemaName)
                .window(
                        TumblingProcessingTimeWindows.of(
                                Time.hours(LionConfig.getSyncTimeWindowIntervalInHours())))
                .process(new RefreshReqAggregator())
                .process(new QueryVirtualAttrValue())
                .addSink(new RefreshVirtualAttrValueSink());

        env.execute("Flink Streaming Gravity Virtual Attr Sync");
    }
}
