<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-all</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>gravity-ignite</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>gravity-ignite</name>

    <modules>
        <module>ignite-external-storage</module>
        <module>ignite-udf</module>
        <module>ignite-tsdb-loader</module>
        <module>bo-ignite-service</module>
        <module>bo-low-level-ignite-service</module>
        <module>tsdb-ignite-service</module>
        <module>gravity-ignite-core</module>
        <module>tbl-obj-ignite-service</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                    </archive>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
