package com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelAsset {
    private String modelId;
    private String assetId;
    private String systemId;
}
