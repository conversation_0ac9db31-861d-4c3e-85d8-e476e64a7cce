package com.envision.gravity.ignite.tsdb.loader.bo;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/** <AUTHOR> 2024/3/26 */
public enum BOModelFieldDataType {
    BOOLEAN("BOOLEAN") {
        @Override
        public Boolean parse(Object value) {
            return value instanceof Boolean ? (Boolean) value : Boolean.valueOf(value.toString());
        }
    },
    INTEGER("INTEGER") { // Int always cast to long
        @Override
        public Long parse(Object value) {
            return value instanceof Long ? (Long) value : Long.valueOf(value.toString());
        }
    },
    LONG("LONG") {
        @Override
        public Long parse(Object value) {
            return value instanceof Long ? (Long) value : Long.valueOf(value.toString());
        }
    },
    FLOAT("FLOAT") {
        @Override
        public Float parse(Object value) {
            return value instanceof Float ? (Float) value : Float.valueOf(value.toString());
        }
    },
    DOUBLE("DOUBLE") {
        @Override
        public Double parse(Object value) {
            return value instanceof Double ? (Double) value : Double.valueOf(value.toString());
        }
    },
    DURATION("DURATION") {
        @Override
        public Double parse(Object value) {
            return value instanceof Double ? (Double) value : Double.valueOf(value.toString());
        }
    },
    DATE("DATE") {
        @Override
        public Date parse(Object value) {
            return value instanceof Date ? (Date) value : Date.valueOf(value.toString());
        }
    },
    TIME("TIME") {
        @Override
        public Time parse(Object value) {
            return value instanceof Time ? (Time) value : Time.valueOf(value.toString());
        }
    },
    DATETIME("DATETIME") {
        @Override
        public Timestamp parse(Object value) {
            return value instanceof Timestamp
                    ? (Timestamp) value
                    : Timestamp.valueOf(value.toString());
        }
    },
    STRING("STRING") {
        @Override
        public String parse(Object value) {
            return value.toString();
        }
    },
    OBJECT("OBJECT") {
        @Override
        public String parse(Object value) {
            return value.toString();
        }
    },
    MAP("MAP") {
        @Override
        public String parse(Object value) {
            return value.toString();
        }
    },
    ENUM("ENUM") {
        @Override
        public String parse(Object value) {
            return value.toString();
        }
    },
    ARRAY("ARRAY") {
        @Override
        public String parse(Object value) {
            return value.toString();
        }
    },
    UNKNOWN("UNKNOWN") {
        @Override
        public Object parse(Object value) {
            throw new UnsupportedOperationException();
        }
    };

    private static final Map<String, BOModelFieldDataType> VALUE_MAP = new HashMap<>();

    static {
        for (BOModelFieldDataType type : BOModelFieldDataType.values()) {
            VALUE_MAP.put(type.name, type);
        }
    }

    public static BOModelFieldDataType of(String value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private final String name;

    public String getName() {
        return name;
    }

    BOModelFieldDataType(String name) {
        this.name = name;
    }

    public Object parse(Object value) {
        throw new UnsupportedOperationException();
    }
}
