package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.sync.QuerySyncLogsReq;
import com.envision.gravity.common.vo.sync.SyncBORelationsReq;
import com.envision.gravity.common.vo.sync.SyncBOsReq;
import com.envision.gravity.low.level.api.rest.service.BOSyncDeletionService;
import com.envision.gravity.low.level.api.rest.service.BOSyncService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @description
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/sync")
@Tag(name = "Sync Operations", description = "APIs for synchronization operations")
public class SyncController {
    @Resource private BOSyncService boSyncService;

    @Resource private BOSyncDeletionService boSyncDeletionService;

    @PostMapping(value = "/bos")
    public ResponseResult<?> syncBOs(
            @NotBlank(message = "syncId can not be blank") @RequestParam String syncId,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody List<@Valid SyncBOsReq> bOs) {
        if (bOs.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        return boSyncService.syncBOs(syncId, orgId, bOs);
    }

    @PostMapping(value = "/bo-relations")
    public ResponseResult<?> syncBORelations(
            @NotBlank(message = "syncId can not be blank") @RequestParam String syncId,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody List<@Valid SyncBORelationsReq> boRelations) {
        if (boRelations.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        return boSyncService.syncBORelations(syncId, orgId, boRelations);
    }

    @DeleteMapping("/bos")
    public ResponseResult<?> deleteBOs(
            @NotBlank(message = "syncId can not be blank") @RequestParam String syncId,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid List<String> assetIdList) {
        if (assetIdList.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        return boSyncDeletionService.deleteBOs(syncId, orgId, assetIdList);
    }

    @DeleteMapping("/bo-relations")
    public ResponseResult<?> deleteBORelations(
            @NotBlank(message = "syncId can not be blank") @RequestParam String syncId,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid List<String> graphIdList) {
        if (graphIdList.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }
        return boSyncDeletionService.deleteBORelations(syncId, orgId, graphIdList);
    }

    @PostMapping("/logs")
    public ResponseResult<?> querySyncLogs(
            @NotBlank(message = "syncId can not be blank") @RequestParam String syncId,
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid QuerySyncLogsReq querySyncLogsReq) {
        return boSyncService.querySyncLogs(syncId, orgId, querySyncLogsReq);
    }
}
