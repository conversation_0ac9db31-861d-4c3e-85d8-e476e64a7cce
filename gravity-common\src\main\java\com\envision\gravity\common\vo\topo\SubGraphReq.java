package com.envision.gravity.common.vo.topo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@Builder
public class SubGraphReq {

    @NotBlank(message = "subGraphId can not blank")
    private String subGraphId;

    @NotNull(message = "subGraphDisplayName can not null")
    private JSONObject subGraphDisplayName;

    private boolean tree;
}
