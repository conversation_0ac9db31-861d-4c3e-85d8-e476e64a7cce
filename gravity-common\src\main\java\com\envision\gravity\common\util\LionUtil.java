package com.envision.gravity.common.util;


import com.envision.arch.lion.client.ConfigCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LionUtil {

    private static final Logger logger = LoggerFactory.getLogger(LionUtil.class);

    private static ConfigCache configCache;

    static {
        configCache = ConfigCache.getInstance();
    }

    private static String getValue(String configName) {
        String val = null;
        try {
            if (configCache != null) {
                val = configCache.getProperty(configName);
            }
        } catch (Exception e) {
            logger.error("lion: get key error, key = " + configName, e);
            return null;
        }
        return val;
    }

    public static String getStringValue(String configName) {
        return getStringValue(configName, null);
    }

    public static String getStringValue(String configName, String defaultVal) {
        String val = getValue(configName);
        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, int defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Integer.parseInt(val);
    }

    public static long getLongValue(String configName, long defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Long.parseLong(val);
    }

    public static boolean getBooleanValue(String configName, boolean defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Boolean.parseBoolean(val);
    }

    public static double getDoubleValue(String configName, double defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Double.parseDouble(val);
    }
}
