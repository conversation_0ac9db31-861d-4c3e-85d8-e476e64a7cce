package com.envision.gravity.low.level.api.sql.common;

import com.envision.gravity.low.level.api.sql.table.CacheCfgTableInfo;
import com.envision.gravity.low.level.api.sql.table.cache.cfg.TblObjPointPartCfg;


import lombok.Getter;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheMode;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @description
 */
@Getter
public enum BOCoreCacheCfgTableEnum {
    // Cache Cfg Table
    TBL_OBJ_POINT_PART(
            "TBL_OBJ_POINT_PART",
            CacheMode.PARTITIONED,
            CacheAtomicityMode.ATOMIC,
            new TblObjPointPartCfg(),
            8);

    BOCoreCacheCfgTableEnum(
            String queryEntityTableName,
            CacheMode cacheMode,
            CacheAtomicityMode atomicityMode,
            CacheCfgTableInfo cacheCfgTableInfo,
            int queryParallelism) {
        this.queryEntityTableName = queryEntityTableName;
        this.cacheMode = cacheMode;
        this.atomicityMode = atomicityMode;
        this.cacheCfgTableInfo = cacheCfgTableInfo;
        this.queryParallelism = queryParallelism;
    }

    private final String queryEntityTableName;
    private final CacheMode cacheMode;
    private final CacheAtomicityMode atomicityMode;
    private final CacheCfgTableInfo cacheCfgTableInfo;
    private final int queryParallelism;
}
