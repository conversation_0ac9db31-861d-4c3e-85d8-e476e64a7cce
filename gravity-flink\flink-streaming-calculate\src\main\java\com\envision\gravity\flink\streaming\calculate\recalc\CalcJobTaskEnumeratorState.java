package com.envision.gravity.flink.streaming.calculate.recalc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 计算作业任务枚举器状态
 *
 * <AUTHOR>
 */
public class CalcJobTaskEnumeratorState implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<CalcJobTaskSplit> pendingSplits;
    private Set<String> completedSplitIds;
    private int nextTaskId;
    private boolean isInitialized;
    private long totalTaskCount;
    private long completedTaskCount;

    public CalcJobTaskEnumeratorState() {
        this.pendingSplits = new ArrayList<>();
        this.completedSplitIds = new HashSet<>();
        this.nextTaskId = 1;
        this.isInitialized = false;
        this.totalTaskCount = 0;
        this.completedTaskCount = 0;
    }

    public CalcJobTaskEnumeratorState(
            List<CalcJobTaskSplit> pendingSplits,
            Set<String> completedSplitIds,
            int nextTaskId,
            boolean isInitialized) {
        this.pendingSplits = pendingSplits != null ? pendingSplits : new ArrayList<>();
        this.completedSplitIds = completedSplitIds != null ? completedSplitIds : new HashSet<>();
        this.nextTaskId = nextTaskId;
        this.isInitialized = isInitialized;
        this.totalTaskCount = this.pendingSplits.size() + this.completedSplitIds.size();
        this.completedTaskCount = this.completedSplitIds.size();
    }

    // Getters and Setters
    public List<CalcJobTaskSplit> getPendingSplits() {
        return pendingSplits;
    }

    public void setPendingSplits(List<CalcJobTaskSplit> pendingSplits) {
        this.pendingSplits = pendingSplits;
    }

    public Set<String> getCompletedSplitIds() {
        return completedSplitIds;
    }

    public void setCompletedSplitIds(Set<String> completedSplitIds) {
        this.completedSplitIds = completedSplitIds;
    }

    public int getNextTaskId() {
        return nextTaskId;
    }

    public void setNextTaskId(int nextTaskId) {
        this.nextTaskId = nextTaskId;
    }

    public boolean isInitialized() {
        return isInitialized;
    }

    public void setInitialized(boolean initialized) {
        isInitialized = initialized;
    }

    public long getTotalTaskCount() {
        return totalTaskCount;
    }

    public void setTotalTaskCount(long totalTaskCount) {
        this.totalTaskCount = totalTaskCount;
    }

    public long getCompletedTaskCount() {
        return completedTaskCount;
    }

    public void setCompletedTaskCount(long completedTaskCount) {
        this.completedTaskCount = completedTaskCount;
    }

    @Override
    public String toString() {
        return "CalcJobTaskEnumeratorState{"
                + "pendingSplits="
                + (pendingSplits != null ? pendingSplits.size() : 0)
                + ", completedSplitIds="
                + (completedSplitIds != null ? completedSplitIds.size() : 0)
                + ", nextTaskId="
                + nextTaskId
                + ", isInitialized="
                + isInitialized
                + ", totalTaskCount="
                + totalTaskCount
                + ", completedTaskCount="
                + completedTaskCount
                + '}';
    }
}
