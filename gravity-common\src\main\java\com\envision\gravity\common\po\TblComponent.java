package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblComponent {
    @KeyColumn(name = "comp_id")
    @ColumnName("comp_id")
    @RequiredField(message = "comp_id field is required")
    private String compId;

    @ValueColumn(name = "comp_name")
    @ColumnName("comp_name")
    @RequiredField(message = "comp_name field is required")
    private String compName;

    @ValueColumn(name = "comp_display_name")
    @ColumnName("comp_display_name")
    @RequiredField(message = "comp_display_name field is required")
    private String compDisplayName;

    @ValueColumn(name = "description")
    @ColumnName("description")
    private String description;

    @ValueColumn(name = "comment")
    @ColumnName("comment")
    private String comment;

    @ValueColumn(name = "anonymous", type = Boolean.class)
    @ColumnName("anonymous")
    private Boolean anonymous;

    @ValueColumn(name = "template")
    @ColumnName("template")
    private String template;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
