package com.envision.gravity.flink.streaming.bo.sync.sink.req;

import com.envision.gravity.common.vo.sync.SyncBORelationsReq;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.alibaba.fastjson.JSON;
import com.envision.apim.poseidon.request.PoseidonRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/2
 * @description
 */
@Getter
public class UpsertBORelationsReq extends PoseidonRequest implements Serializable {

    private static final long serialVersionUID = 9113080734109345308L;
    private String syncId;
    private String orgId;
    @Setter private List<SyncBORelationsReq> boRelations;

    @Override
    public String baseUri() {
        return "/gravity-service/v3.0/sync/bo-relations";
    }

    @Override
    public String method() {
        return "POST";
    }

    @Override
    public Map<String, String> headerParams() {
        Map<String, String> params = new HashMap<>(2);
        params.put("x-audit", "{\"internal_only\":true}");
        return params;
    }

    public UpsertBORelationsReq(Builder builder) {
        setSyncId(builder.syncId);
        setOrgId(builder.orgId);
        setBoRelations(builder.boRelations);
    }

    public void setSyncId(String syncId) {
        this.syncId = syncId;
        queryParams().put("syncId", syncId);
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
        queryParams().put("orgId", orgId);
    }

    @Override
    public String jsonBodyString() {
        return JSON.toJSONString(boRelations);
    }

    public static final class Builder {

        private String syncId;
        private String orgId;
        private List<SyncBORelationsReq> boRelations;

        private Builder() {}

        public static Builder newBuilder() {
            return new Builder();
        }

        public Builder setBORelations(List<SyncBORelationsReq> boRelations) {
            this.boRelations = boRelations;
            return this;
        }

        public Builder setSyncId(String syncId) {
            this.syncId = syncId;
            return this;
        }

        public Builder setOrgId(String orgId) {
            this.orgId = orgId;
            return this;
        }

        public UpsertBORelationsReq build() {
            return new UpsertBORelationsReq(this);
        }
    }
}
