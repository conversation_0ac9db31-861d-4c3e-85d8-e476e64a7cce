<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-ignite</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>bo-low-level-ignite-service</artifactId>
    <packaging>jar</packaging>
    <version>${release.version}</version>
    <name>bo-low-level-ignite-service</name>
    <description>bo-low-level-ignite-service</description>

    <dependencies>
        <!-- gravity -->
        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>gravity-common</artifactId>
        </dependency>
        <!-- gravity -->

        <!-- ignite -->
        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-core</artifactId>
        </dependency>
        <!-- ignite -->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- log -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- log -->

        <!-- for test -->
        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-spring</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.ignite</groupId>
            <artifactId>ignite-indexing</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.eniot</groupId>
            <artifactId>metric-engine-sdk</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.envision.gravity</groupId>
            <artifactId>ignite-udf</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- for test -->

    </dependencies>

</project>
