package com.envision.gravity.flink.streaming.virtual.attr.sync.function;


import com.eniot.metricengine.MetricEngine;
import com.eniot.metricengine.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
@Slf4j
class MetricEngineTest {
    @Test
    void metricEngineTest() {
        long queryStartTime = System.currentTimeMillis();
        String schemaName = "o17231990549791976";
        String querySql =
                "SELECT MDMID,`CbxType` \n"
                        + "FROM _attr\n"
                        + "WHERE MDMID IN ('demo24vc4o9rQa', 'demo24w2G1va01', 'demo24x5xtjEjh', 'demo24ydmZC1Hj', 'demo24z0CFh1Jx', 'demo24zJt8kMi3', 'demo25CpVNI2Hp', 'demo25LPUrIrCJ', 'demo25UPZyOJtO', 'demo25xieUckME', 'demo25vCivsUNb', 'demo25t8bTEgCy', 'demo251SDZC9NL', 'demo252exm1mom', 'demo255SGEjbst', 'demo256wXBjoki', 'demo257gGDB6RY', 'demo259DBCs1HS', 'demo25A76QKcJX', 'demo25A7LOyoju', 'demo25AmEUCSmj', 'demo25DlEHsstb', 'demo25EDHEXhLm', 'demo25EXCUSNho', 'demo25FTee8Wq6', 'demo25Hogb969D', 'demo25IbKa9ySk', 'demo25IleWccbY', 'demo25IsZyeWLl', 'demo25KXJ5pzP7', 'demo25LRbIRZy4', 'demo25OR8PrlGM', 'demo25PRMKNJnQ', 'demo25PWmHdrwQ', 'demo25PeAM3d9s', 'demo25Pz81oMxc', 'demo25QnBahMpE', 'demo25So3V1hBN', 'demo25Tlfr2Leu', 'demo25XjkH8TUO', 'demo25ZO5aAQHA', 'demo25ZQwzsTDv', 'demo25arOjHq2b', 'demo25arn4QHdV', 'demo25dXy95gWA', 'demo25dywU6Tbo', 'demo25fX2ssSbp', 'demo25hQYsTnjQ', 'demo25jInRS5jE', 'demo25lpIU9wE8', 'demo25mFqAyGs8', 'demo25mVp8eZjf', 'demo25o0wRYdKF', 'demo25olWP6uxd', 'demo25pD4PLSie', 'demo25pxvIuT90', 'demo25qE9BcQwM', 'demo25rmEMONxZ', 'demo25sCluIKup', 'demo25smFJH3nL', 'demo25uSkC36Je', 'demo25vc4o9rQa', 'demo25w2G1va01', 'demo25x5xtjEjh', 'demo25ydmZC1Hj', 'demo25z0CFh1Jx', 'demo25zJt8kMi3', 'demo26CpVNI2Hp', 'demo26LPUrIrCJ', 'demo26UPZyOJtO', 'demo26vCivsUNb', 'demo26t8bTEgCy', 'demo261SDZC9NL', 'demo262exm1mom', 'demo265SGEjbst', 'demo266wXBjoki', 'demo267gGDB6RY', 'demo269DBCs1HS', 'demo26A76QKcJX', 'demo26A7LOyoju', 'demo26AmEUCSmj', 'demo26DlEHsstb', 'demo26EDHEXhLm', 'demo26EXCUSNho', 'demo26FTee8Wq6', 'demo26GlDXQ4SE', 'demo26Hogb969D', 'demo26IbKa9ySk', 'demo26IleWccbY', 'demo26IsZyeWLl', 'demo26KXJ5pzP7', 'demo26LRbIRZy4', 'demo26OR8PrlGM', 'demo26PRMKNJnQ', 'demo26PWmHdrwQ', 'demo26PeAM3d9s', 'demo26Pz81oMxc', 'demo26QnBahMpE', 'demo26So3V1hBN', 'demo26Tlfr2Leu');";
        System.out.println(querySql);
        try (MetricEngine metricEngine =
                MetricEngine.builder()
                        .withIgniteIp("***********")
                        .withClientPort(10800)
                        .withIgniteUser("ignite")
                        .withIgnitePasswd("ignite")
                        .withMetricMetaIp("*******************************************************")
                        .withMetricMetaUser("postgres")
                        .withMetricMetaPasswd("Envisi0n4321!")
                        .build()) {

            QueryResult queryResult = metricEngine.execute(schemaName, null, null, querySql, null);

            log.info(queryResult.toString());

            long queryEndTime = System.currentTimeMillis();
            log.info(
                    "Query virtual attr value success, total time cost: [{}] ms. ",
                    (queryEndTime - queryStartTime));
        } catch (Exception e) {
            log.error(
                    "Query virtual attr value error, schemeName: [{}], querySql: [{}]",
                    schemaName,
                    querySql,
                    e);
        }
    }
}
