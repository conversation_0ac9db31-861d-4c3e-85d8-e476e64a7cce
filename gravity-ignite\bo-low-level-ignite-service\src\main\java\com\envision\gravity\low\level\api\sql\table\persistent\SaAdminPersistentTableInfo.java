package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/28
 * @description
 */
public class SaAdminPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.SA_ADMIN\n"
                    + "(\n"
                    + "    SA          VARCHAR,\n"
                    + "    EPOCH       BIGINT,\n"
                    + "    CREATE_TIME TIMESTAMP,\n"
                    + "    PRIMARY KEY (SA)\n"
                    + ")WITH \"template=%s,ATOMICITY=%s,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Collections.singletonList(
                        "CREATE INDEX IF NOT EXISTS INDEX_SA_ADMIN_PK ON %s.SA_ADMIN (SA);");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
