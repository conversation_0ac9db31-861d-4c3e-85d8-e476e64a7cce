package com.envision.gravity.common.calculate;

import com.envision.gravity.common.enums.PrefType;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PropertyFieldInfo {

    private String modelId;

    private String prefName;

    private String fieldId;

    private Integer fieldIndex;

    private Boolean horizontal;

    private String rawFieldId;

    private PrefType prefType;
}
