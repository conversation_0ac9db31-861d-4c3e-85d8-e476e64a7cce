package com.envision.gravity.common.ignite.service;

import com.envision.gravity.common.enums.IDType;
import com.envision.gravity.common.response.ResponseResult;

import java.util.List;


import org.apache.ignite.services.Service;

/**
 * <AUTHOR>
 * @date 2024/2/20
 * @description
 */
public interface IDService extends Service {
    String SERVICE_NAME = "IDService";

    /**
     * Generate id with id type
     *
     * @param count Number of generated IDs
     * @param idType {@link IDType}
     * @return result
     */
    ResponseResult<List<String>> get(int count, IDType idType);
}
