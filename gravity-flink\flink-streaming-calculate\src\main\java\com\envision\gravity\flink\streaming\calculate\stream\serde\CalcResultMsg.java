package com.envision.gravity.flink.streaming.calculate.stream.serde;

import com.envision.gravity.flink.streaming.calculate.stream.PojoFactory;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.flink.api.java.tuple.Tuple2;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TypeInfo(PojoFactory.CalcResultMsgType.class)
public class CalcResultMsg {
    private List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> legacyMsgList;
}
