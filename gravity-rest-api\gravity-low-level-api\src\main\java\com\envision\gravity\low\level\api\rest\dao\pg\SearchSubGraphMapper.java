package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.po.TblEdge;
import com.envision.gravity.common.po.TblEdgeExt;
import com.envision.gravity.common.po.TblStartVid;
import com.envision.gravity.common.vo.search.Sorter;
import com.envision.gravity.common.vo.search.graph.SubGraph;
import com.envision.gravity.low.level.api.rest.dao.ignite.TblSubGraphSqlProvider;
import com.envision.gravity.low.level.api.rest.dto.SearchSubGraphCondition;
import com.envision.gravity.low.level.api.rest.dto.SearchSubGraphEdgesCondition;
import com.envision.gravity.low.level.api.rest.dto.SubGraphVidDto;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/04/17 17:51 @Description: */
public interface SearchSubGraphMapper {

    @SelectProvider(type = TblSubGraphSqlProvider.class, method = "selectStartVidBySubGraphId")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "start_vid", property = "startVid", jdbcType = JdbcType.BIGINT),
        @Result(column = "retain", property = "retain", jdbcType = JdbcType.BIGINT)
    })
    List<TblStartVid> selectStartVidBySubGraphId(List<String> subGraphIds, String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchVidInSubGraph")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "from_vid", property = "vid", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraphVidDto> searchVidInSubGraph(
            List<String> subGraphIds, List<String> vids, String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchEdgesByPagination")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "prop_value", property = "propValue", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "total_size", property = "totalSize", jdbcType = JdbcType.INTEGER)
    })
    List<TblEdgeExt> searchEdgesByPagination(int limit, int offset, String orgId);

    @SelectProvider(
            type = SearchSubGraphSqlProvider.class,
            method = "countEdgesTotalSizeWithCondition")
    long countEdgesTotalSizeWithCondition(SearchSubGraphEdgesCondition condition, String orgId);

    @SelectProvider(
            type = SearchSubGraphSqlProvider.class,
            method = "searchEdgesWithPaginationAndCondition")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "prop_value", property = "propValue", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> searchEdgesWithPaginationAndCondition(
            SearchSubGraphEdgesCondition condition, int limit, int offset, String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchAllEdges")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "prop_value", property = "propValue", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> searchAllEdges(String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchEdgesWithCondition")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "from_vid", property = "fromVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "to_vid", property = "toVid", jdbcType = JdbcType.VARCHAR),
        @Result(column = "edge_type_id", property = "edgeTypeId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "prop_value", property = "propValue", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblEdge> searchEdgesWithCondition(SearchSubGraphEdgesCondition condition, String orgId);

    @SelectProvider(
            type = SearchSubGraphSqlProvider.class,
            method = "searchSubGraphWithPaginationAndCondition")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "sub_graph_display_name",
                property = "subGraphDisplayName",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "sub_graph_tags",
                property = "tags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraph> searchSubGraphWithPaginationAndCondition(
            SearchSubGraphCondition condition,
            List<Sorter> sorters,
            int limit,
            int offset,
            String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchSubGraphBySubGraphIds")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "sub_graph_display_name",
                property = "subGraphDisplayName",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "sub_graph_tags",
                property = "tags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraph> searchSubGraphBySubGraphIds(
            List<String> subGraphIds, List<Sorter> sorters, String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchAllSubGraphs")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "sub_graph_display_name",
                property = "subGraphDisplayName",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "sub_graph_tags",
                property = "tags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraph> searchAllSubGraphs(List<Sorter> sorters, String orgId);

    @SelectProvider(
            type = SearchSubGraphSqlProvider.class,
            method = "countSubGraphTotalSizeWithCondition")
    int countSubGraphTotalSizeWithCondition(SearchSubGraphCondition condition, String orgId);

    @SelectProvider(
            type = SearchSubGraphSqlProvider.class,
            method = "countSubGraphTotalSizeNotCondition")
    int countSubGraphTotalSizeNotCondition(String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchSubGraphByPagination")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "sub_graph_display_name",
                property = "subGraphDisplayName",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "sub_graph_tags",
                property = "tags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraph> searchSubGraphByPagination(
            List<Sorter> sorters, int limit, int offset, String orgId);

    @SelectProvider(type = SearchSubGraphSqlProvider.class, method = "searchSubGraphWithCondition")
    @Results({
        @Result(column = "sub_graph_id", property = "subGraphId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "sub_graph_display_name",
                property = "subGraphDisplayName",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(
                column = "sub_graph_tags",
                property = "tags",
                javaType = JSONObject.class,
                jdbcType = JdbcType.VARCHAR,
                typeHandler = JsonObjectTypeHandler.class),
        @Result(column = "tree", property = "tree", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<SubGraph> searchSubGraphWithCondition(
            SearchSubGraphCondition condition, List<Sorter> sorters, String orgId);
}
