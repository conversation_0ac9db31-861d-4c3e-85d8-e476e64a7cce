package com.envision.gravity.common.po;

import java.sql.Timestamp;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/18 16:48 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TblPgAsset {

    private String assetId;

    private String systemId;

    private String modelId;

    private String orgId;

    private String assetDisplayName;

    private String description;

    private JSONObject assetTags;

    private JSONObject attributes;

    private String uniqueId;

    private Timestamp assetCreatedTime;

    private String assetCreatedUser;

    private Timestamp assetModifiedTime;

    private String assetModifiedUser;

    private Timestamp createdTime;

    private Timestamp modifiedTime;
}
