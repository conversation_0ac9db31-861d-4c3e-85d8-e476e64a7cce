package com.envision.gravity.flink.streaming.calculate.aspect.source.reader;

import com.envision.gravity.flink.streaming.calculate.aspect.source.split.CalcJobSplit;
import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.meta.CalcJobMetaInfo;

import java.util.*;
import java.util.concurrent.CompletableFuture;


import org.apache.flink.api.connector.source.ReaderOutput;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.core.io.InputStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ✅ Job源读取器，负责从分配的Job中生成CalcJobTask
 *
 * <p>功能： 1. 管理分配给当前Reader的Job 2. 生成CalcJobTask并发送给下游 3. 监控任务完成状态 4. 支持动态Job分配
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class CalcJobSourceReader implements SourceReader<CalcJobTask, CalcJobSplit> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobSourceReader.class);

    private final SourceReaderContext context;
    private final long taskCompletionCheckInterval;
    private final int taskBatchSize;
    private final TaskCompletionNotifier taskCompletionNotifier;

    // ✅ Reader状态管理
    private final Map<String, CalcJobMetaInfo> assignedJobs; // jobId -> CalcJobMetaInfo
    private final Queue<CalcJobTask> pendingTasks; // 待发送的任务队列
    private final Set<String> emittedTaskIds; // 已发送的任务ID

    private volatile boolean isRunning = true;
    private long lastTaskGenerationTime = 0;

    public CalcJobSourceReader(
            SourceReaderContext context,
            long taskCompletionCheckInterval,
            int taskBatchSize,
            TaskCompletionNotifier taskCompletionNotifier) {

        this.context = context;
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.taskBatchSize = taskBatchSize;
        this.taskCompletionNotifier = taskCompletionNotifier;

        this.assignedJobs = new HashMap<>();
        this.pendingTasks = new LinkedList<>();
        this.emittedTaskIds = new HashSet<>();

        logger.info(
                "Created CalcJobSourceReader with checkInterval: {}ms, batchSize: {}",
                taskCompletionCheckInterval,
                taskBatchSize);
    }

    @Override
    public void start() {
        logger.info("CalcJobSourceReader started for subtask: {}", context.getIndexOfSubtask());
    }

    @Override
    public InputStatus pollNext(ReaderOutput<CalcJobTask> output) throws Exception {
        if (!isRunning) {
            return InputStatus.END_OF_INPUT;
        }

        // ✅ 1. 检查并清理已完成的任务
        cleanupCompletedTasks();

        // ✅ 2. 生成新任务（如果需要）
        generateTasksIfNeeded();

        // ✅ 3. 发送待处理的任务
        boolean emittedAny = emitPendingTasks(output);

        if (emittedAny) {
            return InputStatus.MORE_AVAILABLE;
        } else {
            // 没有任务可发送，等待一段时间
            return InputStatus.NOTHING_AVAILABLE;
        }
    }

    @Override
    public List<CalcJobSplit> snapshotState(long checkpointId) {
        // ✅ 返回当前分配的Job分片
        List<CalcJobSplit> splits = new ArrayList<>();
        for (String jobId : assignedJobs.keySet()) {
            splits.add(new CalcJobSplit(jobId));
        }

        logger.debug("Snapshot state for checkpoint {}: {} splits", checkpointId, splits.size());
        return splits;
    }

    @Override
    public void addSplits(List<CalcJobSplit> splits) {
        // ✅ 接收新分配的Job分片
        for (CalcJobSplit split : splits) {
            String jobId = split.getJobId();

            if (!assignedJobs.containsKey(jobId)) {
                // TODO: 从元数据获取CalcJobMetaInfo
                CalcJobMetaInfo jobMetaInfo = loadJobMetaInfo(jobId);

                if (jobMetaInfo != null) {
                    assignedJobs.put(jobId, jobMetaInfo);
                    logger.info(
                            "Added new job split: {} to reader {}",
                            jobId,
                            context.getIndexOfSubtask());
                } else {
                    logger.warn("Failed to load job meta info for job: {}", jobId);
                }
            }
        }

        // 立即生成任务
        generateTasksFromNewJobs();
    }

    @Override
    public void notifyNoMoreSplits() {
        logger.info("No more splits will be assigned to reader: {}", context.getIndexOfSubtask());
    }

    @Override
    public void close() throws Exception {
        isRunning = false;
        logger.info("CalcJobSourceReader closed for subtask: {}", context.getIndexOfSubtask());
    }

    @Override
    public CompletableFuture<Void> isAvailable() {
        // ✅ 检查是否有可用的任务或需要生成新任务
        if (!pendingTasks.isEmpty() || shouldGenerateTasks()) {
            return CompletableFuture.completedFuture(null);
        } else {
            // 等待一段时间后再检查
            return CompletableFuture.supplyAsync(
                    () -> {
                        try {
                            Thread.sleep(taskCompletionCheckInterval);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        return null;
                    });
        }
    }

    /** ✅ 清理已完成的任务 */
    private void cleanupCompletedTasks() {
        Set<String> completedTaskIds = taskCompletionNotifier.getCompletedTasks();

        if (!completedTaskIds.isEmpty()) {
            int removedCount = 0;
            Iterator<String> iterator = emittedTaskIds.iterator();
            while (iterator.hasNext()) {
                String taskId = iterator.next();
                if (completedTaskIds.contains(taskId)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.debug(
                        "Cleaned up {} completed tasks for reader {}",
                        removedCount,
                        context.getIndexOfSubtask());
            }
        }
    }

    /** ✅ 根据需要生成新任务 */
    private void generateTasksIfNeeded() {
        if (shouldGenerateTasks()) {
            generateTasksFromAssignedJobs();
            lastTaskGenerationTime = System.currentTimeMillis();
        }
    }

    /** ✅ 检查是否应该生成新任务 */
    private boolean shouldGenerateTasks() {
        // 如果待处理队列为空且有分配的Job，则生成新任务
        return pendingTasks.isEmpty()
                && !assignedJobs.isEmpty()
                && (System.currentTimeMillis() - lastTaskGenerationTime)
                        > taskCompletionCheckInterval;
    }

    /** ✅ 从分配的Job生成任务 */
    private void generateTasksFromAssignedJobs() {
        for (CalcJobMetaInfo jobMetaInfo : assignedJobs.values()) {
            generateTasksForJob(jobMetaInfo);
        }
    }

    /** ✅ 从新分配的Job立即生成任务 */
    private void generateTasksFromNewJobs() {
        generateTasksFromAssignedJobs();
    }

    /** ✅ 为特定Job生成任务 */
    private void generateTasksForJob(CalcJobMetaInfo jobMetaInfo) {
        try {
            // TODO: 实现任务生成逻辑
            // 这里应该根据CalcJobMetaInfo生成CalcJobTask
            // 可能需要查询目标资产等信息

            String jobId = jobMetaInfo.getJobId();
            String taskId = generateTaskId(jobId);

            CalcJobTask task =
                    CalcJobTask.builder()
                            .taskId(taskId)
                            .jobId(jobId)
                            .targetAssetIds(Arrays.asList("asset1", "asset2")) // TODO: 实际查询
                            .startTime(System.currentTimeMillis())
                            .endTime(System.currentTimeMillis())
                            .orgId(jobMetaInfo.getOrgId())
                            .targetModelId(jobMetaInfo.getTargetModelIds().get(0))
                            .build();

            pendingTasks.offer(task);

            logger.debug(
                    "Generated task {} for job {} in reader {}",
                    taskId,
                    jobId,
                    context.getIndexOfSubtask());

        } catch (Exception e) {
            logger.error(
                    "Failed to generate tasks for job {}: {}",
                    jobMetaInfo.getJobId(),
                    e.getMessage(),
                    e);
        }
    }

    /** ✅ 发送待处理的任务 */
    private boolean emitPendingTasks(ReaderOutput<CalcJobTask> output) {
        boolean emittedAny = false;
        int emittedCount = 0;

        // ✅ 批量发送任务
        while (!pendingTasks.isEmpty() && emittedCount < taskBatchSize) {
            CalcJobTask task = pendingTasks.poll();

            if (task != null) {
                emittedTaskIds.add(task.getTaskId());
                output.collect(task);
                emittedCount++;
                emittedAny = true;

                logger.debug(
                        "Emitted task {} from reader {}",
                        task.getTaskId(),
                        context.getIndexOfSubtask());
            }
        }

        if (emittedAny) {
            logger.debug(
                    "Emitted {} tasks from reader {}, pending: {}, emitted total: {}",
                    emittedCount,
                    context.getIndexOfSubtask(),
                    pendingTasks.size(),
                    emittedTaskIds.size());
        }

        return emittedAny;
    }

    /** 加载Job元信息 */
    private CalcJobMetaInfo loadJobMetaInfo(String jobId) {
        // TODO: 实现从元数据加载CalcJobMetaInfo的逻辑
        // 这里应该与GetCalcJobInfoTimer集成
        return null;
    }

    private String generateTaskId(String jobId) {
        return jobId + "_task_" + System.currentTimeMillis() + "_" + context.getIndexOfSubtask();
    }
}
