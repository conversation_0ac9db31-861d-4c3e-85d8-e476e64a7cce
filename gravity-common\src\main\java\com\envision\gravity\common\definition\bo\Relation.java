package com.envision.gravity.common.definition.bo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/15
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Relation {
    private String graphId;
    private String fromVid;
    private String toVid;
    private String edgeType;
}
