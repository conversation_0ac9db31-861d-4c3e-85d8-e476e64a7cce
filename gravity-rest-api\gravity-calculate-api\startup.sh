#!/bin/bash

# jvm options
DEFAULT_JAVA_OPTS="-XX:+UseConcMarkSweepGC"
JAVA_OPTS="${JAVA_OPTS:-$DEFAULT_JAVA_OPTS}"

# java -jar
if [ -f "/sys/fs/cgroup/memory/memory.limit_in_bytes" ]
then
  # cgroup v1
  limit_in_bytes=$(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)
else
  # cgroup v2
  limit_in_bytes=$(cat /sys/fs/cgroup/memory.max)
fi
if [ "$limit_in_bytes" -ne "9223372036854771712" -a "$limit_in_bytes" -ne "9223372036854775807" ]
then
    limit_in_megabytes=$(expr $limit_in_bytes \/ 1048576)
    heap_size=$(expr $limit_in_megabytes \* 4 \/ 5)
    export JAVA_OPTS="-Xmx${heap_size}m $JAVA_OPTS"
    echo JAVA_OPTS=$JAVA_OPTS
fi

if [ "$BUILD_ENV" == "TEST" ]
then
    export JACOCO_OPTS="-javaagent:/opt/tools/qa/jacocoagent.jar=includes=*,output=tcpserver,address=*,port=36300"
    export JAVA_OPTS="$JAVA_OPTS $JACOCO_OPTS -Dlog4j2.formatMsgNoLookups=true"
fi

if [ "$TRACE_ENABLE" == "true" ]
then
    export SW_AGENT_OPTS="-javaagent:/opt/tools/ops/apm/skywalking-agent.jar";
    export JAVA_OPTS="$JAVA_OPTS $SW_AGENT_OPTS";
fi

cd "$APP_DIR" || exit 1
# for shutdown java process gracefully, see: https://lsongseven.github.io/posts/shutdownhook/
trap 'kill -TERM $PID' TERM INT

ZK_SECURITY_OPTS="-Djava.security.auth.login.config=/data/apps/config/zk_client_jaas.conf"
export SW_AGENT_OPTS="-javaagent:/opt/tools/ops/apm/skywalking-agent.jar";
export JAVA_OPTS="$JAVA_OPTS $SW_AGENT_OPTS $ZK_SECURITY_OPTS"

nohup java -jar $JAVA_OPTS gravity-calculate-api.jar &
PID=$!
wait $PID
wait $PID