package com.envision.gravity.flink.common.utils;


import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

public class FlinkCommonUtils {

    public static void tryRunAsLocalModel(String[] args, StreamExecutionEnvironment env) {
        final ParameterTool params = ParameterTool.fromArgs(args);

        if (params.getBoolean("local", false)) {
            // 1. 启用 Checkpoint，设置间隔时间（毫秒）
            env.enableCheckpointing(5000); // 每5秒触发一次checkpoint
            // 2. 设置 Checkpoint 模式（EXACTLY_ONCE 或 AT_LEAST_ONCE）
            env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
            // 3. 设置 Checkpoint 超时时间（毫秒）
            env.getCheckpointConfig().setCheckpointTimeout(60000); // 1分钟超时
            // 4. 设置两次 Checkpoint 之间的最小时间间隔（毫秒）
            env.getCheckpointConfig().setMinPauseBetweenCheckpoints(1000);
            // 5. 设置最大并发 Checkpoint 数量
            env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);

            env.setParallelism(2);
        }
    }
}
