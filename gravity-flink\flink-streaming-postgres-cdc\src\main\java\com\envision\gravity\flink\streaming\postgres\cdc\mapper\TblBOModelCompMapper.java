package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
public interface TblBOModelCompMapper {
    /**
     * @param schemaName schemaName
     * @param modelIdList modelIds
     * @param compIdList compIds
     * @return {@link ModelGroup} list
     */
    @SelectProvider(type = TblBOModelCompSqlProvider.class, method = "selectModelGroupList")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupList(
            String schemaName, List<String> modelIdList, List<String> compIdList);

    /**
     * @param schemaName schemaName
     * @param modelIdList modelIds
     * @return {@link ModelGroup} list
     */
    @SelectProvider(
            type = TblBOModelCompSqlProvider.class,
            method = "selectModelGroupListByModelId")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "group_id", property = "groupId", jdbcType = JdbcType.VARCHAR)
    })
    List<ModelGroup> selectModelGroupListByModelId(String schemaName, List<String> modelIdList);
}
