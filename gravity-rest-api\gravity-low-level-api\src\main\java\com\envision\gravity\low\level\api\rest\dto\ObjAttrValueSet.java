package com.envision.gravity.low.level.api.rest.dto;

import com.envision.gravity.common.enums.IgniteDataTypeEnum;
import com.envision.gravity.common.po.TblField;
import com.envision.gravity.low.level.api.rest.exception.ParamInvalidException;
import com.envision.gravity.low.level.api.rest.util.RestQueryUtils;
import com.envision.gravity.low.level.api.rest.util.RestTimeUtils;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/** <AUTHOR> 2024/10/24 */
@Getter
@Setter
public class ObjAttrValueSet {

    private static final Logger logger = LoggerFactory.getLogger(ObjAttrValueSet.class);

    private Set<String> assetIds;

    private List<String> systemIds;

    private List<Integer> fieldIndexes;

    private List<Boolean> boolValues;

    private List<String> stringValues;

    private List<Long> longValues;

    private List<Double> doubleValues;

    private List<String> jsonValues;

    private List<Timestamp> modifiedTimes;

    public ObjAttrValueSet(int systemIdsSize) {
        this.assetIds = new HashSet<>(systemIdsSize);
        this.systemIds = new ArrayList<>(systemIdsSize);
        this.fieldIndexes = new ArrayList<>(systemIdsSize);
        this.boolValues = new ArrayList<>(systemIdsSize);
        this.stringValues = new ArrayList<>(systemIdsSize);
        this.longValues = new ArrayList<>(systemIdsSize);
        this.doubleValues = new ArrayList<>(systemIdsSize);
        this.jsonValues = new ArrayList<>(systemIdsSize);
        this.modifiedTimes = new ArrayList<>(systemIdsSize);
    }

    public Object addRecord(
            String systemId, TblField field, Object value, IgniteDataTypeEnum typeNum) {
        try {
            Integer fieldIndex = field.getFieldIndex();
            String dataType = typeNum.getIgniteDataType();
            this.assetIds.add(systemId);
            this.systemIds.add(systemId);
            this.fieldIndexes.add(fieldIndex);
            this.modifiedTimes.add(new Timestamp(System.currentTimeMillis()));

            if (IgniteDataTypeEnum.BOOLEAN.getIgniteDataType().equals(dataType)) {
                Boolean parseValue = Boolean.parseBoolean(String.valueOf(value));
                this.boolValues.add(parseValue);
                this.stringValues.add(null);
                this.longValues.add(null);
                this.doubleValues.add(null);
                this.jsonValues.add(null);
                return parseValue;
            } else if (IgniteDataTypeEnum.STRING.getIgniteDataType().equals(dataType)) {
                boolean isJsonb = typeNum.isJsonb();
                String parseValue = isJsonb ? JSON.toJSONString(value) : String.valueOf(value);
                this.boolValues.add(null);
                this.longValues.add(null);
                this.doubleValues.add(null);
                if (isJsonb) {
                    this.stringValues.add(null);
                    this.jsonValues.add(parseValue);
                } else {
                    this.stringValues.add(parseValue);
                    this.jsonValues.add(null);
                }
                return parseValue;
            } else if (IgniteDataTypeEnum.LONG.getIgniteDataType().equals(dataType)) {
                Long parseValue = Long.parseLong(String.valueOf(value));
                this.boolValues.add(null);
                this.stringValues.add(null);
                this.longValues.add(parseValue);
                this.doubleValues.add(null);
                this.jsonValues.add(null);
                return parseValue;
            } else if (IgniteDataTypeEnum.FLOAT.getIgniteDataType().equals(dataType)) {
                Double parseValue = Double.parseDouble(String.valueOf(value));
                this.boolValues.add(null);
                this.stringValues.add(null);
                this.longValues.add(null);
                this.doubleValues.add(parseValue);
                this.jsonValues.add(null);
                return parseValue;
            } else {
                throw new ParamInvalidException(
                        String.format("not support dataType: %s", dataType));
            }
        } catch (Exception e) {
            String errMsg =
                    String.format(
                            "Parse attr value failed: systemId: %s, fieldId: %s, fieldDataType: %s",
                            systemId, field.getFieldId(), field.getDataType());
            logger.error(errMsg);
            throw new ParamInvalidException(errMsg);
        }
    }

    public String getAssetIdsExpr() {
        return RestQueryUtils.concatStr(assetIds);
    }

    public String getSystemIdsExpr() {
        return RestQueryUtils.concatStr(systemIds);
    }

    public String getFieldIndexesExpr() {
        return RestQueryUtils.concatStr(fieldIndexes, false);
    }

    public String getBoolValuesExpr() {
        return RestQueryUtils.concatStr(boolValues, false);
    }

    public String getStringValuesExpr() {
        return RestQueryUtils.concatStr(stringValues);
    }

    public String getLongValuesExpr() {
        return RestQueryUtils.concatStr(longValues, false);
    }

    public String getDoubleValuesExpr() {
        return RestQueryUtils.concatStr(doubleValues, false);
    }

    public String getJsonValuesExpr() {
        return RestQueryUtils.concatStr(jsonValues, true);
    }

    public String getModifiedTimesExpr() {
        List<String> modifiedTimeStr =
                modifiedTimes.stream()
                        .map(
                                t ->
                                        t != null
                                                ? String.format(
                                                        "TIMESTAMP '%s'",
                                                        RestTimeUtils.getCurrentUTCTimeString())
                                                : null)
                        .collect(Collectors.toList());
        return RestQueryUtils.concatStr(modifiedTimeStr, false);
    }

    // ----------------------------------------------------------------------
    public void fillNullValues(Map<String, Set<Integer>> fieldIndexMapping) {
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        for (Map.Entry<String, Set<Integer>> idEntry : fieldIndexMapping.entrySet()) {
            for (Integer fieldIndex : idEntry.getValue()) {
                this.assetIds.add(idEntry.getKey());
                this.systemIds.add(idEntry.getKey());
                this.fieldIndexes.add(fieldIndex);
                this.boolValues.add(null);
                this.stringValues.add(null);
                this.longValues.add(null);
                this.doubleValues.add(null);
                this.jsonValues.add(null);
                this.modifiedTimes.add(updateTime);
            }
        }
    }
}
