package com.envision.gravity.cache.calculate.entity;

import com.envision.gravity.common.enums.PrefType;

import java.util.List;


import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CalcPropertyMeta extends BaseCalcPropertyMeta {
    private String prefName;

    private List<SrcPrefItem> srcPrefItems;

    private boolean isDirectMapping = false;

    private PrefType prefType;

    // Expression use current model's property
    private boolean isExprUseCurrentModel = false;

    private boolean isValidExpr = true;

    private byte invalidType = 0;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((prefName == null) ? 0 : prefName.hashCode());
        result = prime * result + ((srcPrefItems == null) ? 0 : srcPrefItems.hashCode());
        result = prime * result + ((prefType == null) ? 0 : prefType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        CalcPropertyMeta other = (CalcPropertyMeta) obj;

        if (prefName == null) {
            if (other.prefName != null) {
                return false;
            }
        } else if (!prefName.equals(other.prefName)) {
            return false;
        }

        if (srcPrefItems == null) {
            if (other.srcPrefItems != null) {
                return false;
            }
        } else if (!srcPrefItems.equals(other.srcPrefItems)) {
            return false;
        }

        if (prefType == null) {
            if (other.prefType != null) {
                return false;
            }
        } else if (!prefType.equals(other.prefType)) {
            return false;
        }

        return true;
    }
}
