package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;

import java.util.List;


import org.apache.ibatis.annotations.*;

/**
 * MyBatis Mapper for tbl_calc_job_info table operations
 *
 * <AUTHOR>
 */
public interface TblCalcJobInfoMapper {

    /** Insert new calc job info record */
    @InsertProvider(type = TblCalcJobInfoSqlProvider.class, method = "insert")
    int insert(TblCalcJobInfo jobInfo);

    /** Update job status and modified time */
    @UpdateProvider(type = TblCalcJobInfoSqlProvider.class, method = "updateStatus")
    int updateStatus(
            @Param("jobId") String jobId,
            @Param("status") int status,
            @Param("modifiedTime") long modifiedTime);

    /** Update job status with specific time field */
    @UpdateProvider(type = TblCalcJobInfoSqlProvider.class, method = "updateStatusWithTime")
    int updateStatusWithTime(
            @Param("jobId") String jobId,
            @Param("status") int status,
            @Param("timeField") String timeField,
            @Param("timeValue") long timeValue);

    /** Update job progress */
    @UpdateProvider(type = TblCalcJobInfoSqlProvider.class, method = "updateProgress")
    int updateProgress(
            @Param("jobId") String jobId,
            @Param("progress") int progress,
            @Param("modifiedTime") long modifiedTime);

    /** Find conflicting jobs by prefRuleId and status */
    @SelectProvider(type = TblCalcJobInfoSqlProvider.class, method = "findConflictingJobs")
    @Results({
        @Result(column = "job_id", property = "jobId"),
        @Result(column = "pref_rule_id", property = "prefRuleId"),
        @Result(
                column = "rule_info",
                property = "ruleInfo",
                typeHandler = RecCalcMetaInfoTypeHandler.class),
        @Result(column = "calc_start_time", property = "calcStartTime"),
        @Result(column = "calc_end_time", property = "calcEndTime"),
        @Result(column = "status", property = "status"),
        @Result(column = "type", property = "type"),
        @Result(column = "src_org_id", property = "srcOrgId"),
        @Result(column = "target_org_id", property = "targetOrgId"),
        @Result(column = "progress", property = "progress"),
        @Result(column = "started_time", property = "startedTime"),
        @Result(column = "finished_time", property = "finishedTime"),
        @Result(column = "cancelled_time", property = "cancelledTime"),
        @Result(column = "created_time", property = "createdTime"),
        @Result(column = "created_user", property = "createdUser"),
        @Result(column = "modified_time", property = "modifiedTime"),
        @Result(column = "modified_user", property = "modifiedUser"),
        @Result(column = "sys_created_time", property = "sysCreatedTime"),
        @Result(column = "sys_modified_time", property = "sysModifiedTime")
    })
    List<TblCalcJobInfo> findConflictingJobs(
            @Param("prefRuleId") String prefRuleId,
            @Param("excludeJobId") String excludeJobId,
            @Param("statusList") List<Integer> statusList);

    /** Find job by jobId */
    @SelectProvider(type = TblCalcJobInfoSqlProvider.class, method = "findByJobId")
    @Results({
        @Result(column = "job_id", property = "jobId"),
        @Result(column = "pref_rule_id", property = "prefRuleId"),
        @Result(
                column = "rule_info",
                property = "ruleInfo",
                typeHandler = RecCalcMetaInfoTypeHandler.class),
        @Result(column = "calc_start_time", property = "calcStartTime"),
        @Result(column = "calc_end_time", property = "calcEndTime"),
        @Result(column = "status", property = "status"),
        @Result(column = "type", property = "type"),
        @Result(column = "src_org_id", property = "srcOrgId"),
        @Result(column = "target_org_id", property = "targetOrgId"),
        @Result(column = "progress", property = "progress"),
        @Result(column = "started_time", property = "startedTime"),
        @Result(column = "finished_time", property = "finishedTime"),
        @Result(column = "cancelled_time", property = "cancelledTime"),
        @Result(column = "created_time", property = "createdTime"),
        @Result(column = "created_user", property = "createdUser"),
        @Result(column = "modified_time", property = "modifiedTime"),
        @Result(column = "modified_user", property = "modifiedUser"),
        @Result(column = "sys_created_time", property = "sysCreatedTime"),
        @Result(column = "sys_modified_time", property = "sysModifiedTime")
    })
    TblCalcJobInfo findByJobId(@Param("jobId") String jobId);

    /** Find jobs by status */
    @SelectProvider(type = TblCalcJobInfoSqlProvider.class, method = "findByStatus")
    @Results({
        @Result(column = "job_id", property = "jobId"),
        @Result(column = "pref_rule_id", property = "prefRuleId"),
        @Result(
                column = "rule_info",
                property = "ruleInfo",
                typeHandler = RecCalcMetaInfoTypeHandler.class),
        @Result(column = "calc_start_time", property = "calcStartTime"),
        @Result(column = "calc_end_time", property = "calcEndTime"),
        @Result(column = "status", property = "status"),
        @Result(column = "type", property = "type"),
        @Result(column = "src_org_id", property = "srcOrgId"),
        @Result(column = "target_org_id", property = "targetOrgId"),
        @Result(column = "progress", property = "progress"),
        @Result(column = "started_time", property = "startedTime"),
        @Result(column = "finished_time", property = "finishedTime"),
        @Result(column = "cancelled_time", property = "cancelledTime"),
        @Result(column = "created_time", property = "createdTime"),
        @Result(column = "created_user", property = "createdUser"),
        @Result(column = "modified_time", property = "modifiedTime"),
        @Result(column = "modified_user", property = "modifiedUser"),
        @Result(column = "sys_created_time", property = "sysCreatedTime"),
        @Result(column = "sys_modified_time", property = "sysModifiedTime")
    })
    List<TblCalcJobInfo> findByStatus(
            @Param("statusList") List<Integer> statusList, @Param("limit") int limit);

    /** Delete job by jobId */
    @DeleteProvider(type = TblCalcJobInfoSqlProvider.class, method = "deleteByJobId")
    int deleteByJobId(@Param("jobId") String jobId);

    /** Count jobs by status */
    @SelectProvider(type = TblCalcJobInfoSqlProvider.class, method = "countByStatus")
    int countByStatus(@Param("statusList") List<Integer> statusList);

    /** Find jobs by prefRuleId */
    @SelectProvider(type = TblCalcJobInfoSqlProvider.class, method = "findByPrefRuleId")
    @Results({
        @Result(column = "job_id", property = "jobId"),
        @Result(column = "pref_rule_id", property = "prefRuleId"),
        @Result(
                column = "rule_info",
                property = "ruleInfo",
                typeHandler = RecCalcMetaInfoTypeHandler.class),
        @Result(column = "calc_start_time", property = "calcStartTime"),
        @Result(column = "calc_end_time", property = "calcEndTime"),
        @Result(column = "status", property = "status"),
        @Result(column = "type", property = "type"),
        @Result(column = "src_org_id", property = "srcOrgId"),
        @Result(column = "target_org_id", property = "targetOrgId"),
        @Result(column = "progress", property = "progress"),
        @Result(column = "started_time", property = "startedTime"),
        @Result(column = "finished_time", property = "finishedTime"),
        @Result(column = "cancelled_time", property = "cancelledTime"),
        @Result(column = "created_time", property = "createdTime"),
        @Result(column = "created_user", property = "createdUser"),
        @Result(column = "modified_time", property = "modifiedTime"),
        @Result(column = "modified_user", property = "modifiedUser"),
        @Result(column = "sys_created_time", property = "sysCreatedTime"),
        @Result(column = "sys_modified_time", property = "sysModifiedTime")
    })
    List<TblCalcJobInfo> findByPrefRuleId(
            @Param("prefRuleId") String prefRuleId, @Param("limit") int limit);
}
