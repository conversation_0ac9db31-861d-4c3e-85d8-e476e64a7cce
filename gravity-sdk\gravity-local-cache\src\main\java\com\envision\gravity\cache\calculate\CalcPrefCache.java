package com.envision.gravity.cache.calculate;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.PropertyId;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface CalcPrefCache {

    // =========================================================================================
    // Source related
    // =========================================================================================
    public Map<SrcPrefItem, Set<PropertyId>> batchGetTargetBySrcPref(
            String orgId, List<SrcPrefItem> srcPrefItems);

    public Set<PropertyId> getTargetBySrcPref(String orgId, String srcPrefName, String srcModelId);

    public Set<PropertyId> getTargetBySrcModelId(String orgId, String srcModelId);

    // data: srcModleId => list of 'target modelId + compId + prefId'
    public void batchUpdateSrcPref(
            String orgId, String srcPrefName, Map<String, Set<PropertyId>> data);

    public void updateSrcPref(String orgId, String srcPrefName, String srcModelId, PropertyId data);

    public void deleteBySrcModelId(String orgId, String srcModelId);

    // =========================================================================================
    // Target related
    // =========================================================================================
    public Optional<CalcPropertyMeta> getByTargetPref(
            String orgId, String compId, String prefId, String srcCategoryId);

    public Optional<CalcPropertyMeta> getByTargetPref(
            String orgId, String modelId, String compId, String prefId, String srcCategoryId);

    // Return 'target compId + prefId' according to prefId
    public List<PropertyId> getByTargetPrefId(String orgId, String targetPrefId);

    public List<PropertyId> getByTargetCompId(String orgId, String targetPrefId);

    // targetModelId => <srcCategoryId => xxx>
    public void updateTargetPref(
            String orgId,
            String compId,
            String prefId,
            Map<String, Map<String, CalcPropertyMeta>> data);

    public void deleteByTargetModelId(String orgId, String targetModelId);

    public void deleteByTargetCompId(String orgId, String targetCompId);

    public void deleteByTargetModelIdAndCompId(
            String orgId, String targetModelId, String targetCompId);

    public void deleteByTargetPrefId(String orgId, String targetPrefId);

    public void delete(String orgId, String compId, String prefId, String srcCategoryId);

    public Map<String, Long> getApproximateMemorySize();

    public void formatCacheContent(String orgId);
}
