package com.envision.gravity.common.vo.sync;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/4
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncLogResult {
    private String boId;
    private String boType;
    private String status;
    private List<SyncDetail> syncDetail;
}
