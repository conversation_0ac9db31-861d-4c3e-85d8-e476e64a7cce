package com.envision.gravity.flink.streaming.calculate.dto.job;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;

import java.util.Set;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobMetaInfo {

    private String orgId;

    private String jobId;

    // Contains expression
    private BaseCalcPropertyMeta ruleInfo;

    // TODO 在根据 modelIds 获取 asset_id 列表后，会有对应 src BO 的 src_category，整个要与当前的计算规则的 src_category
    // 相同，表明才是真正需要参与计算的 target BO
    private Set<String> targetModelIds;

    // Not contains expression
    private CalcPropertyMeta targetPropertyMeta;
}
