package com.envision.gravity.common.vo.search;

import javax.validation.Valid;
import javax.validation.constraints.Min;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/19 10:04 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchPagination {

    @Min(value = 1, message = "pageNo can not less than 1")
    private int pageNo;

    @Min(value = 1, message = "pageNo can not less than 1")
    private int pageSize;

    @Valid private List<Sorter> sorters;
}
