package com.envision.gravity.flink.steaming.bo.event.generator;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.common.definition.graph.BORelationDataDefinition;
import com.envision.gravity.common.event.GraphEventType;
import com.envision.gravity.flink.steaming.bo.event.entity.Constants;
import com.envision.gravity.flink.steaming.bo.event.entity.EventMsg;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblSubGraph;

import java.util.Map;
import java.util.Objects;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Slf4j
public class GraphEventGenerator implements Generator {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public EventMsg generateByCreate(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                TblSubGraph after = (TblSubGraph) record.getAfter();
                if (after == null) {
                    log.error(
                            "Generate event due to graph create: the cdc record has a null 'after' field, record: {}",
                            record);
                    return null;
                }

                long eventTime = after.getModifiedTime();
                BORelationDataDefinition payload = buildBORelationBuilder(after).build();

                return buildEventMsg(
                        BuildEventMsgReq.builder()
                                .record(record)
                                .key(after.getSubGraphId())
                                .eventTime(eventTime)
                                .eventType(GraphEventType.CREATE)
                                .payload(payload)
                                .build());
            } catch (Exception e) {
                log.error(
                        "Generate event due to graph create: parse the cdc record error, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    @Override
    public EventMsg generateByDelete(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                TblSubGraph before = (TblSubGraph) record.getBefore();
                if (before == null) {
                    log.error(
                            "Generate event due to graph delete: the cdc record has a null 'before' field, record: {}",
                            record);
                    return null;
                }

                long eventTime = before.getModifiedTime();

                BORelationDataDefinition payload =
                        BORelationDataDefinition.builder()
                                .graphId(before.getSubGraphId())
                                .before(buildBORelationBuilder(before).build())
                                .build();

                return buildEventMsg(
                        BuildEventMsgReq.builder()
                                .record(record)
                                .key(before.getSubGraphId())
                                .eventTime(eventTime)
                                .eventType(GraphEventType.DELETE)
                                .payload(payload)
                                .build());
            } catch (Exception e) {
                log.error(
                        "Generate event due to graph delete: parse the cdc record error, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    @Override
    public EventMsg generateByUpdate(ConvertedCdcRecord record) {
        if (record != null) {
            try {
                TblSubGraph before = (TblSubGraph) record.getBefore();
                TblSubGraph after = (TblSubGraph) record.getAfter();
                if (before == null || after == null) {
                    log.error(
                            "Generate event due to graph update: the cdc record has a null 'before' field or 'after' field, record: {}",
                            record);
                    return null;
                }

                if (!Objects.equals(before.getSubGraphDisplayName(), after.getSubGraphDisplayName())
                        || !Objects.equals(before.getSubGraphTags(), after.getSubGraphTags())
                        || !Objects.equals(before.isTree(), after.isTree())) {

                    long eventTime = after.getModifiedTime();

                    BORelationDataDefinition payload =
                            buildBORelationBuilder(after)
                                    .before(buildBORelationBuilder(before).build())
                                    .build();

                    return buildEventMsg(
                            BuildEventMsgReq.builder()
                                    .record(record)
                                    .key(after.getSubGraphId())
                                    .eventTime(eventTime)
                                    .eventType(GraphEventType.UPDATE)
                                    .payload(payload)
                                    .build());
                }

            } catch (JsonProcessingException e) {
                log.error(
                        "Generate event due to graph update: parse the cdc record error, record: {}",
                        record,
                        e);
            } catch (Exception e) {
                log.error(
                        "Generate event due to graph update: unknown exception, record: {}",
                        record,
                        e);
            }
        }

        return null;
    }

    private BORelationDataDefinition.BORelationDataDefinitionBuilder buildBORelationBuilder(
            TblSubGraph tblSubGraph) throws JsonProcessingException {
        return BORelationDataDefinition.builder()
                .graphId(tblSubGraph.getSubGraphId())
                .name(
                        tblSubGraph.getSubGraphDisplayName() != null
                                ? JSONObject.parseObject(tblSubGraph.getSubGraphDisplayName())
                                : null)
                .tree(tblSubGraph.isTree())
                .tags(
                        tblSubGraph.getSubGraphTags() != null
                                ? OBJECT_MAPPER.readValue(
                                        tblSubGraph.getSubGraphTags(),
                                        new TypeReference<Map<String, String>>() {})
                                : null)
                .createdTime(tblSubGraph.getCreatedTime())
                .modifiedTime(tblSubGraph.getModifiedTime());
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BuildEventMsgReq {
        private ConvertedCdcRecord record;
        private String key;
        private Long eventTime;
        private GraphEventType eventType;
        private BORelationDataDefinition payload;
    }

    private EventMsg buildEventMsg(BuildEventMsgReq req) {
        EventMsg event = new EventMsg();
        event.setTsMs(System.currentTimeMillis());
        event.setEventTime(req.getEventTime());
        event.setEventSource(Constants.DFLT_GRAPH_EVENT_SOURCE);
        event.setEventType(req.getEventType().name().toUpperCase());
        event.setOrgId(req.getRecord().getDb());
        event.setKey(req.getKey());
        event.setPayload(req.getPayload());
        event.setVersion(Constants.DFLT_VERSION);
        return event;
    }
}
