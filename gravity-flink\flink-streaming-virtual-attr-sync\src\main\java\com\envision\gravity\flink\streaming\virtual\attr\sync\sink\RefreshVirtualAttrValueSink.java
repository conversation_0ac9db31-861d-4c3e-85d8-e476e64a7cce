package com.envision.gravity.flink.streaming.virtual.attr.sync.sink;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.LionConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.config.PoseidonConfig;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.req.UpsertObjAttrValuePoseidonReq;


import com.envision.apim.poseidon.core.Poseidon;
import com.envision.apim.poseidon.exception.PoseidonException;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

/**
 * <AUTHOR>
 * @date 2024/7/9
 * @description
 */
@Slf4j
public class RefreshVirtualAttrValueSink extends RichSinkFunction<UpsertObjAttrValuePoseidonReq> {

    private static final long serialVersionUID = 8870931810334410689L;
    private static final String GRAVITY_SERVICE_URL = LionConfig.getGravityRestApiUrl();

    @Override
    public void invoke(UpsertObjAttrValuePoseidonReq value, Context context) {
        if (value == null) {
            return;
        }

        try {
            ResponseResult<?> response =
                    Poseidon.config(PoseidonConfig.POSEIDON_CONFIG)
                            .url(GRAVITY_SERVICE_URL)
                            .getResponse(value, ResponseResult.class);

            if (ResponseCodeEnum.SUCCESS.getCode() == response.getCode()) {
                log.info("Invoke upsert obj attr value success.");
            } else {
                log.error(
                        "Invoke upsert obj attr value error, cause: {}, request: {}",
                        response.getMessage(),
                        value);
            }
        } catch (PoseidonException e) {
            log.error("Request gravity service error!", e);
        } catch (Exception ex) {
            log.error("Unknown exception!", ex);
        }
    }
}
