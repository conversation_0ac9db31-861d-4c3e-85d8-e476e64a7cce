package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.QueryIndexType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblPrefCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;
    public static final Map<String, Object> DEFAULT_FIELD_VALUES;

    static {
        // keyFields
        KEY_FIELDS =
                Collections.singletonList(
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "pref_id", String.class, "pref_id"),
                        new JdbcTypeField(Types.VARCHAR, "pref_name", String.class, "pref_name"),
                        new JdbcTypeField(
                                Types.VARCHAR,
                                "pref_display_name",
                                String.class,
                                "pref_display_name"),
                        new JdbcTypeField(Types.VARCHAR, "pref_type", String.class, "pref_type"),
                        new JdbcTypeField(
                                Types.VARCHAR, "description", String.class, "description"),
                        new JdbcTypeField(Types.VARCHAR, "comment", String.class, "comment"),
                        new JdbcTypeField(Types.VARCHAR, "writable", Boolean.class, "writable"),
                        new JdbcTypeField(Types.VARCHAR, "required", Boolean.class, "required"),
                        new JdbcTypeField(
                                Types.VARCHAR, "default_value", String.class, "default_value"),
                        new JdbcTypeField(
                                Types.VARCHAR, "has_quality", Boolean.class, "has_quality"),
                        new JdbcTypeField(
                                Types.VARCHAR, "pref_data_type", String.class, "pref_data_type"),
                        new JdbcTypeField(
                                Types.VARCHAR,
                                "pref_signal_type",
                                String.class,
                                "pref_signal_type"),
                        new JdbcTypeField(
                                Types.VARCHAR, "data_definition", String.class, "data_definition"),
                        new JdbcTypeField(Types.VARCHAR, "request", String.class, "request"),
                        new JdbcTypeField(Types.VARCHAR, "response", String.class, "response"),
                        new JdbcTypeField(Types.VARCHAR, "unit", String.class, "unit"),
                        new JdbcTypeField(
                                Types.VARCHAR, "lower_limit", String.class, "lower_limit"),
                        new JdbcTypeField(
                                Types.VARCHAR, "upper_limit", String.class, "upper_limit"),
                        new JdbcTypeField(Types.VARCHAR, "dimensions", String.class, "dimensions"),
                        new JdbcTypeField(Types.VARCHAR, "intervals", String.class, "intervals"),
                        new JdbcTypeField(
                                Types.VARCHAR,
                                "default_agg_method",
                                String.class,
                                "default_agg_method"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("pref_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("pref_id");
        NOT_NULL_FIELDS.add("pref_name");
        NOT_NULL_FIELDS.add("pref_display_name");
        NOT_NULL_FIELDS.add("pref_type");

        DEFAULT_FIELD_VALUES = new HashMap<>();
        DEFAULT_FIELD_VALUES.put("writable", false);
        DEFAULT_FIELD_VALUES.put("required", false);
        DEFAULT_FIELD_VALUES.put("has_quality", false);

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("pref_id"),
                        new QueryIndex("pref_name"),
                        new QueryIndex(
                                Arrays.asList("pref_id", "pref_name"), QueryIndexType.SORTED));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("pref_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("pref_name", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("pref_display_name", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("pref_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("description", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("comment", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("writable", "java.lang.Boolean");
        QUERY_ENTITY_FIELDS.put("required", "java.lang.Boolean");
        QUERY_ENTITY_FIELDS.put("default_value", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("has_quality", "java.lang.Boolean");
        QUERY_ENTITY_FIELDS.put("pref_data_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("pref_signal_type", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("data_definition", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("request", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("response", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("unit", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("lower_limit", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("upper_limit", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("dimensions", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("intervals", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("default_agg_method", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return DEFAULT_FIELD_VALUES;
    }

    @Override
    public String getAffKeyFieldName() {
        return null;
    }
}
