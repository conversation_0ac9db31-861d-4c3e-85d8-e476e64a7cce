package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.search.graph.SearchSubGraphEdgeReq;
import com.envision.gravity.common.vo.search.graph.SearchSubGraphReq;
import com.envision.gravity.common.vo.topo.SubGraphReq;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.service.SubGraphService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.util.List;

import static com.envision.gravity.low.level.api.rest.common.Constants.MAX_REQUEST_SIZE;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Api(tags = "Topo")
@Slf4j
@RestController
@Validated
@RequestMapping("/sub-graphs")
public class SubGraphController {

    @Resource private SubGraphService subGraphService;

    @PostMapping
    public ResponseResult<?> createOrUpdateSubGraphs(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid List<SubGraphReq> subGraphs,
            HttpServletRequest request) {
        log.info(
                String.format(
                        "Start create or update subgraph, orgId: %s, SubGraphs: %s",
                        orgId, subGraphs));

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (subGraphs == null || subGraphs.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (subGraphs.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of create or update subGraphs exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    subGraphs.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of create or update subGraphs must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, subGraphs.size()))
                    .build();
        }

        return subGraphService.createOrUpdateSubGraphs(orgId, subGraphs, auditHeader);
    }

    @DeleteMapping
    public ResponseResult<?> deleteSubGraphs(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody List<@NotBlank String> subGraphIds) {
        log.info(
                String.format(
                        "Start delete subgraph, orgId: %s, SubGraphIds: %s", orgId, subGraphIds));
        if (subGraphIds.isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (subGraphIds.size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of delete subGraphs exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    subGraphIds.size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of delete subGraphs must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, subGraphIds.size()))
                    .build();
        }
        return subGraphService.deleteSubGraphs(orgId, subGraphIds);
    }

    @GravityLog
    @PostMapping(value = "/search")
    public ResponseResult<?> searchSubGraph(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid SearchSubGraphReq searchSubGraphReq) {
        return subGraphService.searchSubGraph(orgId, searchSubGraphReq);
    }

    @GravityLog
    @PostMapping(value = "/search-edge")
    public ResponseResult<?> searchSubGraphEdges(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid SearchSubGraphEdgeReq searchSubGraphEdgeReq) {
        return subGraphService.searchSubGraphEdges(orgId, searchSubGraphEdgeReq);
    }
}
