package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/19
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblComponentInfo implements CDCTableEntity {
    private static final long serialVersionUID = -1957307713080867108L;
    private String compId;
    private String compName;
    private String compDisplayName;
    private String description;
    private String comment;
    private Boolean anonymous;
    private String template;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
