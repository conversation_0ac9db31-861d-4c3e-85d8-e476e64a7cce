package com.envision.gravity.flink.streaming.calculate.recalc.integration;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.recalc.CalcJobTaskSource;
import com.envision.gravity.flink.streaming.calculate.recalc.ReCalcJobTaskProcessor;
import com.envision.gravity.flink.streaming.calculate.recalc.CalcJobInfoManager;
import com.envision.gravity.flink.streaming.calculate.recalc.TaskCompletionChecker;
import org.apache.flink.streaming.api.datastream.BroadcastConnectedStream;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.TaskManagerOptions;
import org.apache.flink.configuration.JobManagerOptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReCalcBatchJob 集成测试
 * 验证完整的任务拆分、处理和状态管理流程
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ReCalcBatchJobIntegrationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ReCalcBatchJobIntegrationTest.class);
    
    private MiniClusterWithClientResource flinkCluster;
    private TestDataProvider testDataProvider;
    private MockTaskCompletionStorage mockStorage;
    
    @BeforeEach
    public void setUp() throws Exception {
        logger.info("Setting up ReCalcBatchJob integration test");
        
        // 配置 Flink 测试集群
        Configuration config = new Configuration();
        config.setString(TaskManagerOptions.MANAGED_MEMORY_SIZE, "128m");
        config.setString(JobManagerOptions.TOTAL_PROCESS_MEMORY, "512m");
        config.setString(TaskManagerOptions.TOTAL_PROCESS_MEMORY, "512m");
        
        flinkCluster = new MiniClusterWithClientResource(
            new MiniClusterWithClientResource.MiniClusterResourceConfiguration(
                config, 1, 2), false);
        
        flinkCluster.before();
        
        // 初始化测试数据提供者
        testDataProvider = new TestDataProvider();
        
        // 初始化模拟存储
        mockStorage = new MockTaskCompletionStorage();
        
        logger.info("Test setup completed");
    }
    
    @AfterEach
    public void tearDown() throws Exception {
        logger.info("Tearing down test environment");
        
        if (mockStorage != null) {
            mockStorage.cleanup();
        }
        
        if (flinkCluster != null) {
            flinkCluster.after();
        }
        
        logger.info("Test teardown completed");
    }
    
    @Test
    public void testCompleteReCalcBatchJobFlow() throws Exception {
        logger.info("Starting complete ReCalcBatchJob flow test");
        
        // 1. 准备测试数据
        TblCalcJobInfo jobInfo = testDataProvider.createTestJobInfo("test-job-001");
        
        // 2. 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        env.getConfig().setAutoWatermarkInterval(100);
        
        // 3. 构建数据流
        buildTestDataFlow(env, jobInfo);
        
        // 4. 执行作业 (异步)
        env.executeAsync("ReCalcBatchJobIntegrationTest");
        
        // 5. 等待任务完成并验证结果
        verifyJobExecution(jobInfo);
        
        logger.info("Complete ReCalcBatchJob flow test completed successfully");
    }
    
    @Test
    public void testTaskSourceStateRecovery() throws Exception {
        logger.info("Starting task source state recovery test");
        
        TblCalcJobInfo jobInfo = testDataProvider.createTestJobInfo("test-job-002");
        
        // 模拟状态恢复场景
        CalcJobTaskSource source = new CalcJobTaskSource(jobInfo);
        
        // 验证状态恢复逻辑
        // TODO: 实现状态恢复测试逻辑
        
        assertTrue(true, "State recovery test placeholder");
        
        logger.info("Task source state recovery test completed");
    }
    
    @Test
    public void testMultipleJobIsolation() throws Exception {
        logger.info("Starting multiple job isolation test");
        
        // 创建多个作业
        TblCalcJobInfo job1 = testDataProvider.createTestJobInfo("test-job-003");
        TblCalcJobInfo job2 = testDataProvider.createTestJobInfo("test-job-004");
        
        // 验证作业间状态隔离
        // TODO: 实现多作业隔离测试逻辑
        
        assertNotEquals(job1.getJobId(), job2.getJobId(), "Job IDs should be different");
        
        logger.info("Multiple job isolation test completed");
    }
    
    private void buildTestDataFlow(StreamExecutionEnvironment env, TblCalcJobInfo jobInfo) {
        String jobId = jobInfo.getJobId();
        
        // 创建广播流
        DataStream<TblCalcJobInfo> jobInfoStream = env.fromElements(jobInfo)
            .uid("job-info-source-" + jobId)
            .name("JobInfoSource-" + jobId);
        
        BroadcastStream<TblCalcJobInfo> jobInfoBroadcast = jobInfoStream.broadcast(
            CalcJobInfoManager.createJobInfoDescriptor(jobId));
        
        // 创建任务源
        DataStream<CalcJobTask> taskStream = env.addSource(new CalcJobTaskSource(jobInfo))
            .uid("calc-job-task-source-" + jobId)
            .name("CalcJobTaskSource-" + jobId)
            .setParallelism(1)
            .returns(TypeInformation.of(CalcJobTask.class));
        
        // 连接广播流和任务流
        BroadcastConnectedStream<CalcJobTask, TblCalcJobInfo> connectedStream = 
            taskStream.connect(jobInfoBroadcast);
        
        // 处理任务
        connectedStream.process(new TestReCalcJobTaskProcessor(jobId, mockStorage))
            .uid("recalc-job-task-processor-" + jobId)
            .name("ReCalcJobTaskProcessor-" + jobId)
            .setParallelism(2);
    }
    
    private void verifyJobExecution(TblCalcJobInfo jobInfo) throws Exception {
        String jobId = jobInfo.getJobId();
        
        // 等待任务完成
        int maxWaitSeconds = 30;
        int waitedSeconds = 0;
        
        while (waitedSeconds < maxWaitSeconds) {
            Thread.sleep(1000);
            waitedSeconds++;
            
            // 检查任务完成状态
            if (mockStorage.isJobCompleted(jobId)) {
                logger.info("Job {} completed after {} seconds", jobId, waitedSeconds);
                break;
            }
        }
        
        // 验证结果
        assertTrue(mockStorage.isJobCompleted(jobId), 
                  "Job should be completed within " + maxWaitSeconds + " seconds");
        
        int completedTasks = mockStorage.getCompletedTasks(jobId).size();
        assertTrue(completedTasks > 0, "Should have completed tasks");
        
        logger.info("Job {} verification completed: {} tasks completed", jobId, completedTasks);
    }
}
