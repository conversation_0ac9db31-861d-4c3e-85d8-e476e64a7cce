package com.envision.gravity.flink.streaming.bo.view.operator.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelDetailOrigin {
    private String modelId;
    private String modelDisplayName;
    private String description;
    private String orgId;
    private String modelPath;
    private String modelTags;
    private String components;
    private String properties;
    private String attributeTags;
    private String measurepointTags;
    private String commandTags;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private String modelCreatedUser;
    private String modelModifiedUser;
    private Long modelCreatedTime;
    private Long modelModifiedTime;
}
