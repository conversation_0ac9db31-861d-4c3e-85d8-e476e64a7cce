package com.envision.gravity.low.level.api.rest.config;

/** <AUTHOR> 2024/7/12 */
public class GTRestLionConfig {

    // Ignite cache config
    // --------------------------------------------------------------------------------
    public static final String IGNITE_TCP_NO_DELAY = "gravity-rest.ignite.tcpNoDelay";
    public static final boolean IGNITE_TCP_NO_DELAY_DEFAULT = false;

    public static final String IGNITE_CLIENT_TIMEOUT = "gravity-rest.ignite.client.timeout";
    public static final int IGNITE_CLIENT_TIMEOUT_DEFAULT = 60000;

    // Sql gateway jdbc config
    // --------------------------------------------------------------------------------
    public static final String SQL_GATEWAY_MAX_POOL_SIZE = "gravity-rest.sql-gateway.max.pool.size";
    public static final int SQL_GATEWAY_MAX_POOL_SIZE_DEFAULT = 10;

    // Ignite jdbc config
    // --------------------------------------------------------------------------------

    // Postgresql jdbc config
    // --------------------------------------------------------------------------------
    public static final String PGSQL_MAX_POOL_SIZE = "gravity-rest.postgresql.max.pool.size";
    public static final int PGSQL_MAX_POOL_SIZE_DEFAULT = 10;

    // Nebula
    // --------------------------------------------------------------------------------
    public static final String NEBULA_SESSION_CACHE_SIZE = "gravity-rest.nebula.session.cache.size";
    public static final int NEBULA_SESSION_CACHE_SIZE_DEFAULT = 10;

    public static final String NEBULA_SESSION_CACHE_DURATION_MINUTES =
            "gravity-rest.nebula.session.cache.durationInMinutes";
    public static final int NEBULA_SESSION_CACHE_DURATION_MINUTES_DEFAULT = 30;

    // --------------------------------------------------------------------------------
    public static final String LOCALE = "gravity-rest.locale";
    public static final String LOCALE_DEFAULT = "zh_CN,en_US";

    // --------------------------------------------------------------------------------
    public static final String WRITE_EDGE_BATCH_SIZE = "gravity-rest.write.edge.batch.size";
    public static final int WRITE_EDGE_BATCH_SIZE_DEFAULT = 10000;

    public static final String USE_ARRAY_ANY_VALUE_SIZE = "gravity-rest.useArrayAnyValueSize";

    // Tag Service ---------------------------------------------------------------------------
    public static final String TAG_SERVICE_URL = "gravity-rest.tag.service.url";

    public static final String MAX_REQUEST_SIZE = "gravity-rest.max.req.size";

    // Model Service -------------------------------------------------------------------------
    public static final String MODEL_SERVICE_BO_API_PATH = "gravity-rest.model.service.bo-api.path";
    public static final String MODEL_SERVICE_BO_API_PATH_DEFAULT =
            "/business-object-service/v3.0/business-object";
    public static final String INNER_APIM_HOST = "Common.Inner-Apim-Core.Domain.Service";
    public static final String INNER_APIM_HOST_DEFAULT =
            "http://inner-apim-core-runtime.enos.svc.cluster.local:8000";
}
