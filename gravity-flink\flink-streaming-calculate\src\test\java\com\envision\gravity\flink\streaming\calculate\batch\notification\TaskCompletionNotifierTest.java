package com.envision.gravity.flink.streaming.calculate.batch.notification;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskCompletionNotifier 单元测试
 * 
 * 测试功能：
 * 1. Job级别状态隔离
 * 2. 任务完成和失败状态管理
 * 3. 全局实例和Job实例的区别
 * 4. 状态清理功能
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
class TaskCompletionNotifierTest {
    
    private static final String JOB_ID_1 = "test-job-1";
    private static final String JOB_ID_2 = "test-job-2";
    private static final String TASK_ID_1 = "task-1";
    private static final String TASK_ID_2 = "task-2";
    
    @BeforeEach
    void setUp() {
        // 清理所有状态
        TaskCompletionNotifier.cleanup(JOB_ID_1);
        TaskCompletionNotifier.cleanup(JOB_ID_2);
        TaskCompletionNotifier.cleanup("global");
    }
    
    @AfterEach
    void tearDown() {
        // 清理所有状态
        TaskCompletionNotifier.cleanup(JOB_ID_1);
        TaskCompletionNotifier.cleanup(JOB_ID_2);
        TaskCompletionNotifier.cleanup("global");
    }
    
    @Test
    void testJobLevelStateIsolation() {
        // ✅ 测试Job级别状态隔离
        TaskCompletionNotifier notifier1 = TaskCompletionNotifier.getInstance(JOB_ID_1);
        TaskCompletionNotifier notifier2 = TaskCompletionNotifier.getInstance(JOB_ID_2);
        
        // 验证不同Job的实例是不同的
        assertNotSame(notifier1, notifier2);
        
        // Job1 标记任务完成
        notifier1.markTaskCompleted(TASK_ID_1);
        
        // Job2 不应该看到Job1的任务
        assertFalse(notifier2.isTaskCompleted(TASK_ID_1));
        
        // Job1 应该看到自己的任务
        assertTrue(notifier1.isTaskCompleted(TASK_ID_1));
    }
    
    @Test
    void testTaskCompletionTracking() {
        // ✅ 测试任务完成状态跟踪
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 初始状态
        assertFalse(notifier.isTaskCompleted(TASK_ID_1));
        assertTrue(notifier.getCompletedTasks().isEmpty());
        
        // 标记任务完成
        notifier.markTaskCompleted(TASK_ID_1);
        
        // 验证状态
        assertTrue(notifier.isTaskCompleted(TASK_ID_1));
        Set<String> completedTasks = notifier.getCompletedTasks();
        assertEquals(1, completedTasks.size());
        assertTrue(completedTasks.contains(TASK_ID_1));
    }
    
    @Test
    void testTaskFailureTracking() {
        // ✅ 测试任务失败状态跟踪
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        String errorMessage = "Test error message";
        
        // 初始状态
        assertFalse(notifier.isTaskFailed(TASK_ID_1));
        assertTrue(notifier.getFailedTasks().isEmpty());
        
        // 标记任务失败
        notifier.markTaskFailed(TASK_ID_1, errorMessage);
        
        // 验证状态
        assertTrue(notifier.isTaskFailed(TASK_ID_1));
        Set<String> failedTasks = notifier.getFailedTasks();
        assertEquals(1, failedTasks.size());
        assertTrue(failedTasks.contains(TASK_ID_1));
    }
    
    @Test
    void testTaskCompletionOverridesFailure() {
        // ✅ 测试任务完成会覆盖失败状态
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 先标记失败
        notifier.markTaskFailed(TASK_ID_1, "Test error");
        assertTrue(notifier.isTaskFailed(TASK_ID_1));
        
        // 再标记完成
        notifier.markTaskCompleted(TASK_ID_1);
        
        // 验证状态：应该是完成，不是失败
        assertTrue(notifier.isTaskCompleted(TASK_ID_1));
        assertFalse(notifier.isTaskFailed(TASK_ID_1));
        
        // 验证集合状态
        assertTrue(notifier.getCompletedTasks().contains(TASK_ID_1));
        assertFalse(notifier.getFailedTasks().contains(TASK_ID_1));
    }
    
    @Test
    void testGlobalInstanceVsJobInstance() {
        // ✅ 测试全局实例与Job实例的区别
        TaskCompletionNotifier globalNotifier = TaskCompletionNotifier.getGlobalInstance();
        TaskCompletionNotifier jobNotifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 验证不是同一个实例
        assertNotSame(globalNotifier, jobNotifier);
        
        // 全局实例标记任务完成
        globalNotifier.markTaskCompleted(TASK_ID_1);
        
        // Job实例不应该看到全局实例的任务
        assertFalse(jobNotifier.isTaskCompleted(TASK_ID_1));
        
        // 全局实例应该看到自己的任务
        assertTrue(globalNotifier.isTaskCompleted(TASK_ID_1));
    }
    
    @Test
    void testStaticMethodsWithJobId() {
        // ✅ 测试带jobId参数的静态方法
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 使用静态方法标记完成
        notifier.markTaskCompleted(JOB_ID_1, TASK_ID_1);
        
        // 验证状态
        assertTrue(notifier.isTaskCompleted(JOB_ID_1, TASK_ID_1));
        
        // 使用静态方法标记失败
        notifier.markTaskFailed(JOB_ID_1, TASK_ID_2, "Test error");
        
        // 验证状态
        assertTrue(notifier.isTaskFailed(TASK_ID_2));
    }
    
    @Test
    void testStatistics() {
        // ✅ 测试统计信息
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 初始统计
        TaskCompletionNotifier.TaskStatistics stats = notifier.getStatistics();
        assertEquals(0, stats.getCompletedCount());
        assertEquals(0, stats.getFailedCount());
        assertEquals(0, stats.getTotalCount());
        
        // 添加一些任务
        notifier.markTaskCompleted(TASK_ID_1);
        notifier.markTaskFailed(TASK_ID_2, "Test error");
        
        // 验证统计
        stats = notifier.getStatistics();
        assertEquals(1, stats.getCompletedCount());
        assertEquals(1, stats.getFailedCount());
        assertEquals(2, stats.getTotalCount());
        assertTrue(stats.getLastUpdateTime() > 0);
    }
    
    @Test
    void testCleanup() {
        // ✅ 测试状态清理
        TaskCompletionNotifier notifier = TaskCompletionNotifier.getInstance(JOB_ID_1);
        
        // 添加一些状态
        notifier.markTaskCompleted(TASK_ID_1);
        notifier.markTaskFailed(TASK_ID_2, "Test error");
        
        // 验证状态存在
        assertTrue(notifier.isTaskCompleted(TASK_ID_1));
        assertTrue(notifier.isTaskFailed(TASK_ID_2));
        
        // 清理状态
        notifier.cleanup();
        
        // 验证状态已清理
        assertFalse(notifier.isTaskCompleted(TASK_ID_1));
        assertFalse(notifier.isTaskFailed(TASK_ID_2));
        assertTrue(notifier.getCompletedTasks().isEmpty());
        assertTrue(notifier.getFailedTasks().isEmpty());
    }
    
    @Test
    void testActiveJobManagement() {
        // ✅ 测试活跃Job管理
        int initialActiveJobs = TaskCompletionNotifier.getActiveJobCount();
        
        // 创建一些Job实例
        TaskCompletionNotifier.getInstance(JOB_ID_1);
        TaskCompletionNotifier.getInstance(JOB_ID_2);
        
        // 验证活跃Job数量
        assertEquals(initialActiveJobs + 2, TaskCompletionNotifier.getActiveJobCount());
        
        Set<String> activeJobIds = TaskCompletionNotifier.getActiveJobIds();
        assertTrue(activeJobIds.contains(JOB_ID_1));
        assertTrue(activeJobIds.contains(JOB_ID_2));
        
        // 清理一个Job
        TaskCompletionNotifier.cleanup(JOB_ID_1);
        
        // 验证活跃Job数量减少
        assertEquals(initialActiveJobs + 1, TaskCompletionNotifier.getActiveJobCount());
        assertFalse(TaskCompletionNotifier.getActiveJobIds().contains(JOB_ID_1));
        assertTrue(TaskCompletionNotifier.getActiveJobIds().contains(JOB_ID_2));
    }
}
