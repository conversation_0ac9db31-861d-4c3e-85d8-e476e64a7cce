package com.envision.gravity.common.vo.search.asset;

import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/28 11:20 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupByResp {

    private Map<String, Object> groupByKey;

    private Map<String, Object> groupByVal;
}
