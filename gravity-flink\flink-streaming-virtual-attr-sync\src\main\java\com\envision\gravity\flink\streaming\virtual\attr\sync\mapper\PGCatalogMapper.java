package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
public interface PGCatalogMapper {
    @SelectProvider(type = PGCatalogSqlProvider.class, method = "queryScheme")
    @Results({@Result(column = "nspname", property = "nspname", jdbcType = JdbcType.VARCHAR)})
    List<String> queryScheme();
}
