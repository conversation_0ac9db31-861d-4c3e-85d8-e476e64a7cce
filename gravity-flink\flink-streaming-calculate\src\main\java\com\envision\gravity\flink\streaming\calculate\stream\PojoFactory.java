package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.cache.write.Pref;
import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.stream.serde.*;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;


import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.api.java.typeutils.runtime.PojoSerializer;

public class PojoFactory {

    public static class OffsetInfoType extends TypeInfoFactory<OffsetInfo> {

        @Override
        public TypeInformation<OffsetInfo> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("topic", Types.STRING);
            map.put("partition", Types.INT);
            map.put("offset", Types.LONG);
            map.put("collector", Types.MAP(Types.TUPLE(Types.STRING, Types.INT), Types.LONG));
            return Types.POJO(OffsetInfo.class, map);
        }
    }

    public static class LegacyPointType extends TypeInfoFactory<LegacyPoint> {

        @Override
        public TypeInformation<LegacyPoint> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("pref", Types.POJO(Pref.class));
            map.put("stringValue", Types.STRING);
            map.put("doubleValue", Types.DOUBLE);
            map.put("longValue", Types.LONG);
            map.put("boolValue", Types.BOOLEAN);
            map.put("quality", Types.LONG);
            return Types.POJO(LegacyPoint.class, map);
        }
    }

    public static class LegacyPayloadType extends TypeInfoFactory<LegacyPayload> {
        @Override
        public TypeInformation<LegacyPayload> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("points", Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
            map.put("time", Types.LONG);
            map.put("assetId", Types.STRING);
            return Types.POJO(LegacyPayload.class, map);
        }
    }

    public static class LegacyMsgWithMultiAssetsType
            extends TypeInfoFactory<LegacyMsgWithMultiAssets> {
        @Override
        public TypeInformation<LegacyMsgWithMultiAssets> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("payload", Types.LIST(Types.POJO(LegacyPayload.class)));
            map.put("orgId", Types.STRING);
            map.put("modelId", Types.STRING);
            map.put("modelIdPath", Types.STRING);
            return Types.POJO(LegacyMsgWithMultiAssets.class, map);
        }
    }

    public static class LegacyMsgWithSingleAssetType
            extends TypeInfoFactory<LegacyMsgWithSingleAsset> {
        @Override
        public TypeInformation<LegacyMsgWithSingleAsset> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("payload", Types.POJO(LegacyPayload.class));
            map.put("orgId", Types.STRING);
            map.put("modelId", Types.STRING);
            map.put("modelIdPath", Types.STRING);
            return Types.POJO(LegacyMsgWithSingleAsset.class, map);
        }
    }

    public static class LegacyMsgListType extends TypeInfoFactory<LegacyMsgList> {
        @Override
        public TypeInformation<LegacyMsgList> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put("legacyMsgList", Types.LIST(Types.POJO(LegacyMsg.class)));
            return Types.POJO(LegacyMsgList.class, map);
        }
    }

    public static class LatestPointMapType extends TypeInfoFactory<LatestPointMap> {

        @Override
        public TypeInformation<LatestPointMap> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put(
                    "measurePointsMapByOu",
                    Types.MAP(
                            Types.STRING,
                            Types.MAP(
                                    Types.STRING,
                                    Types.LIST(Types.POJO(LatestMeasurePointEntityLocal.class)))));
            map.put(
                    "toUpdateSchemaHash",
                    Types.MAP(Types.STRING, Types.LIST(Types.POJO(BinarySchemaIdentity.class))));
            return Types.POJO(LatestPointMap.class, map);
        }
    }

    public static class CalcResultMsgType extends TypeInfoFactory<CalcResultMsg> {
        @Override
        public TypeInformation<CalcResultMsg> createTypeInfo(
                Type t, Map<String, TypeInformation<?>> genericParameters) {
            Map<String, TypeInformation<?>> map = new HashMap<>();
            map.put(
                    "legacyMsgList",
                    Types.LIST(
                            Types.TUPLE(
                                    Types.POJO(LegacyMsgWithMultiAssets.class), Types.BOOLEAN)));
            return Types.POJO(CalcResultMsg.class, map);
        }
    }

    public static <T> void assertSerializedAsPojo(Class<T> clazz) throws AssertionError {
        final TypeInformation<T> typeInformation = TypeInformation.of(clazz);
        final TypeSerializer<T> actualSerializer =
                typeInformation.createSerializer(new ExecutionConfig());

        System.out.printf(
                "Instances of the class '%s' cannot be serialized as a POJO, but would use a '%s' instead. %n"
                        + "Re-run this test with INFO logging enabled and check messages from the '%s' for possible reasons.%n",
                clazz.getSimpleName(),
                actualSerializer.getClass().getSimpleName(),
                TypeExtractor.class.getCanonicalName());

        if (!(actualSerializer instanceof PojoSerializer)) {
            throw new RuntimeException("class " + clazz + " is not pojo");
        }
    }

    /**
     * Verifies that instances of the given class fulfill all conditions to be serialized with the
     * {@link PojoSerializer}, as documented <a
     * href="https://nightlies.apache.org/flink/flink-docs-stable/docs/dev/datastream/fault-tolerance/serialization/types_serialization/#pojos">here</a>,
     * without any field being serialized with Kryo.
     *
     * @param clazz class to analyze
     * @param <T> class type
     * @throws AssertionError if instances of the class cannot be serialized as a POJO or required
     *     Kryo for one or more fields
     */
    public static <T> void assertSerializedAsPojoWithoutKryo(Class<T> clazz) throws AssertionError {
        final ExecutionConfig serializerConfig = new ExecutionConfig();
        serializerConfig.disableGenericTypes();

        final TypeInformation<T> typeInformation = TypeInformation.of(clazz);
        final TypeSerializer<T> actualSerializer;
        try {
            actualSerializer = typeInformation.createSerializer(serializerConfig);
        } catch (UnsupportedOperationException e) {
            throw new AssertionError(e);
        }
        System.out.printf(
                "Instances of the class '%s' cannot be serialized as a POJO, but would use a '%s' instead. %n"
                        + "Re-run this test with INFO logging enabled and check messages from the '%s' for possible reasons.",
                clazz.getSimpleName(),
                actualSerializer.getClass().getSimpleName(),
                TypeExtractor.class.getCanonicalName());

        System.out.println();
        if (!(actualSerializer instanceof PojoSerializer)) {
            throw new RuntimeException("class " + clazz + " is not pojo");
        }
    }
}
