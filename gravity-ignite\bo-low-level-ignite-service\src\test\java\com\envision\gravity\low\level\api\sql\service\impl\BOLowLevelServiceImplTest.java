package com.envision.gravity.low.level.api.sql.service.impl;

import com.envision.gravity.low.level.api.sql.common.Constants;

import java.util.List;

import static com.envision.gravity.low.level.api.sql.common.Constants.QUERY_CACHE_TABLE_PATTERN;

import org.apache.ignite.Ignition;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/4/17
 * @description
 */
class BOLowLevelServiceImplTest {

    @Test
    void putAllCache() {}

    @Test
    void cacheAlreadySyncTest() {
        ClientConfiguration cfg =
                new ClientConfiguration()
                        .setAddresses("*************:10800")
                        .setPartitionAwarenessEnabled(true)
                        .setUserName("ignite")
                        .setUserPassword("ignite");
        try (IgniteClient igniteClient = Ignition.startClient(cfg)) {
            List<List<?>> queryResult =
                    igniteClient
                            .query(
                                    new SqlFieldsQuery(QUERY_CACHE_TABLE_PATTERN)
                                            .setArgs(
                                                    "o17186913277371853".toUpperCase(),
                                                    Constants.TBL_START_VID_TABLE_NAME))
                            .getAll();
            System.out.println(Integer.parseInt(queryResult.get(0).get(0).toString()) > 0);
        }
    }
}
