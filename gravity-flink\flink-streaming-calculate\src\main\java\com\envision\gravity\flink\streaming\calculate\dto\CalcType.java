package com.envision.gravity.flink.streaming.calculate.dto;

public enum CalcType {
    PREF_CALC(0),
    ADHOC(1);

    private final int type;

    CalcType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static CalcType getCalcType(int type) {
        for (CalcType calcType : CalcType.values()) {
            if (calcType.getType() == type) {
                return calcType;
            }
        }
        throw new IllegalArgumentException("Invalid calc type: " + type);
    }
}
