package com.envision.gravity.common.vo.obj;

import com.envision.gravity.common.bo.ObjectField;
import com.envision.gravity.common.vo.category.CategoryReq;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ObjReq {
    @NotBlank(message = "systemId can not be blank")
    private String systemId;

    private String systemDisplayName;

    @Valid
    @NotNull(message = "categoryReq can not be null")
    private CategoryReq categoryReq;

    @Valid private List<ObjectField> fields;

    private String createdUser;

    private String modifiedUser;
}
