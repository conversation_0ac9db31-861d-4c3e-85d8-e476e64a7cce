package com.envision.gravity.obj;


import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
public class MeasurePointEntity implements Comparable<MeasurePointEntity> {
    private String rowFieldId;
    private Object value;
    private Long time;
    private Long quality;

    public MeasurePointEntity(String rowFieldId, Object value, Long time) {
        this.rowFieldId = rowFieldId;
        this.value = value;
        this.time = time;
    }

    public MeasurePointEntity(String rowFieldId, Object value, Long time, Long quality) {
        this.rowFieldId = rowFieldId;
        this.value = value;
        this.time = time;
        this.quality = quality;
    }

    @Override
    public int compareTo(@NotNull MeasurePointEntity o) {
        return rowFieldId.compareTo(o.getRowFieldId());
    }
}
