package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblObj;
import com.envision.gravity.common.vo.obj.Pagination;

import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Set;

/** @Author: qi.jiang2 @Date: 2024/03/04 14:37 @Description: */
public interface TblObjMapper {

    @SelectProvider(type = TblObjSqlProvider.class, method = "queryObjBySystemIds")
    @Results({
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "system_display_name",
                property = "systemDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblObj> queryObjBySystemIds(Set<String> systemIds, String orgId);

    @SelectProvider(type = TblObjSqlProvider.class, method = "queryObjByCategoryId")
    @Results({
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "system_display_name",
                property = "systemDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblObj> queryObjByCategoryId(String objCategory, Pagination pagination, String orgId);

    @SelectProvider(type = TblObjSqlProvider.class, method = "countObjByCategoryId")
    int countObjByCategoryId(String objCategory, String orgId);

    @SelectProvider(type = TblObjSqlProvider.class, method = "countObjTotalSize")
    int countObjTotalSize(String orgId);

    @SelectProvider(type = TblObjSqlProvider.class, method = "queryAllObj")
    @Results({
        @Result(column = "system_id", property = "systemId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "system_display_name",
                property = "systemDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblObj> queryAllObj(Pagination pagination, String orgId);

    @SelectProvider(type = TblObjSqlProvider.class, method = "countPropertyValueNum")
    String countPropertyValueNum(
            List<String> rawFieldIds,
            List<Object> values,
            List<String> systemIds,
            String dataType,
            String orgId);
}
