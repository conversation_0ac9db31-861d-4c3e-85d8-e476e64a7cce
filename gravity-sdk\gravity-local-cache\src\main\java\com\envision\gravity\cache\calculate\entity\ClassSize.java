package com.envision.gravity.cache.calculate.entity;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

/**
 * Utility class for calculating object memory sizes. This class provides constants and methods for
 * estimating the memory footprint of Java objects.
 */
public class ClassSize {
    // Basic object overhead
    public static final int OBJECT = 16; // Minimum object size (header + alignment)

    // Reference size (4 bytes on 32-bit JVM, 8 bytes on 64-bit JVM)
    public static final int REFERENCE = is64BitJVM() ? 8 : 4;

    // Array overhead
    public static final int ARRAY = 16; // Array header size

    // String overhead
    public static final int STRING =
            OBJECT + REFERENCE + 4; // String object + char[] reference + length field

    // Collection overheads
    public static final int ARRAYLIST =
            OBJECT + REFERENCE + 4; // ArrayList object + elementData reference + size field
    public static final int LINKEDLIST =
            OBJECT + REFERENCE + 4; // LinkedList object + first/last references + size field
    public static final int LINKEDLIST_ENTRY =
            OBJECT + REFERENCE * 3; // Entry object + item + next + prev references

    // Map overheads
    public static final int HASHMAP =
            OBJECT + REFERENCE + 4; // HashMap object + table reference + size field
    public static final int HASHMAP_ENTRY =
            OBJECT + REFERENCE * 3; // Entry object + key + value + next references
    public static final int CONCURRENT_HASHMAP =
            OBJECT + REFERENCE + 4; // ConcurrentHashMap object + table reference + size field
    public static final int CONCURRENT_HASHMAP_ENTRY =
            OBJECT + REFERENCE * 3; // Entry object + key + value + next references

    // Primitive type sizes
    public static final int BOOLEAN = 1;
    public static final int BYTE = 1;
    public static final int CHAR = 2;
    public static final int SHORT = 2;
    public static final int INT = 4;
    public static final int FLOAT = 4;
    public static final int LONG = 8;
    public static final int DOUBLE = 8;

    // Atomic types
    public static final int ATOMIC_LONG = OBJECT + LONG;
    public static final int ATOMIC_INTEGER = OBJECT + INT;
    public static final int ATOMIC_BOOLEAN = OBJECT + BOOLEAN;
    public static final int ATOMIC_REFERENCE = OBJECT + REFERENCE;

    // Lock overheads
    public static final int REENTRANT_LOCK =
            OBJECT + REFERENCE * 3; // Lock object + sync + owner + waiters references

    /** Check if running on a 64-bit JVM */
    public static boolean is64BitJVM() {
        String model = System.getProperty("sun.arch.data.model");
        return model != null && model.equals("64");
    }

    /** Align a number to 8 bytes (64-bit alignment) */
    public static long align(long num) {
        return (num + 7) & ~7;
    }

    /** Align a number to 8 bytes (64-bit alignment) */
    public static int align(int num) {
        return (int) align((long) num);
    }

    /** Calculate the size of a byte array including its header */
    public static long sizeOfByteArray(int length) {
        return align(ARRAY + length);
    }

    /** Calculate the size of a string including its header and char array */
    public static long sizeOfString(String str) {
        if (str == null) {
            return 0;
        }
        return align(STRING + sizeOfByteArray(str.length() * 2));
    }

    /** Calculate the size of an object based on its fields */
    public static long sizeOfObject(Object obj) {
        if (obj == null) {
            return 0;
        }

        long size = OBJECT;
        Class<?> clazz = obj.getClass();

        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                if (Modifier.isStatic(field.getModifiers())) {
                    continue;
                }

                Class<?> type = field.getType();
                if (type.isPrimitive()) {
                    if (type == boolean.class) size += BOOLEAN;
                    else if (type == byte.class) size += BYTE;
                    else if (type == char.class) size += CHAR;
                    else if (type == short.class) size += SHORT;
                    else if (type == int.class) size += INT;
                    else if (type == float.class) size += FLOAT;
                    else if (type == long.class) size += LONG;
                    else if (type == double.class) size += DOUBLE;
                } else {
                    size += REFERENCE;
                }
            }
            clazz = clazz.getSuperclass();
        }

        return align(size);
    }
}
