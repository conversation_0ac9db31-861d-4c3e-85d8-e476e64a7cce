package com.envision.gravity.common.definition.bo;

import javax.validation.constraints.NotBlank;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationDef {
    @NotBlank(message = "Relation: graphId can not be blank")
    private String graphId;

    private List<String> assetPath;
    private int order;
    private String edgeType;
    private boolean tree;
}
