package com.envision.gravity.flink.streaming.calculate.dto.recalc;

import java.util.Optional;

public enum ReCalcJobTypeEnum {
    DEFAULT(0),
    SANDBOX(1);

    private final int code;

    ReCalcJobTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static Optional<ReCalcJobTypeEnum> getByCode(int code) {
        for (ReCalcJobTypeEnum type : ReCalcJobTypeEnum.values()) {
            if (type.getCode() == code) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }
}
