package com.envision.gravity.flink.streaming.calculate.batch.splitter;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

/**
 * CalcJobTaskSplitEnumerator 单元测试
 *
 * <p>测试功能：
 * 1. 任务分片生成
 * 2. 时间范围分割
 * 3. 资产分页处理
 * 4. 任务ID生成
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@ExtendWith(MockitoExtension.class)
class CalcJobTaskSplitEnumeratorTest {

    private CalcJobTaskSplitEnumerator enumerator;
    private TblCalcJobInfo testJobInfo;

    private static final String TEST_JOB_ID = "test-job-001";
    private static final String TEST_ORG_ID = "o17186913277371853";

    @BeforeEach
    void setUp() {
        testJobInfo = createTestJobInfo();
        enumerator = new CalcJobTaskSplitEnumerator(testJobInfo);
    }

    @Test
    void testConstructorWithValidJobInfo() {
        // ✅ 测试构造函数参数验证
        assertNotNull(enumerator);
        
        // 测试无效参数
        assertThrows(IllegalArgumentException.class, () -> {
            new CalcJobTaskSplitEnumerator(null);
        });
    }

    @Test
    void testTimeRangeSplitting() {
        // ✅ 测试时间范围分割
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(3600L); // 1小时
            
            long startTime = System.currentTimeMillis() - 7200000; // 2小时前
            long endTime = System.currentTimeMillis();
            long splitSeconds = CalcLionConfig.getReCalcJobTaskTimeSplitSeconds();
            
            // 计算预期的时间分片数量
            long totalDuration = endTime - startTime;
            long splitDuration = splitSeconds * 1000; // 转换为毫秒
            int expectedSplits = (int) Math.ceil((double) totalDuration / splitDuration);
            
            assertTrue(expectedSplits >= 1);
            assertEquals(3600L, splitSeconds);
        }
    }

    @Test
    void testAssetPagination() {
        // ✅ 测试资产分页处理
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            configMock.when(CalcLionConfig::getCalcQueryAssetPageSize).thenReturn(1000);
            
            int pageSize = CalcLionConfig.getCalcQueryAssetPageSize();
            assertEquals(1000, pageSize);
            
            // 测试分页逻辑
            int totalAssets = 2500;
            int expectedPages = (int) Math.ceil((double) totalAssets / pageSize);
            assertEquals(3, expectedPages);
        }
    }

    @Test
    void testTaskIdGeneration() {
        // ✅ 测试任务ID生成
        String assetId = "asset-001";
        long startTime = 1640995200000L; // 2022-01-01 00:00:00
        long endTime = 1641081600000L;   // 2022-01-02 00:00:00
        
        String taskId = generateTestTaskId(TEST_JOB_ID, assetId, startTime, endTime);
        
        assertNotNull(taskId);
        assertTrue(taskId.contains(TEST_JOB_ID));
        assertTrue(taskId.contains(assetId));
        assertTrue(taskId.contains(String.valueOf(startTime)));
        assertTrue(taskId.contains(String.valueOf(endTime)));
    }

    @Test
    void testTaskGenerationWithMultipleAssets() {
        // ✅ 测试多资产任务生成
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(3600L);
            configMock.when(CalcLionConfig::getCalcQueryAssetPageSize).thenReturn(100);
            
            // 模拟多个资产的情况
            int assetCount = 250; // 假设有250个资产
            int pageSize = CalcLionConfig.getCalcQueryAssetPageSize();
            int expectedPages = (int) Math.ceil((double) assetCount / pageSize);
            
            assertEquals(3, expectedPages); // 250个资产，每页100个，需要3页
        }
    }

    @Test
    void testTaskGenerationWithLongTimeRange() {
        // ✅ 测试长时间范围的任务生成
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(3600L); // 1小时分片
            
            // 测试24小时的时间范围
            long startTime = System.currentTimeMillis() - 86400000; // 24小时前
            long endTime = System.currentTimeMillis();
            long splitSeconds = CalcLionConfig.getReCalcJobTaskTimeSplitSeconds();
            
            long totalDuration = endTime - startTime;
            long splitDuration = splitSeconds * 1000;
            int expectedTimeSplits = (int) Math.ceil((double) totalDuration / splitDuration);
            
            assertTrue(expectedTimeSplits >= 24); // 至少24个时间分片
        }
    }

    @Test
    void testJobInfoValidation() {
        // ✅ 测试Job信息验证
        TblCalcJobInfo jobInfo = createTestJobInfo();
        
        // 验证必要字段
        assertNotNull(jobInfo.getJobId());
        assertNotNull(jobInfo.getSrcOrgId());
        assertTrue(jobInfo.getStartTime() > 0);
        assertTrue(jobInfo.getEndTime() > 0);
        assertTrue(jobInfo.getStartTime() < jobInfo.getEndTime());
    }

    @Test
    void testTaskSplittingConfiguration() {
        // ✅ 测试任务分片配置
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            // 测试不同的分片配置
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(1800L); // 30分钟
            assertEquals(1800L, CalcLionConfig.getReCalcJobTaskTimeSplitSeconds());
            
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(7200L); // 2小时
            assertEquals(7200L, CalcLionConfig.getReCalcJobTaskTimeSplitSeconds());
        }
    }

    @Test
    void testEmptyTimeRange() {
        // ✅ 测试空时间范围
        TblCalcJobInfo invalidJobInfo = new TblCalcJobInfo();
        invalidJobInfo.setJobId(TEST_JOB_ID);
        invalidJobInfo.setSrcOrgId(TEST_ORG_ID);
        invalidJobInfo.setStartTime(System.currentTimeMillis());
        invalidJobInfo.setEndTime(System.currentTimeMillis()); // 相同的开始和结束时间
        
        // 验证时间范围为0的情况
        assertEquals(invalidJobInfo.getStartTime(), invalidJobInfo.getEndTime());
    }

    @Test
    void testTaskIdUniqueness() {
        // ✅ 测试任务ID唯一性
        String assetId1 = "asset-001";
        String assetId2 = "asset-002";
        long startTime = 1640995200000L;
        long endTime = 1641081600000L;
        
        String taskId1 = generateTestTaskId(TEST_JOB_ID, assetId1, startTime, endTime);
        String taskId2 = generateTestTaskId(TEST_JOB_ID, assetId2, startTime, endTime);
        
        assertNotEquals(taskId1, taskId2); // 不同资产应该有不同的任务ID
        
        // 测试相同资产但不同时间范围
        String taskId3 = generateTestTaskId(TEST_JOB_ID, assetId1, startTime + 3600000, endTime + 3600000);
        assertNotEquals(taskId1, taskId3); // 不同时间范围应该有不同的任务ID
    }

    @Test
    void testConfigurationDefaults() {
        // ✅ 测试配置默认值
        try (MockedStatic<CalcLionConfig> configMock = mockStatic(CalcLionConfig.class)) {
            // 设置默认配置值
            configMock.when(CalcLionConfig::getReCalcJobTaskTimeSplitSeconds).thenReturn(3600L);
            configMock.when(CalcLionConfig::getCalcQueryAssetPageSize).thenReturn(1000);
            
            // 验证默认值
            assertEquals(3600L, CalcLionConfig.getReCalcJobTaskTimeSplitSeconds());
            assertEquals(1000, CalcLionConfig.getCalcQueryAssetPageSize());
        }
    }

    private TblCalcJobInfo createTestJobInfo() {
        TblCalcJobInfo jobInfo = new TblCalcJobInfo();
        jobInfo.setJobId(TEST_JOB_ID);
        jobInfo.setSrcOrgId(TEST_ORG_ID);
        jobInfo.setStatus(ReCalcJobStatusEnum.PENDING.getCode());
        jobInfo.setStartTime(System.currentTimeMillis() - 3600000); // 1小时前
        jobInfo.setEndTime(System.currentTimeMillis());
        return jobInfo;
    }

    private String generateTestTaskId(String jobId, String assetId, long startTime, long endTime) {
        return String.format("%s-%s-%d-%d", jobId, assetId, startTime, endTime);
    }
}
