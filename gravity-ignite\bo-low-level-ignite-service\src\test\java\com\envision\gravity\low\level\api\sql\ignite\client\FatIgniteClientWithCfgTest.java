package com.envision.gravity.low.level.api.sql.ignite.client;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.Ignition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description
 */
@Slf4j
public class FatIgniteClientWithCfgTest {
    private static Ignite ignite;

    @BeforeAll
    public static void init() {
        ignite = Ignition.start("config/default-config-dev.xml");
    }

    @Test
    void loadExternalStorageTest() {
        long loadCacheStartTime = System.currentTimeMillis();
        ignite.cache("o18729267101625555_TBL_OBJ_ATTR").loadCache(null);
        long loadCacheEndTime = System.currentTimeMillis();
        log.info(
                "Load cache success, total time cost: [{}]",
                (loadCacheEndTime - loadCacheStartTime) / 1000.0 + "s");
    }

    @Test
    void cacheTest() {
        Assertions.assertNull(ignite.cache("xxx"));
        System.out.println(">>> " + ignite.cacheNames());
    }
}
