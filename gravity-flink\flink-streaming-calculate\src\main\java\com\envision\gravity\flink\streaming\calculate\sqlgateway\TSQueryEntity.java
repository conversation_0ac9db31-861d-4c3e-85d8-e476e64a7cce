package com.envision.gravity.flink.streaming.calculate.sqlgateway;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TSQueryEntity {

    private String orgId;

    private String modelId;

    private List<String> assetIds;

    private List<String> pointNames;

    // milliseconds
    private long startTime;

    // milliseconds
    private long endTime;
}
