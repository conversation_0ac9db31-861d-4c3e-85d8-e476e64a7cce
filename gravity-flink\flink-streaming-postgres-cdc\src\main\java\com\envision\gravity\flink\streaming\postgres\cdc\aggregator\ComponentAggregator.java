package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblComponentInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblComponentRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public class ComponentAggregator implements Aggregator {

    private final TblComponentRepository tblComponentRepository;

    public ComponentAggregator(SqlSessionFactory sqlSessionFactory) {
        tblComponentRepository = new TblComponentRepository(sqlSessionFactory);
    }

    @Override
    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        // 2.refresh object detail
        List<String> compIdList =
                records.stream()
                        .map(record -> ((TblComponentInfo) record.getAfter()).getCompId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!compIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentRepository.selectModelGroupByComp(schemaName, compIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> modelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                return AggregatedResults.builder()
                        .refreshModelReq(
                                RefreshModelReq.builder().updateRefresh(modelIdList).build())
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        // 2.refresh object detail
        RefreshModelReq refreshModelReq = null;
        List<String> compIdList =
                records.stream()
                        .map(record -> ((TblComponentInfo) record.getBefore()).getCompId())
                        .distinct()
                        .collect(Collectors.toList());

        if (!compIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentRepository.selectModelGroupList(schemaName, compIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> modelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                return AggregatedResults.builder()
                        .refreshModelReq(
                                RefreshModelReq.builder().updateRefresh(modelIdList).build())
                        .refreshObjectReq(
                                RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelGroupList)
                                        .build())
                        .build();
            }
        }

        return null;
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.if comp_name changed, refresh model detail
        RefreshModelReq refreshModelReq = null;
        List<String> compIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblComponentInfo before = (TblComponentInfo) record.getBefore();
                                    TblComponentInfo after = (TblComponentInfo) record.getAfter();

                                    if (!Objects.equals(
                                            before.getCompName(), after.getCompName())) {
                                        return after.getCompId();
                                    }

                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        if (!compIdList.isEmpty()) {
            List<ModelGroup> modelGroupList =
                    tblComponentRepository.selectModelGroupByComp(schemaName, compIdList);

            if (!modelGroupList.isEmpty()) {
                List<String> modelIdList =
                        modelGroupList.stream()
                                .map(ModelGroup::getModelId)
                                .distinct()
                                .collect(Collectors.toList());

                refreshModelReq = RefreshModelReq.builder().updateRefresh(modelIdList).build();
            }
        }

        // 2.if comp_name or anonymous changed, refresh object detail
        List<String> filteredCompIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblComponentInfo before = (TblComponentInfo) record.getBefore();
                                    TblComponentInfo after = (TblComponentInfo) record.getAfter();

                                    if (!Objects.equals(before.getCompName(), after.getCompName())
                                            && !after.getAnonymous()) {
                                        return after.getCompId();
                                    }

                                    if (!Objects.equals(
                                            before.getAnonymous(), after.getAnonymous())) {
                                        return after.getCompId();
                                    }

                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        List<ModelGroup> modelUpdateRefresh = new ArrayList<>();
        if (!filteredCompIdList.isEmpty()) {
            modelUpdateRefresh =
                    tblComponentRepository.selectModelGroupByComp(schemaName, filteredCompIdList);
        }

        return AggregatedResults.builder()
                .refreshModelReq(refreshModelReq)
                .refreshObjectReq(
                        modelUpdateRefresh.isEmpty()
                                ? null
                                : RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelUpdateRefresh)
                                        .build())
                .build();
    }
}
