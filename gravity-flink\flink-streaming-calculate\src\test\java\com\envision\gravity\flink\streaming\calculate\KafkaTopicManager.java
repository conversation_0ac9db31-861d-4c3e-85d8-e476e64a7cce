package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.Collections;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutionException;


import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Kafka Topic 管理工具 用于测试环境中自动创建和管理 topic */
public class KafkaTopicManager {
    private static final Logger logger = LoggerFactory.getLogger(KafkaTopicManager.class);

    private final AdminClient adminClient;

    public KafkaTopicManager() {
        Properties props = new Properties();
        props.put("bootstrap.servers", CalcLionConfig.getCalcCommonKafkaServers());
        props.put("client.id", "gravity-calc-test-topic-manager");
        this.adminClient = AdminClient.create(props);
    }

    /** 检查 topic 是否存在 */
    public boolean topicExists(String topicName) {
        try {
            ListTopicsResult listTopics = adminClient.listTopics();
            Set<String> topicNames = listTopics.names().get();
            return topicNames.contains(topicName);
        } catch (InterruptedException | ExecutionException e) {
            logger.error("检查 topic 是否存在时出错: {}", topicName, e);
            return false;
        }
    }

    /** 创建 topic */
    public boolean createTopic(String topicName, int numPartitions, short replicationFactor) {
        try {
            if (topicExists(topicName)) {
                logger.info("Topic {} 已存在，跳过创建", topicName);
                return true;
            }

            NewTopic newTopic = new NewTopic(topicName, numPartitions, replicationFactor);
            CreateTopicsResult result =
                    adminClient.createTopics(Collections.singletonList(newTopic));
            result.all().get(); // 等待创建完成

            logger.info("✅ 成功创建 topic: {}", topicName);
            return true;
        } catch (InterruptedException | ExecutionException e) {
            logger.error("❌ 创建 topic 失败: {}", topicName, e);
            return false;
        }
    }

    /** 创建测试用的输入和输出 topic */
    public boolean createTestTopics(String orgId) {
        String inputTopic = "MEASURE_POINT_ORIGIN_GRAVITY_" + orgId;
        String outputTopic = "MEASURE_POINT_CAL_" + orgId;

        logger.info("=== 创建测试 Topics ===");
        logger.info("输入 Topic: {}", inputTopic);
        logger.info("输出 Topic: {}", outputTopic);

        boolean inputCreated = createTopic(inputTopic, 3, (short) 1);
        boolean outputCreated = createTopic(outputTopic, 3, (short) 1);

        if (inputCreated && outputCreated) {
            logger.info("✅ 所有测试 topics 创建成功");
            return true;
        } else {
            logger.error("❌ 部分 topics 创建失败");
            return false;
        }
    }

    /** 关闭管理客户端 */
    public void close() {
        if (adminClient != null) {
            adminClient.close();
        }
    }

    /** 获取测试输入 topic 名称 */
    public static String getTestInputTopic(String orgId) {
        return "MEASURE_POINT_ORIGIN_GRAVITY_" + orgId;
    }

    /** 获取测试输出 topic 名称 */
    public static String getTestOutputTopic(String orgId) {
        return "MEASURE_POINT_CAL_" + orgId;
    }
}
