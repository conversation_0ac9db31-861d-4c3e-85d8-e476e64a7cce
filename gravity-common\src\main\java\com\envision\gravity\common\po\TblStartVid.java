package com.envision.gravity.common.po;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/05/09 21:19 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblStartVid {

    private String subGraphId;

    private String startVid;

    private boolean retain;

    private Timestamp createdTime;

    private String createdUser;

    private Timestamp modifiedTime;

    private String modifiedUser;
}
