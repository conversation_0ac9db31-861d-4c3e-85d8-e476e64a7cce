package com.envision.gravity.flink.streaming.bo.view.operator.util;

import com.envision.gravity.flink.streaming.bo.view.operator.model.ModelDetailOrigin;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @description
 */
public class NameUtil {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    public static final String MDM_TYPE = "mdmType";
    public static final String DIM_MDMTYPE = "dim_mdmtype";

    public static String genViewName(ModelDetailOrigin record) throws JsonProcessingException {
        String modelId = record.getModelId();
        String modelTags = record.getModelTags();

        JsonNode jsonNode = OBJECT_MAPPER.readTree(modelTags);
        JsonNode mdmTypeNode = jsonNode.get(MDM_TYPE);

        if (mdmTypeNode != null && !mdmTypeNode.asText().isEmpty()) {
            return String.format("%s_%s", DIM_MDMTYPE, mdmTypeNode.asText());
        }

        return String.format("%s_%s", DIM_MDMTYPE, modelId);
    }
}
