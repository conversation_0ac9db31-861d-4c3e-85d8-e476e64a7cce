package com.envision.gravity.bo.ignite.service.impl;

import com.envision.gravity.common.ignite.service.BOQueryCacheLoadService;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.bo.BOQueryCacheAbstractLoadRequest;

import java.util.List;


import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteLogger;
import org.apache.ignite.resources.IgniteInstanceResource;
import org.apache.ignite.resources.LoggerResource;

public class BOQueryCacheLoadServiceImpl implements BOQueryCacheLoadService {
    private static final long serialVersionUID = 5733384158524153286L;

    @IgniteInstanceResource private Ignite ignite;
    @LoggerResource private IgniteLogger log;

    @Override
    public ResponseResult<Void> loadAll(List<BOQueryCacheAbstractLoadRequest> loadRequests) {
        return null;
    }
}
