package com.envision.gravity.flink.steaming.bo.event.generator;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.steaming.bo.event.entity.EventMsg;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
public interface Generator {
    EventMsg generateByCreate(ConvertedCdcRecord record);

    EventMsg generateByDelete(ConvertedCdcRecord record);

    EventMsg generateByUpdate(ConvertedCdcRecord record);
}
