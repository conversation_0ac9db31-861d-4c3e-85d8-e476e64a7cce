package com.envision.gravity.ignite.tsdb.loader;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;


import org.junit.jupiter.api.Test;

/** <AUTHOR> 2024/6/11 */
public class BaseTest {

    @Test
    public void mapTest() {
        Map<String, String> kv = new HashMap<>();
        kv.put("k1", "v1");
        kv.put("k2", "v2");

        Set<String> keys = kv.keySet();
        keys.add("k3");
        System.out.printf(keys.toString());
    }
}
