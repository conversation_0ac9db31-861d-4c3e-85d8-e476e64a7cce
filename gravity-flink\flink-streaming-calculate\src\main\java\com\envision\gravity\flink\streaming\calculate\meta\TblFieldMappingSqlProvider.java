package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingKey;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingRecord;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class TblFieldMappingSqlProvider {

    public String batchUpdate(
            String orgId, Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap) {
        String values =
                fieldMappingRecordMap.values().stream()
                        .map(
                                fmr ->
                                        String.format(
                                                "('%s', '%s', '%s', '%s', %d, %b, CURRENT_TIMESTAMP, 'gravity')",
                                                fmr.getCompId(),
                                                fmr.getPrefId(),
                                                fmr.getFieldId(),
                                                fmr.getRawFieldId(),
                                                fmr.getFieldIndex(),
                                                fmr.getHorizontal()))
                        .collect(Collectors.joining(","));

        return "INSERT INTO "
                + orgId
                + ".tbl_component_pref_field_mapping (comp_id, pref_id, field_id, raw_field_id, field_index, horizontal, created_time, created_user) VALUES "
                + values
                + "\n"
                + "ON CONFLICT (comp_id, pref_id, field_id)\n"
                + "DO UPDATE SET\n"
                + "     raw_field_id=COALESCE(EXCLUDED.raw_field_id, tbl_component_pref_field_mapping.raw_field_id),\n"
                + "     field_index=COALESCE(EXCLUDED.field_index, tbl_component_pref_field_mapping.field_index),\n"
                + "     horizontal=COALESCE(EXCLUDED.horizontal, tbl_component_pref_field_mapping.horizontal),\n"
                + "     modified_user='gravity', modified_time=CURRENT_TIMESTAMP\n"
                + "WHERE EXCLUDED.raw_field_id IS NOT NULL AND EXCLUDED.field_index IS NOT NULL AND EXCLUDED.horizontal IS NOT NULL;";
    }

    public String batchDelete(String orgId, Set<FieldMappingKey> fieldMappingKeys) {
        String values =
                fieldMappingKeys.stream()
                        .map(
                                k ->
                                        String.format(
                                                "('%s', '%s', '%s')",
                                                k.getCompId(), k.getPrefId(), k.getFieldId()))
                        .collect(Collectors.joining(","));
        return "DELETE FROM "
                + orgId
                + ".TBL_COMPONENT_PREF_FIELD_MAPPING WHERE (comp_id, pref_id, field_id) IN ("
                + values
                + ");";
    }
}
