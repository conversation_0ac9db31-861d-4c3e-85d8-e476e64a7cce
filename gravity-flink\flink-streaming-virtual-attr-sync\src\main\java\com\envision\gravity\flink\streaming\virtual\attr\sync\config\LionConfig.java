package com.envision.gravity.flink.streaming.virtual.attr.sync.config;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class LionConfig {

    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getIgniteHostname() {
        return getStringValue("gravity-common.ignite.hostname", null);
    }

    public static int getIgniteClientPort() {
        return getIntValue("gravity-common.ignite.client.port", 10800);
    }

    public static String getIgniteUsername() {
        return getStringValue("gravity-common.ignite.username", "ignite");
    }

    public static String getIgnitePassword() {
        return getStringValue("gravity-common.ignite.password", "ignite");
    }

    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.pg.cdc.database", "gravity");
    }

    public static String getPgSchemaList() {
        return getStringValue("gravity-flink.virtual-attr-sync.pg.cdc.schema.list", "^o.*");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static int getPgMaxPoolSize() {
        return getIntValue("gravity-flink.virtual-attr-sync.pg.datasource.max.pool.size", 4);
    }

    public static String getPgJdbcUrl() {
        return getStringValue("gravity-common.postgresql.jdbc-url", null);
    }

    public static String getPgTableList() {
        return getStringValue(
                "gravity-flink.virtual-attr-sync.pg.cdc.table.list",
                "^o.*\\.tbl_pref_ext$,^o.*\\.tbl_bo$,^o.*\\.tbl_edge$,^o.*\\.tbl_obj_attr$");
    }

    public static String getPublicationName() {
        return getStringValue(
                "gravity-flink.virtual-attr-sync.pg.publication.name",
                "virtual_attr_sync_publication");
    }

    public static String getSlotName() {
        return getStringValue(
                "gravity-flink.virtual-attr-sync.slot.name", "gravity_virtual_attr_sync");
    }

    public static int getAssetPageSize() {
        return getIntValue("gravity-flink.virtual-attr-sync.asset.page.size", 100);
    }

    public static String getModelRegex() {
        return getStringValue("gravity-flink.virtual-attr-sync.model.regex", ".*");
    }

    public static String getAttrRegex() {
        return getStringValue("gravity-flink.virtual-attr-sync.attribute.regex", ".*");
    }

    public static String getSchemeRegex() {
        return getStringValue("gravity-flink.virtual-attr-sync.scheme.regex", "^o.*");
    }

    public static String getSchemeLike() {
        return getStringValue("gravity-flink.virtual-attr-sync.scheme.like", "o%");
    }

    public static String getGravityRestApiUrl() {
        return getStringValue("gravity-common.rest.url", "http://localhost:8080");
    }

    public static int getScheduleTimeIntervalInHours() {
        return getIntValue("gravity-flink.virtual-attr-sync.schedule.time.interval.in.hours", 3);
    }

    public static int getSyncTimeWindowIntervalInHours() {
        return getIntValue("gravity-flink.virtual-attr-sync.sync.time.window.interval.in.hours", 1);
    }

    public static String getMaxBatchSize() {
        return getStringValue("gravity-flink.virtual-attr-sync.source.max.batch.size", "5");
    }

    public static String getMaxQueueSize() {
        return getStringValue("gravity-flink.virtual-attr-sync.source.max.queue.size", "10");
    }
}
