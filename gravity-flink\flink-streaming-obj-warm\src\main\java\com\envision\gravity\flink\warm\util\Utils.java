package com.envision.gravity.flink.warm.util;

import com.envision.gravity.common.po.PropFieldMeta;
import com.envision.gravity.common.util.RawFieldIdUtil;
import com.envision.gravity.flink.warm.model.PrefValue;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class Utils {
    public static void setRawFieldId(List<PrefValue> prefValues) {
        List<PropFieldMeta> propFieldMetas =
                prefValues.stream()
                        .map(
                                prefValue ->
                                        PropFieldMeta.builder()
                                                .fieldId(prefValue.getFieldId())
                                                .fieldDataType(prefValue.getFieldDataType())
                                                .prefName(prefValue.getPrefName())
                                                .compName(prefValue.getCompName())
                                                .categoryId(prefValue.getCategoryId())
                                                .build())
                        .collect(Collectors.toList());
        Map<String, String> rawFieldIdMap = RawFieldIdUtil.genRawFieldIdByPropMeta(propFieldMetas);
        prefValues.forEach(
                prefValue -> prefValue.setRawFieldId(rawFieldIdMap.get(prefValue.getFieldId())));
    }
}
