package com.envision.gravity.common.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description Attribute/Measurement Point/Metric/Service
 */
@Getter
public enum TagType {
    MODEL("MODEL"),
    PROPERTY("PROPERTY"),
    OBJECT("OBJECT"),
    OBJECT_RELATION_GRAPH("OBJECT_RELATION_GRAPH"),
    COMMAND("COMMAND");

    private final String name;

    TagType(String name) {
        this.name = name;
    }
}
