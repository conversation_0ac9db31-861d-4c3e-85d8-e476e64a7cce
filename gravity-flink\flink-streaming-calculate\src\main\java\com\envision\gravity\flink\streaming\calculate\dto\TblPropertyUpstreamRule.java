package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblPropertyUpstreamRule implements CdcTableEntity {
    private static final long serialVersionUID = 5449839612910096687L;
    private String prefRuleId;
    private String targetCategory;
    private String targetCompId;
    private String targetPrefId;
    private String srcCategory;
    private String expression;
    private int calcType;
    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;

    public CalcType getCalcTypeEnum() {
        return CalcType.getCalcType(calcType);
    }
}
