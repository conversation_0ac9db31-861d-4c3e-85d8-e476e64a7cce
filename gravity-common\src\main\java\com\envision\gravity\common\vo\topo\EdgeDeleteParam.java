package com.envision.gravity.common.vo.topo;

import javax.validation.constraints.NotBlank;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/16 10:31 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EdgeDeleteParam {

    @NotBlank(message = "subGraphId can not blank")
    private String subGraphId;

    @NotBlank(message = "edgeType can not blank")
    private String edgeType;

    private String fromVid;

    private String toVid;
}
