package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcJobTaskStatusEnum;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * CalcJobTaskEnumeratorState 单元测试
 *
 * <AUTHOR>
 */
class CalcJobTaskEnumeratorStateTest {

    private List<CalcJobTaskSplit> testSplits;
    private Set<String> testCompletedIds;

    @BeforeEach
    void setUp() {
        // 创建测试分片
        testSplits = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            CalcJobTask task =
                    CalcJobTask.builder()
                            .jobId("test_job_001")
                            .taskId("task_" + i)
                            .targetPrefName("test_pref")
                            .targetAssetIds(java.util.Arrays.asList("asset_" + i))
                            .startTime(System.currentTimeMillis())
                            .endTime(System.currentTimeMillis() + 3600000)
                            .status(RecCalcJobTaskStatusEnum.INIT)
                            .build();

            testSplits.add(new CalcJobTaskSplit("split_" + i, task));
        }

        // 创建已完成任务ID集合
        testCompletedIds = new HashSet<>();
        testCompletedIds.add("completed_task_1");
        testCompletedIds.add("completed_task_2");
    }

    @Test
    void testDefaultConstructor() {
        CalcJobTaskEnumeratorState state = new CalcJobTaskEnumeratorState();

        assertNotNull(state.getPendingSplits());
        assertTrue(state.getPendingSplits().isEmpty());
        assertNotNull(state.getCompletedSplitIds());
        assertTrue(state.getCompletedSplitIds().isEmpty());
        assertEquals(1, state.getNextTaskId());
        assertFalse(state.isInitialized());
        assertEquals(0, state.getTotalTaskCount());
        assertEquals(0, state.getCompletedTaskCount());
    }

    @Test
    void testParameterizedConstructor() {
        CalcJobTaskEnumeratorState state =
                new CalcJobTaskEnumeratorState(testSplits, testCompletedIds, 10, true);

        assertEquals(testSplits, state.getPendingSplits());
        assertEquals(testCompletedIds, state.getCompletedSplitIds());
        assertEquals(10, state.getNextTaskId());
        assertTrue(state.isInitialized());
        assertEquals(5, state.getTotalTaskCount()); // 3 pending + 2 completed
        assertEquals(2, state.getCompletedTaskCount());
    }

    @Test
    void testParameterizedConstructor_WithNullValues() {
        CalcJobTaskEnumeratorState state = new CalcJobTaskEnumeratorState(null, null, 5, false);

        assertNotNull(state.getPendingSplits());
        assertTrue(state.getPendingSplits().isEmpty());
        assertNotNull(state.getCompletedSplitIds());
        assertTrue(state.getCompletedSplitIds().isEmpty());
        assertEquals(5, state.getNextTaskId());
        assertFalse(state.isInitialized());
        assertEquals(0, state.getTotalTaskCount());
        assertEquals(0, state.getCompletedTaskCount());
    }

    @Test
    void testSettersAndGetters() {
        CalcJobTaskEnumeratorState state = new CalcJobTaskEnumeratorState();

        state.setPendingSplits(testSplits);
        state.setCompletedSplitIds(testCompletedIds);
        state.setNextTaskId(15);
        state.setInitialized(true);
        state.setTotalTaskCount(100);
        state.setCompletedTaskCount(50);

        assertEquals(testSplits, state.getPendingSplits());
        assertEquals(testCompletedIds, state.getCompletedSplitIds());
        assertEquals(15, state.getNextTaskId());
        assertTrue(state.isInitialized());
        assertEquals(100, state.getTotalTaskCount());
        assertEquals(50, state.getCompletedTaskCount());
    }

    @Test
    void testToString() {
        CalcJobTaskEnumeratorState state =
                new CalcJobTaskEnumeratorState(testSplits, testCompletedIds, 10, true);

        String result = state.toString();

        assertTrue(result.contains("CalcJobTaskEnumeratorState"));
        assertTrue(result.contains("pendingSplits=3"));
        assertTrue(result.contains("completedSplitIds=2"));
        assertTrue(result.contains("nextTaskId=10"));
        assertTrue(result.contains("isInitialized=true"));
        assertTrue(result.contains("totalTaskCount=5"));
        assertTrue(result.contains("completedTaskCount=2"));
    }

    @Test
    void testToString_WithNullCollections() {
        CalcJobTaskEnumeratorState state = new CalcJobTaskEnumeratorState();
        state.setPendingSplits(null);
        state.setCompletedSplitIds(null);

        String result = state.toString();

        assertTrue(result.contains("pendingSplits=0"));
        assertTrue(result.contains("completedSplitIds=0"));
    }
}
