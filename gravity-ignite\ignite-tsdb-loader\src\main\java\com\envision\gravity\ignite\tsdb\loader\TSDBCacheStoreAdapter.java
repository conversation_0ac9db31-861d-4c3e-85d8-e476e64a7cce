package com.envision.gravity.ignite.tsdb.loader;

import com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldDataType;
import com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldType;
import com.envision.gravity.ignite.tsdb.loader.bo.BOMultiFieldMapping;
import com.envision.gravity.ignite.tsdb.loader.bo.BOPropertyCacheKey;
import com.envision.gravity.ignite.tsdb.loader.bo.BOPropertyRecord;

import javax.cache.Cache;
import javax.cache.integration.CacheLoaderException;
import javax.cache.integration.CacheWriterException;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldDataType.BOOLEAN;
import static com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldDataType.DOUBLE;
import static com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldDataType.LONG;
import static com.envision.gravity.ignite.tsdb.loader.bo.BOModelFieldDataType.STRING;
import static io.eniot.tsdb.common.util.Constants.DEFAULT_TABLE_NAME;

import com.github.benmanes.caffeine.cache.Caffeine;
import io.eniot.tsdb.adapter.interpolator.InterpolatorType;
import io.eniot.tsdb.adapter.query.Query;
import io.eniot.tsdb.adapter.queryoptions.AggregationQueryOptions;
import io.eniot.tsdb.adapter.queryoptions.BaseQueryOptions;
import io.eniot.tsdb.adapter.queryoptions.RawDataQueryOptions;
import io.eniot.tsdb.common.TSDBRecord;
import io.eniot.tsdb.common.TSDBResultSet;
import io.eniot.tsdb.common.TSDBResultStyle;
import io.eniot.tsdb.common.TimeRange;
import io.eniot.tsdb.common.exception.ConnectionCloseException;
import io.eniot.tsdb.common.sharding.ShardingRule;
import io.eniot.tsdb.common.util.AuthMethod;
import io.eniot.tsdb.common.util.CommonUtil;
import io.eniot.tsdb.configuration.Configuration;
import io.eniot.tsdb.configuration.SDKOptions;
import io.eniot.tsdb.sdk.api.TSDBApi;
import io.eniot.tsdb.sdk.api.TSDBApiFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.IgniteCheckedException;
import org.apache.ignite.IgniteLogger;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.CacheStoreAdapter;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.internal.util.typedef.internal.U;
import org.apache.ignite.lang.IgniteBiInClosure;
import org.apache.ignite.resources.CacheNameResource;
import org.apache.ignite.resources.IgniteInstanceResource;
import org.apache.ignite.resources.LoggerResource;
import org.apache.ignite.thread.IgniteThreadFactory;

public class TSDBCacheStoreAdapter extends CacheStoreAdapter<BinaryObject, BinaryObject> {

    private static final String ASSET_TIMEZONE_COLUMN = "_timezone";
    private static final String DEFAULT_TIMEZONE = "+08:00";
    private static final String TIME_GROUP_RAW = "RAW";

    private static final String TSDB_CACHE_LOADER_THREAD_NAME = "tsdb-cache-metric-loader";

    @CacheNameResource private String cacheName;

    @IgniteInstanceResource private Ignite ignite;

    @LoggerResource private IgniteLogger log;

    private final com.github.benmanes.caffeine.cache.Cache<BOPropertyCacheKey, BOPropertyRecord>
            boPropertyCache;

    private JdbcType[] types;

    public void setTypes(JdbcType[] types) {
        this.types = types;
    }

    // TSDB Configuration
    private transient TSDBApi tsdbApi;

    private String connectionURL;

    private String userName;

    private String password;

    private String clusterMetaURL;

    private String clusterMetaUserName;

    private String clusterMetaPassword;

    private int clusterReplicaFactor;

    private String clusterShardingRule;

    // put all concurrency
    private int putCacheSplitSize;

    private int putCacheParallelism;

    public void setConnectionURL(String connectionURL) {
        this.connectionURL = connectionURL;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setClusterMetaURL(String clusterMetaURL) {
        this.clusterMetaURL = clusterMetaURL;
    }

    public void setClusterMetaUserName(String clusterMetaUserName) {
        this.clusterMetaUserName = clusterMetaUserName;
    }

    public void setClusterMetaPassword(String clusterMetaPassword) {
        this.clusterMetaPassword = clusterMetaPassword;
    }

    public void setClusterReplicaFactor(String clusterReplicaFactor) {
        this.clusterReplicaFactor = Integer.parseInt(clusterReplicaFactor);
    }

    public void setClusterShardingRule(String clusterShardingRule) {
        this.clusterShardingRule = clusterShardingRule;
    }

    public void setPutCacheSplitSize(int putCacheSplitSize) {
        this.putCacheSplitSize = putCacheSplitSize;
    }

    public void setPutCacheParallelism(int putCacheParallelism) {
        this.putCacheParallelism = putCacheParallelism;
    }

    public TSDBCacheStoreAdapter(int cacheSize, int cacheExpireSeconds) {
        this.boPropertyCache =
                Caffeine.newBuilder()
                        .maximumSize(cacheSize)
                        .expireAfterAccess(cacheExpireSeconds, TimeUnit.SECONDS)
                        .build();
    }

    @Override
    public void loadCache(IgniteBiInClosure<BinaryObject, BinaryObject> clo, Object... args) {
        long start = System.currentTimeMillis();
        if (types == null || types.length == 0) {
            throw new IllegalArgumentException("Jdbc type is required ...");
        }
        if (args == null || args.length == 0 || args[0] == null) {
            throw new IllegalArgumentException("TSDB cache loader parameter not found ...");
        }

        initTSDBApi();
        TSDBLoaderRequest request = (TSDBLoaderRequest) args[0];
        ExecutorService pool =
                Executors.newFixedThreadPool(
                        1, new IgniteThreadFactory(ignite.name(), TSDB_CACHE_LOADER_THREAD_NAME));
        try {
            Future<?> future = pool.submit(() -> doLoadData(request));
            U.get(future);
        } catch (IgniteCheckedException e) {
            throw new TSDBLoaderException(
                    "Failed to load cache: " + U.maskName(cacheName), e.getCause());
        } finally {
            U.shutdownNow(getClass(), pool, null);
            log.info(
                    String.format(
                            "Total time of loadCache = %d ms", System.currentTimeMillis() - start));
        }
    }

    private void doLoadData(TSDBLoaderRequest request) {
        long start = System.currentTimeMillis();
        log.info(String.format("TSDBLoaderRequest: %s", request));

        Set<String> prefNames = new HashSet<>(request.getPointWithAggrs().keySet());
        prefNames.add(ASSET_TIMEZONE_COLUMN);

        BOMultiFieldMapping multiFieldMapping = null;
        if (CommonUtil.isEmpty(request.getFieldMapping())) {
            log.warning("NOTICE: request's fieldMapping not set, use legacy method ...");
            multiFieldMapping =
                    getFieldMapping(
                            request.getOrgId().toUpperCase(), request.getAssetIds(), prefNames);
        }
        TSDBSdkParams sdkParams = buildSdkParams(request, multiFieldMapping);

        TSDBResultSet rs;
        try {
            rs = executeTSDBQuery(request, sdkParams);
        } catch (ConnectionCloseException e) {
            log.error("TSDB connection closed, try reconnect ...");
            resetTsdbApi();
            throw new TSDBLoaderException("TSDB connection closed, try reconnect: ", e);
        } catch (Exception e) {
            log.error("TSDB query failed: ", e);
            throw new TSDBLoaderException("TSDB query failed: ", e);
        }

        if (CommonUtil.isEmpty(rs)) {
            log.warning("TSDB query result is empty ...");
            return;
        }

        if (log.isDebugEnabled()) {
            log.debug("Row size of TSDBResultSet: " + rs.rowSize());
        }

        String keyType;
        String valueType;
        JdbcType type = types[0];
        if (type.getCacheName().equals(cacheName)) {
            keyType = type.getKeyType();
            valueType = type.getValueType();
        } else {
            throw new TSDBLoaderException(type.getCacheName() + " not belongs to this loader");
        }

        int totalCell = 0;
        long putStart = System.currentTimeMillis();

        Map<BinaryObject, BinaryObject> batchPutMap = new HashMap<>(rs.rowSize());
        for (TSDBRecord row : rs.getRows()) {
            BinaryObjectBuilder valueBuilder = ignite.binary().builder(valueType);
            BinaryObjectBuilder keyBuilder = ignite.binary().builder(keyType);

            String prefName = row.getMeasurement();
            String timeZone = row.getTimeZone();

            valueBuilder.setField(TSDBLoaderConstants.REQUESTID, request.getRequestId());
            keyBuilder.setField(TSDBLoaderConstants.REQUESTID, request.getRequestId());

            valueBuilder.setField(TSDBLoaderConstants.MDMID, row.getAssetId());
            keyBuilder.setField(TSDBLoaderConstants.MDMID, row.getAssetId());

            valueBuilder.setField(TSDBLoaderConstants.TIMEZONE, timeZone);
            keyBuilder.setField(TSDBLoaderConstants.TIMEZONE, timeZone);

            valueBuilder.setField(TSDBLoaderConstants.KPI, prefName);
            keyBuilder.setField(TSDBLoaderConstants.KPI, prefName);

            valueBuilder.setField(TSDBLoaderConstants.TS, row.getTime());
            keyBuilder.setField(TSDBLoaderConstants.TS, row.getTime());

            String localTime = TSDBLoaderUtils.formatTimestamp(row.getTime(), timeZone);
            valueBuilder.setField(TSDBLoaderConstants.LOCAL_TIME, localTime);
            keyBuilder.setField(TSDBLoaderConstants.LOCAL_TIME, localTime);

            if (sdkParams.getQueryType() == TSDBQueryType.TS) {
                keyBuilder.setField(TSDBLoaderConstants.AGGTYPE, null);
                valueBuilder.setField(TSDBLoaderConstants.AGGTYPE, null);
                keyBuilder.setField(TSDBLoaderConstants.TIMEGROUP, request.getTimeGroup());
                valueBuilder.setField(TSDBLoaderConstants.TIMEGROUP, request.getTimeGroup());

                if (row.getQuality() != null) {
                    valueBuilder.setField(TSDBLoaderConstants.QUALITY, row.getQuality());
                }
            } else {
                String aggrType = request.getPointWithAggrs().get(prefName);
                keyBuilder.setField(TSDBLoaderConstants.AGGTYPE, aggrType);
                valueBuilder.setField(TSDBLoaderConstants.AGGTYPE, aggrType);

                String timeGroup = request.getTimeGroup();
                keyBuilder.setField(TSDBLoaderConstants.TIMEGROUP, timeGroup);
                valueBuilder.setField(TSDBLoaderConstants.TIMEGROUP, timeGroup);
            }

            BOModelFieldDataType dataType;
            if (multiFieldMapping != null) {
                dataType = multiFieldMapping.getDataType(prefName);
            } else {
                dataType = BOModelFieldDataType.valueOf(request.getPrefDataTypes().get(prefName));
            }

            Object value = row.getValue();
            switch (dataType) {
                case INTEGER:
                case LONG:
                    valueBuilder.setField(TSDBLoaderConstants.VALUELONG, LONG.parse(value));
                    break;
                case FLOAT:
                case DOUBLE:
                    valueBuilder.setField(TSDBLoaderConstants.VALUEDOUBLE, DOUBLE.parse(value));
                    break;
                case BOOLEAN:
                    valueBuilder.setField(TSDBLoaderConstants.VALUEBOOL, BOOLEAN.parse(value));
                    break;
                default:
                    valueBuilder.setField(TSDBLoaderConstants.VALUESTRING, STRING.parse(value));
            }

            totalCell++;
            batchPutMap.put(keyBuilder.build(), valueBuilder.build());
        }
        processWriteCache(batchPutMap);

        log.info(
                String.format(
                        "Total time = %d ms, put cache time = %d ms, total records = %d\n",
                        System.currentTimeMillis() - start,
                        System.currentTimeMillis() - putStart,
                        totalCell));
    }

    private void processWriteCache(Map<BinaryObject, BinaryObject> batchPutMap) {
        IgniteCache<BinaryObject, BinaryObject> cache = ignite.cache(cacheName);
        if (batchPutMap.size() <= this.putCacheSplitSize) {
            cache.putAll(batchPutMap);
            return;
        }

        List<Map<BinaryObject, BinaryObject>> subMaps =
                TSDBLoaderUtils.splitMap(batchPutMap, this.putCacheSplitSize);
        ExecutorService putExecutor = Executors.newFixedThreadPool(this.putCacheParallelism);
        try {
            List<Future<Void>> futures = new ArrayList<>();
            for (Map<BinaryObject, BinaryObject> subMap : subMaps) {
                futures.add(
                        putExecutor.submit(
                                () -> {
                                    cache.putAll(subMap);
                                    return null; // Future<Void>
                                }));
            }
            // Waiting for task finished
            for (Future<Void> future : futures) {
                future.get();
            }
        } catch (Exception e) {
            log.error("Concurrent put failed: ", e);
            throw new TSDBLoaderException("Concurrent put failed: ", e);
        } finally {
            putExecutor.shutdown();
        }
    }

    private TSDBResultSet executeTSDBQuery(TSDBLoaderRequest request, TSDBSdkParams sdkParams) {
        if (CommonUtil.emptyCollection(sdkParams.getSystemIds())
                || CommonUtil.isEmpty(sdkParams.getPrefName2fieldId())) {
            return TSDBResultSet.emptyResult();
        }

        Map<String, Set<String>> prefName2fieldId = sdkParams.getPrefName2fieldId();
        Set<String> fieldIds = new HashSet<>();
        for (Map.Entry<String, Set<String>> entry : prefName2fieldId.entrySet()) {
            fieldIds.addAll(entry.getValue());
        }

        if (sdkParams.getUseLocalTime()) {
            List<TSDBRecord> allRows = new ArrayList<>();
            for (Map.Entry<String, Set<String>> entry :
                    sdkParams.getTimeZone2assetId().entrySet()) {
                String timeZone =
                        StringUtils.isEmpty(entry.getKey()) ? DEFAULT_TIMEZONE : entry.getKey();
                Long startTs = TSDBLoaderUtils.parseLocalTime(request.getStartTime(), timeZone);
                Long endTs = TSDBLoaderUtils.parseLocalTime(request.getEndTime(), timeZone);
                TimeRange tr = TimeRange.of(startTs, endTs);
                TSDBResultSet rs =
                        executeTSDBQueryInternal(
                                fieldIds, entry.getValue(), tr, timeZone, sdkParams);
                allRows.addAll(rs.getRows());
            }
            return TSDBResultSet.returnAsLongTable(allRows);
        } else {
            return executeTSDBQueryInternal(
                    fieldIds,
                    sdkParams.getAsset2systemId().keySet(),
                    sdkParams.getTimeRange(),
                    DEFAULT_TIMEZONE,
                    sdkParams);
        }
    }

    private TSDBResultSet executeTSDBQueryInternal(
            Set<String> fieldIds,
            Set<String> assetIds,
            TimeRange timeRange,
            String timeZone,
            TSDBSdkParams sdkParams) {
        Map<String, String> asset2systemId = sdkParams.getAsset2systemId();
        Set<String> filterSystemIds =
                assetIds.stream()
                        .map(aid -> sdkParams.getAsset2systemId().get(aid))
                        .collect(Collectors.toSet());

        if (CommonUtil.emptyCollection(filterSystemIds)) {
            log.warning("idMapping is empty");
            return TSDBResultSet.emptyResult();
        }

        if (CommonUtil.isEmpty(sdkParams.getPrefName2fieldId())) {
            log.warning("fieldMapping is empty");
            return TSDBResultSet.emptyResult();
        }

        BaseQueryOptions options;
        if (sdkParams.getQueryType().equals(TSDBQueryType.TS)) {
            options = new RawDataQueryOptions();
        } else {
            options = new AggregationQueryOptions();
        }
        if (sdkParams.getSlimit() != null) {
            options.setSeriesLimit(sdkParams.getSlimit());
        }
        if (sdkParams.getInterpolatorType() != null) {
            options.setInterpolationType(sdkParams.getInterpolatorType());
            options.setInterpolationInterval(sdkParams.getTimeGroup());
        }
        options.setAssetTimeZone(ZoneId.of(timeZone));

        if (sdkParams.getQueryType().equals(TSDBQueryType.TS)) {
            assert options instanceof RawDataQueryOptions;
            RawDataQueryOptions rawOptions = (RawDataQueryOptions) options;
            rawOptions.setWithDQ(true);

            return this.tsdbApi
                    .query()
                    .useDatabase(sdkParams.getOrgId())
                    .fromNormalizedTable(DEFAULT_TABLE_NAME)
                    .executeRawDataQuery(
                            fieldIds,
                            filterSystemIds,
                            timeRange,
                            rawOptions,
                            sdkParams.getPrefName2fieldId(),
                            asset2systemId,
                            TSDBResultStyle.LONG_TABLE);
        } else {
            assert options instanceof AggregationQueryOptions;
            AggregationQueryOptions aggOptions = (AggregationQueryOptions) options;

            return this.tsdbApi
                    .query()
                    .useDatabase(sdkParams.getOrgId())
                    .fromNormalizedTable(DEFAULT_TABLE_NAME)
                    .executeAggregationQuery(
                            sdkParams.getFieldIdWithAggrs(),
                            filterSystemIds,
                            timeRange,
                            sdkParams.getTimeGroup(),
                            aggOptions,
                            sdkParams.getPrefName2fieldId(),
                            asset2systemId,
                            TSDBResultStyle.LONG_TABLE);
        }
    }

    private TSDBSdkParams buildSdkParams(
            TSDBLoaderRequest request, BOMultiFieldMapping multiFieldMapping) {
        TSDBSdkParams sdkParams = new TSDBSdkParams();
        checkRequestParams(request, sdkParams);
        sdkParams.setOrgId(request.getOrgId());

        Map<String, Set<String>> prefName2fieldId;
        Map<String, Pair<String, String>> idTimezoneMapping;
        if (multiFieldMapping != null) {
            Set<String> prefNames = request.getPointWithAggrs().keySet();
            prefName2fieldId = filterFieldMapping(multiFieldMapping.getFieldIds(prefNames));
            idTimezoneMapping =
                    getIdTimezoneMapping(
                            request.getOrgId().toUpperCase(),
                            request.getAssetIds(),
                            multiFieldMapping);
        } else {
            prefName2fieldId = filterFieldMapping(request.getFieldMapping());
            idTimezoneMapping = request.getIdTimezoneMapping();
        }

        Map<String, String> asset2systemId =
                idTimezoneMapping.entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Map.Entry::getKey, entry -> entry.getValue().getLeft()));
        sdkParams.setPrefName2fieldId(prefName2fieldId);
        sdkParams.setAsset2systemId(asset2systemId);

        Map<String, Set<Query.AggregationType>> fieldIdWithAggrs =
                new HashMap<>(request.getPointWithAggrs().size());
        for (Map.Entry<String, Set<String>> entry : prefName2fieldId.entrySet()) {
            for (String fieldId : entry.getValue()) {
                Query.AggregationType aggrType =
                        Query.AggregationType.getByType(
                                request.getPointWithAggrs().get(entry.getKey()));
                fieldIdWithAggrs.put(fieldId, Collections.singleton(aggrType));
            }
        }
        sdkParams.setFieldIdWithAggrs(fieldIdWithAggrs);

        if (request.getStartTs() != null) {
            sdkParams.setUseLocalTime(false);
            TimeRange timeRange = TimeRange.of(request.getStartTs(), request.getEndTs());
            sdkParams.setTimeRange(timeRange);
        } else if (!StringUtils.isEmpty(request.getStartTime())) {
            sdkParams.setUseLocalTime(true);
            Map<String, Set<String>> timeZone2assetId = new HashMap<>();
            // assetId => (systemId, timeZone)
            for (Map.Entry<String, Pair<String, String>> entry : idTimezoneMapping.entrySet()) {
                String timeZone = entry.getValue().getRight();
                timeZone2assetId.computeIfAbsent(timeZone, tz -> new HashSet<>());
                timeZone2assetId.get(timeZone).add(entry.getKey());
            }
            sdkParams.setTimeZone2assetId(timeZone2assetId);
        }

        sdkParams.setSystemIds(new HashSet<>(asset2systemId.values()));
        sdkParams.setSlimit(request.getSlimit());

        return sdkParams;
    }

    // Exclude property which raw_field_id not exist
    private Map<String, Set<String>> filterFieldMapping(Map<String, Set<String>> fieldMapping) {
        return fieldMapping.entrySet().stream()
                .filter(entry -> !CommonUtil.emptyCollection(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private void checkRequestParams(TSDBLoaderRequest request, TSDBSdkParams sdkParams) {
        if (CommonUtil.isEmpty(request.getPointWithAggrs())) {
            throw new IllegalArgumentException("Measurement points is required ...");
        }

        if (StringUtils.isEmpty(request.getStartTime())
                && StringUtils.isEmpty(request.getEndTime())
                && (request.getStartTs() == null)
                && (request.getEndTs() == null)) {
            throw new IllegalArgumentException("LocalTime or UTC time required ...");
        }

        if (!StringUtils.isEmpty(request.getStartTime())
                && !StringUtils.isEmpty(request.getEndTime())
                && (request.getStartTs() != null)
                && (request.getEndTs() != null)) {
            throw new IllegalArgumentException("LocalTime and UTC time specified at same time ...");
        }

        Optional<CDSTimeGroup> timeGroupRes = CDSTimeGroup.match(request.getTimeGroup());
        if (!timeGroupRes.isPresent()) {
            throw new IllegalArgumentException("Unrecognized time group ...");
        }

        CDSTimeGroup cdsTimeGroup = timeGroupRes.get();
        TSDBQueryType queryType = getQueryType(cdsTimeGroup);
        if (queryType == TSDBQueryType.TS_WINDOW) { // Aggr query
            for (Map.Entry<String, String> entry : request.getPointWithAggrs().entrySet()) {
                Query.AggregationType aggrType = Query.AggregationType.getByType(entry.getValue());
                if (aggrType == null) {
                    throw new IllegalArgumentException("Measurement points is required ...");
                }
            }
        }

        sdkParams.setQueryType(queryType);
        sdkParams.setTimeGroup(cdsTimeGroup.getRetention());

        if (request.getAutoInterpolate() != null && request.getAutoInterpolate()) {
            sdkParams.setInterpolatorType(InterpolatorType.SAMPLE_CDS_PREVIOUS);
        }
    }

    private TSDBQueryType getQueryType(CDSTimeGroup cdsTimeGroup) {
        if (cdsTimeGroup == CDSTimeGroup.RAW || cdsTimeGroup == CDSTimeGroup.RAW_NULL) {
            return TSDBQueryType.TS;
        }
        return TSDBQueryType.TS_WINDOW;
    }

    private BOMultiFieldMapping getFieldMapping(
            String orgId, Set<String> assetIds, Set<String> prefNames) {
        if (CommonUtil.emptyCollection(assetIds) || CommonUtil.emptyCollection(prefNames)) {
            throw new IllegalArgumentException("assetIds or prefNames is empty ...");
        }

        List<BOPropertyCacheKey> propertyCacheKeys =
                new ArrayList<>(assetIds.size() * prefNames.size());
        for (String assetId : assetIds) {
            for (String prefName : prefNames) {
                propertyCacheKeys.add(new BOPropertyCacheKey(orgId, assetId, prefName));
            }
        }

        long start = System.currentTimeMillis();
        Map<BOPropertyCacheKey, BOPropertyRecord> propertyRecordMap =
                this.boPropertyCache.getAll(
                        propertyCacheKeys,
                        keys -> {
                            String assetIdExpr = TSDBLoaderUtils.flattenElements(assetIds);
                            String prefNameExpr = TSDBLoaderUtils.flattenElements(prefNames);
                            try (FieldsQueryCursor<List<?>> cursor =
                                    ignite.cache(cacheName)
                                            .query(
                                                    new SqlFieldsQuery(
                                                                    fieldMappingSQL(
                                                                            orgId,
                                                                            assetIdExpr,
                                                                            prefNameExpr))
                                                            .setSchema(orgId))) {
                                List<List<?>> data = cursor.getAll();
                                if (CollectionUtils.isEmpty(data)) {
                                    return Collections.emptyMap();
                                }

                                Map<BOPropertyCacheKey, BOPropertyRecord> queryResult =
                                        new HashMap<>(data.size());
                                for (List<?> item : data) {
                                    // comp:pref, raw_field_id, pref_type, has_quality, data_type,
                                    // asset_id
                                    String fullPrefName = item.get(0).toString();
                                    String assetId = item.get(5).toString();
                                    BOPropertyCacheKey propertyCacheKey =
                                            new BOPropertyCacheKey(orgId, assetId, fullPrefName);
                                    BOPropertyRecord propertyRecord =
                                            new BOPropertyRecord(
                                                    fullPrefName,
                                                    item.get(1).toString(),
                                                    BOModelFieldType.of(item.get(2).toString()),
                                                    item.get(3) instanceof Boolean
                                                            ? (Boolean) item.get(3)
                                                            : Boolean.valueOf(
                                                                    item.get(3).toString()),
                                                    BOModelFieldDataType.of(
                                                            item.get(4).toString()));
                                    queryResult.put(propertyCacheKey, propertyRecord);
                                }
                                return queryResult;
                            } catch (Exception e) {
                                log.error("Execute fieldMappingSQL failed: " + e.getMessage());
                                throw new TSDBLoaderException(e);
                            } finally {
                                log.info(
                                        String.format(
                                                "Exec getFieldMapping sql time %d ms, assets=%d, points=%d",
                                                System.currentTimeMillis() - start,
                                                assetIds.size(),
                                                prefNames.size()));
                            }
                        });

        BOMultiFieldMapping fieldMapping = new BOMultiFieldMapping();
        for (Map.Entry<BOPropertyCacheKey, BOPropertyRecord> propertyEntry :
                propertyRecordMap.entrySet()) {
            BOPropertyRecord propertyRecord = propertyEntry.getValue();
            fieldMapping.put(
                    propertyRecord.getFullPrefName(),
                    propertyRecord.getRawFieldId(),
                    propertyRecord.getPrefType(),
                    propertyRecord.getHasQuality(),
                    propertyRecord.getDataType());
        }

        log.info(
                String.format(
                        "Build BOMultiFieldMapping total time %d ms, assets=%d, points=%d",
                        System.currentTimeMillis() - start, assetIds.size(), prefNames.size()));
        return fieldMapping;
    }

    private Map<String, String> getIdMapping(String orgId, Set<String> assetIds) {
        String assetIdExpr = TSDBLoaderUtils.flattenElements(assetIds);
        String sql =
                String.format(
                        "SELECT asset_id, system_id FROM %s.tbl_bo WHERE asset_id IN (%s)",
                        orgId, assetIdExpr);
        FieldsQueryCursor<List<?>> cursor =
                ignite.cache(cacheName).query(new SqlFieldsQuery(sql).setSchema(orgId));
        List<List<?>> data = cursor.getAll();
        Map<String, String> idMapping = new HashMap<>(assetIds.size());
        for (List<?> item : data) {
            idMapping.put(item.get(0).toString(), item.get(1).toString());
        }

        return idMapping;
    }

    // assetId => (systemId, timeZone)
    private Map<String, Pair<String, String>> getIdTimezoneMapping(
            String orgId, Set<String> assetIds, BOMultiFieldMapping fieldMapping) {
        String timezoneColumn = "'" + DEFAULT_TIMEZONE + "'";
        if (fieldMapping.contains(ASSET_TIMEZONE_COLUMN)) {
            Set<String> fieldIds = fieldMapping.getFieldIds(ASSET_TIMEZONE_COLUMN);
            if (fieldIds != null && fieldIds.size() == 1) {
                timezoneColumn = "obj.\"" + fieldIds.stream().iterator().next() + "\"";
            } else if (fieldIds != null && fieldIds.size() > 1) {
                StringBuilder tzBuilder = new StringBuilder();
                tzBuilder.append("COALESCE(");
                for (String id : fieldIds) {
                    tzBuilder.append("obj.\"");
                    tzBuilder.append(id);
                    tzBuilder.append("\",");
                }
                tzBuilder.append("'" + DEFAULT_TIMEZONE + "')");
                timezoneColumn = tzBuilder.toString();
            }
        }
        String assetIdExpr = TSDBLoaderUtils.flattenElements(assetIds);
        String sql =
                String.format(
                        "SELECT\n"
                                + "    bo.asset_id,\n"
                                + "    bo.system_id,\n"
                                + "    %3$s\n"
                                + "FROM\n"
                                + "    (\n"
                                + "        SELECT\n"
                                + "            asset_id,\n"
                                + "            system_id,\n"
                                + "            asset_display_name\n"
                                + "        FROM\n"
                                + "            %1$s.tbl_bo\n"
                                + "        WHERE\n"
                                + "            asset_id IN (%2$s)\n"
                                + "    ) AS bo\n"
                                + "    JOIN %1$s.tbl_obj AS obj ON bo.system_id = obj.system_id",
                        orgId, assetIdExpr, timezoneColumn);
        FieldsQueryCursor<List<?>> cursor =
                ignite.cache(cacheName).query(new SqlFieldsQuery(sql).setSchema(orgId));
        List<List<?>> data = cursor.getAll();
        Map<String, Pair<String, String>> idTimezoneMapping = new HashMap<>(assetIds.size());
        for (List<?> item : data) {
            idTimezoneMapping.put(
                    item.get(0).toString(),
                    Pair.of(item.get(1).toString(), item.get(2).toString()));
        }
        return idTimezoneMapping;
    }

    private String fieldMappingSQL(String orgId, String assetIdExpr, String prefNameExpr) {
        return String.format(
                "SELECT /*+ ENFORCE_JOIN_ORDER */ \n"
                        + "    CASE\n"
                        + "        WHEN tbl_component.anonymous THEN tbl_pref.pref_name\n"
                        + "        ELSE CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name)\n"
                        + "    END,\n"
                        + "    tbl_component_pref.raw_field_id,\n"
                        + "    tbl_pref.pref_type,\n"
                        + "    tbl_pref.has_quality,\n"
                        + "    tbl_pref.pref_data_type, \n"
                        + "    model.asset_id\n"
                        + "FROM\n"
                        + "    (\n"
                        + "        SELECT\n"
                        + "            DISTINCT tbl_bo_model.model_id AS model_id, tbl_bo_group_relation.asset_id AS asset_id\n"
                        + "        FROM\n"
                        + "            %s.tbl_bo_model\n"
                        + "            JOIN %s.tbl_bo_group_relation ON tbl_bo_model.group_id = tbl_bo_group_relation.group_id\n"
                        + "        WHERE\n"
                        + "            tbl_bo_group_relation.asset_id IN (%s)\n"
                        + "    ) AS model\n"
                        + "    JOIN %s.tbl_bo_model_comp ON model.model_id = tbl_bo_model_comp.model_id\n"
                        + "    JOIN %s.tbl_component ON tbl_component.comp_id = tbl_bo_model_comp.comp_id\n"
                        + "    JOIN %s.tbl_component_pref ON tbl_component_pref.comp_id = tbl_component.comp_id\n"
                        + "    JOIN %s.tbl_pref ON tbl_pref.pref_id = tbl_component_pref.pref_id\n"
                        + "WHERE\n"
                        + "    (\n"
                        + "        tbl_component.anonymous = false\n"
                        + "        AND CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name) IN (%s)\n"
                        + "    )\n"
                        + "    OR (\n"
                        + "        tbl_component.anonymous = true\n"
                        + "        AND tbl_pref.pref_name IN (%s)\n"
                        + "    );",
                orgId, orgId, assetIdExpr, orgId, orgId, orgId, orgId, prefNameExpr, prefNameExpr);
    }

    private void initTSDBApi() {
        try {
            if (this.tsdbApi == null) {
                this.tsdbApi = TSDBApiFactory.create(initConfig());
            }
        } catch (Exception e) {
            log.error("Connected TSDB failed: ", e);
        }
    }

    private Configuration initConfig() {
        Configuration config = new Configuration();
        config.set(SDKOptions.INSTANCE, "TSDBLoader");
        config.set(SDKOptions.CONNECTION_URL, this.connectionURL);
        config.set(SDKOptions.AUTH_METHOD, AuthMethod.SIMPLE);
        config.set(SDKOptions.AUTH_INFO_USERNAME, this.userName);
        config.set(SDKOptions.AUTH_INFO_PASSWORD, this.password);
        if (this.connectionURL.contains("cluster")) {
            config.set(SDKOptions.CLUSTER_META_ADDRESS, this.clusterMetaURL);
            config.set(SDKOptions.CLUSTER_META_USERNAME, this.clusterMetaUserName);
            config.set(SDKOptions.CLUSTER_META_PASSWORD, this.clusterMetaPassword);
            config.set(
                    SDKOptions.CLUSTER_SHARDING_RULE,
                    ShardingRule.valueOf(this.clusterShardingRule));
            config.set(SDKOptions.CLUSTER_REPLICA_FACTOR, this.clusterReplicaFactor);
        }
        config.set(SDKOptions.QUERY_PARALLELISM, this.putCacheParallelism);

        return config;
    }

    private void resetTsdbApi() {
        try {
            if (this.tsdbApi != null) {
                this.tsdbApi.close();
            }
        } catch (Exception e) {
            log.warning("Reconnect TSDB failed: ", e);
        } finally {
            this.tsdbApi = null;
            this.tsdbApi = TSDBApiFactory.create(initConfig());
        }
    }

    @Override
    public BinaryObject load(BinaryObject binaryObject) throws CacheLoaderException {
        return null;
    }

    @Override
    public void write(Cache.Entry<? extends BinaryObject, ? extends BinaryObject> entry)
            throws CacheWriterException {}

    @Override
    public void delete(Object o) throws CacheWriterException {}
}
