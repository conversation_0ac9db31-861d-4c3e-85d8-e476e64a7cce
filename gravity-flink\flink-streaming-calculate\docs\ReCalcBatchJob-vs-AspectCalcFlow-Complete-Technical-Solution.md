# 📋 **ReCalcBatchJob vs AspectCalcFlow 完整技术方案对比**

## 🎯 **1. 核心差异总结**

### 1.1 场景对比表

| 维度 | ReCalcBatchJob (重跑批处理) | AspectCalcFlow (定时切面计算) |
|------|---------------------------|----------------------------|
| **计算场景** | 重新计算历史时间范围数据 | 定时切面计算最新数据 |
| **数据查询** | 查询时间范围内的历史值 | 查询当前最新值 |
| **时间拆分** | 需要按时间段拆分处理 | 不需要时间拆分 |
| **数据输出** | 每个子时间段完成后立即写入Kafka | 整个Task完成后通过KafkaSink输出 |
| **执行模式** | 批处理，单Job处理完成后退出 | 流式处理，持续运行处理多Job |
| **生命周期** | 有限生命周期 | 无限生命周期 |

### 1.2 架构对比图

```mermaid
graph TD
    subgraph "ReCalcBatchJob 批处理架构"
        A1[单个TblCalcJobInfo] --> A2[CalcJobTaskSource<br/>并行度=1]
        A2 --> A3[ReCalcJobTaskProcessor<br/>并行度=N]
        A3 --> A4[时间范围拆分<br/>subTaskStartTime-subTaskEndTime]
        A4 --> A5[查询历史范围数据<br/>queryValues方法]
        A5 --> A6[每个子时间段<br/>立即写入Kafka]
        A6 --> A7[Job完成后退出]
    end
    
    subgraph "AspectCalcFlow 流式架构"
        B1[多个TblCalcJobInfo<br/>定时获取] --> B2[CalcJobTaskSource<br/>并行度=N<br/>支持多Job]
        B2 --> B3[AspectCalcJobTaskProcessor<br/>并行度=M]
        B3 --> B4[无时间拆分<br/>直接处理整个Task]
        B4 --> B5[查询最新值数据<br/>queryLatestValues方法]
        B5 --> B6[整个Task完成后<br/>通过KafkaSink输出]
        B6 --> B7[持续运行<br/>处理新Job]
    end
```

## 🔧 **2. ReCalcBatchJob 技术方案 (重跑批处理)**

### 2.1 ReCalcBatchJob 主流程

```java
public class ReCalcBatchJob {
    
    private static final Logger logger = LoggerFactory.getLogger(ReCalcBatchJob.class);
    
    public static void main(String[] args) throws Exception {
        if (args.length < 1) {
            throw new IllegalArgumentException("Job ID is required");
        }
        
        String jobId = args[0];
        logger.info("Starting ReCalcBatchJob for jobId: {} (Historical Data Reprocessing)", jobId);
        
        try {
            // 1. 读取单个作业信息
            TblCalcJobInfo jobInfo = readJobInfo(jobId);
            
            // 2. 创建批处理执行环境
            StreamExecutionEnvironment env = createBatchExecutionEnvironment(jobId);
            
            // 3. 构建批处理数据流
            buildBatchDataFlow(env, jobInfo);
            
            // 4. 执行批处理作业（完成后退出）
            String jobName = "ReCalcBatchJob-" + jobId;
            env.execute(jobName);
            
            logger.info("ReCalcBatchJob completed for jobId: {}", jobId);
            
        } catch (Exception e) {
            logger.error("ReCalcBatchJob failed for jobId: {}", jobId, e);
            throw e;
        } finally {
            // 清理批处理相关状态
            TaskCompletionNotifier.cleanup(jobId);
        }
    }
    
    private static void buildBatchDataFlow(StreamExecutionEnvironment env, TblCalcJobInfo jobInfo) {
        String jobId = jobInfo.getJobId();
        
        // ✅ 单Job广播流
        DataStream<TblCalcJobInfo> jobInfoStream = env.fromElements(jobInfo)
            .uid("job-info-source-" + jobId)
            .name("JobInfoSource-" + jobId);
        
        BroadcastStream<TblCalcJobInfo> jobInfoBroadcast = jobInfoStream.broadcast(
            CalcJobInfoManager.createJobInfoDescriptor(jobId));
        
        // ✅ 批处理任务源（单Job处理）
        DataStream<CalcJobTask> taskStream = env.addSource(new CalcJobTaskSource(jobInfo))
            .uid("calc-job-task-source-" + jobId)
            .name("CalcJobTaskSource-" + jobId)
            .setParallelism(1)
            .returns(TypeInformation.of(CalcJobTask.class));
        
        // ✅ 连接广播流和任务流
        BroadcastConnectedStream<CalcJobTask, TblCalcJobInfo> connectedStream = 
            taskStream.connect(jobInfoBroadcast);
        
        // ✅ 批处理任务处理器（历史数据重跑）
        connectedStream.process(new ReCalcJobTaskProcessor(jobId))
            .uid("recalc-job-task-processor-" + jobId)
            .name("ReCalcJobTaskProcessor-" + jobId)
            .setParallelism(CalcLionConfig.getReCalcJobTaskProcessorParallelism());
    }
}
```

### 2.2 ReCalcJobTaskProcessor 核心差异

```java
public class ReCalcJobTaskProcessor extends BroadcastProcessFunction<CalcJobTask, TblCalcJobInfo, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobTaskProcessor.class);

    private final String jobId;
    private final MapStateDescriptor<String, TblCalcJobInfo> jobInfoDescriptor;

    // ✅ 批处理专用：直接写入Kafka的Producer
    private transient FlinkKafkaProducer<LegacyMsgWithMultiAssets> kafkaProducer;
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient TblCalcJobInfoMapper jobInfoMapper;

    @Override
    public void processElement(CalcJobTask calcJobTask, ReadOnlyContext ctx, Collector<Void> out)
            throws Exception {

        String taskId = calcJobTask.getTaskId();
        String currentJobId = calcJobTask.getJobId();

        logger.info("Processing historical data reprocessing task: {} for job: {}", taskId, currentJobId);

        try {
            // ✅ 关键检查1：Job状态检查（CANCELLED检查）
            if (!checkJobStatusNotCancelled(currentJobId)) {
                logger.warn("Job {} is CANCELLED, terminating ReCalcBatchJob", currentJobId);
                // ✅ 批处理任务终止退出
                throw new JobCancelledException("Job " + currentJobId + " has been cancelled, terminating batch job");
            }

            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(currentJobId, taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, currentJobId);
                return;
            }

            // 获取作业信息
            TblCalcJobInfo jobInfo = ctx.getBroadcastState(jobInfoDescriptor).get(currentJobId);
            if (jobInfo == null) {
                logger.error("Job info not found for job: {}", currentJobId);
                return;
            }

            // ✅ 批处理核心：历史数据重跑处理
            executeHistoricalDataReprocessing(jobInfo, calcJobTask);

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(currentJobId, taskId);

            logger.info("Historical reprocessing task {} completed for job {}", taskId, currentJobId);

        } catch (JobCancelledException e) {
            logger.error("Job {} cancelled, stopping ReCalcBatchJob: {}", currentJobId, e.getMessage());
            throw e; // 重新抛出，让作业终止
        } catch (Exception e) {
            logger.error("Historical reprocessing task {} failed for job {}: {}",
                        taskId, currentJobId, e.getMessage(), e);
            taskCompletionNotifier.markTaskFailed(currentJobId, taskId, e.getMessage());
        }
    }

    /**
     * ✅ 关键检查：Job状态检查，防止处理已取消的Job
     */
    private boolean checkJobStatusNotCancelled(String jobId) throws Exception {
        try {
            TblCalcJobInfo currentJobInfo = jobInfoMapper.selectByJobId(jobId);

            if (currentJobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return false;
            }

            int jobStatus = currentJobInfo.getStatus();
            logger.debug("Job {} current status: {}", jobId, jobStatus);

            // ✅ 状态码 3 表示 CANCELLED
            if (jobStatus == 3) {
                logger.warn("Job {} status is CANCELLED (status=3), should terminate", jobId);
                return false;
            }

            return true;

        } catch (Exception e) {
            logger.error("Failed to check job status for job {}: {}", jobId, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * ✅ 批处理核心方法：历史数据重跑处理
     */
    private void executeHistoricalDataReprocessing(TblCalcJobInfo jobInfo, CalcJobTask calcJobTask) 
            throws Exception {
        
        String orgId = jobInfo.getRuleInfo().getOrgId();
        
        // ✅ 关键差异1：需要按时间段拆分处理
        List<TimeRange> timeRanges = splitTaskTimeRange(calcJobTask);
        
        logger.info("Historical reprocessing task {} split into {} time ranges", 
                   calcJobTask.getTaskId(), timeRanges.size());
        
        for (TimeRange timeRange : timeRanges) {
            // ✅ 每个时间段都执行 replace 操作
            replaceHistoricalData(orgId, jobInfo, calcJobTask, 
                                 timeRange.getStartTime(), timeRange.getEndTime());
        }
    }
    
    /**
     * ✅ 关键差异：查询历史时间范围数据
     */
    private CalcResultMsg queryHistoricalRangeData(String orgId, TblCalcJobInfo jobInfo, 
                                                  CalcJobTask calcJobTask, 
                                                  long subTaskStartTime, long subTaskEndTime) 
            throws Exception {
        
        // 1. 构建历史范围查询实体
        Tuple2<Map<String, LatestQueryEntity>, Map<String, TSQueryEntity>> queryEntities = 
            buildHistoricalRangeQueryEntities(jobInfo, calcJobTask, subTaskStartTime, subTaskEndTime);
        
        // 2. ✅ 关键差异：查询历史范围数据
        Map<String, LegacyPayload> allLatestValues = queryAllLatestValues(queryEntities.f0);
        Map<String, List<LegacyPayload>> allTSValues = queryAllHistoricalRangeValues(queryEntities.f1);
        
        // 3. 构建目标资产数据
        Map<String, LegacyPayload> targetAssetValues = 
            buildTargetAssetValuesFromHistorical(calcJobTask, allLatestValues, allTSValues);
        
        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = 
            execTargetPropExprCalc(orgId, jobInfo, calcJobTask, targetAssetValues, modelPathMap);
        
        return CalcResultMsg.builder().targetModel2MsgMap(targetModel2MsgMap).build();
    }
    
    /**
     * ✅ 关键差异：查询历史范围值（使用 queryValues 方法）
     */
    private Map<String, List<LegacyPayload>> queryAllHistoricalRangeValues(
            Map<String, TSQueryEntity> tsQueryEntities) throws Exception {
        
        Map<String, List<LegacyPayload>> allTSValues = new HashMap<>();
        
        for (Map.Entry<String, TSQueryEntity> entry : tsQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            TSQueryEntity queryEntity = entry.getValue();
            
            // ✅ 使用范围查询方法
            Map<String, List<LegacyPayload>> modelTSValues = 
                calculationServiceHelper.queryValues(queryEntity.getOrgId(), modelId, queryEntity);
            
            allTSValues.putAll(modelTSValues);
        }
        
        return allTSValues;
    }
    
    /**
     * ✅ 关键差异：立即写入Kafka
     */
    private void writeToKafkaImmediately(String orgId, TblCalcJobInfo jobInfo, CalcJobTask calcJobTask,
                                        CalcResultMsg calcResults) throws Exception {

        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // ✅ 前置校验1：检查是否应该写入Kafka
            if (!shouldWriteToKafka(jobInfo, message)) {
                continue;
            }

            // ✅ 立即写入Kafka
            ProducerRecord<String, LegacyMsgWithMultiAssets> record = new ProducerRecord<>(
                buildTopicName(message.getModelId()),
                message.getModelId(),
                message
            );

            // ✅ 前置检查2：检查是否为直白映射，添加 direct_mapping header
            if (isDirectMapping(jobInfo)) {
                record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
                logger.debug("Added direct_mapping header for model: {}", message.getModelId());
            }

            kafkaProducer.send(record);

            logger.debug("Immediately sent historical data to Kafka for model: {}",
                        message.getModelId());
        }
    }

    /**
     * ✅ 前置检查：判断是否应该写入Kafka
     */
    private boolean shouldWriteToKafka(TblCalcJobInfo jobInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不写入Kafka）
        if (jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip writing attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /**
     * ✅ 前置检查：判断是否为直白映射
     */
    private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
        try {
            boolean directMapping = jobInfo.getRuleInfo()
                .getTargetPropertyMeta()
                .isDirectMapping();

            logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
            return directMapping;

        } catch (Exception e) {
            logger.warn("Failed to check direct mapping for job {}: {}",
                       jobInfo.getJobId(), e.getMessage());
            return false;
        }
    }

    /**
     * 构建Topic名称
     */
    private String buildTopicName(String modelId) {
        return CalcLionConfig.getReCalcKafkaSinkTopicPattern().replace("{modelId}", modelId);
    }
}

/**
 * ✅ 自定义异常：Job取消异常
 */
public class JobCancelledException extends Exception {
    public JobCancelledException(String message) {
        super(message);
    }

    public JobCancelledException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

## 🔧 **3. AspectCalcFlow 技术方案 (定时切面计算)**

### 3.1 AspectCalcFlow 主流程

```java
public class AspectCalcFlow {
    
    private static final Logger logger = LoggerFactory.getLogger(AspectCalcFlow.class);
    
    public static void main(String[] args) throws Exception {
        logger.info("Starting AspectCalcFlow (Scheduled Aspect Calculation)");
        
        // ✅ 创建流式执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置流式配置
        env.setParallelism(CalcLionConfig.getAspectCalcDefaultParallelism());
        env.enableCheckpointing(CalcLionConfig.getAspectCalcCheckpointIntervalMs());
        
        // ✅ 构建流式数据流
        buildAspectCalcDataFlow(env);
        
        // 执行流式作业（持续运行）
        env.execute("AspectCalcFlow");
    }
    
    private static void buildAspectCalcDataFlow(StreamExecutionEnvironment env) {
        
        // ✅ 1. 定时获取多个Job信息
        DataStream<TblCalcJobInfo> jobInfoStream = env
            .addSource(new GetCalcJobInfoTimer(
                CalcLionConfig.getJobInfoCheckIntervalMs(),
                CalcLionConfig.getJobInfoBatchSize(),
                Arrays.asList(0, 1) // PENDING, RUNNING
            ))
            .name("GetCalcJobInfoTimer")
            .uid("get-calc-job-info-timer")
            .setParallelism(1);
        
        // ✅ 2. 流式任务源（支持多Job并行处理）
        Source<CalcJobTask, CalcJobSplit, CalcJobEnumState> calcJobSource = 
            CalcJobSource.builder()
                .setTaskCompletionCheckInterval(CalcLionConfig.getTaskCompletionCheckIntervalMs())
                .setMaxJobsPerReader(CalcLionConfig.getMaxJobsPerReader())
                .setTaskBatchSize(CalcLionConfig.getTaskBatchSize())
                .build();
        
        DataStream<CalcJobTask> taskStream = env
            .fromSource(calcJobSource, WatermarkStrategy.noWatermarks(), "CalcJobTaskSource")
            .name("CalcJobTaskSource")
            .uid("calc-job-task-source")
            .setParallelism(CalcLionConfig.getAspectCalcJobTaskSourceParallelism());
        
        // ✅ 3. 切面计算处理器（输出计算结果）
        DataStream<LegacyMsgWithMultiAssets> calcResultStream = taskStream
            .keyBy(task -> task.getJobId())
            .process(new AspectCalcJobTaskProcessor())
            .name("AspectCalcJobTaskProcessor")
            .uid("aspect-calc-job-task-processor")
            .setParallelism(CalcLionConfig.getAspectCalcJobTaskProcessorParallelism());
        
        // ✅ 4. 关键差异：使用KafkaSink输出（而不是在Processor中直接写入）
        KafkaSink<LegacyMsgWithMultiAssets> kafkaSink = KafkaSink.<LegacyMsgWithMultiAssets>builder()
            .setBootstrapServers(CalcLionConfig.getAspectCalcKafkaBootstrapServers())
            .setRecordSerializer(new AspectCalcKafkaRecordSerializer())
            .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
            .build();
        
        calcResultStream
            .sinkTo(kafkaSink)
            .name("AspectCalcKafkaSink")
            .uid("aspect-calc-kafka-sink")
            .setParallelism(CalcLionConfig.getAspectCalcKafkaSinkParallelism());
    }
}
```

### 3.2 AspectCalcJobTaskProcessor 核心差异

```java
public class AspectCalcJobTaskProcessor extends KeyedProcessFunction<String, CalcJobTask, LegacyMsgWithMultiAssets> {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcJobTaskProcessor.class);

    // ✅ 流式处理专用：输出到下游KafkaSink
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient CalculationServiceHelper calculationServiceHelper;
    private transient ModelMetaQueryHandler modelMetaQueryHandler;

    // ✅ 状态管理
    private transient MapState<String, TblCalcJobInfo> jobInfoState;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.taskCompletionNotifier = TaskCompletionNotifier.getGlobalInstance();
        this.calculationServiceHelper = new CalculationServiceHelper();
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();

        // 初始化状态
        jobInfoState = getRuntimeContext().getMapState(
            new MapStateDescriptor<>("job-info-state", String.class, TblCalcJobInfo.class));

        logger.info("AspectCalcJobTaskProcessor opened for aspect calculation");
    }

    @Override
    public void processElement(CalcJobTask calcJobTask, Context ctx,
                              Collector<LegacyMsgWithMultiAssets> out) throws Exception {

        String taskId = calcJobTask.getTaskId();
        String jobId = calcJobTask.getJobId();

        logger.info("Processing aspect calculation task: {} for job: {}", taskId, jobId);

        try {
            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(jobId, taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, jobId);
                return;
            }

            // 获取作业信息
            TblCalcJobInfo jobInfo = getJobInfo(jobId);
            if (jobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return;
            }

            // ✅ 切面计算核心：最新值计算处理
            List<LegacyMsgWithMultiAssets> calcResults = executeAspectCalculation(jobInfo, calcJobTask);

            // ✅ 关键差异：整个Task完成后才输出到下游KafkaSink
            for (LegacyMsgWithMultiAssets result : calcResults) {
                out.collect(result);
            }

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(jobId, taskId);

            logger.info("Aspect calculation task {} completed for job {}, output {} results",
                       taskId, jobId, calcResults.size());

        } catch (Exception e) {
            logger.error("Aspect calculation task {} failed for job {}: {}",
                        taskId, jobId, e.getMessage(), e);
            taskCompletionNotifier.markTaskFailed(jobId, taskId, e.getMessage());
        }
    }

    /**
     * ✅ 切面计算核心方法：最新值计算处理
     */
    private List<LegacyMsgWithMultiAssets> executeAspectCalculation(TblCalcJobInfo jobInfo,
                                                                   CalcJobTask calcJobTask)
            throws Exception {

        String orgId = jobInfo.getRuleInfo().getOrgId();

        // ✅ 关键差异1：不需要时间拆分，直接处理整个Task
        logger.info("Aspect calculation task {} processing without time splitting",
                   calcJobTask.getTaskId());

        // ✅ 直接查询最新值并计算
        CalcResultMsg calcResults = queryLatestDataAndCalculate(orgId, jobInfo, calcJobTask);

        // 转换为输出格式
        return convertToOutputMessages(calcResults, jobInfo);
    }

    /**
     * ✅ 关键差异：查询最新值数据并计算
     */
    private CalcResultMsg queryLatestDataAndCalculate(String orgId, TblCalcJobInfo jobInfo,
                                                     CalcJobTask calcJobTask) throws Exception {

        // 1. 构建最新值查询实体
        Map<String, LatestQueryEntity> latestQueryEntities =
            buildLatestValueQueryEntities(jobInfo, calcJobTask);

        // 2. ✅ 关键差异：统一使用最新值查询方法
        Map<String, LegacyPayload> allLatestValues = queryAllLatestValuesOnly(latestQueryEntities);

        // 3. 构建目标资产数据（基于最新值）
        Map<String, LegacyPayload> targetAssetValues =
            buildTargetAssetValuesFromLatest(calcJobTask, allLatestValues);

        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
            execTargetPropExprCalc(orgId, jobInfo, calcJobTask, targetAssetValues, modelPathMap);

        return CalcResultMsg.builder()
            .targetModel2MsgMap(targetModel2MsgMap)
            .build();
    }

    /**
     * ✅ 关键差异：只构建最新值查询实体
     */
    private Map<String, LatestQueryEntity> buildLatestValueQueryEntities(TblCalcJobInfo jobInfo,
                                                                        CalcJobTask calcJobTask) {

        List<SrcPrefItem> srcPrefItems = jobInfo.getRuleInfo().getTargetPropertyMeta().getSrcPrefItems();
        Map<String, LatestQueryEntity> latestQueryEntities = new HashMap<>();

        RecCalcMetaInfo calcMetaInfo = jobInfo.getRuleInfo();

        // ✅ 所有依赖项都使用最新值查询
        for (SrcPrefItem srcPrefItem : srcPrefItems) {
            String srcModelId = srcPrefItem.getModelId();
            LatestQueryEntity queryEntity = latestQueryEntities.computeIfAbsent(srcModelId, k ->
                LatestQueryEntity.builder()
                    .orgId(calcMetaInfo.getOrgId())
                    .modelId(srcModelId)
                    .build());

            queryEntity.getPropertyNames().add(srcPrefItem.getPrefName());

            List<String> targetAssetIds = calcJobTask.getTargetAssetIds();
            if (calcMetaInfo.getTargetModelIds().contains(srcModelId)) {
                queryEntity.getAssetIds().addAll(targetAssetIds);
            } else {
                // 通过 AssetInfo 获取依赖资产
                List<String> dependentAssetIds = getDependentAssetIds(targetAssetIds, srcModelId);
                queryEntity.getAssetIds().addAll(dependentAssetIds);
            }
        }

        return latestQueryEntities;
    }

    /**
     * ✅ 关键差异：统一使用最新值查询方法
     */
    private Map<String, LegacyPayload> queryAllLatestValuesOnly(
            Map<String, LatestQueryEntity> latestQueryEntities) throws Exception {

        Map<String, LegacyPayload> allLatestValues = new HashMap<>();

        for (Map.Entry<String, LatestQueryEntity> entry : latestQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            LatestQueryEntity queryEntity = entry.getValue();

            // ✅ 关键差异：统一使用最新值查询方法
            Map<String, LegacyPayload> modelLatestValues =
                calculationServiceHelper.queryLatestValues(
                    queryEntity.getOrgId(), modelId, queryEntity);

            allLatestValues.putAll(modelLatestValues);

            logger.debug("Queried latest values for model {}: {} assets",
                        modelId, modelLatestValues.size());
        }

        return allLatestValues;
    }

    /**
     * ✅ 转换为输出消息格式
     */
    private List<LegacyMsgWithMultiAssets> convertToOutputMessages(CalcResultMsg calcResults,
                                                                  TblCalcJobInfo jobInfo) {

        List<LegacyMsgWithMultiAssets> outputMessages = new ArrayList<>();
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // 前置校验
            if (shouldOutputToKafka(jobInfo, message)) {
                // 添加必要的header信息
                enrichMessageHeaders(message, jobInfo);
                outputMessages.add(message);
            }
        }

        return outputMessages;
    }

    /**
     * ✅ 前置检查：判断是否应该输出到Kafka
     */
    private boolean shouldOutputToKafka(TblCalcJobInfo jobInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不输出到Kafka）
        if (jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip outputting attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /**
     * ✅ 丰富消息头信息，包含直白映射检查
     */
    private void enrichMessageHeaders(LegacyMsgWithMultiAssets message, TblCalcJobInfo jobInfo) {
        // 添加切面计算相关的header信息
        message.getHeaders().put("calc_type", "aspect");
        message.getHeaders().put("job_id", jobInfo.getJobId());
        message.getHeaders().put("calc_time", String.valueOf(System.currentTimeMillis()));

        // ✅ 前置检查：判断是否为直白映射，添加 direct_mapping header
        if (isDirectMapping(jobInfo)) {
            message.getHeaders().put("direct_mapping", "1");
            logger.debug("Added direct_mapping header for aspect calc model: {}", message.getModelId());
        }
    }

    /**
     * ✅ 前置检查：判断是否为直白映射（与ReCalcJobTaskProcessor相同逻辑）
     */
    private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
        try {
            boolean directMapping = jobInfo.getRuleInfo()
                .getTargetPropertyMeta()
                .isDirectMapping();

            logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
            return directMapping;

        } catch (Exception e) {
            logger.warn("Failed to check direct mapping for job {}: {}",
                       jobInfo.getJobId(), e.getMessage());
            return false;
        }
    }
}
```

### 3.3 AspectCalcKafkaRecordSerializer

```java
public class AspectCalcKafkaRecordSerializer implements KafkaRecordSerializationSchema<LegacyMsgWithMultiAssets> {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcKafkaRecordSerializer.class);

    @Override
    public ProducerRecord<byte[], byte[]> serialize(LegacyMsgWithMultiAssets message,
                                                   KafkaSinkContext context,
                                                   Long timestamp) {

        try {
            // ✅ 构建Topic名称
            String topicName = buildAspectCalcTopicName(message.getModelId());

            // ✅ 序列化消息
            byte[] key = message.getModelId().getBytes(StandardCharsets.UTF_8);
            byte[] value = serializeMessage(message);

            // ✅ 创建ProducerRecord
            ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(topicName, key, value);

            // ✅ 添加Headers（包含direct_mapping等所有header）
            for (Map.Entry<String, String> header : message.getHeaders().entrySet()) {
                record.headers().add(header.getKey(), header.getValue().getBytes(StandardCharsets.UTF_8));

                // ✅ 特别记录direct_mapping header
                if ("direct_mapping".equals(header.getKey())) {
                    logger.debug("Added direct_mapping header: {} for model: {}",
                               header.getValue(), message.getModelId());
                }
            }

            logger.debug("Serialized aspect calc message for model: {} to topic: {}, headers: {}",
                        message.getModelId(), topicName, message.getHeaders().keySet());

            return record;

        } catch (Exception e) {
            logger.error("Failed to serialize aspect calc message for model {}: {}",
                        message.getModelId(), e.getMessage(), e);
            throw new RuntimeException("Serialization failed", e);
        }
    }

    private String buildAspectCalcTopicName(String modelId) {
        return CalcLionConfig.getAspectCalcKafkaSinkTopicPattern().replace("{modelId}", modelId);
    }

    private byte[] serializeMessage(LegacyMsgWithMultiAssets message) throws Exception {
        // 使用现有的序列化逻辑
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsBytes(message);
    }
}
```

## 🔧 **4. 配置对比**

### 4.1 ReCalcBatchJob 配置

```properties
# ReCalc 批处理配置
gravity-flink.recalc.checkpoint-dir=hdfs://namenode:9000/flink/checkpoints/recalc
gravity-flink.recalc.checkpoint-interval-ms=60000
gravity-flink.recalc.checkpoint-max-concurrent=1
gravity-flink.recalc.checkpoint-timeout-ms=600000

# ReCalc 任务处理配置
gravity-flink.recalc.job-task-processor.parallelism=4
gravity-flink.recalc.task-completion-check-interval-ms=1000
gravity-flink.recalc.time-range-split-seconds=3600

# ReCalc Kafka 配置（直接写入）
gravity-flink.recalc.kafka.bootstrap-servers=localhost:9092
gravity-flink.recalc.kafka.sink.topic-pattern=MEASURE_POINT_CAL_{modelId}
```

### 4.2 AspectCalcFlow 配置

```properties
# AspectCalc 流式计算配置
gravity-flink.aspect-calc.default-parallelism=4
gravity-flink.aspect-calc.checkpoint-interval-ms=60000
gravity-flink.aspect-calc.checkpoint-dir=hdfs://namenode:9000/flink/checkpoints/aspect-calc

# AspectCalc 算子并行度配置
gravity-flink.aspect-calc.calc-job-task-source.parallelism=4
gravity-flink.aspect-calc.job-task-processor.parallelism=8
gravity-flink.aspect-calc.kafka-sink.parallelism=4

# AspectCalc Job 信息获取配置
gravity-flink.aspect-calc.job-info-check-interval-ms=30000
gravity-flink.aspect-calc.job-info-batch-size=10

# AspectCalc 任务处理配置
gravity-flink.aspect-calc.task-completion-check-interval-ms=1000
gravity-flink.aspect-calc.max-jobs-per-reader=5
gravity-flink.aspect-calc.task-batch-size=10

# AspectCalc Kafka 配置（KafkaSink）
gravity-flink.aspect-calc.kafka.bootstrap-servers=localhost:9092
gravity-flink.aspect-calc.kafka.sink.topic-pattern=MEASURE_POINT_CAL_{modelId}
```

## 🎯 **5. 核心差异总结**

### 5.1 数据查询差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **查询方法** | `queryValues(orgId, modelId, queryEntity)` | `queryLatestValues(orgId, modelId, latestQueryEntity)` |
| **查询类型** | 时间范围查询 | 最新值查询 |
| **返回类型** | `Map<String, List<LegacyPayload>>` | `Map<String, LegacyPayload>` |
| **时间处理** | 需要处理时间序列数据 | 只处理当前最新值 |

### 5.2 前置检查机制

| 检查类型 | ReCalcBatchJob | AspectCalcFlow | 说明 |
|---------|---------------|----------------|------|
| **Job状态检查** | ✅ 检查status=3(CANCELLED)，终止批处理 | ✅ 检查Job状态，跳过已取消Job | 防止处理已取消的Job |
| **属性过滤检查** | ✅ 属性不写入Kafka | ✅ 属性不输出到Kafka | 属性数据不发送到Kafka |
| **直白映射检查** | ✅ isDirectMapping=true时添加header | ✅ isDirectMapping=true时添加header | 添加direct_mapping=1 header |
| **幂等性检查** | ✅ 检查任务是否已完成 | ✅ 检查任务是否已完成 | 避免重复处理 |

### 5.3 Job状态检查详细逻辑

```java
/**
 * 共同的Job状态检查逻辑
 */
private boolean checkJobStatusNotCancelled(String jobId) throws Exception {
    TblCalcJobInfo currentJobInfo = jobInfoMapper.selectByJobId(jobId);

    if (currentJobInfo == null) {
        logger.error("Job info not found for job: {}", jobId);
        return false;
    }

    int jobStatus = currentJobInfo.getStatus();

    // ✅ 状态码定义：
    // 0 = PENDING (待处理)
    // 1 = RUNNING (运行中)
    // 2 = PAUSED (暂停)
    // 3 = CANCELLED (已取消) ← 关键检查点
    // 4 = FINISHED (已完成)
    // 5 = FAILED (失败)

    if (jobStatus == 3) {
        logger.warn("Job {} status is CANCELLED (status=3)", jobId);
        return false;
    }

    return true;
}
```

### 5.4 直白映射检查详细逻辑

```java
/**
 * 共同的直白映射检查逻辑
 */
private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
    try {
        // ✅ 检查路径：TblCalcJobInfo -> ruleInfo -> targetPropertyMeta -> isDirectMapping
        boolean directMapping = jobInfo.getRuleInfo()
            .getTargetPropertyMeta()
            .isDirectMapping();

        logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
        return directMapping;

    } catch (Exception e) {
        logger.warn("Failed to check direct mapping for job {}: {}",
                   jobInfo.getJobId(), e.getMessage());
        return false;
    }
}

/**
 * Kafka消息Header添加逻辑
 */
private void addDirectMappingHeaderIfNeeded(ProducerRecord record, TblCalcJobInfo jobInfo) {
    if (isDirectMapping(jobInfo)) {
        // ✅ 添加特殊header：key="direct_mapping", value="1"
        record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
        logger.debug("Added direct_mapping header for job: {}", jobInfo.getJobId());
    }
}
```

### 5.5 任务处理差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **时间拆分** | 需要按时间段拆分 | 不需要时间拆分 |
| **处理粒度** | 子时间段级别 | 整个Task级别 |
| **输出时机** | 每个子时间段完成后 | 整个Task完成后 |
| **输出方式** | 直接写入Kafka | 通过KafkaSink输出 |
| **Job取消处理** | 抛出异常，终止批处理 | 跳过任务，继续处理其他Job |

### 5.6 架构差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **执行模式** | 批处理，有限生命周期 | 流式处理，无限生命周期 |
| **Job管理** | 单Job处理 | 多Job并发处理 |
| **状态隔离** | Job级别隔离 | Reader级别多Job管理 |
| **扩展性** | 垂直扩展 | 水平扩展 |

### 5.7 使用场景

| 场景 | 推荐方案 | 原因 |
|------|---------|------|
| **历史数据重跑** | ReCalcBatchJob | 需要处理特定时间范围的历史数据 |
| **定时切面计算** | AspectCalcFlow | 需要基于最新值进行实时计算 |
| **一次性计算任务** | ReCalcBatchJob | 任务完成后可以退出，节省资源 |
| **持续监控计算** | AspectCalcFlow | 需要持续运行，处理新的计算需求 |
| **Job取消场景** | ReCalcBatchJob | 检测到取消立即终止，避免资源浪费 |
| **多Job并发场景** | AspectCalcFlow | 单个Job取消不影响其他Job处理 |

## 📋 **6. 部署和运维对比**

### 6.1 部署命令对比

```bash
# ReCalcBatchJob 部署
flink run -c com.envision.gravity.flink.streaming.calculate.recalc.ReCalcBatchJob \
  flink-streaming-calculate-1.0.jar \
  job-001

# AspectCalcFlow 部署
flink run -c com.envision.gravity.flink.streaming.calculate.aspect.AspectCalcFlow \
  -p 8 \
  flink-streaming-calculate-1.0.jar
```

### 6.2 监控指标对比

| 指标类型 | ReCalcBatchJob | AspectCalcFlow |
|---------|---------------|----------------|
| **任务进度** | 单Job任务完成率 | 多Job并发处理率 |
| **处理速度** | 历史数据处理速度 | 实时计算处理速度 |
| **资源使用** | 临时资源占用 | 持续资源占用 |
| **错误处理** | 单Job错误影响 | 多Job错误隔离 |

这个完整的技术方案清晰地展示了 ReCalcBatchJob 和 AspectCalcFlow 的核心差异，为两种不同场景提供了针对性的解决方案。
```
