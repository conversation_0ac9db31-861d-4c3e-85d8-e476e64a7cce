# 📋 **ReCalcBatchJob vs AspectCalcFlow 完整技术方案对比**

## 🎯 **1. 核心差异总结**

### 1.1 场景对比表

| 维度 | ReCalcBatchJob (重跑批处理) | AspectCalcFlow (定时切面计算) |
|------|---------------------------|----------------------------|
| **计算场景** | 重新计算历史时间范围数据 | 定时切面计算最新数据 |
| **数据查询** | 查询时间范围内的历史值 | 查询当前最新值 |
| **时间拆分** | 需要按时间段拆分处理 | 不需要时间拆分 |
| **数据输出** | 每个子时间段完成后立即写入Kafka | 整个Task完成后通过KafkaSink输出 |
| **执行模式** | 批处理，单Job处理完成后退出 | 流式处理，持续运行处理多Job |
| **生命周期** | 有限生命周期 | 无限生命周期 |

### 1.2 架构对比图

```mermaid
graph TD
    subgraph "ReCalcBatchJob 批处理架构"
        A1[单个TblCalcJobInfo] --> A2[CalcJobTaskSource<br/>并行度=1]
        A2 --> A3[ReCalcJobTaskProcessor<br/>并行度=N]
        A3 --> A4[时间范围拆分<br/>subTaskStartTime-subTaskEndTime]
        A4 --> A5[查询历史范围数据<br/>queryValues方法]
        A5 --> A6[每个子时间段<br/>立即写入Kafka]
        A6 --> A7[Job完成后退出]
    end
    
    subgraph "AspectCalcFlow 流式架构"
        B1[多个TblCalcJobInfo<br/>定时获取] --> B2[CalcJobTaskSource<br/>并行度=N<br/>支持多Job]
        B2 --> B3[AspectCalcJobTaskProcessor<br/>并行度=M]
        B3 --> B4[无时间拆分<br/>直接处理整个Task]
        B4 --> B5[查询最新值数据<br/>queryLatestValues方法]
        B5 --> B6[整个Task完成后<br/>通过KafkaSink输出]
        B6 --> B7[持续运行<br/>处理新Job]
    end
```

## 🔧 **2. ReCalcBatchJob 技术方案 (重跑批处理)**

### 2.1 ReCalcBatchJob 主流程

```java
public class ReCalcBatchJob {
    
    private static final Logger logger = LoggerFactory.getLogger(ReCalcBatchJob.class);
    
    public static void main(String[] args) throws Exception {
        if (args.length < 1) {
            throw new IllegalArgumentException("Job ID is required");
        }
        
        String jobId = args[0];
        logger.info("Starting ReCalcBatchJob for jobId: {} (Historical Data Reprocessing)", jobId);
        
        try {
            // 1. 读取单个作业信息
            TblCalcJobInfo jobInfo = readJobInfo(jobId);
            
            // 2. 创建批处理执行环境
            StreamExecutionEnvironment env = createBatchExecutionEnvironment(jobId);
            
            // 3. 构建批处理数据流
            buildBatchDataFlow(env, jobInfo);
            
            // 4. 执行批处理作业（完成后退出）
            String jobName = "ReCalcBatchJob-" + jobId;
            env.execute(jobName);
            
            logger.info("ReCalcBatchJob completed for jobId: {}", jobId);
            
        } catch (Exception e) {
            logger.error("ReCalcBatchJob failed for jobId: {}", jobId, e);
            throw e;
        } finally {
            // 清理批处理相关状态
            TaskCompletionNotifier.cleanup(jobId);
        }
    }
    
    private static void buildBatchDataFlow(StreamExecutionEnvironment env, TblCalcJobInfo jobInfo) {
        String jobId = jobInfo.getJobId();
        
        // ✅ 单Job广播流
        DataStream<TblCalcJobInfo> jobInfoStream = env.fromElements(jobInfo)
            .uid("job-info-source-" + jobId)
            .name("JobInfoSource-" + jobId);
        
        BroadcastStream<TblCalcJobInfo> jobInfoBroadcast = jobInfoStream.broadcast(
            CalcJobInfoManager.createJobInfoDescriptor(jobId));
        
        // ✅ 批处理任务源（单Job处理）
        DataStream<CalcJobTask> taskStream = env.addSource(new CalcJobTaskSource(jobInfo))
            .uid("calc-job-task-source-" + jobId)
            .name("CalcJobTaskSource-" + jobId)
            .setParallelism(1)
            .returns(TypeInformation.of(CalcJobTask.class));
        
        // ✅ 连接广播流和任务流
        BroadcastConnectedStream<CalcJobTask, TblCalcJobInfo> connectedStream = 
            taskStream.connect(jobInfoBroadcast);
        
        // ✅ 批处理任务处理器（历史数据重跑）
        connectedStream.process(new ReCalcJobTaskProcessor(jobId))
            .uid("recalc-job-task-processor-" + jobId)
            .name("ReCalcJobTaskProcessor-" + jobId)
            .setParallelism(CalcLionConfig.getReCalcJobTaskProcessorParallelism());
    }
}
```

### 2.2 ReCalcJobTaskProcessor 核心差异

```java
public class ReCalcJobTaskProcessor extends BroadcastProcessFunction<CalcJobTask, TblCalcJobInfo, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobTaskProcessor.class);

    private final String jobId;
    private final MapStateDescriptor<String, TblCalcJobInfo> jobInfoDescriptor;

    // ✅ 批处理专用：直接写入Kafka的Producer
    private transient FlinkKafkaProducer<LegacyMsgWithMultiAssets> kafkaProducer;
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient TblCalcJobInfoMapper jobInfoMapper;

    @Override
    public void processElement(CalcJobTask calcJobTask, ReadOnlyContext ctx, Collector<Void> out)
            throws Exception {

        String taskId = calcJobTask.getTaskId();
        String currentJobId = calcJobTask.getJobId();

        logger.info("Processing historical data reprocessing task: {} for job: {}", taskId, currentJobId);

        try {
            // ✅ 关键检查1：Job状态检查（CANCELLED检查）
            if (!checkJobStatusNotCancelled(currentJobId)) {
                logger.warn("Job {} is CANCELLED, terminating ReCalcBatchJob", currentJobId);
                // ✅ 批处理任务终止退出
                throw new JobCancelledException("Job " + currentJobId + " has been cancelled, terminating batch job");
            }

            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(currentJobId, taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, currentJobId);
                return;
            }

            // 获取作业信息
            TblCalcJobInfo jobInfo = ctx.getBroadcastState(jobInfoDescriptor).get(currentJobId);
            if (jobInfo == null) {
                logger.error("Job info not found for job: {}", currentJobId);
                return;
            }

            // ✅ 批处理核心：历史数据重跑处理
            executeHistoricalDataReprocessing(jobInfo, calcJobTask);

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(currentJobId, taskId);

            logger.info("Historical reprocessing task {} completed for job {}", taskId, currentJobId);

        } catch (JobCancelledException e) {
            logger.error("Job {} cancelled, stopping ReCalcBatchJob: {}", currentJobId, e.getMessage());
            throw e; // 重新抛出，让作业终止
        } catch (Exception e) {
            logger.error("Historical reprocessing task {} failed for job {}: {}",
                        taskId, currentJobId, e.getMessage(), e);
            taskCompletionNotifier.markTaskFailed(currentJobId, taskId, e.getMessage());
        }
    }

    /**
     * ✅ 关键检查：Job状态检查，防止处理已取消的Job
     */
    private boolean checkJobStatusNotCancelled(String jobId) throws Exception {
        try {
            TblCalcJobInfo currentJobInfo = jobInfoMapper.selectByJobId(jobId);

            if (currentJobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return false;
            }

            int jobStatus = currentJobInfo.getStatus();
            logger.debug("Job {} current status: {}", jobId, jobStatus);

            // ✅ 状态码 3 表示 CANCELLED
            if (jobStatus == 3) {
                logger.warn("Job {} status is CANCELLED (status=3), should terminate", jobId);
                return false;
            }

            return true;

        } catch (Exception e) {
            logger.error("Failed to check job status for job {}: {}", jobId, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * ✅ 批处理核心方法：历史数据重跑处理
     */
    private void executeHistoricalDataReprocessing(TblCalcJobInfo jobInfo, CalcJobTask calcJobTask) 
            throws Exception {
        
        String orgId = jobInfo.getRuleInfo().getOrgId();
        
        // ✅ 关键差异1：需要按时间段拆分处理
        List<TimeRange> timeRanges = splitTaskTimeRange(calcJobTask);
        
        logger.info("Historical reprocessing task {} split into {} time ranges", 
                   calcJobTask.getTaskId(), timeRanges.size());
        
        for (TimeRange timeRange : timeRanges) {
            // ✅ 每个时间段都执行 replace 操作
            replaceHistoricalData(orgId, jobInfo, calcJobTask, 
                                 timeRange.getStartTime(), timeRange.getEndTime());
        }
    }
    
    /**
     * ✅ 关键差异：查询历史时间范围数据
     */
    private CalcResultMsg queryHistoricalRangeData(String orgId, TblCalcJobInfo jobInfo, 
                                                  CalcJobTask calcJobTask, 
                                                  long subTaskStartTime, long subTaskEndTime) 
            throws Exception {
        
        // 1. 构建历史范围查询实体
        Tuple2<Map<String, LatestQueryEntity>, Map<String, TSQueryEntity>> queryEntities = 
            buildHistoricalRangeQueryEntities(jobInfo, calcJobTask, subTaskStartTime, subTaskEndTime);
        
        // 2. ✅ 关键差异：查询历史范围数据
        Map<String, LegacyPayload> allLatestValues = queryAllLatestValues(queryEntities.f0);
        Map<String, List<LegacyPayload>> allTSValues = queryAllHistoricalRangeValues(queryEntities.f1);
        
        // 3. 构建目标资产数据
        Map<String, LegacyPayload> targetAssetValues = 
            buildTargetAssetValuesFromHistorical(calcJobTask, allLatestValues, allTSValues);
        
        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = 
            execTargetPropExprCalc(orgId, jobInfo, calcJobTask, targetAssetValues, modelPathMap);
        
        return CalcResultMsg.builder().targetModel2MsgMap(targetModel2MsgMap).build();
    }
    
    /**
     * ✅ 关键差异：查询历史范围值（使用 queryTSValues 方法）
     */
    private Map<String, List<LegacyPayload>> queryAllHistoricalRangeValues(
            Map<String, TSQueryEntity> tsQueryEntities) throws Exception {

        Map<String, List<LegacyPayload>> allTSValues = new HashMap<>();

        for (Map.Entry<String, TSQueryEntity> entry : tsQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            TSQueryEntity queryEntity = entry.getValue();

            // ✅ 使用时间序列范围查询方法
            Map<String, List<LegacyPayload>> modelTSValues =
                calculationServiceHelper.queryTSValues(queryEntity.getOrgId(), modelId, queryEntity);

            allTSValues.putAll(modelTSValues);
        }

        return allTSValues;
    }
    
    /**
     * ✅ 关键差异：立即写入Kafka
     */
    private void writeToKafkaImmediately(String orgId, TblCalcJobInfo jobInfo, CalcJobTask calcJobTask,
                                        CalcResultMsg calcResults) throws Exception {

        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // ✅ 前置校验1：检查是否应该写入Kafka
            if (!shouldWriteToKafka(jobInfo, message)) {
                continue;
            }

            // ✅ 立即写入Kafka
            ProducerRecord<String, LegacyMsgWithMultiAssets> record = new ProducerRecord<>(
                buildTopicName(orgId),
                message.getModelId(),
                message
            );

            // ✅ 前置检查2：检查是否为直白映射，添加 direct_mapping header
            if (isDirectMapping(jobInfo)) {
                record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
                logger.debug("Added direct_mapping header for model: {}", message.getModelId());
            }

            kafkaProducer.send(record);

            logger.debug("Immediately sent historical data to Kafka for model: {}",
                        message.getModelId());
        }
    }

    /**
     * ✅ 前置检查：判断是否应该写入Kafka
     */
    private boolean shouldWriteToKafka(TblCalcJobInfo jobInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不写入Kafka）
        if (jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip writing attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /**
     * ✅ 前置检查：判断是否为直白映射
     */
    private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
        try {
            boolean directMapping = jobInfo.getRuleInfo()
                .getTargetPropertyMeta()
                .isDirectMapping();

            logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
            return directMapping;

        } catch (Exception e) {
            logger.warn("Failed to check direct mapping for job {}: {}",
                       jobInfo.getJobId(), e.getMessage());
            return false;
        }
    }

    /**
     * 构建Topic名称
     */
    private String buildTopicName(String orgId) {
        return CalcLionConfig.getReCalcKafkaSinkTopicPattern() + orgId;
    }
}

/**
 * ✅ 自定义异常：Job取消异常
 */
public class JobCancelledException extends Exception {
    public JobCancelledException(String message) {
        super(message);
    }

    public JobCancelledException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

### 2.3 CalcJobTaskSource (批处理版本)

```java
public class CalcJobTaskSource extends RichSourceFunction<CalcJobTask>
    implements CheckpointedFunction {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSource.class);

    private final TblCalcJobInfo jobInfo;
    private final String jobId;
    private volatile boolean isRunning = true;

    // ✅ 批处理：单Job任务状态管理
    private transient Queue<CalcJobTask> pendingTasks;
    private transient Set<String> emittedTaskIds; // 已发送的任务ID
    private transient int totalTaskCount;
    private transient int emittedTaskCount;
    private transient boolean allTasksEmitted;

    // ✅ 内存状态通知机制
    private transient TaskCompletionNotifier taskCompletionNotifier;

    // ✅ Checkpoint 状态：保存所有未确认完成的任务
    private transient ListState<CalcJobTask> checkpointedPendingTasks;
    private transient ListState<String> checkpointedEmittedTaskIds;
    private transient ValueState<Integer> checkpointedTotalCount;
    private transient ValueState<Integer> checkpointedEmittedCount;
    private transient ValueState<Boolean> checkpointedAllTasksEmitted;

    public CalcJobTaskSource(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
        this.jobId = jobInfo.getJobId();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.pendingTasks = new LinkedList<>();
        this.emittedTaskIds = new HashSet<>();
        this.totalTaskCount = 0;
        this.emittedTaskCount = 0;
        this.allTasksEmitted = false;

        // ✅ 初始化Job级别的内存状态通知机制
        this.taskCompletionNotifier = TaskCompletionNotifier.getInstance(jobId);

        logger.info("CalcJobTaskSource opened for job: {} with memory-based completion tracking", jobId);
    }

    @Override
    public void run(SourceContext<CalcJobTask> ctx) throws Exception {
        final Object lock = ctx.getCheckpointLock();

        // 初始化任务
        synchronized (lock) {
            if (totalTaskCount == 0) {
                initializeTasks();
            }
        }

        logger.info("Starting task emission for job: {}, total tasks: {}, pending: {}",
                   jobId, totalTaskCount, pendingTasks.size());

        while (isRunning) {
            boolean hasWork = false;

            synchronized (lock) {
                // ✅ 1. 检查并清理已完成的任务
                cleanupCompletedTasks();

                // ✅ 2. 发送待处理的任务
                hasWork = emitPendingTasks(ctx);

                // ✅ 3. 检查是否所有任务都完成
                if (allTasksCompleted()) {
                    logger.info("All tasks completed for job: {}, marking job as FINISHED", jobId);
                    markJobAsFinished();
                    break;
                }
            }

            if (!hasWork) {
                // 没有新任务发送时，短暂休眠
                Thread.sleep(CalcLionConfig.getTaskCompletionCheckIntervalMs());
            }
        }

        logger.info("CalcJobTaskSource finished for job: {}", jobId);
    }

    private void initializeTasks() throws Exception {
        logger.info("Initializing tasks for job: {}", jobId);

        // 生成所有任务
        CalcJobTaskSplitEnumerator enumerator = new CalcJobTaskSplitEnumerator(jobInfo);
        List<CalcJobTask> allTasks = enumerator.generateAllTasks();

        // 添加到待处理队列
        pendingTasks.addAll(allTasks);
        totalTaskCount = allTasks.size();

        logger.info("Initialized {} tasks for job: {}", totalTaskCount, jobId);
    }

    private void cleanupCompletedTasks() {
        // ✅ 从Job级别的内存状态获取已完成的任务
        Set<String> completedTaskIds = taskCompletionNotifier.getCompletedTasks();

        if (!completedTaskIds.isEmpty()) {
            // 从已发送任务集合中移除已完成的任务
            int removedCount = 0;
            Iterator<String> iterator = emittedTaskIds.iterator();
            while (iterator.hasNext()) {
                String taskId = iterator.next();
                if (completedTaskIds.contains(taskId)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.info("Cleaned up {} completed tasks for job {}, remaining emitted: {}",
                           removedCount, jobId, emittedTaskIds.size());
            }
        }
    }

    private boolean emitPendingTasks(SourceContext<CalcJobTask> ctx) {
        boolean emittedAny = false;

        // ✅ 发送所有待处理的任务（支持下游并行处理）
        while (!pendingTasks.isEmpty()) {
            CalcJobTask task = pendingTasks.poll();

            // 记录已发送的任务
            emittedTaskIds.add(task.getTaskId());
            emittedTaskCount++;

            logger.info("Emitting task: {} for job: {} (emitted: {}/{}, pending: {})",
                       task.getTaskId(), jobId, emittedTaskCount, totalTaskCount, pendingTasks.size());

            ctx.collect(task);
            emittedAny = true;
        }

        // 检查是否所有任务都已发送
        if (pendingTasks.isEmpty() && !allTasksEmitted) {
            allTasksEmitted = true;
            logger.info("All tasks emitted for job: {}, waiting for completion", jobId);
        }

        return emittedAny;
    }

    private boolean allTasksCompleted() {
        if (!allTasksEmitted) {
            return false; // 还有任务未发送
        }

        // ✅ 检查是否所有已发送的任务都已完成
        boolean completed = emittedTaskIds.isEmpty();

        if (completed) {
            logger.info("All tasks completed for job: {}, total processed: {}", jobId, totalTaskCount);
        } else {
            logger.debug("Job {} progress: {} tasks still processing", jobId, emittedTaskIds.size());
        }

        return completed;
    }

    private void markJobAsFinished() {
        try {
            TblCalcJobInfoMapper mapper = new TblCalcJobInfoMapper();
            TblCalcJobInfo currentJobInfo = mapper.selectByJobId(jobId);

            if (currentJobInfo != null) {
                currentJobInfo.setStatus(4); // 4 = FINISHED
                currentJobInfo.setEndTime(System.currentTimeMillis());
                mapper.updateByJobId(currentJobInfo);

                logger.info("Job {} marked as FINISHED in database", jobId);
            }

        } catch (Exception e) {
            logger.error("Failed to mark job {} as FINISHED: {}", jobId, e.getMessage(), e);
        }
    }

    @Override
    public void cancel() {
        logger.info("CalcJobTaskSource cancelled for job: {}", jobId);
        isRunning = false;
    }

    @Override
    public void close() throws Exception {
        super.close();
        // ✅ 清理Job级别的内存状态
        if (taskCompletionNotifier != null) {
            taskCompletionNotifier.cleanup();
        }
        logger.info("CalcJobTaskSource closed and cleaned up for job: {}", jobId);
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        logger.debug("Snapshotting state for job: {}, pending: {}, emitted: {}",
                    jobId, pendingTasks.size(), emittedTaskIds.size());

        // ✅ 保存待处理任务
        checkpointedPendingTasks.clear();
        for (CalcJobTask task : pendingTasks) {
            checkpointedPendingTasks.add(task);
        }

        // ✅ 保存已发送但未完成的任务ID
        checkpointedEmittedTaskIds.clear();
        for (String taskId : emittedTaskIds) {
            checkpointedEmittedTaskIds.add(taskId);
        }

        checkpointedTotalCount.update(totalTaskCount);
        checkpointedEmittedCount.update(emittedTaskCount);
        checkpointedAllTasksEmitted.update(allTasksEmitted);

        logger.debug("State snapshot completed for job: {}", jobId);
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        logger.info("Initializing state for job: {}", jobId);

        // ✅ Job级别的状态描述符（完全隔离）
        checkpointedPendingTasks = context.getOperatorStateStore()
            .getListState(new ListStateDescriptor<>(
                "pending-tasks-" + jobId, CalcJobTask.class));

        checkpointedEmittedTaskIds = context.getOperatorStateStore()
            .getListState(new ListStateDescriptor<>(
                "emitted-task-ids-" + jobId, String.class));

        checkpointedTotalCount = context.getOperatorStateStore()
            .getState(new ValueStateDescriptor<>(
                "total-task-count-" + jobId, Integer.class));

        checkpointedEmittedCount = context.getOperatorStateStore()
            .getState(new ValueStateDescriptor<>(
                "emitted-task-count-" + jobId, Integer.class));

        checkpointedAllTasksEmitted = context.getOperatorStateStore()
            .getState(new ValueStateDescriptor<>(
                "all-tasks-emitted-" + jobId, Boolean.class));

        // ✅ 状态恢复
        if (context.isRestored()) {
            logger.info("Restoring state for job: {}", jobId);

            // 恢复待处理任务
            pendingTasks = new LinkedList<>();
            for (CalcJobTask task : checkpointedPendingTasks.get()) {
                pendingTasks.offer(task);
            }

            // ✅ 恢复已发送任务ID（这些任务需要重新处理，依赖幂等性）
            emittedTaskIds = new HashSet<>();
            for (String taskId : checkpointedEmittedTaskIds.get()) {
                emittedTaskIds.add(taskId);
            }

            // ✅ 将已发送但未完成的任务重新加入待处理队列
            if (!emittedTaskIds.isEmpty()) {
                regenerateEmittedTasks();
            }

            // 恢复其他状态
            Integer restoredTotalCount = checkpointedTotalCount.value();
            if (restoredTotalCount != null) {
                totalTaskCount = restoredTotalCount;
            }

            Integer restoredEmittedCount = checkpointedEmittedCount.value();
            if (restoredEmittedCount != null) {
                emittedTaskCount = restoredEmittedCount;
            }

            Boolean restoredAllTasksEmitted = checkpointedAllTasksEmitted.value();
            if (restoredAllTasksEmitted != null) {
                allTasksEmitted = restoredAllTasksEmitted;
            }

            // ✅ 重置已发送任务集合，因为这些任务会重新处理
            emittedTaskIds.clear();

            logger.info("State restored for job {}: total={}, pending={}, will re-process emitted tasks",
                       jobId, totalTaskCount, pendingTasks.size());
        }
    }

    private void regenerateEmittedTasks() throws Exception {
        // ✅ 重新生成已发送但未完成的任务
        CalcJobTaskSplitEnumerator enumerator = new CalcJobTaskSplitEnumerator(jobInfo);
        List<CalcJobTask> allTasks = enumerator.generateAllTasks();

        // 将需要重新处理的任务加入队列
        for (CalcJobTask task : allTasks) {
            if (emittedTaskIds.contains(task.getTaskId())) {
                pendingTasks.offer(task);
                logger.info("Re-queued task for processing: {}", task.getTaskId());
            }
        }
    }
}
```

### 2.4 TaskCompletionNotifier (内存状态通知机制)

```java
public class TaskCompletionNotifier {

    private static final Logger logger = LoggerFactory.getLogger(TaskCompletionNotifier.class);

    // ✅ 多Job状态隔离：使用jobId作为命名空间
    private static final Map<String, TaskCompletionNotifier> INSTANCES = new ConcurrentHashMap<>();

    private final String jobId;
    private final Set<String> completedTasks;
    private final Set<String> failedTasks;
    private final AtomicLong lastUpdateTime;

    private TaskCompletionNotifier(String jobId) {
        this.jobId = jobId;
        this.completedTasks = ConcurrentHashMap.newKeySet();
        this.failedTasks = ConcurrentHashMap.newKeySet();
        this.lastUpdateTime = new AtomicLong(System.currentTimeMillis());
    }

    /**
     * ✅ 获取Job级别的实例（完全隔离）
     */
    public static TaskCompletionNotifier getInstance(String jobId) {
        return INSTANCES.computeIfAbsent(jobId, TaskCompletionNotifier::new);
    }

    /**
     * ✅ 获取全局实例（用于流式场景）
     */
    public static TaskCompletionNotifier getGlobalInstance() {
        return getInstance("global");
    }

    /**
     * 标记任务完成（线程安全，Job级别隔离）
     */
    public void markTaskCompleted(String taskId) {
        completedTasks.add(taskId);
        failedTasks.remove(taskId); // 从失败集合中移除
        lastUpdateTime.set(System.currentTimeMillis());
        logger.debug("Task {} marked as completed for job {}", taskId, jobId);
    }

    /**
     * 标记任务完成（带jobId参数，用于流式场景）
     */
    public void markTaskCompleted(String jobId, String taskId) {
        TaskCompletionNotifier instance = getInstance(jobId);
        instance.markTaskCompleted(taskId);
    }

    /**
     * 标记任务失败（线程安全，Job级别隔离）
     */
    public void markTaskFailed(String taskId, String errorMessage) {
        failedTasks.add(taskId);
        lastUpdateTime.set(System.currentTimeMillis());
        logger.warn("Task {} marked as failed for job {}: {}", taskId, jobId, errorMessage);
    }

    /**
     * 标记任务失败（带jobId参数，用于流式场景）
     */
    public void markTaskFailed(String jobId, String taskId, String errorMessage) {
        TaskCompletionNotifier instance = getInstance(jobId);
        instance.markTaskFailed(taskId, errorMessage);
    }

    /**
     * 获取已完成的任务（返回副本，避免并发修改）
     */
    public Set<String> getCompletedTasks() {
        return new HashSet<>(completedTasks);
    }

    /**
     * 获取失败的任务（返回副本，避免并发修改）
     */
    public Set<String> getFailedTasks() {
        return new HashSet<>(failedTasks);
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isTaskCompleted(String taskId) {
        return completedTasks.contains(taskId);
    }

    /**
     * 检查任务是否已完成（带jobId参数，用于流式场景）
     */
    public boolean isTaskCompleted(String jobId, String taskId) {
        TaskCompletionNotifier instance = getInstance(jobId);
        return instance.isTaskCompleted(taskId);
    }

    /**
     * 检查任务是否失败
     */
    public boolean isTaskFailed(String taskId) {
        return failedTasks.contains(taskId);
    }

    /**
     * ✅ 清理特定Job的状态（状态隔离）
     */
    public void cleanup() {
        completedTasks.clear();
        failedTasks.clear();
        INSTANCES.remove(jobId);
        logger.info("Cleaned up task completion state for job {}", jobId);
    }

    /**
     * ✅ 静态方法：清理特定Job的状态
     */
    public static void cleanup(String jobId) {
        TaskCompletionNotifier instance = INSTANCES.remove(jobId);
        if (instance != null) {
            instance.cleanup();
        }
    }

    /**
     * ✅ 获取所有活跃的Job数量（监控用）
     */
    public static int getActiveJobCount() {
        return INSTANCES.size();
    }

    /**
     * ✅ 获取所有活跃的JobID（监控用）
     */
    public static Set<String> getActiveJobIds() {
        return new HashSet<>(INSTANCES.keySet());
    }
}
```

## 🔧 **3. AspectCalcFlow 技术方案 (定时切面计算)**

### 3.1 AspectCalcFlow 主流程

```java
public class AspectCalcFlow {
    
    private static final Logger logger = LoggerFactory.getLogger(AspectCalcFlow.class);
    
    public static void main(String[] args) throws Exception {
        logger.info("Starting AspectCalcFlow (Scheduled Aspect Calculation)");
        
        // ✅ 创建流式执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置流式配置
        env.setParallelism(CalcLionConfig.getAspectCalcDefaultParallelism());
        env.enableCheckpointing(CalcLionConfig.getAspectCalcCheckpointIntervalMs());
        
        // ✅ 构建流式数据流
        buildAspectCalcDataFlow(env);
        
        // 执行流式作业（持续运行）
        env.execute("AspectCalcFlow");
    }
    
    private static void buildAspectCalcDataFlow(StreamExecutionEnvironment env) {
        
        // ✅ 1. 定时获取多个Job信息
        DataStream<TblCalcJobInfo> jobInfoStream = env
            .addSource(new GetCalcJobInfoTimer(
                CalcLionConfig.getJobInfoCheckIntervalMs(),
                CalcLionConfig.getJobInfoBatchSize(),
                Arrays.asList(0, 1) // PENDING, RUNNING
            ))
            .name("GetCalcJobInfoTimer")
            .uid("get-calc-job-info-timer")
            .setParallelism(1);
        
        // ✅ 2. 流式任务源（支持多Job并行处理）
        Source<CalcJobTask, CalcJobSplit, CalcJobEnumState> calcJobSource = 
            CalcJobSource.builder()
                .setTaskCompletionCheckInterval(CalcLionConfig.getTaskCompletionCheckIntervalMs())
                .setMaxJobsPerReader(CalcLionConfig.getMaxJobsPerReader())
                .setTaskBatchSize(CalcLionConfig.getTaskBatchSize())
                .build();
        
        DataStream<CalcJobTask> taskStream = env
            .fromSource(calcJobSource, WatermarkStrategy.noWatermarks(), "CalcJobTaskSource")
            .name("CalcJobTaskSource")
            .uid("calc-job-task-source")
            .setParallelism(CalcLionConfig.getAspectCalcJobTaskSourceParallelism());
        
        // ✅ 3. 切面计算处理器（输出计算结果）
        DataStream<LegacyMsgWithMultiAssets> calcResultStream = taskStream
            .keyBy(task -> task.getJobId())
            .process(new AspectCalcJobTaskProcessor())
            .name("AspectCalcJobTaskProcessor")
            .uid("aspect-calc-job-task-processor")
            .setParallelism(CalcLionConfig.getAspectCalcJobTaskProcessorParallelism());
        
        // ✅ 4. 关键差异：使用KafkaSink输出（而不是在Processor中直接写入）
        KafkaSink<LegacyMsgWithMultiAssets> kafkaSink = KafkaSink.<LegacyMsgWithMultiAssets>builder()
            .setBootstrapServers(CalcLionConfig.getAspectCalcKafkaBootstrapServers())
            .setRecordSerializer(new AspectCalcKafkaRecordSerializer())
            .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
            .build();
        
        calcResultStream
            .sinkTo(kafkaSink)
            .name("AspectCalcKafkaSink")
            .uid("aspect-calc-kafka-sink")
            .setParallelism(CalcLionConfig.getAspectCalcKafkaSinkParallelism());
    }
}
```

### 3.2 AspectCalcJobTaskProcessor 核心差异

```java
public class AspectCalcJobTaskProcessor extends KeyedProcessFunction<String, CalcJobTask, LegacyMsgWithMultiAssets> {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcJobTaskProcessor.class);

    // ✅ 流式处理专用：输出到下游KafkaSink
    private transient TaskCompletionNotifier taskCompletionNotifier;
    private transient CalculationServiceHelper calculationServiceHelper;
    private transient ModelMetaQueryHandler modelMetaQueryHandler;

    // ✅ 状态管理
    private transient MapState<String, TblCalcJobInfo> jobInfoState;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        this.taskCompletionNotifier = TaskCompletionNotifier.getGlobalInstance();
        this.calculationServiceHelper = new CalculationServiceHelper();
        this.modelMetaQueryHandler = ModelMetaQueryHandler.getInstance();

        // 初始化状态
        jobInfoState = getRuntimeContext().getMapState(
            new MapStateDescriptor<>("job-info-state", String.class, TblCalcJobInfo.class));

        logger.info("AspectCalcJobTaskProcessor opened for aspect calculation");
    }

    @Override
    public void processElement(CalcJobTask calcJobTask, Context ctx,
                              Collector<LegacyMsgWithMultiAssets> out) throws Exception {

        String taskId = calcJobTask.getTaskId();
        String jobId = calcJobTask.getJobId();

        logger.info("Processing aspect calculation task: {} for job: {}", taskId, jobId);

        try {
            // ✅ 幂等性检查
            if (taskCompletionNotifier.isTaskCompleted(jobId, taskId)) {
                logger.info("Task {} already completed for job {}, skipping", taskId, jobId);
                return;
            }

            // 获取作业信息
            TblCalcJobInfo jobInfo = getJobInfo(jobId);
            if (jobInfo == null) {
                logger.error("Job info not found for job: {}", jobId);
                return;
            }

            // ✅ 切面计算核心：最新值计算处理
            List<LegacyMsgWithMultiAssets> calcResults = executeAspectCalculation(jobInfo, calcJobTask);

            // ✅ 关键差异：整个Task完成后才输出到下游KafkaSink
            for (LegacyMsgWithMultiAssets result : calcResults) {
                out.collect(result);
            }

            // 标记任务完成
            taskCompletionNotifier.markTaskCompleted(jobId, taskId);

            logger.info("Aspect calculation task {} completed for job {}, output {} results",
                       taskId, jobId, calcResults.size());

        } catch (Exception e) {
            logger.error("Aspect calculation task {} failed for job {}: {}",
                        taskId, jobId, e.getMessage(), e);
            taskCompletionNotifier.markTaskFailed(jobId, taskId, e.getMessage());
        }
    }

    /**
     * ✅ 切面计算核心方法：最新值计算处理
     */
    private List<LegacyMsgWithMultiAssets> executeAspectCalculation(TblCalcJobInfo jobInfo,
                                                                   CalcJobTask calcJobTask)
            throws Exception {

        String orgId = jobInfo.getRuleInfo().getOrgId();

        // ✅ 关键差异1：不需要时间拆分，直接处理整个Task
        logger.info("Aspect calculation task {} processing without time splitting",
                   calcJobTask.getTaskId());

        // ✅ 直接查询最新值并计算
        CalcResultMsg calcResults = queryLatestDataAndCalculate(orgId, jobInfo, calcJobTask);

        // 转换为输出格式
        return convertToOutputMessages(calcResults, jobInfo);
    }

    /**
     * ✅ 关键差异：查询最新值数据并计算
     */
    private CalcResultMsg queryLatestDataAndCalculate(String orgId, TblCalcJobInfo jobInfo,
                                                     CalcJobTask calcJobTask) throws Exception {

        // 1. 构建最新值查询实体
        Map<String, LatestQueryEntity> latestQueryEntities =
            buildLatestValueQueryEntities(jobInfo, calcJobTask);

        // 2. ✅ 关键差异：统一使用最新值查询方法
        Map<String, LegacyPayload> allLatestValues = queryAllLatestValuesOnly(latestQueryEntities);

        // 3. 构建目标资产数据（基于最新值）
        Map<String, LegacyPayload> targetAssetValues =
            buildTargetAssetValuesFromLatest(calcJobTask, allLatestValues);

        // 4. 执行表达式计算
        Map<String, String> modelPathMap = getModelPathMap(orgId, calcJobTask);
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap =
            execTargetPropExprCalc(orgId, jobInfo, calcJobTask, targetAssetValues, modelPathMap);

        return CalcResultMsg.builder()
            .targetModel2MsgMap(targetModel2MsgMap)
            .build();
    }

    /**
     * ✅ 关键差异：只构建最新值查询实体
     */
    private Map<String, LatestQueryEntity> buildLatestValueQueryEntities(TblCalcJobInfo jobInfo,
                                                                        CalcJobTask calcJobTask) {

        List<SrcPrefItem> srcPrefItems = jobInfo.getRuleInfo().getTargetPropertyMeta().getSrcPrefItems();
        Map<String, LatestQueryEntity> latestQueryEntities = new HashMap<>();

        RecCalcMetaInfo calcMetaInfo = jobInfo.getRuleInfo();

        // ✅ 所有依赖项都使用最新值查询
        for (SrcPrefItem srcPrefItem : srcPrefItems) {
            String srcModelId = srcPrefItem.getModelId();
            LatestQueryEntity queryEntity = latestQueryEntities.computeIfAbsent(srcModelId, k ->
                LatestQueryEntity.builder()
                    .orgId(calcMetaInfo.getOrgId())
                    .modelId(srcModelId)
                    .build());

            queryEntity.getPropertyNames().add(srcPrefItem.getPrefName());

            List<String> targetAssetIds = calcJobTask.getTargetAssetIds();
            if (calcMetaInfo.getTargetModelIds().contains(srcModelId)) {
                queryEntity.getAssetIds().addAll(targetAssetIds);
            } else {
                // 通过 AssetInfo 获取依赖资产
                List<String> dependentAssetIds = getDependentAssetIds(targetAssetIds, srcModelId);
                queryEntity.getAssetIds().addAll(dependentAssetIds);
            }
        }

        return latestQueryEntities;
    }

    /**
     * ✅ 关键差异：统一使用最新值查询方法
     */
    private Map<String, LegacyPayload> queryAllLatestValuesOnly(
            Map<String, LatestQueryEntity> latestQueryEntities) throws Exception {

        Map<String, LegacyPayload> allLatestValues = new HashMap<>();

        for (Map.Entry<String, LatestQueryEntity> entry : latestQueryEntities.entrySet()) {
            String modelId = entry.getKey();
            LatestQueryEntity queryEntity = entry.getValue();

            // ✅ 关键差异：统一使用最新值查询方法
            Map<String, LegacyPayload> modelLatestValues =
                calculationServiceHelper.queryLatestValues(
                    queryEntity.getOrgId(), modelId, queryEntity);

            allLatestValues.putAll(modelLatestValues);

            logger.debug("Queried latest values for model {}: {} assets",
                        modelId, modelLatestValues.size());
        }

        return allLatestValues;
    }

    /**
     * ✅ 转换为输出消息格式
     */
    private List<LegacyMsgWithMultiAssets> convertToOutputMessages(CalcResultMsg calcResults,
                                                                  TblCalcJobInfo jobInfo) {

        List<LegacyMsgWithMultiAssets> outputMessages = new ArrayList<>();
        Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap = calcResults.getTargetModel2MsgMap();

        for (LegacyMsgWithMultiAssets message : targetModel2MsgMap.values()) {
            // 前置校验
            if (shouldOutputToKafka(jobInfo, message)) {
                // 添加必要的header信息
                enrichMessageHeaders(message, jobInfo);
                outputMessages.add(message);
            }
        }

        return outputMessages;
    }

    /**
     * ✅ 前置检查：判断是否应该输出到Kafka
     */
    private boolean shouldOutputToKafka(TblCalcJobInfo jobInfo, LegacyMsgWithMultiAssets message) {
        // 检查是否为属性（属性不输出到Kafka）
        if (jobInfo.getRuleInfo().getTargetPropertyMeta().getPrefType() == PrefType.ATTRIBUTE) {
            logger.debug("Skip outputting attribute to Kafka for model: {}", message.getModelId());
            return false;
        }

        return true;
    }

    /**
     * ✅ 丰富消息头信息，包含直白映射检查
     */
    private void enrichMessageHeaders(LegacyMsgWithMultiAssets message, TblCalcJobInfo jobInfo) {
        // 添加切面计算相关的header信息
        message.getHeaders().put("calc_type", "aspect");
        message.getHeaders().put("job_id", jobInfo.getJobId());
        message.getHeaders().put("calc_time", String.valueOf(System.currentTimeMillis()));

        // ✅ 添加orgId到消息头（用于Topic名称构建）
        String orgId = jobInfo.getRuleInfo().getOrgId();
        message.getHeaders().put("org_id", orgId);

        // ✅ 前置检查：判断是否为直白映射，添加 direct_mapping header
        if (isDirectMapping(jobInfo)) {
            message.getHeaders().put("direct_mapping", "1");
            logger.debug("Added direct_mapping header for aspect calc model: {}", message.getModelId());
        }
    }

    /**
     * ✅ 前置检查：判断是否为直白映射（与ReCalcJobTaskProcessor相同逻辑）
     */
    private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
        try {
            boolean directMapping = jobInfo.getRuleInfo()
                .getTargetPropertyMeta()
                .isDirectMapping();

            logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
            return directMapping;

        } catch (Exception e) {
            logger.warn("Failed to check direct mapping for job {}: {}",
                       jobInfo.getJobId(), e.getMessage());
            return false;
        }
    }
}
```

### 3.3 流式 CalcJobTaskSource (支持多Job)

```java
/**
 * ✅ 流式版本的CalcJobTaskSource，支持多Job并行处理
 * 借鉴KafkaSource的设计模式，使用SplitEnumerator和SourceReader
 */
public class StreamingCalcJobTaskSource implements Source<CalcJobTask, CalcJobSplit, CalcJobEnumState> {

    private final long taskCompletionCheckInterval;
    private final int maxJobsPerReader;
    private final int taskBatchSize;

    private StreamingCalcJobTaskSource(long taskCompletionCheckInterval, int maxJobsPerReader, int taskBatchSize) {
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.maxJobsPerReader = maxJobsPerReader;
        this.taskBatchSize = taskBatchSize;
    }

    // ✅ 构建器模式，方便参数传递
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private long taskCompletionCheckInterval = 1000L;
        private int maxJobsPerReader = 5;
        private int taskBatchSize = 10;

        public Builder setTaskCompletionCheckInterval(long interval) {
            this.taskCompletionCheckInterval = interval;
            return this;
        }

        public Builder setMaxJobsPerReader(int maxJobs) {
            this.maxJobsPerReader = maxJobs;
            return this;
        }

        public Builder setTaskBatchSize(int batchSize) {
            this.taskBatchSize = batchSize;
            return this;
        }

        public StreamingCalcJobTaskSource build() {
            return new StreamingCalcJobTaskSource(taskCompletionCheckInterval, maxJobsPerReader, taskBatchSize);
        }
    }

    @Override
    public SplitEnumerator<CalcJobSplit, CalcJobEnumState> createEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context) throws Exception {

        // ✅ 传递参数给SplitEnumerator
        return new CalcJobSplitEnumerator(
            context,
            maxJobsPerReader,                    // 每个Reader最大Job数
            taskCompletionCheckInterval          // 检查间隔
        );
    }

    @Override
    public SourceReader<CalcJobTask, CalcJobSplit> createReader(SourceReaderContext context) throws Exception {

        // ✅ 传递参数给SourceReader
        return new CalcJobSourceReader(
            context,
            taskCompletionCheckInterval,         // 任务完成检查间隔
            taskBatchSize,                       // 任务批量大小
            TaskCompletionNotifier.getGlobalInstance()  // 全局任务完成通知器
        );
    }

    @Override
    public Boundedness getBoundedness() {
        return Boundedness.CONTINUOUS_UNBOUNDED; // 流式处理，无界
    }
}

/**
 * ✅ Job分片枚举器，管理Job到SourceReader的分配
 */
public class CalcJobSplitEnumerator implements SplitEnumerator<CalcJobSplit, CalcJobEnumState> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobSplitEnumerator.class);

    private final SplitEnumeratorContext<CalcJobSplit> context;
    private final int maxJobsPerReader;
    private final long taskCompletionCheckInterval;

    // ✅ 管理Job到SourceReader的分配
    private final Map<Integer, Set<String>> readerJobs;  // readerId -> jobIds
    private final Set<String> unassignedJobs;            // 未分配的jobIds

    public CalcJobSplitEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context,
            int maxJobsPerReader,
            long taskCompletionCheckInterval) {

        this.context = context;
        this.maxJobsPerReader = maxJobsPerReader;
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.readerJobs = new HashMap<>();
        this.unassignedJobs = new HashSet<>();
    }

    @Override
    public void start() {
        // ✅ 使用检查间隔参数注册定时器
        context.callAsync(
            this::checkForNewJobs,
            (result, throwable) -> {
                if (throwable != null) {
                    logger.error("Error checking for new jobs", throwable);
                }
            },
            taskCompletionCheckInterval,
            taskCompletionCheckInterval
        );
    }

    @Override
    public void handleSplitRequest(int subtaskId, @Nullable String requesterHostname) {
        // ✅ 使用maxJobsPerReader参数控制分配
        Set<String> currentJobs = readerJobs.getOrDefault(subtaskId, new HashSet<>());

        if (currentJobs.size() < maxJobsPerReader && !unassignedJobs.isEmpty()) {
            String jobId = selectJobForReader(subtaskId);

            CalcJobSplit split = new CalcJobSplit(jobId);
            context.assignSplits(new SplitsAssignment<>(Map.of(subtaskId, List.of(split))));

            unassignedJobs.remove(jobId);
            currentJobs.add(jobId);
            readerJobs.put(subtaskId, currentJobs);

            logger.info("Assigned job {} to reader {}, total jobs: {}/{}",
                       jobId, subtaskId, currentJobs.size(), maxJobsPerReader);
        }
    }

    @Override
    public void addSplitsBack(List<CalcJobSplit> splits, int subtaskId) {
        // ✅ 处理SourceReader故障时的分片回收
        for (CalcJobSplit split : splits) {
            unassignedJobs.add(split.getJobId());
        }

        // 重新分配给其他健康的SourceReader
        reassignJobs();
    }

    /**
     * ✅ 动态添加新的Job
     */
    public void addNewJob(String jobId) {
        unassignedJobs.add(jobId);

        // 立即分配给负载最轻的SourceReader
        int targetReader = findLeastLoadedReader();
        if (targetReader >= 0) {
            CalcJobSplit split = new CalcJobSplit(jobId);
            context.assignSplits(new SplitsAssignment<>(Map.of(targetReader, List.of(split))));

            unassignedJobs.remove(jobId);
            readerJobs.computeIfAbsent(targetReader, k -> new HashSet<>()).add(jobId);
        }
    }

    private String selectJobForReader(int subtaskId) {
        return unassignedJobs.iterator().next();
    }

    private int findLeastLoadedReader() {
        return readerJobs.entrySet().stream()
            .min(Map.Entry.comparingByValue((s1, s2) -> Integer.compare(s1.size(), s2.size())))
            .map(Map.Entry::getKey)
            .orElse(-1);
    }

    private void reassignJobs() {
        // 重新分配未分配的Job
        for (String jobId : new HashSet<>(unassignedJobs)) {
            int targetReader = findLeastLoadedReader();
            if (targetReader >= 0) {
                addNewJob(jobId);
            }
        }
    }

    private void checkForNewJobs() {
        // 定期检查新的Job（由GetCalcJobInfoTimer提供）
        // 这里可以实现与GetCalcJobInfoTimer的集成逻辑
    }

    @Override
    public CalcJobEnumState snapshotState(long checkpointId) throws Exception {
        return new CalcJobEnumState(readerJobs, unassignedJobs);
    }

    @Override
    public void close() throws Exception {
        logger.info("CalcJobSplitEnumerator closed");
    }
}

/**
 * ✅ Job源读取器，每个并行实例管理分配给它的Job
 */
public class CalcJobSourceReader implements SourceReader<CalcJobTask, CalcJobSplit> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobSourceReader.class);

    private final SourceReaderContext context;
    private final long taskCompletionCheckInterval;
    private final int taskBatchSize;
    private final TaskCompletionNotifier taskCompletionNotifier;

    // ✅ 每个并行实例管理多个Job
    private final Map<String, JobTaskManager> jobManagers;

    public CalcJobSourceReader(
            SourceReaderContext context,
            long taskCompletionCheckInterval,
            int taskBatchSize,
            TaskCompletionNotifier taskCompletionNotifier) {

        this.context = context;
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.taskBatchSize = taskBatchSize;
        this.taskCompletionNotifier = taskCompletionNotifier;
        this.jobManagers = new HashMap<>();
    }

    @Override
    public void start() {
        // ✅ 向SplitEnumerator请求分片
        context.sendSplitRequest();

        // ✅ 使用检查间隔参数注册定时任务
        scheduleTaskCompletionCheck();
    }

    @Override
    public InputStatus pollNext(ReaderOutput<CalcJobTask> output) throws Exception {
        boolean hasOutput = false;

        Iterator<Map.Entry<String, JobTaskManager>> iterator = jobManagers.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, JobTaskManager> entry = iterator.next();
            JobTaskManager jobManager = entry.getValue();

            // 清理已完成任务
            jobManager.cleanupCompletedTasks();

            // ✅ 使用taskBatchSize参数批量获取任务
            List<CalcJobTask> tasks = jobManager.pollPendingTasks(taskBatchSize);
            for (CalcJobTask task : tasks) {
                output.collect(task);
                hasOutput = true;
            }

            // 检查Job是否完成
            if (jobManager.isJobCompleted()) {
                markJobAsFinished(jobManager.getJobId());
                iterator.remove();
                logger.info("Job {} completed and removed", jobManager.getJobId());
            }
        }

        return hasOutput ? InputStatus.MORE_AVAILABLE : InputStatus.NOTHING_AVAILABLE;
    }

    @Override
    public void addSplits(List<CalcJobSplit> splits) {
        // ✅ 接收分配的Job
        for (CalcJobSplit split : splits) {
            String jobId = split.getJobId();

            if (!jobManagers.containsKey(jobId)) {
                // 从数据库加载Job信息
                TblCalcJobInfo jobInfo = loadJobInfo(jobId);
                JobTaskManager jobManager = new JobTaskManager(jobInfo, taskCompletionNotifier);
                jobManagers.put(jobId, jobManager);

                logger.info("Added job {} to reader", jobId);
            }
        }
    }

    @Override
    public List<CalcJobSplit> snapshotState(long checkpointId) {
        List<CalcJobSplit> splits = new ArrayList<>();
        for (String jobId : jobManagers.keySet()) {
            splits.add(new CalcJobSplit(jobId));
        }
        return splits;
    }

    @Override
    public void close() throws Exception {
        logger.info("CalcJobSourceReader closed");
    }

    private void scheduleTaskCompletionCheck() {
        // ✅ 使用参数配置的间隔
        context.getMailboxExecutor().execute(
            () -> {
                // 检查任务完成状态
                checkTaskCompletion();

                // 重新调度
                context.getMailboxExecutor().executeAfter(
                    this::scheduleTaskCompletionCheck,
                    Duration.ofMillis(taskCompletionCheckInterval)
                );
            },
            "task-completion-check"
        );
    }

    private void checkTaskCompletion() {
        // 检查所有Job的任务完成状态
        for (JobTaskManager jobManager : jobManagers.values()) {
            jobManager.cleanupCompletedTasks();
        }
    }

    private TblCalcJobInfo loadJobInfo(String jobId) {
        try {
            TblCalcJobInfoMapper mapper = new TblCalcJobInfoMapper();
            return mapper.selectByJobId(jobId);
        } catch (Exception e) {
            logger.error("Failed to load job info for job {}: {}", jobId, e.getMessage(), e);
            return null;
        }
    }

    private void markJobAsFinished(String jobId) {
        try {
            TblCalcJobInfoMapper mapper = new TblCalcJobInfoMapper();
            TblCalcJobInfo jobInfo = mapper.selectByJobId(jobId);

            if (jobInfo != null) {
                jobInfo.setStatus(4); // 4 = FINISHED
                jobInfo.setEndTime(System.currentTimeMillis());
                mapper.updateByJobId(jobInfo);

                logger.info("Job {} marked as FINISHED", jobId);
            }
        } catch (Exception e) {
            logger.error("Failed to mark job {} as FINISHED: {}", jobId, e.getMessage(), e);
        }
    }
}

/**
 * ✅ 单个Job的任务管理器
 */
public class JobTaskManager {

    private static final Logger logger = LoggerFactory.getLogger(JobTaskManager.class);

    private final TblCalcJobInfo jobInfo;
    private final String jobId;
    private final TaskCompletionNotifier taskCompletionNotifier;

    // 任务状态
    private Queue<CalcJobTask> pendingTasks;
    private Set<String> emittedTaskIds;
    private int totalTaskCount;
    private boolean allTasksEmitted;
    private boolean initialized;

    public JobTaskManager(TblCalcJobInfo jobInfo, TaskCompletionNotifier notifier) {
        this.jobInfo = jobInfo;
        this.jobId = jobInfo.getJobId();
        this.taskCompletionNotifier = notifier;
        this.pendingTasks = new LinkedList<>();
        this.emittedTaskIds = new HashSet<>();
        this.initialized = false;
    }

    public boolean hasWork() throws Exception {
        if (!initialized) {
            initializeTasks();
            initialized = true;
        }

        return !pendingTasks.isEmpty() || !emittedTaskIds.isEmpty();
    }

    public void cleanupCompletedTasks() {
        Set<String> completedTaskIds = taskCompletionNotifier.getCompletedTasks();

        if (!completedTaskIds.isEmpty()) {
            int removedCount = 0;
            Iterator<String> iterator = emittedTaskIds.iterator();
            while (iterator.hasNext()) {
                String taskId = iterator.next();
                if (completedTaskIds.contains(taskId)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.debug("Cleaned up {} completed tasks for job {}", removedCount, jobId);
            }
        }
    }

    public List<CalcJobTask> pollPendingTasks(int batchSize) {
        List<CalcJobTask> tasks = new ArrayList<>();

        for (int i = 0; i < batchSize && !pendingTasks.isEmpty(); i++) {
            CalcJobTask task = pendingTasks.poll();
            emittedTaskIds.add(task.getTaskId());
            tasks.add(task);
        }

        if (pendingTasks.isEmpty() && !allTasksEmitted) {
            allTasksEmitted = true;
            logger.info("All tasks emitted for job: {}", jobId);
        }

        return tasks;
    }

    public boolean isJobCompleted() {
        return allTasksEmitted && emittedTaskIds.isEmpty();
    }

    private void initializeTasks() throws Exception {
        CalcJobTaskSplitEnumerator enumerator = new CalcJobTaskSplitEnumerator(jobInfo);
        List<CalcJobTask> allTasks = enumerator.generateAllTasks();

        pendingTasks.addAll(allTasks);
        totalTaskCount = allTasks.size();

        logger.info("Initialized {} tasks for job: {}", totalTaskCount, jobId);
    }

    // Getters
    public String getJobId() { return jobId; }
    public TblCalcJobInfo getJobInfo() { return jobInfo; }
}
```

### 3.4 AspectCalcKafkaRecordSerializer

```java
public class AspectCalcKafkaRecordSerializer implements KafkaRecordSerializationSchema<LegacyMsgWithMultiAssets> {

    private static final Logger logger = LoggerFactory.getLogger(AspectCalcKafkaRecordSerializer.class);

    @Override
    public ProducerRecord<byte[], byte[]> serialize(LegacyMsgWithMultiAssets message,
                                                   KafkaSinkContext context,
                                                   Long timestamp) {

        try {
            // ✅ 从消息头中获取orgId
            String orgId = message.getHeaders().get("org_id");
            if (orgId == null) {
                logger.warn("Missing org_id in message headers for model: {}", message.getModelId());
                orgId = "default"; // 使用默认值或抛出异常
            }

            // ✅ 构建Topic名称
            String topicName = buildAspectCalcTopicName(orgId);

            // ✅ 序列化消息
            byte[] key = message.getModelId().getBytes(StandardCharsets.UTF_8);
            byte[] value = serializeMessage(message);

            // ✅ 创建ProducerRecord
            ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(topicName, key, value);

            // ✅ 添加Headers（包含direct_mapping等所有header）
            for (Map.Entry<String, String> header : message.getHeaders().entrySet()) {
                record.headers().add(header.getKey(), header.getValue().getBytes(StandardCharsets.UTF_8));

                // ✅ 特别记录direct_mapping header
                if ("direct_mapping".equals(header.getKey())) {
                    logger.debug("Added direct_mapping header: {} for model: {}",
                               header.getValue(), message.getModelId());
                }
            }

            logger.debug("Serialized aspect calc message for model: {} to topic: {}, headers: {}",
                        message.getModelId(), topicName, message.getHeaders().keySet());

            return record;

        } catch (Exception e) {
            logger.error("Failed to serialize aspect calc message for model {}: {}",
                        message.getModelId(), e.getMessage(), e);
            throw new RuntimeException("Serialization failed", e);
        }
    }

    private String buildAspectCalcTopicName(String orgId) {
        return CalcLionConfig.getAspectCalcKafkaSinkTopicPattern() + orgId;
    }

    private byte[] serializeMessage(LegacyMsgWithMultiAssets message) throws Exception {
        // 使用现有的序列化逻辑
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsBytes(message);
    }
}
```

### 3.5 相关实体类和枚举器

```java
/**
 * ✅ Job分片实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobSplit implements Serializable {
    private String jobId;

    public CalcJobSplit(String jobId) {
        this.jobId = jobId;
    }
}

/**
 * ✅ Job枚举器状态
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobEnumState implements Serializable {
    private Map<Integer, Set<String>> readerJobs;  // readerId -> jobIds
    private Set<String> unassignedJobs;            // 未分配的jobIds
}

/**
 * ✅ 任务拆分枚举器
 */
public class CalcJobTaskSplitEnumerator {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSplitEnumerator.class);

    private final TblCalcJobInfo jobInfo;

    public CalcJobTaskSplitEnumerator(TblCalcJobInfo jobInfo) {
        this.jobInfo = jobInfo;
    }

    /**
     * ✅ 生成所有任务
     */
    public List<CalcJobTask> generateAllTasks() throws Exception {
        List<CalcJobTask> allTasks = new ArrayList<>();

        String orgId = jobInfo.getRuleInfo().getOrgId();
        String jobId = jobInfo.getJobId();
        long startTime = jobInfo.getStartTime();
        long endTime = jobInfo.getEndTime();

        // 1. 查询目标资产
        List<String> targetAssetIds = queryTargetAssets(orgId, jobInfo);

        // 2. 按资产拆分任务
        for (String assetId : targetAssetIds) {
            CalcJobTask task = CalcJobTask.builder()
                .taskId(generateTaskId(jobId, assetId))
                .jobId(jobId)
                .targetAssetIds(Arrays.asList(assetId))
                .startTime(startTime)
                .endTime(endTime)
                .orgId(orgId)
                .targetModelId(jobInfo.getRuleInfo().getTargetModelIds().get(0))
                .build();

            allTasks.add(task);
        }

        logger.info("Generated {} tasks for job: {}", allTasks.size(), jobId);
        return allTasks;
    }

    private List<String> queryTargetAssets(String orgId, TblCalcJobInfo jobInfo) throws Exception {
        // 查询目标资产逻辑
        List<String> targetModelIds = jobInfo.getRuleInfo().getTargetModelIds();
        List<String> targetAssetIds = new ArrayList<>();

        for (String modelId : targetModelIds) {
            // 这里应该调用实际的资产查询服务
            // List<String> assetIds = assetQueryService.queryAssetsByModel(orgId, modelId);
            // targetAssetIds.addAll(assetIds);

            // 示例：生成模拟资产ID
            for (int i = 1; i <= 10; i++) {
                targetAssetIds.add(modelId + "_asset_" + i);
            }
        }

        return targetAssetIds;
    }

    private String generateTaskId(String jobId, String assetId) {
        return jobId + "_" + assetId + "_" + System.currentTimeMillis();
    }
}

/**
 * ✅ 计算任务实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcJobTask implements Serializable {
    private String taskId;
    private String jobId;
    private List<String> targetAssetIds;
    private long startTime;
    private long endTime;
    private String orgId;
    private String targetModelId;
}

/**
 * ✅ 计算结果消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcResultMsg implements Serializable {
    private Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap;
}

/**
 * ✅ 时间范围实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeRange implements Serializable {
    private long startTime;
    private long endTime;
}

/**
 * ✅ GetCalcJobInfoTimer (定时获取Job信息)
 */
public class GetCalcJobInfoTimer extends RichSourceFunction<TblCalcJobInfo>
    implements CheckpointedFunction {

    private static final Logger logger = LoggerFactory.getLogger(GetCalcJobInfoTimer.class);

    private final long checkIntervalMs;
    private final int batchSize;
    private final List<Integer> targetStatuses;

    private volatile boolean isRunning = true;
    private transient TblCalcJobInfoMapper jobInfoMapper;
    private transient ValueState<Long> lastCheckTime;

    public GetCalcJobInfoTimer(long checkIntervalMs, int batchSize, List<Integer> targetStatuses) {
        this.checkIntervalMs = checkIntervalMs;
        this.batchSize = batchSize;
        this.targetStatuses = targetStatuses;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.jobInfoMapper = new TblCalcJobInfoMapper();
        logger.info("GetCalcJobInfoTimer opened with interval: {}ms, batch size: {}",
                   checkIntervalMs, batchSize);
    }

    @Override
    public void run(SourceContext<TblCalcJobInfo> ctx) throws Exception {
        while (isRunning) {
            try {
                // ✅ 使用传入的参数
                List<TblCalcJobInfo> pendingJobs = fetchPendingJobs(batchSize, targetStatuses);

                synchronized (ctx.getCheckpointLock()) {
                    for (TblCalcJobInfo jobInfo : pendingJobs) {
                        logger.info("Emitting job info: {}", jobInfo.getJobId());
                        ctx.collect(jobInfo);
                    }

                    if (lastCheckTime != null) {
                        lastCheckTime.update(System.currentTimeMillis());
                    }
                }

                // 使用配置的检查间隔
                Thread.sleep(checkIntervalMs);

            } catch (Exception e) {
                logger.error("Error in GetCalcJobInfoTimer: {}", e.getMessage(), e);
                Thread.sleep(5000);
            }
        }
    }

    private List<TblCalcJobInfo> fetchPendingJobs(int batchSize, List<Integer> statuses) throws Exception {
        TblCalcJobInfoMapper mapper = new TblCalcJobInfoMapper();

        // ✅ 使用参数查询
        return mapper.selectByStatusesWithLimit(statuses, batchSize);
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        if (lastCheckTime != null) {
            // 保存最后检查时间
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        lastCheckTime = context.getOperatorStateStore()
            .getState(new ValueStateDescriptor<>("last-check-time", Long.class));
    }
}
```

## 🔧 **4. 配置对比**

### 4.1 ReCalcBatchJob 配置

```properties
# ReCalc 批处理配置
gravity-flink.recalc.checkpoint-dir=hdfs://namenode:9000/flink/checkpoints/recalc
gravity-flink.recalc.checkpoint-interval-ms=60000
gravity-flink.recalc.checkpoint-max-concurrent=1
gravity-flink.recalc.checkpoint-timeout-ms=600000

# ReCalc 任务处理配置
gravity-flink.recalc.job-task-processor.parallelism=4
gravity-flink.recalc.task-completion-check-interval-ms=1000
gravity-flink.recalc.time-range-split-seconds=3600

# ReCalc Kafka 配置（直接写入）
gravity-flink.recalc.kafka.bootstrap-servers=localhost:9092
gravity-flink.recalc.kafka.sink.topic-pattern=MEASURE_POINT_CAL_
```

### 4.2 AspectCalcFlow 配置

```properties
# AspectCalc 流式计算配置
gravity-flink.aspect-calc.default-parallelism=4
gravity-flink.aspect-calc.checkpoint-interval-ms=60000
gravity-flink.aspect-calc.checkpoint-dir=hdfs://namenode:9000/flink/checkpoints/aspect-calc

# AspectCalc 算子并行度配置
gravity-flink.aspect-calc.calc-job-task-source.parallelism=4
gravity-flink.aspect-calc.job-task-processor.parallelism=8
gravity-flink.aspect-calc.kafka-sink.parallelism=4

# AspectCalc Job 信息获取配置
gravity-flink.aspect-calc.job-info-check-interval-ms=30000
gravity-flink.aspect-calc.job-info-batch-size=10

# AspectCalc 任务处理配置
gravity-flink.aspect-calc.task-completion-check-interval-ms=1000
gravity-flink.aspect-calc.max-jobs-per-reader=5
gravity-flink.aspect-calc.task-batch-size=10

# AspectCalc Kafka 配置（KafkaSink）
gravity-flink.aspect-calc.kafka.bootstrap-servers=localhost:9092
gravity-flink.aspect-calc.kafka.sink.topic-pattern=MEASURE_POINT_CAL_
```

## 🎯 **5. 核心差异总结**

### 5.1 数据查询差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **查询方法** | `queryTSValues(orgId, modelId, queryEntity)` | `queryLatestValues(orgId, modelId, latestQueryEntity)` |
| **查询类型** | 时间范围查询 | 最新值查询 |
| **返回类型** | `Map<String, List<LegacyPayload>>` | `Map<String, LegacyPayload>` |
| **时间处理** | 需要处理时间序列数据 | 只处理当前最新值 |

### 5.2 前置检查机制

| 检查类型 | ReCalcBatchJob | AspectCalcFlow | 说明 |
|---------|---------------|----------------|------|
| **Job状态检查** | ✅ 检查status=3(CANCELLED)，终止批处理 | ✅ 检查Job状态，跳过已取消Job | 防止处理已取消的Job |
| **属性过滤检查** | ✅ 属性不写入Kafka | ✅ 属性不输出到Kafka | 属性数据不发送到Kafka |
| **直白映射检查** | ✅ isDirectMapping=true时添加header | ✅ isDirectMapping=true时添加header | 添加direct_mapping=1 header |
| **幂等性检查** | ✅ 检查任务是否已完成 | ✅ 检查任务是否已完成 | 避免重复处理 |

### 5.3 Job状态检查详细逻辑

```java
/**
 * 共同的Job状态检查逻辑
 */
private boolean checkJobStatusNotCancelled(String jobId) throws Exception {
    TblCalcJobInfo currentJobInfo = jobInfoMapper.selectByJobId(jobId);

    if (currentJobInfo == null) {
        logger.error("Job info not found for job: {}", jobId);
        return false;
    }

    int jobStatus = currentJobInfo.getStatus();

    // ✅ 状态码定义：
    // 0 = PENDING (待处理)
    // 1 = RUNNING (运行中)
    // 2 = PAUSED (暂停)
    // 3 = CANCELLED (已取消) ← 关键检查点
    // 4 = FINISHED (已完成)
    // 5 = FAILED (失败)

    if (jobStatus == 3) {
        logger.warn("Job {} status is CANCELLED (status=3)", jobId);
        return false;
    }

    return true;
}
```

### 5.4 直白映射检查详细逻辑

```java
/**
 * 共同的直白映射检查逻辑
 */
private boolean isDirectMapping(TblCalcJobInfo jobInfo) {
    try {
        // ✅ 检查路径：TblCalcJobInfo -> ruleInfo -> targetPropertyMeta -> isDirectMapping
        boolean directMapping = jobInfo.getRuleInfo()
            .getTargetPropertyMeta()
            .isDirectMapping();

        logger.debug("Job {} direct mapping check: {}", jobInfo.getJobId(), directMapping);
        return directMapping;

    } catch (Exception e) {
        logger.warn("Failed to check direct mapping for job {}: {}",
                   jobInfo.getJobId(), e.getMessage());
        return false;
    }
}

/**
 * Kafka消息Header添加逻辑
 */
private void addDirectMappingHeaderIfNeeded(ProducerRecord record, TblCalcJobInfo jobInfo) {
    if (isDirectMapping(jobInfo)) {
        // ✅ 添加特殊header：key="direct_mapping", value="1"
        record.headers().add("direct_mapping", "1".getBytes(StandardCharsets.UTF_8));
        logger.debug("Added direct_mapping header for job: {}", jobInfo.getJobId());
    }
}
```

### 5.5 任务处理差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **时间拆分** | 需要按时间段拆分 | 不需要时间拆分 |
| **处理粒度** | 子时间段级别 | 整个Task级别 |
| **输出时机** | 每个子时间段完成后 | 整个Task完成后 |
| **输出方式** | 直接写入Kafka | 通过KafkaSink输出 |
| **Job取消处理** | 抛出异常，终止批处理 | 跳过任务，继续处理其他Job |

### 5.6 CalcJobTaskSource 架构差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **Source类型** | RichSourceFunction | Source API (SplitEnumerator + SourceReader) |
| **Job处理模式** | 单Job处理 | 多Job并发处理 |
| **并行度支持** | 固定并行度=1 | 动态并行度，支持水平扩展 |
| **任务分配** | 直接处理单个Job的所有任务 | 通过SplitEnumerator分配Job到不同Reader |
| **状态管理** | Job级别状态隔离 | Reader级别多Job状态管理 |
| **故障恢复** | 单Job故障影响整个批处理 | 单Job故障不影响其他Job |
| **内存通知** | TaskCompletionNotifier.getInstance(jobId) | TaskCompletionNotifier.getGlobalInstance() |
| **生命周期** | Job完成后Source退出 | 持续运行，动态处理新Job |

### 5.7 架构差异

| 方面 | ReCalcBatchJob | AspectCalcFlow |
|------|---------------|----------------|
| **执行模式** | 批处理，有限生命周期 | 流式处理，无限生命周期 |
| **Job管理** | 单Job处理 | 多Job并发处理 |
| **状态隔离** | Job级别隔离 | Reader级别多Job管理 |
| **扩展性** | 垂直扩展 | 水平扩展 |
| **资源利用** | 临时资源占用 | 持续资源占用 |

### 5.8 使用场景

| 场景 | 推荐方案 | 原因 |
|------|---------|------|
| **历史数据重跑** | ReCalcBatchJob | 需要处理特定时间范围的历史数据 |
| **定时切面计算** | AspectCalcFlow | 需要基于最新值进行实时计算 |
| **一次性计算任务** | ReCalcBatchJob | 任务完成后可以退出，节省资源 |
| **持续监控计算** | AspectCalcFlow | 需要持续运行，处理新的计算需求 |
| **Job取消场景** | ReCalcBatchJob | 检测到取消立即终止，避免资源浪费 |
| **多Job并发场景** | AspectCalcFlow | 单个Job取消不影响其他Job处理 |

## 📋 **6. 部署和运维对比**

### 6.1 部署命令对比

```bash
# ReCalcBatchJob 部署
flink run -c com.envision.gravity.flink.streaming.calculate.recalc.ReCalcBatchJob \
  flink-streaming-calculate-1.0.jar \
  job-001

# AspectCalcFlow 部署
flink run -c com.envision.gravity.flink.streaming.calculate.aspect.AspectCalcFlow \
  -p 8 \
  flink-streaming-calculate-1.0.jar
```

### 6.2 监控指标对比

| 指标类型 | ReCalcBatchJob | AspectCalcFlow |
|---------|---------------|----------------|
| **任务进度** | 单Job任务完成率 | 多Job并发处理率 |
| **处理速度** | 历史数据处理速度 | 实时计算处理速度 |
| **资源使用** | 临时资源占用 | 持续资源占用 |
| **错误处理** | 单Job错误影响 | 多Job错误隔离 |

这个完整的技术方案清晰地展示了 ReCalcBatchJob 和 AspectCalcFlow 的核心差异，为两种不同场景提供了针对性的解决方案。
```
