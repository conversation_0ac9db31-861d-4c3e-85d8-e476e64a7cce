package com.envision.gravity.low.level.api.sql.service.impl;

import com.envision.gravity.common.exception.BadRequestException;
import com.envision.gravity.common.exception.InternalException;
import com.envision.gravity.common.ignite.service.BOLowLevelService;
import com.envision.gravity.common.vo.api.LoadCacheReq;
import com.envision.gravity.ignite.udf.GravitySqlFunctions;
import com.envision.gravity.low.level.api.sql.common.*;
import com.envision.gravity.low.level.api.sql.metric.MetricManager;
import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;
import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;
import com.envision.gravity.low.level.api.sql.util.IgniteUtils;

import javax.cache.CacheException;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.envision.gravity.low.level.api.sql.common.Constants.*;
import static org.apache.ignite.cache.CacheAtomicityMode.TRANSACTIONAL;
import static org.apache.ignite.cache.CacheMode.PARTITIONED;
import static org.apache.ignite.cache.CacheMode.REPLICATED;

import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.IgniteException;
import org.apache.ignite.IgniteLogger;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.binary.BinaryObjectException;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheKeyConfiguration;
import org.apache.ignite.cache.CacheMode;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.cluster.ClusterState;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.internal.IgniteEx;
import org.apache.ignite.lifecycle.LifecycleBean;
import org.apache.ignite.lifecycle.LifecycleEventType;
import org.apache.ignite.resources.IgniteInstanceResource;
import org.apache.ignite.resources.LoggerResource;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/17
 * @description
 */
public class BOLowLevelServiceImpl implements BOLowLevelService, LifecycleBean {
    private static final long serialVersionUID = 9033384158524153286L;

    @IgniteInstanceResource private Ignite ignite;
    @IgniteInstanceResource private transient IgniteEx igniteEx;
    @LoggerResource private IgniteLogger log;

    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE =
            new ScheduledThreadPoolExecutor(
                    1,
                    new ThreadFactory() {
                        private final AtomicInteger poolNumber = new AtomicInteger(1);

                        @Override
                        public Thread newThread(@NotNull Runnable r) {
                            return new Thread(
                                    r,
                                    String.format(
                                            "Register-Metrics-Scheduled-Thread-Pool-%d",
                                            poolNumber.getAndIncrement()));
                        }
                    });

    @Override
    public void onLifecycleEvent(LifecycleEventType evt) throws IgniteException {
        try {
            if (evt == LifecycleEventType.AFTER_NODE_JOINED) {

                if (ClusterState.INACTIVE.equals(ignite.cluster().state())) {
                    log.info(">>>>>> Active ignite cluster.");
                    ignite.cluster().state(ClusterState.ACTIVE);
                }

                loadCacheFromExtStorage();
                // check lost partitions and reset
                resetLostPartitionsCache();
            }
        } catch (InterruptedException e) {
            log.error("Init BO cache was interrupted.", e);
            throw new InternalException("Init BO cache was interrupted.", e);
        }
    }

    @Override
    public void init() {
        log.info("Init BO Low Level Ignite Service on node: " + ignite.cluster().localNode());

        registerMetrics();
    }

    @Override
    public void loadCache(LoadCacheReq req) {
        long putAllStartTime = System.currentTimeMillis();
        IgniteCache<BinaryObject, BinaryObject> igniteCache =
                IgniteUtils.getIgniteCache(ignite, req.getCacheName());
        String keyTypeName = req.getKeyTypeName();
        String valueTypeName = req.getValueTypeName();
        Map<String, Class<?>> cacheKeyCols = req.getCacheKeyCols();
        Map<String, Class<?>> cacheValueCols = req.getCacheValueCols();
        List<Map<String, Object>> keyValues = req.getKeyValues();

        try {
            Map<BinaryObject, BinaryObject> cacheMap = new LinkedHashMap<>();

            for (Map<String, Object> keyValue : keyValues) {
                BinaryObjectBuilder cacheKeyBuilder = ignite.binary().builder(keyTypeName);
                BinaryObjectBuilder cacheValueBuilder = ignite.binary().builder(valueTypeName);

                populateKeyFields(cacheKeyCols, cacheKeyBuilder, keyValue);
                populateValueFields(cacheValueCols, cacheValueBuilder, keyValue);

                cacheMap.put(cacheKeyBuilder.build(), cacheValueBuilder.build());
            }

            igniteCache.putAll(cacheMap);

        } catch (BinaryObjectException e) {
            log.error(String.format("Load cache error, cause: %s", e.getMessage()), e);
            throw new InternalException(
                    String.format("Load cache error, cause: %s", e.getMessage()), e);
        }
        long putAllEndTime = System.currentTimeMillis();
        log.info(
                ">>>>>> load cache time cost: " + (putAllEndTime - putAllStartTime) / 1000.0 + "s");
    }

    @Override
    public void initOU(String ouId, String resourceLevel) {
        try {
            // register ou
            registerOu(ouId, resourceLevel);
            // create database
            createSqlScheme(ouId);
            // create cache table
            createCacheTable(ouId);
            // create persistent table
            createPersistentTable(ouId);
        } catch (Exception e) {
            log.error(String.format("Init ou [%s] error, cause: %s", ouId, e.getMessage()), e);
            throw new InternalException(
                    String.format("Init ou [%s] error, cause: %s", ouId, e.getMessage()), e);
        }
    }

    @Override
    public void restoreCacheTable() {
        List<String> registeredOuIdList = queryRegisteredOuId();
        if (!registeredOuIdList.isEmpty()) {
            loadBOCacheValues(getBOCacheName(registeredOuIdList, null));
        } else {
            log.info(">>>>>> Registered ou is empty.");
        }
    }

    @Override
    public void loadExternalStorage(List<String> cacheNameList) {
        if (cacheNameList.isEmpty()) {
            return;
        }
        loadBOCacheValues(cacheNameList);
    }

    private void loadCacheFromExtStorage() throws InterruptedException {
        List<String> registeredOuIdList = queryRegisteredOuId();
        if (!registeredOuIdList.isEmpty()) {
            if (!cacheLoadAlready(registeredOuIdList)) {
                registerCacheTable(registeredOuIdList);
                // load replicated cache once
                while (true) {
                    try {
                        loadBOCacheValues(getBOCacheName(registeredOuIdList, REPLICATED));
                        break;
                    } catch (Exception e) {
                        log.error(
                                String.format(
                                        ">>>>>> Load replicated cache error, retry after waiting for three seconds, cause: %s",
                                        e.getMessage()),
                                e);
                        Thread.sleep(3000);
                    }
                }
            } else {
                log.info(">>>>>> BO cache already load.");
            }

            // load partitioned cache each time
            localLoadBOCacheValues(getBOCacheName(registeredOuIdList, PARTITIONED));
            log.info(">>>>>> Load all bo cache success.");
        } else {
            log.info(">>>>>> Registered ou is empty.");
        }
    }

    private void resetLostPartitionsCache() throws InterruptedException {
        try (FieldsQueryCursor<List<?>> cursor =
                igniteEx.context()
                        .query()
                        .querySqlFields(new SqlFieldsQuery(QUERY_LOST_PARTITIONS), false)) {
            List<List<?>> queryResult = cursor.getAll();

            if (!queryResult.isEmpty()) {
                Set<String> cacheNames =
                        queryResult.stream()
                                .filter(row -> !row.isEmpty())
                                .map(row -> String.valueOf(row.get(0)))
                                .collect(Collectors.toSet());

                if (!cacheNames.isEmpty()) {
                    while (true) {
                        try {
                            ignite.resetLostPartitions(cacheNames);
                            log.info(
                                    String.format(
                                            ">>>>>> Reset lost partitions success, reset total count: %d",
                                            cacheNames.size()));
                            break;
                        } catch (Exception e) {
                            log.error(
                                    String.format(
                                            ">>>>>> Reset lost partitions error, retry after waiting for ten seconds, cause: %s",
                                            e.getMessage()),
                                    e);
                            Thread.sleep(10000);
                        }
                    }
                }
            }
        }
    }

    /** Registers metrics. */
    private void registerMetrics() {
        SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(
                () -> {
                    try {
                        MetricManager.registerCacheMetrics(igniteEx, log);
                        MetricManager.registerPartitionStatesMetrics(igniteEx, log);
                    } catch (Exception e) {
                        log.error(">>> Register metrics error.", e);
                    }
                },
                0,
                2,
                TimeUnit.MINUTES);
    }

    private void populateKeyFields(
            Map<String, Class<?>> cacheKeyCols,
            BinaryObjectBuilder cacheKeyBuilder,
            Map<String, Object> keyValue) {
        cacheKeyCols.forEach(
                (cacheKeyName, cacheKeyDataType) -> {
                    Object value = keyValue.get(cacheKeyName);

                    if (value != null) {
                        if (cacheKeyDataType.isInstance(value)) {
                            cacheKeyBuilder.setField(cacheKeyName, cacheKeyDataType.cast(value));
                        } else {
                            log.warning(cacheKeyName + " field has an incorrect type!");
                        }
                    } else {
                        log.warning(cacheKeyName + " cannot be null!");
                        throw new BadRequestException(cacheKeyName + " cannot be null!");
                    }
                });
    }

    private void populateValueFields(
            Map<String, Class<?>> cacheValueCols,
            BinaryObjectBuilder cacheValueBuilder,
            Map<String, Object> keyValue) {
        cacheValueCols.forEach(
                (cacheValueName, cacheValueDataType) -> {
                    Object value = keyValue.get(cacheValueName);

                    if (value != null) {
                        if (cacheValueDataType.isInstance(value)) {
                            cacheValueBuilder.setField(
                                    cacheValueName, cacheValueDataType.cast(value));
                        } else {
                            log.warning(cacheValueName + " field has an incorrect type!");
                        }
                    }
                });
    }

    private void registerCacheTable(List<String> registeredOuIdList) {
        long registerStartTime = System.currentTimeMillis();
        registeredOuIdList.forEach(
                ouId -> {
                    try {
                        createCacheTable(ouId);
                        log.info(String.format("Register cache table by ou [%s] success.", ouId));
                    } catch (Exception e) {
                        log.error(
                                String.format(
                                        "Register cache table by ou [%s] error, cause: %s",
                                        ouId, e.getMessage()),
                                e);
                        throw new InternalException(
                                String.format(
                                        "Register cache table by ou [%s] error, cause: %s",
                                        ouId, e.getMessage()),
                                e);
                    }
                });
        long registerEndTime = System.currentTimeMillis();
        log.info(
                String.format(
                        ">>>>>> Register cache table total time cost: [%s].",
                        (registerEndTime - registerStartTime) / 1000.0 + "s"));
    }

    /** load all values from underlying persistent storage. */
    private void loadBOCacheValues(List<String> boCacheList) {
        log.info(">>>>>> Start loading bo cache...");
        long loadStartTime = System.currentTimeMillis();

        for (String cacheName : boCacheList) {
            IgniteCache<?, ?> cache = ignite.cache(cacheName);
            if (cache == null) {
                log.error(String.format("Cache [%s] is null.", cacheName));
                throw new InternalException(String.format("Cache [%s] is null.", cacheName));
            }

            try {
                cache.loadCache(null);
            } catch (CacheException e) {
                log.error(String.format("Load bo cache [%s] error!", cacheName), e);
                throw new InternalException(
                        String.format("Load bo cache [%s] error!", cacheName), e);
            }
        }
        long loadEndTime = System.currentTimeMillis();
        log.info(
                String.format(
                        ">>>>>> Load bo cache completed, total time cost: [%s].",
                        (loadEndTime - loadStartTime) / 1000.0 + "s"));
    }

    private void localLoadBOCacheValues(List<String> boCacheList) {
        log.info(">>>>>> Start local loading bo cache...");
        long loadStartTime = System.currentTimeMillis();

        for (String cacheName : boCacheList) {
            IgniteCache<?, ?> cache = ignite.cache(cacheName);
            if (cache == null) {
                log.warning(
                        String.format(
                                ">>>>>> Partitioned cache [%s] is null, skip load.", cacheName));
                return;
            }

            try {
                cache.localLoadCache(null);
            } catch (CacheException e) {
                log.error(
                        String.format("Local load bo partitioned cache [%s] error!", cacheName), e);
                throw new InternalException(
                        String.format("Local load bo partitioned cache [%s] error!", cacheName), e);
            }
        }
        long loadEndTime = System.currentTimeMillis();
        log.info(
                String.format(
                        ">>>>>> Local load bo partitioned cache completed, total time cost: [%s].",
                        (loadEndTime - loadStartTime) / 1000.0 + "s"));
    }

    private List<String> queryRegisteredOuId() {
        IgniteCache<?, ?> cache = ignite.cache(TBL_OU_INFO_CACHE_NAME);
        if (cache == null) {
            log.warning("Table [TBL_OU_INFO] has not been created!");
            return Collections.emptyList();
        }
        List<List<?>> queryOuIdResult =
                cache.query(new SqlFieldsQuery(QUERY_OU_ID_SQL_PATTERN)).getAll();
        List<List<?>> querySchemasResult =
                cache.query(new SqlFieldsQuery(QUERY_SCHEMAS_SQL_PATTERN)).getAll();
        List<String> ouIdList =
                queryOuIdResult.stream()
                        .map(rowRecord -> String.valueOf(rowRecord.get(0)))
                        .collect(Collectors.toList());
        List<String> schemaList =
                querySchemasResult.stream()
                        .map(rowRecord -> String.valueOf(rowRecord.get(0)))
                        .collect(Collectors.toList());
        return ouIdList.stream()
                .filter(ouId -> schemaList.contains(ouId.toUpperCase()))
                .collect(Collectors.toList());
    }

    private List<String> getBOCacheName(List<String> registeredOuIdList, CacheMode cacheMode) {
        List<String> tableNames =
                Arrays.stream(BOCoreCacheTableEnum.values())
                        .filter(
                                cacheTableEnum -> {
                                    if (cacheMode != null) {
                                        return cacheMode.equals(cacheTableEnum.getCacheMode());
                                    }
                                    return true;
                                })
                        .map(BOCoreCacheTableEnum::getQueryEntityTableName)
                        .collect(Collectors.toList());

        return registeredOuIdList.stream()
                .flatMap(ouId -> tableNames.stream().map(tableName -> ouId + "_" + tableName))
                .collect(Collectors.toList());
    }

    private void registerOu(String ouId, String resourceLevel) {
        IgniteCache<?, ?> cache = ignite.cache(TBL_OU_INFO_CACHE_NAME);
        if (cache == null) {
            throw new InternalException("Table [TBL_OU_INFO] has not been created!");
        }
        try {
            cache.query(
                            new SqlFieldsQuery(REGISTER_OU_SQL_PATTERN)
                                    .setArgs(
                                            ouId,
                                            resourceLevel,
                                            "gravity",
                                            "gravity",
                                            new Timestamp(System.currentTimeMillis()),
                                            new Timestamp(System.currentTimeMillis())))
                    .getAll();
        } catch (Exception e) {
            log.error(String.format("Register ou [%s] error, cause: %s", ouId, e.getMessage()), e);
            throw new InternalException(
                    String.format("Register ou [%s] error, cause: %s", ouId, e.getMessage()), e);
        }
    }

    private void createSqlScheme(String ouId) {
        try {
            CacheConfiguration<BinaryObject, BinaryObject> cfg = new CacheConfiguration<>();
            String cacheName = String.format(CACHE_NAME_PATTERN, ouId, GRAVITY_UDF);
            cfg.setName(cacheName);
            cfg.setSqlSchema(ouId);
            cfg.setCacheMode(REPLICATED);
            cfg.setAtomicityMode(TRANSACTIONAL);
            cfg.setSqlFunctionClasses(GravitySqlFunctions.class);
            ignite.getOrCreateCache(cfg);
        } catch (CacheException e) {
            log.error(
                    String.format("Create Sql Scheme [%s] error, cause: %s", ouId, e.getMessage()),
                    e);
            throw new InternalException(
                    String.format("Create Sql Scheme [%s] error, cause: %s", ouId, e.getMessage()),
                    e);
        }
    }

    private void createCacheTable(String ouId) {
        Arrays.stream(BOCoreCacheTableEnum.values())
                .forEach(
                        table -> {
                            String dataSourceTableName = table.getDataSourceTableName();
                            String queryEntityTableName = table.getQueryEntityTableName();
                            CacheMode cacheMode = table.getCacheMode();
                            CacheAtomicityMode atomicityMode = table.getAtomicityMode();
                            boolean readThrough = table.isReadThrough();
                            boolean writeThrough = table.isWriteThrough();
                            CacheTableInfo cacheTableInfo = table.getCacheTableInfo();
                            try {
                                CacheConfiguration<BinaryObject, BinaryObject> cfg =
                                        new CacheConfiguration<>();
                                cfg.setName(
                                        String.format(
                                                CACHE_NAME_PATTERN, ouId, queryEntityTableName));
                                cfg.setCacheMode(cacheMode);
                                if (PARTITIONED.equals(cacheMode)) {
                                    cfg.setBackups(1);
                                }
                                cfg.setAtomicityMode(atomicityMode);
                                cfg.setDataRegionName(BO_IN_MEMORY_REGION);
                                cfg.setSqlSchema(ouId);

                                // cacheStoreFactory
                                CacheJdbcPojoStoreFactory<BinaryObject, BinaryObject> storeFactory =
                                        new CacheJdbcPojoStoreFactory<>();
                                storeFactory.setDataSourceBean(POSTGRES_DATA_SOURCE_BEAN_NAME);
                                storeFactory.setDialect(new BasicJdbcDialect());
                                // types
                                JdbcType jdbcType = new JdbcType();
                                jdbcType.setCacheName(
                                        String.format(
                                                CACHE_NAME_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setKeyType(
                                        String.format(
                                                KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setValueType(
                                        String.format(
                                                VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
                                jdbcType.setDatabaseSchema(ouId);
                                jdbcType.setDatabaseTable(dataSourceTableName);

                                // key fields
                                List<JdbcTypeField> keyFields = cacheTableInfo.getKeyFields();
                                jdbcType.setKeyFields(keyFields.toArray(new JdbcTypeField[0]));
                                // value fields
                                List<JdbcTypeField> valFields = cacheTableInfo.getValueFields();
                                jdbcType.setValueFields(valFields.toArray(new JdbcTypeField[0]));
                                storeFactory.setTypes(jdbcType);
                                cfg.setCacheStoreFactory(storeFactory);

                                cfg.setReadThrough(readThrough);
                                cfg.setWriteThrough(writeThrough);

                                // create QueryEntity
                                QueryEntity qryEntity = new QueryEntity();
                                qryEntity.setTableName(queryEntityTableName);
                                qryEntity.setKeyType(
                                        String.format(
                                                KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setValueType(
                                        String.format(
                                                VALUE_TYPE_PATTERN, ouId, queryEntityTableName));
                                qryEntity.setKeyFields(cacheTableInfo.getQueryEntityKeyFields());
                                qryEntity.setNotNullFields(cacheTableInfo.getNotNullFields());
                                // index
                                qryEntity.setIndexes(cacheTableInfo.getIndexes());
                                qryEntity.setFields(cacheTableInfo.getQueryEntityFields());
                                if (cacheTableInfo.getDefaultFieldValues() != null) {
                                    qryEntity.setDefaultFieldValues(
                                            cacheTableInfo.getDefaultFieldValues());
                                }
                                cfg.setQueryEntities(Collections.singleton(qryEntity));

                                if (cacheTableInfo.getAffKeyFieldName() != null
                                        && !cacheTableInfo.getAffKeyFieldName().isEmpty()) {
                                    CacheKeyConfiguration cacheKeyCfg = new CacheKeyConfiguration();
                                    cacheKeyCfg.setTypeName(
                                            String.format(
                                                    KEY_TYPE_PATTERN, ouId, queryEntityTableName));
                                    cacheKeyCfg.setAffinityKeyFieldName(
                                            cacheTableInfo.getAffKeyFieldName());
                                    cfg.setKeyConfiguration(cacheKeyCfg);
                                }

                                ignite.getOrCreateCache(cfg);
                            } catch (CacheException e) {
                                log.error(
                                        String.format(
                                                "Create cache table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                                throw new InternalException(
                                        String.format(
                                                "Create cache table [%s] error, cause: %s",
                                                queryEntityTableName, e.getMessage()),
                                        e);
                            }
                        });
    }

    private void createPersistentTable(String ouId) {
        // create persistent table
        Arrays.stream(BOCorePersistentTableEnum.values())
                .forEach(
                        table -> {
                            String tableName = table.getTableName();
                            String cacheMode = table.getCacheMode();
                            String atomicityMode = table.getAtomicityMode();
                            PersistentTableInfo persistentTableInfo =
                                    table.getPersistentTableInfo();

                            try {
                                String keyType = String.format(KEY_TYPE_PATTERN, ouId, tableName);
                                String valueType =
                                        String.format(VALUE_TYPE_PATTERN, ouId, tableName);
                                String cacheName =
                                        String.format(CACHE_NAME_PATTERN, ouId, tableName);

                                // create table
                                String createTableSql =
                                        String.format(
                                                persistentTableInfo.getCreateTableSQLPattern(),
                                                ouId,
                                                cacheMode,
                                                atomicityMode,
                                                keyType,
                                                valueType,
                                                cacheName);

                                try (FieldsQueryCursor<List<?>> createTableQueryCursor =
                                        igniteEx.context()
                                                .query()
                                                .querySqlFields(
                                                        new SqlFieldsQuery(createTableSql),
                                                        false)) {
                                    createTableQueryCursor.getAll();
                                }

                                Thread.sleep(1000);

                                // create indexes
                                List<String> createIndexSQLPatternList =
                                        persistentTableInfo.getCreateIndexSQLPatternList();
                                createIndexSQLPatternList.forEach(
                                        createIndexSQLPattern -> {
                                            String createIndexSql =
                                                    String.format(createIndexSQLPattern, ouId);
                                            try (FieldsQueryCursor<List<?>> createIndexQueryCursor =
                                                    igniteEx.context()
                                                            .query()
                                                            .querySqlFields(
                                                                    new SqlFieldsQuery(
                                                                            createIndexSql),
                                                                    false)) {
                                                createIndexQueryCursor.getAll();
                                            }
                                        });
                            } catch (Exception e) {
                                log.error(
                                        String.format(
                                                "Create persistent table [%s] error, cause: %s",
                                                tableName, e.getMessage()),
                                        e);
                                throw new InternalException(
                                        String.format(
                                                "Create persistent table [%s] error, cause: %s",
                                                tableName, e.getMessage()),
                                        e);
                            }
                        });
    }

    private boolean cacheLoadAlready(List<String> registeredOuIdList) {
        String firstOuId = registeredOuIdList.get(0);
        try (FieldsQueryCursor<List<?>> queryCacheTableCursor =
                igniteEx.context()
                        .query()
                        .querySqlFields(
                                new SqlFieldsQuery(QUERY_CACHE_TABLE_PATTERN)
                                        .setArgs(
                                                firstOuId.toUpperCase(),
                                                Constants.TBL_START_VID_TABLE_NAME),
                                false)) {
            List<List<?>> queryResult = queryCacheTableCursor.getAll();
            return Integer.parseInt(queryResult.get(0).get(0).toString()) > 0;
        }
    }
}
