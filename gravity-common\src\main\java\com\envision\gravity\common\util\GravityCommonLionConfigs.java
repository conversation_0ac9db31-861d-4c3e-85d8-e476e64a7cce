package com.envision.gravity.common.util;

/** <AUTHOR> 2024/7/22 */
public class GravityCommonLionConfigs {

    // Rest API ---------------------------------------------------------------------------
    public static final String REST_URL = "gravity-common.rest.url";

    // SQL Gateway ------------------------------------------------------------------------
    public static final String SQL_GATEWAY_ADDRESS = "gravity-common.sql-gateway.address";

    public static final String SQL_GATEWAY_JDBC_URL = "gravity-common.sql-gateway.jdbc-url";

    public static final String SQL_GATEWAY_BO_SYNC_USERNAME =
            "gravity-common.sql-gateway.bo-sync.username";

    public static final String SQL_GATEWAY_BO_SYNC_PASSWORD =
            "gravity-common.sql-gateway.bo-sync.password";

    // TSDB --------------------------------------------------------------------------------
    public static final String TSDB_URL = "gravity-common.tsdb.url";

    public static final String TSDB_USERNAME = "gravity-common.tsdb.username";

    public static final String TSDB_PASSWORD = "gravity-common.tsdb.password";

    public static final String TSDB_SHARDING_RULE = "gravity-common.tsdb.sharding-rule";

    public static final String TSDB_REPLICA_FACTOR = "gravity-common.tsdb.replica-factor";

    public static final String TSDB_META_CACHE_SIZE = "gravity-common.tsdb.meta-cache-size";
    public static final long TSDB_META_CACHE_SIZE_DEFAULT = 500_000L;

    public static final String TSDB_META_CACHE_EXPIRE_SECONDS =
            "gravity-common.tsdb.meta-cache-expire-seconds";
    public static final int TSDB_META_CACHE_EXPIRE_SECONDS_DEFAULT = 600;

    // Ignite ------------------------------------------------------------------------------
    public static final String IGNITE_ADDRESS = "gravity-common.ignite.address";

    public static final String IGNITE_JDBC_DRIVER = "gravity-common.ignite.jdbc-driver";
    public static final String IGNITE_JDBC_DRIVER_DEFAULT =
            "org.apache.ignite.IgniteJdbcThinDriver";

    public static final String IGNITE_JDBC_URL = "gravity-common.ignite.jdbc-url";

    public static final String IGNITE_USERNAME = "gravity-common.ignite.username";

    public static final String IGNITE_PASSWORD = "gravity-common.ignite.password";

    // PostgreSQL --------------------------------------------------------------------------
    public static final String PGSQL_JDBC_DRIVER = "gravity-common.postgresql.jdbc-driver";
    public static final String PGSQL_JDBC_DRIVER_DEFAULT = "org.postgresql.Driver";

    public static final String PGSQL_JDBC_URL = "gravity-common.postgresql.jdbc-url";

    public static final String PGSQL_USERNAME = "gravity-common.postgresql.username";

    public static final String PGSQL_PASSWORD = "gravity-common.postgresql.password";

    public static final String PGSQL_HOSTNAME = "gravity-common.postgresql.hostname";

    public static final String PGSQL_PORT = "gravity-common.postgresql.port";

    // Nebula ------------------------------------------------------------------------------
    public static final String NEBULA_ADDRESS = "gravity-common.nebula.address";
    public static final String NEBULA_ADDRESS_DEFAULT = "127.0.0.1:9669";

    public static final String NEBULA_USERNAME = "gravity-common.nebula.username";
    public static final String NEBULA_USERNAME_DEFAULT = "root";

    public static final String NEBULA_PASSWORD = "gravity-common.nebula.password";
    public static final String NEBULA_PASSWORD_DEFAULT = "Envisi0n4321!";

    public static final String NEBULA_HEARTBEAT_INTERVAL_IN_MS =
            "gravity-common.nebula.heartbeat.interval.in.ms";
    public static final int NEBULA_HEARTBEAT_INTERVAL_IN_MS_DEFAULT = 5000;

    public static final String NEBULA_ENABLE = "gravity-common.nebula.enable";
    public static final boolean NEBULA_ENABLE_DEFAULT = true;

    // --------------------------------------------------------------------------------
    public static final String KAFKA_BOOTSTRAP_SERVERS = "Camel.kafka-common";

    // Ignite Query CommonJar ---------------------------------------------------------
    public static final String COMMON_JAR_CACHE_EXPIRE_MINUTES =
            "gravity-common.query.cache.expire.minutes";
    public static final int COMMON_JAR_CACHE_EXPIRE_MINUTES_DEFAULT = 10;
    public static final String COMMON_JAR_CACHE_SIZE_MAX = "gravity-common.query.cache.size.max";
    public static final long COMMON_JAR_CACHE_SIZE_MAX_DEFAULT = 100000;

    // Data auth
    public static final String DATA_AUTH_ENABLE = "gravity-common.data-auth.enable";
    public static final boolean DATA_AUTH_ENABLE_DEFAULT = true;
    public static final String DATA_AUTH_DEFAULT_IS_ADMIN =
            "gravity-common.data-auth.default.is-admin";
    public static final boolean DATA_AUTH_DEFAULT_IS_ADMIN_DEFAULT = true;
    public static final String DATA_AUTH_ATTR_NAME = "gravity-common.data-auth.attr.name";
    public static final String DATA_AUTH_ATTR_NAME_DEFAULT = "AUTH_VENDOR";
    public static final String DATA_AUTH_ATTR_RAW_FIELD_ID =
            "gravity-common.data-auth.attr.raw-field-id";
    public static final String DATA_AUTH_ATTR_RAW_FIELD_ID_DEFAULT = "AUTH_VENDOR__VARCHAR";
}
