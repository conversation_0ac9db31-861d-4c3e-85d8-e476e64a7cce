package com.envision.gravity.flink.streaming.calculate.utils;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.calculate.CalcConstant;
import com.envision.gravity.common.calculate.PageResultProcessor;
import com.envision.gravity.common.calculate.PropertyId;
import com.envision.gravity.common.calculate.PropertyInfo;

import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.univers.business.object.calc.dto.ExpressionParserInput;
import com.univers.business.object.calc.dto.ExpressionParserOutput;
import com.univers.business.object.calc.request.ParseExpressionRequest;
import com.univers.business.object.calc.response.ParseExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UpstreamCalcUtils {

    private static final Logger logger = LoggerFactory.getLogger(UpstreamCalcUtils.class);

    private static final String TBL_PROPERTY_UPSTREAM_RULE = "TBL_PROPERTY_UPSTREAM_RULE";

    /**
     * 通用分页查询方法
     * @param orgId 组织ID
     * @param processor 结果处理器
     */
    public static void queryTblUpstreamRuleWithPagination(String orgId,
                                                          PageResultProcessor<Map<PropertyId, List<BaseCalcPropertyMeta>>> processor) {
        int offset = 0;
        boolean hasMore = true;
        int totalProcessed = 0;
        int pageSize = CalcLionConfig.getCalcMetaLoadPageSize();

        while (hasMore) {
            String sql =
                    String.format(
                            "SELECT PREF_RULE_ID, TARGET_CATEGORY, TARGET_COMP_ID, TARGET_PREF_ID, SRC_CATEGORY, EXPRESSION, CALC_TYPE "
                                    + "FROM %s.%s WHERE CALC_TYPE = 0"
                                    + "ORDER BY CREATED_TIME "
                                    + "LIMIT %d OFFSET %d",
                            orgId, TBL_PROPERTY_UPSTREAM_RULE, pageSize, offset);
            List<List<?>> results = IgniteUtil.query(orgId, sql);
            if (GTCommonUtils.emptyCollection(results)) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
                continue;
            }

            // compId + prefId => metas
            Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropMetaMap =
                    new HashMap<>(results.size());
            for (List<?> row : results) {
                String prefRuleId = (String) row.get(0);
                String targetCategoryId = (String) row.get(1);
                String targetCompId = (String) row.get(2);
                String targetPrefId = (String) row.get(3);
                String srcCategoryId = (String) row.get(4);
                String expression = (String) row.get(5);
                Integer calcType = (Integer) row.get(6);

                targetPropMetaMap
                        .computeIfAbsent(
                                new PropertyId(targetCompId, targetPrefId), k -> new ArrayList<>())
                        .add(
                                BaseCalcPropertyMeta.builder()
                                        .prefRuleId(prefRuleId)
                                        .targetCategoryId(targetCategoryId)
                                        .targetCompId(targetCompId)
                                        .targetPrefId(targetPrefId)
                                        .srcCategoryId(srcCategoryId)
                                        .expression(expression)
                                        .calcType(calcType)
                                        .build());
                totalProcessed++;
            }

            // Post process
            if (processor != null) {
                processor.process(orgId, targetPropMetaMap);
            }

            offset += pageSize;

            // If the number of results is less than the page size, we've reached the last page
            if (results.size() < pageSize) {
                hasMore = false;
                logger.info(
                        "Finished load orgId {}, total records processed: {}",
                        orgId,
                        totalProcessed);
            }
        }
    }

    /**
     * Filter targetPropertyMap's records with no model definition by propertyInfos
     *
     * @param orgId
     * @param targetPropertyMap
     * @return
     */
    public static Map<PropertyId, List<BaseCalcPropertyMeta>> filterNotDefinedProperty(
            String orgId,
            Map<PropertyId, List<BaseCalcPropertyMeta>> targetPropertyMap,
            Map<PropertyId, PropertyInfo> propertyInfos) {
        Map<PropertyId, List<BaseCalcPropertyMeta>> notDefinedTargetPropertyMap = new HashMap<>(1);
        for (Map.Entry<PropertyId, List<BaseCalcPropertyMeta>> targetMetaEntry :
                targetPropertyMap.entrySet()) {
            boolean isNotDefined = true;
            PropertyId targetMetaId = targetMetaEntry.getKey();
            for (Map.Entry<PropertyId, PropertyInfo> propInfoEntry : propertyInfos.entrySet()) {
                PropertyId propInfoId = propInfoEntry.getKey();
                if (propInfoId.getCompId().equals(targetMetaId.getCompId())
                        && propInfoId.getPrefId().equals(targetMetaId.getPrefId())) {
                    isNotDefined = false;
                    break;
                }
            }

            if (isNotDefined) {
                notDefinedTargetPropertyMap.put(targetMetaId, targetMetaEntry.getValue());
            }
        }
        return notDefinedTargetPropertyMap;
    }

    public static List<CalcPropertyMeta> parseExpr(
            String targetModelId,
            String targetCompId,
            String targetPrefId,
            List<BaseCalcPropertyMeta> baseCalcPropertyMetas) {
        ParseExpressionRequest parseExprReq = new ParseExpressionRequest();
        List<ExpressionParserInput> inputs =
                baseCalcPropertyMetas.stream()
                        .map(
                                tp -> {
                                    Map<String, Object> args = new HashMap<>();
                                    args.put("prefRuleId", tp.getPrefRuleId());
                                    args.put("targetCategoryId", tp.getTargetCategoryId());
                                    args.put("targetCompId", targetCompId);
                                    args.put("targetPrefId", targetPrefId);
                                    args.put("srcCategoryId", tp.getSrcCategoryId());
                                    args.put("expression", tp.getExpression());

                                    return ExpressionParserInput.builder()
                                            .expression(tp.getExpression())
                                            .additionalArgs(args)
                                            .build();
                                })
                        .collect(Collectors.toList());
        parseExprReq.setParseInputs(inputs);

        List<ParseExpressionResponse> parseExprRes = new ArrayList<>();
        try {
            parseExprRes = ExpressionUtil.parseExpression(parseExprReq);
        } catch (Exception e) {
            logger.error(
                    "Parse expression failed, param: {}, error: {}", parseExprReq, e.getMessage());
        }

        return parseExprRes.stream()
                .map(
                        pr -> {
                            ExpressionParserOutput output = pr.getExpressionParsedResult();
                            Map<String, Object> args = output.getAdditionalArgs();

                            List<SrcPrefItem> srcPrefItems =
                                    output.getSrcProperties().stream()
                                            .map(
                                                    sp -> {
                                                        // If the modelId is 'thisModel', use the
                                                        // target
                                                        // modelId
                                                        // current target modelId with 'thisModel'
                                                        // according to isExprUseCurrentModel flag
                                                        String srcModelId =
                                                                CalcConstant.THIS.equals(
                                                                                sp.getModelId())
                                                                        ? targetModelId
                                                                        : sp.getModelId();

                                                        return SrcPrefItem.builder()
                                                                .modelId(srcModelId)
                                                                .prefName(sp.getPropertyName())
                                                                .build();
                                                    })
                                            .collect(Collectors.toList());
                            boolean isExprUseCurrentModel =
                                    isExprUseCurrentModel(targetModelId, srcPrefItems);

                            return CalcPropertyMeta.builder()
                                    .prefRuleId((String) args.get("prefRuleId"))
                                    .targetCategoryId((String) args.get("targetCategoryId"))
                                    .targetCompId(targetCompId)
                                    .targetPrefId(targetPrefId)
                                    .srcCategoryId((String) args.get("srcCategoryId"))
                                    .expression((String) args.get("expression"))
                                    .srcPrefItems(srcPrefItems)
                                    .isDirectMapping(output.getIsDirectMapping())
                                    .isExprUseCurrentModel(isExprUseCurrentModel)
                                    .build();
                        })
                .collect(Collectors.toList());
    }

    private static boolean isExprUseCurrentModel(
            String targetModelId, List<SrcPrefItem> srcPrefItems) {
        for (SrcPrefItem spi : srcPrefItems) {
            if (targetModelId.equals(spi.getModelId())) {
                return true;
            }
        }
        return false;
    }
}
