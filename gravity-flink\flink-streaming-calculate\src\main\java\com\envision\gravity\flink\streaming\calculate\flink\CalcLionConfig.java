package com.envision.gravity.flink.streaming.calculate.flink;

import java.util.Properties;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Slf4j
public class CalcLionConfig {

    private static final Logger logger = LoggerFactory.getLogger(CalcLionConfig.class);

    private static ConfigCache configCache;

    static {
        configCache = ConfigCache.getInstance();
        configCache.addChange(
                (key, value) -> {
                    Properties properties = new Properties();
                    properties.setProperty(key, value);
                    configCache.setPts(properties);
                });
    }

    private static String getValue(String configName) {
        String val = null;
        try {
            if (configCache != null) {
                val = configCache.getProperty(configName);
            }
        } catch (Exception e) {
            logger.error("lion: get key error, key = " + configName, e);
            return null;
        }
        return val;
    }

    public static String getStringValue(String configName, String defaultVal) {
        String val = getValue(configName);
        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, int defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Integer.parseInt(val);
    }

    public static long getLongValue(String configName, long defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Long.parseLong(val);
    }

    public static boolean getBooleanValue(String configName, boolean defaultVal) {
        String val = getValue(configName);
        return val == null ? defaultVal : Boolean.parseBoolean(val);
    }

    // ===============================================================================================================
    // sql-gateway common configs
    // ===============================================================================================================
    public static String getSqlGatewayJdbcUrl() {
        return getStringValue("gravity-common.sql-gateway.jdbc-url", null);
    }

    public static String getSqlGatewayUserName() {
        return getStringValue("gravity-common.sql-gateway.calculate.username", null);
    }

    public static String getSqlGatewayPassword() {
        return getStringValue("gravity-common.sql-gateway.calculate.password", null);
    }

    // ===============================================================================================================
    // pg common configs
    // ===============================================================================================================
    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static int getPgMaxPoolSize() {
        return getIntValue("gravity-flink.gravity-core.pg.datasource.max.pool.size", 8);
    }

    public static String getPgJdbcUrl() {
        return getStringValue("gravity-common.postgresql.jdbc-url", null);
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.pg.cdc.database", "gravity");
    }

    // ===============================================================================================================
    // Ignite configs
    // ===============================================================================================================
    public static String getIgniteAddress() {
        return getStringValue("gravity-common.ignite.address", null);
    }

    public static String getIgniteJdbcUrl() {
        return getStringValue("gravity-common.ignite.jdbc-url", null);
    }

    public static String getIgniteJdbcDriver() {
        return getStringValue(
                "gravity-common.ignite.jdbc-driver", "org.apache.ignite.IgniteJdbcThinDriver");
    }

    public static String getIgniteUsername() {
        return getStringValue("gravity-common.ignite.username", null);
    }

    public static String getIgnitePassword() {
        return getStringValue("gravity-common.ignite.password", null);
    }

    // ===============================================================================================================
    // Calculate Common configs
    // ===============================================================================================================
    public static String getCalcCommonKafkaServers() {
        return getStringValue("gravity-flink.calc-common.kafka.server.address", "");
    }

    // MEASURE_POINT_CAL_
    // MEASURE_POINT_CAL_OFFLINE
    // TODO 多个 pattern 是否有效
    public static String getCalcCommonKafkaSinkTopicPattern() {
        return getStringValue(
                "gravity-flink.calc-common.kafka.sink.topic.pattern", "MEASURE_POINT_CAL_");
    }

    public static String getCalcFlinkJarPrefix() {
        return getStringValue("gravity-flink.calc-common.jar-prefix", "gravity-calculate");
    }

    public static int getCalcQueryAssetPageSize() {
        return getIntValue("gravity-flink.calc-common.query-asset-page-size", 1000);
    }

    public static int getCalcAssetSplitSize() {
        return getIntValue("gravity-flink.calc-common.asset-split-size", 100);
    }

    public static int getCalcTimeRangeSplitSeconds() {
        return getIntValue("gravity-flink.calc-common.time-range-split-seconds", 3600);
    }

    // ===============================================================================================================
    // Stream flow configs
    // ===============================================================================================================
    public static int getCalcStreamPgCdcWindowBatchSize() {
        return getIntValue("gravity-flink.calc-stream.pg.cdc.window-batch-size", 100);
    }

    public static int getCalcStreamPgCdcWindowTimeoutSeconds() {
        return getIntValue("gravity-flink.calc-stream.pg.cdc.window-timeout-seconds", 10);
    }

    public static String getCalcStreamKafkaSourceConsumerGroup() {
        return getStringValue(
                "gravity-flink.calc-stream.kafka.source.consumer.group", "gravity_calc_stream");
    }

    public static String getCalcStreamKafkaSourceTopicPattern() {
        return getStringValue(
                "gravity-flink.calc-stream.kafka.source.topic.pattern", "MEASURE_POINT_ORIGIN");
    }

    public static int getCalcStreamKafkaSourceBatchSize() {
        return getIntValue("gravity-flink.calc-stream.kafka.batch-size", 1000);
    }

    public static int getCalcStreamKafkaSourceBatchTimeoutSeconds() {
        return getIntValue("gravity-flink.calc-stream.kafka.batch-timeout-seconds", 5);
    }

    public static int getCalcStreamKafkaOffsetWindowTimeSeconds() {
        return getIntValue("gravity-flink.calc-stream.kafka.offset-window-time-seconds", 5);
    }

    public static String getCalcStreamPgCdcSlotName() {
        return getStringValue(
                "gravity-flink.calc-stream.pg.cdc.slot-name", "gravity_calc_stream_pg_cdc");
    }

    // ===============================================================================================================
    // ReCalc flow configs
    // ===============================================================================================================
    public static long getReCalcTimeRangeSeconds() {
        return getLongValue("gravity-flink.recalc.time-range-seconds", 90 * 86400L);
    }

    public static int getReCalcJobTimeSplitSeconds() {
        return getIntValue("gravity-flink.recalc.job-split-seconds", 3600);
    }

    public static int getReCalcJobTaskTimeSplitSeconds() {
        return getIntValue("gravity-flink.recalc.job-task-split-seconds", 600);
    }

    public static int getReCalcSubTaskTimeSplitSeconds() {
        return getIntValue("gravity-flink.recalc.job_", 2);
    }

    public static int getReCalcJobTaskProcessorParallelism() {
        return getIntValue("gravity-flink.recalc.job-task-processor-parallelism", 2);
    }

    public static int getReCalcJobSubmitRetryLimit() {
        return getIntValue("gravity-flink.recalc.job-submit-retry-limit", 3);
    }

    public static int getReCalcJobSubmitRetryIntervalSeconds() {
        return getIntValue("gravity-flink.recalc.job-submit-retry-interval-seconds", 5);
    }

    public static String getReCalcPgCdcSchemaList() {
        return getStringValue("gravity-flink.recalc.pg.cdc.schema-list", "^(o|sysenos2018).*");
    }

    public static int getReCalcPgCdcWindowBatchSize() {
        return getIntValue("gravity-flink.recalc.pg.cdc.window-batch-size", 10);
    }

    public static int getReCalcPgCdcWindowTimeoutSeconds() {
        return getIntValue("gravity-flink.recalc.pg.cdc.window-timeout-seconds", 3);
    }

    public static String getReCalcPgCdcTableList() {
        return getStringValue(
                "gravity-flink.recalc.pg.cdc.table-list", "^o.*\\.(tbl_property_upstream_rule)$");
    }

    public static String getReCalcPgCdcSlotName() {
        return getStringValue("gravity-flink.recalc.pg.cdc.slot-name", "gravity_recalc_pg_cdc");
    }

    public static String getReCalcPgCdcMaxBatchSize() {
        return getStringValue("gravity-flink.recalc.pg.cdc.max-batch-size", "500");
    }

    public static String getReCalcPgCdcMaxQueueSize() {
        return getStringValue("gravity-flink.recalc.pg.cdc.max-queue-size", "1000");
    }

    // ===============================================================================================================
    // Flink Cluster configs for job submission
    // ===============================================================================================================
    public static String getFlinkJobManagerHost() {
        return getStringValue("gravity-flink.cluster.jobmanager.host", "localhost");
    }

    public static int getFlinkJobManagerPort() {
        return getIntValue("gravity-flink.cluster.jobmanager.port", 8081);
    }

    public static String getFlinkJobManagerRestUrl() {
        return getStringValue(
                "gravity-flink.cluster.jobmanager.rest-url",
                "http://" + getFlinkJobManagerHost() + ":" + getFlinkJobManagerPort());
    }

    public static int getFlinkJobSubmitTimeoutSeconds() {
        return getIntValue("gravity-flink.cluster.job-submit-timeout-seconds", 300);
    }

    public static String getFlinkJobMainClass() {
        return getStringValue(
                "gravity-flink.recalc.job-main-class",
                "com.envision.gravity.flink.streaming.calculate.batch.ReCalcBatchJob");
    }

    public static int getFlinkJobParallelism() {
        return getIntValue("gravity-flink.recalc.job-parallelism", 4);
    }

    public static boolean getFlinkJobUsePreUploadedJar() {
        return Boolean.parseBoolean(
                getStringValue("gravity-flink.recalc.use-pre-uploaded-jar", "true"));
    }

    // ===============================================================================================================
    // Meta flow configs
    // ===============================================================================================================
    public static int getCalcMetaLoadPageSize() {
        return getIntValue("gravity-flink.calc-meta.load-page-size", 500);
    }

    public static String getCalcMetaPgCdcSchemaList() {
        return getStringValue("gravity-flink.calc-meta.pg.cdc.schema-list", "^(o|sysenos2018).*");
    }

    public static int getCalcMetaPgCdcWindowBatchSize() {
        return getIntValue("gravity-flink.calc-meta.pg.cdc.window-batch-size", 10);
    }

    public static int getCalcMetaPgCdcWindowTimeoutSeconds() {
        return getIntValue("gravity-flink.calc-meta.pg.cdc.window-timeout-seconds", 3);
    }

    // "^o.*\\.(tbl_pref|object_detail_origin|tbl_edge)$"
    public static String getCalcMetaPgCdcTableList() {
        return getStringValue(
                "gravity-flink.calc-meta.pg.cdc.table-list",
                "^o.*\\.(tbl_property_upstream_rule|tbl_bo_model|tbl_bo_model_comp|tbl_pref|tbl_component|tbl_component_pref)$");
    }

    public static String getCalcMetaPgCdcSlotName() {
        return getStringValue(
                "gravity-flink.calc-meta.pg.cdc.slot-name", "gravity_calc_meta_pg_cdc");
    }

    public static String getCalcMetaPgCdcMaxBatchSize() {
        return getStringValue("gravity-flink.calc-meta.pg.cdc.max-batch-size", "500");
    }

    public static String getCalcMetaPgCdcMaxQueueSize() {
        return getStringValue("gravity-flink.calc-meta.pg.cdc.max-queue-size", "1000");
    }

    // ===============================================================================================================
    // ReCalc Batch Job configs
    // ===============================================================================================================

    public static int getReCalcDefaultParallelism() {
        return getIntValue("gravity-flink.recalc.default-parallelism", 1);
    }

    public static int getReCalcJobTaskProcessorParallelism() {
        return getIntValue("gravity-flink.recalc.job-task-processor.parallelism", 4);
    }

    public static long getReCalcCheckpointIntervalMs() {
        return getLongValue("gravity-flink.recalc.checkpoint-interval-ms", 30000L);
    }

    public static long getReCalcCheckpointTimeoutMs() {
        return getLongValue("gravity-flink.recalc.checkpoint-timeout-ms", 600000L);
    }

    public static int getReCalcCheckpointMaxConcurrent() {
        return getIntValue("gravity-flink.recalc.checkpoint-max-concurrent", 1);
    }

    public static int getReCalcRestartAttempts() {
        return getIntValue("gravity-flink.recalc.restart-attempts", 3);
    }

    public static long getReCalcRestartDelayMs() {
        return getLongValue("gravity-flink.recalc.restart-delay-ms", 10000L);
    }

    public static long getReCalcJobTaskTimeSplitSeconds() {
        return getLongValue("gravity-flink.recalc.task-time-split-seconds", 3600L);
    }

    public static long getTaskCompletionCheckIntervalMs() {
        return getLongValue("gravity-flink.recalc.task-completion-check-interval-ms", 1000L);
    }

    public static String getReCalcKafkaBootstrapServers() {
        return getStringValue("gravity-flink.recalc.kafka.bootstrap-servers", "localhost:9092");
    }

    public static String getReCalcKafkaSinkTopicPattern() {
        return getStringValue("gravity-flink.recalc.kafka.sink.topic-pattern", "MEASURE_POINT_CAL_");
    }

    public static int getCalcQueryAssetPageSize() {
        return getIntValue("gravity-flink.recalc.query-asset-page-size", 1000);
    }

    // ===============================================================================================================
    // AspectCalc Flow configs
    // ===============================================================================================================

    public static int getAspectCalcDefaultParallelism() {
        return getIntValue("gravity-flink.aspect-calc.default-parallelism", 4);
    }

    public static long getJobInfoCheckIntervalMs() {
        return getLongValue("gravity-flink.aspect-calc.job-info-check-interval-ms", 10000L);
    }

    public static int getCalcJobSourceReaderParallelism() {
        return getIntValue("gravity-flink.aspect-calc.calc-job-source-reader.parallelism", 4);
    }

    public static int getAspectCalcJobTaskProcessorParallelism() {
        return getIntValue("gravity-flink.aspect-calc.aspect-calc-job-task-processor.parallelism", 8);
    }

    public static int getKafkaSinkParallelism() {
        return getIntValue("gravity-flink.aspect-calc.kafka-sink.parallelism", 4);
    }

    public static long getAspectCalcCheckpointIntervalMs() {
        return getLongValue("gravity-flink.aspect-calc.checkpoint-interval-ms", 30000L);
    }

    public static long getAspectCalcCheckpointTimeoutMs() {
        return getLongValue("gravity-flink.aspect-calc.checkpoint-timeout-ms", 600000L);
    }

    public static int getAspectCalcCheckpointMaxConcurrent() {
        return getIntValue("gravity-flink.aspect-calc.checkpoint-max-concurrent", 1);
    }

    public static int getAspectCalcRestartAttempts() {
        return getIntValue("gravity-flink.aspect-calc.restart-attempts", 3);
    }

    public static long getAspectCalcRestartDelayMs() {
        return getLongValue("gravity-flink.aspect-calc.restart-delay-ms", 10000L);
    }

    public static int getMaxJobsPerReader() {
        return getIntValue("gravity-flink.aspect-calc.max-jobs-per-reader", 5);
    }

    public static int getTaskBatchSize() {
        return getIntValue("gravity-flink.aspect-calc.task-batch-size", 10);
    }

    public static String getAspectCalcKafkaBootstrapServers() {
        return getStringValue("gravity-flink.aspect-calc.kafka.bootstrap-servers", "localhost:9092");
    }

    public static String getAspectCalcKafkaSinkTopicPattern() {
        return getStringValue("gravity-flink.aspect-calc.kafka.sink.topic-pattern", "MEASURE_POINT_CAL_");
    }
}
