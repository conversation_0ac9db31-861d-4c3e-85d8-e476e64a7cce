gravity-common.ignite.address=*************:10800
gravity-common.ignite.jdbc-url=jdbc:ignite:thin://*************:10800?distributedJoins=true&partitionAwareness=true
gravity-common.ignite.jdbc-driver=org.apache.ignite.IgniteJdbcThinDriver
gravity-common.ignite.username=ignite
gravity-common.ignite.password=ignite

gravity-common.postgresql.jdbc-driver=org.postgresql.Driver
gravity-common.postgresql.jdbc-url=jdbc:postgresql://*************:5432/gravity
gravity-common.postgresql.username=postgres
gravity-common.postgresql.password=Envisi0n4321!
gravity-common.postgresql.hostname=*************
gravity-common.postgresql.port=5432


# ===============================================================================================================
# Meta Flow Configuration
# ===============================================================================================================

gravity-flink.calc-meta.load-page-size=1000

gravity-flink.calc-meta.pg.cdc.schema-list=^(o|sysenos2018).*
gravity-flink.calc-meta.pg.cdc.table-list=^o.*\.(tbl_property_upstream_rule|tbl_bo_model|tbl_bo_model_comp|tbl_pref|tbl_component|tbl_component_pref)$
gravity-flink.calc-meta.pg.cdc.slot-name=gravity_calc_meta_pg_cdc_local
### CDC?????????
gravity-flink.calc-meta.pg.cdc.window-batch-size=10
gravity-flink.calc-meta.pg.cdc.window-timeout-seconds=3

gravity-flink.calc-meta.pg.cdc.max-batch-size=500
gravity-flink.calc-meta.pg.cdc.max-queue-size=1000

# ===============================================================================================================
# Stream Flow Configuration
# ===============================================================================================================

gravity-flink.calc-stream.window-batch-size=2
gravity-flink.calc-stream.window-timeout-seconds=3

gravity-flink.calc-stream.kafka.server.address=*************:9092
gravity-flink.calc-stream.kafka.source.consumer.group=gravity_calc_stream
## MEASURE_POINT_ORIGIN_o17186913277371853
## MEASURE_POINT_ORIGIN_o17206124543051682
## gravity-flink.calc-stream.kafka.source.topic.pattern=MEASURE_POINT_ORIGIN_.*
gravity-flink.calc-stream.kafka.source.topic.pattern=MEASURE_POINT_ORIGIN_GRAVITY_.*
gravity-flink.calc-stream.kafka.batch-size=2
gravity-flink.calc-stream.kafka.batch-timeout-seconds=3
## TODO multi patterns
## MEASURE_POINT_CAL_OFFLINE_ | MEASURE_POINT_CAL_
gravity-flink.calc-stream.kafka.sink.topic.pattern=MEASURE_POINT_CAL_

gravity-flink.calc-stream.kafka.offset-window-time-seconds=3
gravity-flink.calc-stream.pg.cdc.slot-name=gravity_calc_stream_pg_cdc

## sql gateway
gravity-common.sql-gateway.jdbc-url=jdbc:mysql://*************:13306?connectionTimeZone=UTC&characterEncoding=utf8&autoReconnect=true&databaseTerm=SCHEMA&connectTimeout=30000&socket_timeout=120000&useSSL=false
## gravity-common.sql-gateway.jdbc-url=************************************************************************************************************************************************************************
gravity-common.sql-gateway.calculate.username=demo
gravity-common.sql-gateway.calculate.password=4BF5cj


# ===============================================================================================================
# ReCalc Flow Configuration
# ===============================================================================================================

# Job submission retry configuration
gravity-flink.recalc.job-submit-retry-limit=3
gravity-flink.recalc.job-submit-retry-interval-seconds=5

# Time range for recalculation (in days)
gravity-flink.recalc.time-range-days=90

# PostgreSQL CDC configuration for ReCalc
gravity-flink.recalc.pg.cdc.schema-list=^(o|sysenos2018).*
gravity-flink.recalc.pg.cdc.window-batch-size=10
gravity-flink.recalc.pg.cdc.window-timeout-seconds=3
gravity-flink.recalc.pg.cdc.table-list=^o.*\\.(tbl_property_upstream_rule)$
gravity-flink.recalc.pg.cdc.slot-name=gravity_recalc_pg_cdc
gravity-flink.recalc.pg.cdc.max-batch-size=500
gravity-flink.recalc.pg.cdc.max-queue-size=1000

# ===============================================================================================================
# Flink Cluster Configuration for Job Submission
# ===============================================================================================================

# Flink JobManager configuration
gravity-flink.cluster.jobmanager.host=flink-gravity.apaas-beta30.eniot.io
gravity-flink.cluster.jobmanager.port=80
gravity-flink.cluster.jobmanager.rest-url=http://flink-gravity.apaas-beta30.eniot.io

# Job submission configuration
gravity-flink.cluster.job-submit-timeout-seconds=300

# ReCalc batch job configuration
gravity-flink.recalc.job-jar-id=gravity-calculate-1.0.0-20250730-SNAPSHOT.jar
gravity-flink.recalc.job-main-class=com.envision.gravity.flink.streaming.calculate.batch.ReCalcBatchJob
gravity-flink.recalc.job-parallelism=2
gravity-flink.recalc.checkpoint-dir=file:///tmp/flink-checkpoints

# ===============================================================================================================
# Environment-specific Configuration Examples
# ===============================================================================================================

# Development Environment
# gravity-flink.cluster.jobmanager.host=dev-flink-jobmanager
# gravity-flink.cluster.jobmanager.port=8081
# gravity-flink.recalc.job-jar-id=recalc-batch-job-dev.jar
# gravity-flink.recalc.checkpoint-dir=hdfs://dev-hdfs:9000/flink/checkpoints

# Production Environment
# gravity-flink.cluster.jobmanager.host=prod-flink-jobmanager
# gravity-flink.cluster.jobmanager.port=8081
# gravity-flink.recalc.job-jar-id=recalc-batch-job-prod.jar
# gravity-flink.recalc.checkpoint-dir=hdfs://prod-hdfs:9000/flink/checkpoints
# gravity-flink.recalc.job-parallelism=8

# Kubernetes Environment
# gravity-flink.cluster.jobmanager.host=flink-jobmanager-service
# gravity-flink.cluster.jobmanager.port=8081
# gravity-flink.recalc.job-jar-id=recalc-batch-job-k8s.jar
# gravity-flink.recalc.checkpoint-dir=s3://flink-checkpoints/recalc

# ===============================================================================================================
# Advanced Configuration Options
# ===============================================================================================================

# Flink job resource configuration
# gravity-flink.recalc.job-memory-mb=2048
# gravity-flink.recalc.job-cpu-cores=2
# gravity-flink.recalc.job-max-parallelism=128

# Checkpoint configuration
# gravity-flink.recalc.checkpoint-interval-ms=60000
# gravity-flink.recalc.checkpoint-timeout-ms=600000
# gravity-flink.recalc.checkpoint-min-pause-ms=5000

# Restart strategy configuration
# gravity-flink.recalc.restart-strategy=fixed-delay
# gravity-flink.recalc.restart-attempts=3
# gravity-flink.recalc.restart-delay-seconds=10

# Network configuration
# gravity-flink.cluster.connection-timeout-ms=30000
# gravity-flink.cluster.socket-timeout-ms=60000
# gravity-flink.cluster.max-connections=10

# Security configuration (if needed)
# gravity-flink.cluster.ssl-enabled=false
# gravity-flink.cluster.ssl-keystore-path=
# gravity-flink.cluster.ssl-keystore-password=
# gravity-flink.cluster.ssl-truststore-path=
# gravity-flink.cluster.ssl-truststore-password=

# Authentication configuration (if needed)
# gravity-flink.cluster.auth-enabled=false
# gravity-flink.cluster.auth-username=
# gravity-flink.cluster.auth-password=
# gravity-flink.cluster.auth-token=