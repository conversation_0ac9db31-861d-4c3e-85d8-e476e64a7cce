package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.id.IdReq;
import com.envision.gravity.low.level.api.rest.service.IdService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/** @Author: qi.jiang2 @Date: 2024/02/28 11:00 @Description: */
@Slf4j
@Api(tags = "Id")
@RestController
@Validated
@RequestMapping("/ids")
public class IdController {

    @Resource private IdService idService;

    @PostMapping
    public ResponseResult<?> getIdList(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @Valid @RequestBody IdReq idReq) {

        log.info("Start get id list. count: {}", idReq.getCount());
        return idService.getIdList(idReq.getCount());
    }
}
