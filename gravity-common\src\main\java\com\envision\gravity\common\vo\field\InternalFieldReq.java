package com.envision.gravity.common.vo.field;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/** <AUTHOR> 2024/11/28 */
@Data
@EqualsAndHashCode(callSuper = true) // Calls super equals/hashCode
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class InternalFieldReq extends FieldReq {

    private String rawFieldId;

    private Integer fieldIndex;

    private Boolean horizontal;
}
