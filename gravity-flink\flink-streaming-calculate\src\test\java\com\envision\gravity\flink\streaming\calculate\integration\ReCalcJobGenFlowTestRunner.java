package com.envision.gravity.flink.streaming.calculate.integration;

import com.envision.gravity.flink.streaming.calculate.ReCalcJobGenFlow;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Test runner for ReCalc Job Generation Flow integration testing This class starts the
 * ReCalcJobGenFlow in a separate thread for testing
 *
 * <AUTHOR>
 */
public class ReCalcJobGenFlowTestRunner {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobGenFlowTestRunner.class);

    private CompletableFuture<Void> flowExecution;
    private volatile boolean isRunning = false;

    /** Start the ReCalc Job Generation Flow for testing */
    public void startFlow() throws Exception {
        if (isRunning) {
            logger.warn("ReCalc Job Generation Flow is already running");
            return;
        }

        logger.info("Starting ReCalc Job Generation Flow for integration testing...");

        // Start the flow in a separate thread
        flowExecution =
                CompletableFuture.runAsync(
                        () -> {
                            try {
                                isRunning = true;

                                // Create Flink execution environment
                                StreamExecutionEnvironment env =
                                        StreamExecutionEnvironment.getExecutionEnvironment();

                                // Set parallelism for testing
                                env.setParallelism(1);

                                // Enable checkpointing for testing
                                env.enableCheckpointing(5000);

                                // Initialize ReCalc Job Generation Flow
                                ReCalcJobGenFlow.initReCalcJobGenFlow(env);

                                logger.info(
                                        "ReCalc Job Generation Flow initialized, starting execution...");

                                // Execute the flow
                                env.execute("ReCalc Job Generation Flow - Integration Test");

                            } catch (Exception e) {
                                logger.error(
                                        "Error in ReCalc Job Generation Flow execution: {}",
                                        e.getMessage(),
                                        e);
                                throw new RuntimeException(e);
                            } finally {
                                isRunning = false;
                            }
                        });

        // Wait a bit for the flow to start
        Thread.sleep(5000);

        if (isRunning) {
            logger.info("ReCalc Job Generation Flow started successfully");
        } else {
            throw new RuntimeException("Failed to start ReCalc Job Generation Flow");
        }
    }

    /** Stop the ReCalc Job Generation Flow */
    public void stopFlow() throws Exception {
        if (!isRunning) {
            logger.warn("ReCalc Job Generation Flow is not running");
            return;
        }

        logger.info("Stopping ReCalc Job Generation Flow...");

        if (flowExecution != null) {
            // Cancel the flow execution
            flowExecution.cancel(true);

            try {
                // Wait for graceful shutdown
                flowExecution.get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                logger.warn("Flow did not stop gracefully: {}", e.getMessage());
            }
        }

        isRunning = false;
        logger.info("ReCalc Job Generation Flow stopped");
    }

    /** Check if the flow is running */
    public boolean isRunning() {
        return isRunning;
    }

    /** Wait for the flow to be ready for testing */
    public void waitForReady(int timeoutSeconds) throws Exception {
        int attempts = 0;

        while (attempts < timeoutSeconds && !isRunning) {
            Thread.sleep(1000);
            attempts++;

            if (attempts % 5 == 0) {
                logger.debug(
                        "Waiting for ReCalc Job Generation Flow to be ready... ({}s)", attempts);
            }
        }

        if (!isRunning) {
            throw new RuntimeException(
                    "ReCalc Job Generation Flow did not start within "
                            + timeoutSeconds
                            + " seconds");
        }

        // Additional wait for CDC source to be ready
        logger.info(
                "ReCalc Job Generation Flow is ready, waiting for CDC source initialization...");
        Thread.sleep(5000);
    }

    /** Validate configuration for integration testing */
    public void validateConfiguration() {
        logger.info("Validating integration test configuration...");

        // Validate required configuration
        String jdbcUrl = CalcLionConfig.getPgJdbcUrl();
        String username = CalcLionConfig.getPgUsername();
        String flinkHost = CalcLionConfig.getFlinkJobManagerHost();
        int flinkPort = CalcLionConfig.getFlinkJobManagerPort();

        if (jdbcUrl == null || jdbcUrl.isEmpty()) {
            throw new RuntimeException("PostgreSQL JDBC URL is not configured");
        }

        if (username == null || username.isEmpty()) {
            throw new RuntimeException("PostgreSQL username is not configured");
        }

        if (flinkHost == null || flinkHost.isEmpty()) {
            throw new RuntimeException("Flink JobManager host is not configured");
        }

        if (flinkPort <= 0) {
            throw new RuntimeException("Flink JobManager port is not configured");
        }

        logger.info("Configuration validation passed:");
        logger.info("  PostgreSQL: {}", jdbcUrl);
        logger.info("  Flink: {}:{}", flinkHost, flinkPort);
        logger.info("  CDC Schema: {}", CalcLionConfig.getReCalcPgCdcSchemaList());
        logger.info("  CDC Tables: {}", CalcLionConfig.getReCalcPgCdcTableList());
    }

    /** Get flow execution status */
    public String getExecutionStatus() {
        if (!isRunning) {
            return "STOPPED";
        }

        if (flowExecution == null) {
            return "STARTING";
        }

        if (flowExecution.isDone()) {
            if (flowExecution.isCompletedExceptionally()) {
                return "FAILED";
            } else {
                return "COMPLETED";
            }
        }

        return "RUNNING";
    }

    /** Get any execution exception */
    public Throwable getExecutionException() {
        if (flowExecution != null && flowExecution.isCompletedExceptionally()) {
            try {
                flowExecution.get();
            } catch (Exception e) {
                return e.getCause();
            }
        }
        return null;
    }
}
