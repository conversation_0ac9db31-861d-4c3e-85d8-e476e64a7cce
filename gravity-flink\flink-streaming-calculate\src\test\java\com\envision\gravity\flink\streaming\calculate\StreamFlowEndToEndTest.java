package com.envision.gravity.flink.streaming.calculate;

import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.integration.CalcRuleManager;
import com.envision.gravity.flink.streaming.calculate.integration.CalcRuleManager.CalcRule;
import com.envision.gravity.flink.streaming.calculate.integration.TestMessageFactory;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** StreamFlow 端到端集成测试 测试完整的消息流：Kafka输入 -> StreamFlow处理 -> Kafka输出 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StreamFlowEndToEndTest {
    private static final Logger logger = LoggerFactory.getLogger(StreamFlowEndToEndTest.class);

    // 使用公共测试常量
    private static final String TEST_ORG_ID = "o17186913277371853";
    private static KafkaTopicManager topicManager;
    private static String inputTopic;
    private static String outputTopic;
    private static Thread streamFlowThread;

    // 计算规则管理
    private static CalcRuleManager ruleManager;
    private static List<CalcRule> testRules;

    @BeforeAll
    static void setUp() {
        System.out.println("\n" + "============================================================");
        System.out.println("🚀 StreamFlow 端到端集成测试开始");
        System.out.println("============================================================");

        // 初始化 topic 管理器
        System.out.println("📋 步骤 1/4: 初始化 Kafka Topic 管理器...");
        topicManager = new KafkaTopicManager();
        inputTopic = KafkaTopicManager.getTestInputTopic(TEST_ORG_ID);
        outputTopic = KafkaTopicManager.getTestOutputTopic(TEST_ORG_ID);

        // 创建测试 topics
        System.out.println("📋 步骤 2/4: 创建测试 Topics...");
        System.out.println("  输入 Topic: " + inputTopic);
        System.out.println("  输出 Topic: " + outputTopic);
        boolean topicsCreated = topicManager.createTestTopics(TEST_ORG_ID);
        Assertions.assertTrue(topicsCreated, "测试 topics 创建失败");

        // 初始化计算规则管理器
        System.out.println("📋 步骤 3/5: 初始化计算规则管理器...");
        // initializeRuleManager();

        // 启动 StreamFlow
        System.out.println("📋 步骤 4/5: 启动 StreamFlow...");
        startStreamFlow();

        // 等待 StreamFlow 启动
        System.out.println("📋 步骤 5/5: 等待 StreamFlow 启动完成...");
        try {
            Thread.sleep(30000); // 等待30秒让StreamFlow完全启动
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        System.out.println("✅ StreamFlow 启动完成，开始执行测试");
        System.out.println("============================================================\n");
    }

    @AfterAll
    static void tearDown() {
        System.out.println("\n" + "============================================================");
        System.out.println("🧹 清理测试环境");
        System.out.println("============================================================");

        // 停止 StreamFlow
        System.out.println("🛑 停止 StreamFlow...");
        if (streamFlowThread != null && streamFlowThread.isAlive()) {
            streamFlowThread.interrupt();
            try {
                streamFlowThread.join(5000); // 等待5秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Clean up calculation rules
        System.out.println("🧹 Cleaning up calculation rules...");
        if (ruleManager != null && testRules != null) {
            // ruleManager.cleanupRules(testRules);
            ruleManager.close(); // Close the data source
        }

        // Close topic manager
        System.out.println("🔌 Closing Kafka connections...");
        if (topicManager != null) {
            topicManager.close();
        }

        System.out.println("✅ End-to-end integration test completed");
        System.out.println("============================================================\n");
    }

    private static void startStreamFlow() {
        System.out.println("  🔄 Starting StreamFlow background process...");
        streamFlowThread =
                new Thread(
                        () -> {
                            try {
                                StreamFlow.main(new String[] {"--local", "true"});
                            } catch (Exception e) {
                                System.err.println(
                                        "❌ StreamFlow execution failed: " + e.getMessage());
                            }
                        });
        streamFlowThread.setDaemon(true);
        streamFlowThread.start();
        System.out.println("  ✅ StreamFlow background process started");
    }

    @Test
    @Order(1)
    void testTopicCreation() {
        logger.info("=== Test 1: Verify Topic Creation ===");

        Assertions.assertTrue(
                topicManager.topicExists(inputTopic), "Input topic does not exist: " + inputTopic);
        Assertions.assertTrue(
                topicManager.topicExists(outputTopic),
                "Output topic does not exist: " + outputTopic);

        logger.info("✅ Topic creation verification passed");
    }

    //    @Test
    //    @Order(2)
    void testConfigurableEndToEndWithFastFail() {
        logger.info("=== Test 7: Configurable End-to-End Test with Fast Fail (JSON Format) ===");

        try {
            // Load test scenarios from JSON
            TestMessageFactory messageFactory = new TestMessageFactory();
            List<TestMessageFactory.YamlTestScenario> jsonScenarios =
                    messageFactory.loadScenariosFromYaml("test-data/test-messages2.json");

            logger.info(
                    "📋 Loaded {} JSON test scenarios for fast fail testing", jsonScenarios.size());

            ObjectMapper objectMapper = new ObjectMapper();
            int successCount = 0;
            int totalScenarios = 0;

            // Count total expected outputs across all scenarios
            for (TestMessageFactory.YamlTestScenario jsonScenario : jsonScenarios) {
                totalScenarios += jsonScenario.getExpectedOutputMessages().size();
            }

            logger.info("📊 Total expected outputs to test: {}", totalScenarios);

            int testIndex = 0;
            for (TestMessageFactory.YamlTestScenario jsonScenario : jsonScenarios) {
                logger.info("🧪 Testing JSON scenario: {}", jsonScenario.getName());
                logger.info("📝 Description: {}", jsonScenario.getDescription());

                // Set up topics for this scenario
                String scenarioInputTopic = messageFactory.getInputTopicName(jsonScenario);
                String scenarioOutputTopic = messageFactory.getOutputTopicName(jsonScenario);

                logger.info("📡 Input topic: {}", scenarioInputTopic);
                logger.info("📡 Output topic: {}", scenarioOutputTopic);

                // Test each expected output
                for (int outputIndex = 0;
                        outputIndex < jsonScenario.getExpectedOutputMessages().size();
                        outputIndex++) {
                    testIndex++;
                    String expectedOutputMessage =
                            jsonScenario.getExpectedOutputMessages().get(outputIndex);
                    JsonNode expectedOutputJson =
                            jsonScenario.getExpectedOutputJsons().get(outputIndex);

                    logger.info(
                            "🔍 Testing output {}/{} for scenario '{}' (test {}/{})",
                            outputIndex + 1,
                            jsonScenario.getExpectedOutputMessages().size(),
                            jsonScenario.getName(),
                            testIndex,
                            totalScenarios);

                    // Determine expected message type based on content
                    String expectedMessageType = determineExpectedMessageType(expectedOutputJson);
                    logger.info("🎯 Expected message type: {}", expectedMessageType);

                    // Test with proper timing: start consumer first, then send message
                    String outputMessage =
                            testScenarioWithProperTiming(
                                    jsonScenario,
                                    scenarioInputTopic,
                                    scenarioOutputTopic,
                                    expectedMessageType);

                    if (outputMessage == null) {
                        logger.error(
                                "❌ FAST FAIL: No output received for scenario: {} (output {}, type: {})",
                                jsonScenario.getName(),
                                outputIndex + 1,
                                expectedMessageType);
                        Assertions.fail(
                                "No output message received for scenario: "
                                        + jsonScenario.getName());
                        return; // Fast fail - exit immediately
                    }

                    // Validate output against expected result
                    boolean isValid =
                            validateOutputMessage(outputMessage, expectedOutputJson, objectMapper);

                    if (!isValid) {
                        logger.error(
                                "❌ FAST FAIL: Output validation failed for scenario: {} (output {})",
                                jsonScenario.getName(),
                                outputIndex + 1);
                        logger.error("Expected: {}", expectedOutputMessage);
                        logger.error("Actual: {}", outputMessage);
                        Assertions.fail(
                                "Output validation failed for scenario: " + jsonScenario.getName());
                        return; // Fast fail - exit immediately
                    }

                    successCount++;
                    logger.info(
                            "✅ Test {}/{} passed: {} (output {})",
                            testIndex,
                            totalScenarios,
                            jsonScenario.getName(),
                            outputIndex + 1);
                }
            }

            logger.info("🎉 All {} tests passed successfully!", successCount);
            Assertions.assertEquals(totalScenarios, successCount, "All tests should pass");

        } catch (Exception e) {
            logger.error("❌ FAST FAIL: Test execution failed with exception", e);
            Assertions.fail("Test execution failed: " + e.getMessage());
        }
    }

    /** Test scenario with proper timing: start consumer first, then send message */
    private String testScenarioWithProperTiming(
            TestMessageFactory.YamlTestScenario jsonScenario,
            String inputTopic,
            String outputTopic,
            String expectedMessageType) {

        logger.info("🔄 Starting consumer-first test for scenario: {}", jsonScenario.getName());

        // Create consumer properties
        Properties consumerProps = new Properties();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                CalcLionConfig.getCalcCommonKafkaServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG, "test-consumer-" + System.currentTimeMillis());
        consumerProps.put(
                ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        consumerProps.put(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        // Use "latest" since we'll start consumer before sending message
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
        consumerProps.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");

        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps)) {
            // Step 1: Start consumer and subscribe to output topic
            consumer.subscribe(Collections.singletonList(outputTopic));
            logger.info("📡 Consumer subscribed to output topic: {}", outputTopic);

            // Step 2: Wait for consumer to be ready (poll once to trigger partition assignment)
            logger.info("⏳ Waiting for consumer to be ready...");
            consumer.poll(Duration.ofMillis(2000)); // Initial poll to trigger partition assignment
            logger.info("✅ Consumer is ready");

            // Step 3: Send input message
            String inputMessage = jsonScenario.getInputMessage();
            logger.info("🚀 Sending input message to topic: {}", inputTopic);
            boolean sent = sendMessage(inputTopic, inputMessage);

            if (!sent) {
                logger.error("❌ Failed to send input message");
                return null;
            }

            // Step 4: Wait for output message
            logger.info("🔍 Waiting for output message of type: {}", expectedMessageType);
            long startTime = System.currentTimeMillis();
            long timeoutMs = 60 * 1000L; // 60 seconds timeout

            while (System.currentTimeMillis() - startTime < timeoutMs) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                for (ConsumerRecord<String, String> record : records) {
                    String message = record.value();
                    logger.info("📨 Received output message: {}", message);

                    // Check if this message matches the expected type
                    if (message.contains("orgId") && message.contains("payload")) {
                        boolean isDirectMapping = isDirectMappingMessage(record);

                        if (expectedMessageType == null) {
                            // Return any valid message if no specific type requested
                            logger.info("✅ Found valid output message, returning: {}", message);
                            return message;
                        } else if ("direct".equals(expectedMessageType) && isDirectMapping) {
                            logger.info("✅ Found direct mapping message, returning: {}", message);
                            return message;
                        } else if ("non-direct".equals(expectedMessageType) && !isDirectMapping) {
                            logger.info(
                                    "✅ Found non-direct mapping message, returning: {}", message);
                            return message;
                        } else {
                            logger.info(
                                    "📝 Message type mismatch. Expected: {}, Actual: {}",
                                    expectedMessageType,
                                    isDirectMapping ? "direct" : "non-direct");
                            // Continue waiting for the right message type
                        }
                    }
                }
            }

            logger.warn("⚠️ No matching output message received within timeout");
            return null;

        } catch (Exception e) {
            logger.error("❌ Error in testScenarioWithProperTiming", e);
            return null;
        }
    }

    private boolean sendMessage(String topic, String message) {
        logger.info("🚀 Attempting to send message to topic: {}", topic);
        logger.info("📝 Message content: {}", message);

        Properties props = new Properties();
        props.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                CalcLionConfig.getCalcCommonKafkaServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        logger.info("🔧 Kafka bootstrap servers: {}", CalcLionConfig.getCalcCommonKafkaServers());

        try (KafkaProducer<String, String> producer = new KafkaProducer<>(props)) {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);
            logger.info("📤 Sending message to Kafka...");
            producer.send(record).get(10, TimeUnit.SECONDS);
            logger.info("✅ Message sent successfully to topic: {}", topic);
            return true;
        } catch (Exception e) {
            logger.error("❌ Failed to send message to topic: {}", topic, e);
            return false;
        }
    }

    private String waitForOutputMessage(int timeoutSeconds) {
        return waitForOutputMessage(timeoutSeconds, outputTopic);
    }

    private String waitForOutputMessage(int timeoutSeconds, String topicName) {
        return waitForOutputMessage(timeoutSeconds, topicName, null);
    }

    private String waitForOutputMessage(int timeoutSeconds, String topicName, String messageType) {
        Properties props = new Properties();
        props.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                CalcLionConfig.getCalcCommonKafkaServers());
        // Use a fixed consumer group ID to maintain offset state
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "test-consumer-fixed-group");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        // Use "latest" to only read new messages, not old ones
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        // Enable auto commit to automatically commit offsets
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");

        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
            consumer.subscribe(Collections.singletonList(topicName));

            long startTime = System.currentTimeMillis();
            long timeoutMs = timeoutSeconds * 1000L;
            List<String> receivedMessages = new ArrayList<>();

            logger.info(
                    "🔍 Waiting for output message from topic: {}, messageType: {}",
                    topicName,
                    messageType);

            while (System.currentTimeMillis() - startTime < timeoutMs) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                for (ConsumerRecord<String, String> record : records) {
                    String message = record.value();
                    logger.info("📨 Received output message: {}", message);
                    receivedMessages.add(message);

                    // Check if this message matches the expected type
                    if (message.contains("orgId") && message.contains("payload")) {
                        boolean isDirectMapping = isDirectMappingMessage(record);

                        if (messageType == null) {
                            // Return any valid message if no specific type requested
                            logger.info("✅ Found valid output message, returning: {}", message);
                            return message;
                        } else if ("direct".equals(messageType) && isDirectMapping) {
                            logger.info("✅ Found direct mapping message, returning: {}", message);
                            return message;
                        } else if ("non-direct".equals(messageType) && !isDirectMapping) {
                            logger.info(
                                    "✅ Found non-direct mapping message, returning: {}", message);
                            return message;
                        } else {
                            logger.info(
                                    "📝 Message type mismatch. Expected: {}, Actual: {}",
                                    messageType,
                                    isDirectMapping ? "direct" : "non-direct");
                        }
                    }
                }

                // Continue waiting if we haven't found the right message type
                if (messageType != null) {
                    continue;
                }

                // If no specific type requested and we have messages, return the most recent one
                if (!receivedMessages.isEmpty()) {
                    String lastMessage = receivedMessages.get(receivedMessages.size() - 1);
                    logger.info("📤 Returning most recent message: {}", lastMessage);
                    return lastMessage;
                }
            }

            logger.warn(
                    "⚠️ No output message received within {} seconds from topic: {}, messageType: {}",
                    timeoutSeconds,
                    topicName,
                    messageType);
            return null;
        } catch (Exception e) {
            logger.error("❌ Error while waiting for output message from topic: {}", topicName, e);
            return null;
        }
    }

    /** Determine the expected message type based on the expected output JSON */
    private String determineExpectedMessageType(JsonNode expectedOutputJson) {
        try {
            if (expectedOutputJson.has("payload") && expectedOutputJson.get("payload").isArray()) {
                JsonNode payload = expectedOutputJson.get("payload").get(0);
                if (payload.has("points")) {
                    JsonNode points = payload.get("points");
                    Iterator<String> fieldNames = points.fieldNames();

                    // Check if any point name contains calculation indicators
                    while (fieldNames.hasNext()) {
                        String pointName = fieldNames.next();
                        if (pointName.contains("_Plus")
                                || pointName.contains("_Calc")
                                || pointName.contains("_Minus")
                                || pointName.contains("_Multiply")
                                || pointName.contains("Efficiency")
                                || pointName.contains("Ratio")
                                || pointName.contains("Index")
                                || pointName.contains("Doubled")) {
                            return "non-direct"; // This is a non-direct mapping message
                        }
                    }
                }
            }

            return "direct"; // This is a direct mapping message
        } catch (Exception e) {
            logger.warn("Failed to parse expected output for type detection: {}", e.getMessage());
            return "direct"; // Default to direct mapping if parsing fails
        }
    }

    /** Determine if a message is a direct mapping message by checking the Kafka header */
    private boolean isDirectMappingMessage(ConsumerRecord<String, String> record) {
        try {
            // Check for directMapping header
            org.apache.kafka.common.header.Header directMappingHeader =
                    record.headers().lastHeader("directMapping");

            if (directMappingHeader != null) {
                String headerValue = new String(directMappingHeader.value());
                boolean isDirectMapping = "1".equals(headerValue);
                logger.debug(
                        "Found directMapping header: {}, isDirectMapping: {}",
                        headerValue,
                        isDirectMapping);
                return isDirectMapping;
            } else {
                logger.debug("No directMapping header found, defaulting to non-direct mapping");
                return false; // Default to non-direct mapping if no header found
            }
        } catch (Exception e) {
            logger.warn("Failed to read directMapping header: {}", e.getMessage());
            return false; // Default to non-direct mapping if header reading fails
        }
    }

    /**
     * Validate output message against expected result
     *
     * @param actualOutput The actual output message received
     * @param expectedOutput The expected output JSON node
     * @param objectMapper JSON object mapper
     * @return true if validation passes, false otherwise
     */
    private boolean validateOutputMessage(
            String actualOutput, JsonNode expectedOutput, ObjectMapper objectMapper) {
        try {
            JsonNode actualJson = objectMapper.readTree(actualOutput);

            // Validate basic structure
            if (!actualJson.has("orgId")
                    || !actualJson.has("modelId")
                    || !actualJson.has("payload")) {
                logger.error("Output message missing required fields: orgId, modelId, or payload");
                return false;
            }

            // Validate orgId
            String actualOrgId = actualJson.get("orgId").asText();
            String expectedOrgId = expectedOutput.get("orgId").asText();
            if (!actualOrgId.equals(expectedOrgId)) {
                logger.error(
                        "OrgId mismatch. Expected: {}, Actual: {}", expectedOrgId, actualOrgId);
                return false;
            }

            // Validate modelId
            String actualModelId = actualJson.get("modelId").asText();
            String expectedModelId = expectedOutput.get("modelId").asText();
            if (!actualModelId.equals(expectedModelId)) {
                logger.error(
                        "ModelId mismatch. Expected: {}, Actual: {}",
                        expectedModelId,
                        actualModelId);
                return false;
            }

            // Validate payload structure - payload is an array
            JsonNode actualPayload = actualJson.get("payload");
            JsonNode expectedPayload = expectedOutput.get("payload");

            if (!actualPayload.isArray() || actualPayload.size() == 0) {
                logger.error("Payload is not an array or is empty");
                return false;
            }

            if (!expectedPayload.isArray() || expectedPayload.size() == 0) {
                logger.error("Expected payload is not an array or is empty");
                return false;
            }

            // Get the first element from both arrays
            JsonNode actualPayloadItem = actualPayload.get(0);
            JsonNode expectedPayloadItem = expectedPayload.get(0);

            if (!actualPayloadItem.has("assetId") || !actualPayloadItem.has("points")) {
                logger.error("Payload item missing required fields: assetId or points");
                return false;
            }

            // Validate assetId
            String actualAssetId = actualPayloadItem.get("assetId").asText();
            String expectedAssetId = expectedPayloadItem.get("assetId").asText();
            if (!actualAssetId.equals(expectedAssetId)) {
                logger.error(
                        "AssetId mismatch. Expected: {}, Actual: {}",
                        expectedAssetId,
                        actualAssetId);
                return false;
            }

            // Validate points values (note: it's "points" not "measurepoints")
            JsonNode actualPoints = actualPayloadItem.get("points");
            JsonNode expectedPoints = expectedPayloadItem.get("points");

            Iterator<String> fieldNames = expectedPoints.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                if (!actualPoints.has(fieldName)) {
                    logger.error("Missing expected point: {}", fieldName);
                    return false;
                }

                double expectedValue = expectedPoints.get(fieldName).asDouble();
                double actualValue = actualPoints.get(fieldName).asDouble();

                // Allow small floating point differences
                if (Math.abs(expectedValue - actualValue) > 0.0001) {
                    logger.error(
                            "Point value mismatch for {}: Expected: {}, Actual: {}",
                            fieldName,
                            expectedValue,
                            actualValue);
                    return false;
                }
            }

            logger.info("✅ Output validation passed");
            return true;

        } catch (Exception e) {
            logger.error("Error validating output message", e);
            return false;
        }
    }
}
