package com.envision.gravity.low.level.api.sql.table.cache.cfg;

import com.envision.gravity.low.level.api.sql.table.CacheCfgTableInfo;

import java.util.*;


import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.QueryIndexType;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblObjPointPartCfg implements CacheCfgTableInfo {
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("SERIES_ID");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("SERIES_ID");
        NOT_NULL_FIELDS.add("SYSTEM_ID");
        NOT_NULL_FIELDS.add("FIELD_INDEX");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("SYSTEM_ID").setInlineSize(25),
                        new QueryIndex("FIELD_INDEX").setInlineSize(15),
                        new QueryIndex(
                                        Arrays.asList("SYSTEM_ID", "FIELD_INDEX"),
                                        QueryIndexType.SORTED)
                                .setInlineSize(35));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("SERIES_ID", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("SYSTEM_ID", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("FIELD_INDEX", "java.lang.Integer");
        QUERY_ENTITY_FIELDS.put("VALUE_BOOL", "java.lang.Boolean");
        QUERY_ENTITY_FIELDS.put("VALUE_STRING", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("VALUE_LONG", "java.lang.Long");
        QUERY_ENTITY_FIELDS.put("VALUE_DOUBLE", "java.lang.Double");
        QUERY_ENTITY_FIELDS.put("QUALITY", "java.lang.Long");
        QUERY_ENTITY_FIELDS.put("TIME", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("LAST_CHANGED_TIME", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("LAST_UPDATE_TIME", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("CREATED_USER", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("MODIFIED_USER", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("CREATED_TIME", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("MODIFIED_TIME", "java.sql.Timestamp");
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return "SERIES_ID";
    }
}
