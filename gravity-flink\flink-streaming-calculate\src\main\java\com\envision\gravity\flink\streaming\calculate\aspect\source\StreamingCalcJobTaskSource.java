package com.envision.gravity.flink.streaming.calculate.aspect.source;

import com.envision.gravity.flink.streaming.calculate.aspect.source.enumerator.CalcJobSplitEnumerator;
import com.envision.gravity.flink.streaming.calculate.aspect.source.reader.CalcJobSourceReader;
import com.envision.gravity.flink.streaming.calculate.aspect.source.split.CalcJobEnumState;
import com.envision.gravity.flink.streaming.calculate.aspect.source.split.CalcJobSplit;
import com.envision.gravity.flink.streaming.calculate.batch.notification.TaskCompletionNotifier;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;


import org.apache.flink.api.connector.source.*;
import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ✅ 流式版本的CalcJobTaskSource，支持多Job并行处理 借鉴KafkaSource的设计模式，使用SplitEnumerator和SourceReader
 *
 * <p>特点： 1. 多Job并发处理 2. 水平扩展支持 3. 动态Job分配 4. 持续运行
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
public class StreamingCalcJobTaskSource
        implements Source<CalcJobTask, CalcJobSplit, CalcJobEnumState> {

    private static final Logger logger = LoggerFactory.getLogger(StreamingCalcJobTaskSource.class);

    private final long taskCompletionCheckInterval;
    private final int maxJobsPerReader;
    private final int taskBatchSize;

    private StreamingCalcJobTaskSource(
            long taskCompletionCheckInterval, int maxJobsPerReader, int taskBatchSize) {
        this.taskCompletionCheckInterval = taskCompletionCheckInterval;
        this.maxJobsPerReader = maxJobsPerReader;
        this.taskBatchSize = taskBatchSize;
    }

    // ✅ 构建器模式，方便参数传递
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private long taskCompletionCheckInterval = 1000L;
        private int maxJobsPerReader = 5;
        private int taskBatchSize = 10;

        public Builder setTaskCompletionCheckInterval(long interval) {
            this.taskCompletionCheckInterval = interval;
            return this;
        }

        public Builder setMaxJobsPerReader(int maxJobs) {
            this.maxJobsPerReader = maxJobs;
            return this;
        }

        public Builder setTaskBatchSize(int batchSize) {
            this.taskBatchSize = batchSize;
            return this;
        }

        public StreamingCalcJobTaskSource build() {
            return new StreamingCalcJobTaskSource(
                    taskCompletionCheckInterval, maxJobsPerReader, taskBatchSize);
        }
    }

    @Override
    public SplitEnumerator<CalcJobSplit, CalcJobEnumState> createEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context) throws Exception {

        // ✅ 传递参数给SplitEnumerator
        return new CalcJobSplitEnumerator(
                context,
                maxJobsPerReader, // 每个Reader最大Job数
                taskCompletionCheckInterval // 检查间隔
                );
    }

    @Override
    public SplitEnumerator<CalcJobSplit, CalcJobEnumState> restoreEnumerator(
            SplitEnumeratorContext<CalcJobSplit> context, CalcJobEnumState checkpoint)
            throws Exception {

        // ✅ 从checkpoint恢复SplitEnumerator
        return new CalcJobSplitEnumerator(
                context, maxJobsPerReader, taskCompletionCheckInterval, checkpoint // 传递checkpoint状态
                );
    }

    @Override
    public SourceReader<CalcJobTask, CalcJobSplit> createReader(SourceReaderContext context)
            throws Exception {

        // ✅ 传递参数给SourceReader
        return new CalcJobSourceReader(
                context,
                taskCompletionCheckInterval, // 任务完成检查间隔
                taskBatchSize, // 任务批量大小
                TaskCompletionNotifier.getGlobalInstance() // 全局任务完成通知器
                );
    }

    @Override
    public SimpleVersionedSerializer<CalcJobSplit> getSplitSerializer() {
        return new CalcJobSplitSerializer();
    }

    @Override
    public SimpleVersionedSerializer<CalcJobEnumState> getEnumeratorCheckpointSerializer() {
        return new CalcJobEnumStateSerializer();
    }

    @Override
    public Boundedness getBoundedness() {
        return Boundedness.CONTINUOUS_UNBOUNDED; // 流式处理，无界
    }

    /** CalcJobSplit序列化器 */
    private static class CalcJobSplitSerializer implements SimpleVersionedSerializer<CalcJobSplit> {

        @Override
        public int getVersion() {
            return 1;
        }

        @Override
        public byte[] serialize(CalcJobSplit split) throws java.io.IOException {
            return split.getJobId().getBytes(java.nio.charset.StandardCharsets.UTF_8);
        }

        @Override
        public CalcJobSplit deserialize(int version, byte[] serialized) throws java.io.IOException {
            String jobId = new String(serialized, java.nio.charset.StandardCharsets.UTF_8);
            return new CalcJobSplit(jobId);
        }
    }

    /** CalcJobEnumState序列化器 */
    private static class CalcJobEnumStateSerializer
            implements SimpleVersionedSerializer<CalcJobEnumState> {

        @Override
        public int getVersion() {
            return 1;
        }

        @Override
        public byte[] serialize(CalcJobEnumState state) throws java.io.IOException {
            // 简化实现：将状态序列化为JSON字符串
            // 实际实现中可能需要更高效的序列化方式
            try {
                com.fasterxml.jackson.databind.ObjectMapper mapper =
                        new com.fasterxml.jackson.databind.ObjectMapper();
                return mapper.writeValueAsBytes(state);
            } catch (Exception e) {
                throw new java.io.IOException("Failed to serialize CalcJobEnumState", e);
            }
        }

        @Override
        public CalcJobEnumState deserialize(int version, byte[] serialized)
                throws java.io.IOException {
            try {
                com.fasterxml.jackson.databind.ObjectMapper mapper =
                        new com.fasterxml.jackson.databind.ObjectMapper();
                return mapper.readValue(serialized, CalcJobEnumState.class);
            } catch (Exception e) {
                throw new java.io.IOException("Failed to deserialize CalcJobEnumState", e);
            }
        }
    }
}
