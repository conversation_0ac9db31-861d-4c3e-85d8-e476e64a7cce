<configuration>
    <!-- 测试专用的日志配置 - 最小化无关日志输出 -->

    <!-- 简洁的控制台日志模式 -->
    <property name="TEST_LOG_PATTERN" value="%d{HH:mm:ss.SSS} %-5level %logger{20} - %msg%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${TEST_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 完全抑制第三方库的日志 -->

    <!-- Flink 框架日志 - 只显示错误 -->
    <logger name="org.apache.flink" level="ERROR"/>

    <!-- Kafka 客户端日志 - 只显示错误 -->
    <logger name="org.apache.kafka" level="ERROR"/>
    <logger name="kafka" level="ERROR"/>

    <!-- Ignite 日志 - 只显示错误 -->
    <logger name="org.apache.ignite" level="ERROR"/>

    <!-- 网络相关日志 - 完全抑制 -->
    <logger name="io.netty" level="OFF"/>
    <logger name="akka" level="OFF"/>

    <!-- 数据库相关日志 - 显示连接问题 -->
    <logger name="org.apache.zookeeper" level="ERROR"/>
    <logger name="io.debezium" level="ERROR"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.mybatis" level="DEBUG"/>
    <logger name="org.apache.http" level="ERROR"/>

    <!-- 数据库连接池和驱动日志 - 显示连接问题 -->
    <!--    <logger name="com.zaxxer.hikari" level="DEBUG"/>-->
    <!--    <logger name="org.postgresql" level="DEBUG"/>-->
    <!--    <logger name="java.sql" level="DEBUG"/>-->

    <!-- 我们的数据库配置类 - 显示详细日志 -->
    <logger name="com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig" level="DEBUG"/>

    <!-- 保持我们的应用日志可见 -->
    <logger name="com.envision.gravity.flink.streaming.calculate" level="DEBUG"/>

    <!-- 测试相关日志 - 保持详细 -->
    <logger name="com.envision.gravity.flink.streaming.calculate.StreamFlowEndToEndTest" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.KafkaTopicManager" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.StreamFlowLocalTest" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.StreamFlowDiagnosticTest" level="DEBUG"/>

    <!-- 关键组件的 DEBUG 日志 -->
    <logger name="com.envision.gravity.flink.streaming.calculate.stream.StreamCalcProcessor" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.stream.ParseMsgAndUpdateCache" level="DEBUG"/>
    <logger name="com.envision.gravity.flink.streaming.calculate.stream.serde" level="DEBUG"/>

    <!-- Kafka 消息处理日志 -->
    <logger name="org.apache.kafka.clients.consumer" level="INFO"/>
    <logger name="org.apache.kafka.clients.producer" level="INFO"/>

    <!-- 根日志级别设为 ERROR，只显示严重错误 -->
    <root level="ERROR">
        <appender-ref ref="console"/>
    </root>
</configuration>
