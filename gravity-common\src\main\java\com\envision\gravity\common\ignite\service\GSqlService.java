package com.envision.gravity.common.ignite.service;

import com.envision.gravity.common.response.QueryResult;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.api.SQLReq;

import java.util.List;
import java.util.Map;


import org.apache.ignite.services.Service;

/**
 * <AUTHOR>
 * @date 2024/1/10
 * @description:
 */
public interface GSqlService extends Service {
    String SERVICE_NAME = "GSqlService";

    ResponseResult<QueryResult> execute(
            String gatewayAccount, String databaseName, String sql, Map<String, String> hint);

    /**
     * @param gatewayAccount gatewayAccount
     * @param databaseName databaseName
     * @param sqlList Only supports INSERT/UPDATE/DELETE SQL, {@link SQLReq}
     * @return {@link QueryResult}
     */
    ResponseResult<QueryResult> executeBatch(
            String gatewayAccount, String databaseName, List<SQLReq> sqlList);
}
