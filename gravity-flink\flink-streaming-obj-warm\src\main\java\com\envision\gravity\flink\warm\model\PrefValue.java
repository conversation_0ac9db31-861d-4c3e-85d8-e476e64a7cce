package com.envision.gravity.flink.warm.model;


import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class PrefValue {
    private String prefName;
    private String fieldId;
    private String fieldDataType;
    private String compName;
    private String categoryId;
    private boolean isAttribute;
    private String rawFieldId;

    public PrefValue(
            String prefName,
            String fieldId,
            String fieldDataType,
            String compName,
            String categoryId,
            boolean isAttribute) {
        this.prefName = prefName;
        this.fieldId = fieldId;
        this.fieldDataType = fieldDataType;
        this.compName = compName;
        this.categoryId = categoryId;
        this.isAttribute = isAttribute;
    }

    public boolean hasNull() {
        return prefName == null
                || fieldId == null
                || fieldDataType == null
                || compName == null
                || categoryId == null
                || rawFieldId == null;
    }
}
