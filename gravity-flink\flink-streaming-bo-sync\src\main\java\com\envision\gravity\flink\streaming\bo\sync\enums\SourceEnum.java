package com.envision.gravity.flink.streaming.bo.sync.enums;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/13
 * @description
 */
public enum SourceEnum {
    BO,
    BO_RELATION;

    public static Optional<SourceEnum> find(String expr) {
        for (SourceEnum t : SourceEnum.values()) {
            if (expr.equalsIgnoreCase(t.name())) {
                return Optional.of(t);
            }
        }

        return Optional.empty();
    }
}
