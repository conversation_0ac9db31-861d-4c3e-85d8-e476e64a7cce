package com.envision.gravity.flink.streaming.calculate.flink;

import com.envision.gravity.flink.streaming.calculate.meta.TblFieldMappingMapper;
import com.envision.gravity.flink.streaming.calculate.meta.TblPrefUpstreamMapper;
import com.envision.gravity.flink.streaming.calculate.recalc.TblCalcJobInfoMapper;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description
 */
public class CalcPGSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(CalcPGSourceConfig.class);

    private static volatile SqlSessionFactory SQL_SESSION_FACTORY;
    private static volatile HikariDataSource HIKARI_DATA_SOURCE;

    public static SqlSessionFactory getSqlSessionFactory() {
        if (SQL_SESSION_FACTORY == null) {
            synchronized (CalcPGSourceConfig.class) {
                if (SQL_SESSION_FACTORY == null) {
                    logger.info("Start initializing pg dataSource...");

                    HikariConfig config = new HikariConfig();
                    config.setDriverClassName(CalcLionConfig.getPgDriverClassName());
                    config.setJdbcUrl(CalcLionConfig.getPgJdbcUrl());
                    config.setUsername(CalcLionConfig.getPgUsername());
                    config.setPassword(CalcLionConfig.getPgPassword());
                    config.setMaximumPoolSize(CalcLionConfig.getPgMaxPoolSize());
                    config.setConnectionTestQuery("SELECT 1;");
                    HIKARI_DATA_SOURCE = new HikariDataSource(config);

                    TransactionFactory transactionFactory = new JdbcTransactionFactory();
                    Environment environment =
                            new Environment("gravity", transactionFactory, HIKARI_DATA_SOURCE);
                    org.apache.ibatis.session.Configuration configuration =
                            new org.apache.ibatis.session.Configuration(environment);
                    configuration.addMapper(TblFieldMappingMapper.class);
                    configuration.addMapper(TblPrefUpstreamMapper.class);
                    configuration.addMapper(TblCalcJobInfoMapper.class);

                    SQL_SESSION_FACTORY = new SqlSessionFactoryBuilder().build(configuration);

                    logger.info("Init PG dataSource success.");
                }
            }
        }
        return SQL_SESSION_FACTORY;
    }

    public static void closeDataSource() {
        if (SQL_SESSION_FACTORY != null || HIKARI_DATA_SOURCE != null) {
            synchronized (CalcPGSourceConfig.class) {
                if (SQL_SESSION_FACTORY != null || HIKARI_DATA_SOURCE != null) {
                    HIKARI_DATA_SOURCE.close();
                    HIKARI_DATA_SOURCE = null;
                    SQL_SESSION_FACTORY = null;
                    logger.info("Close pg dataSource success.");
                }
            }
        }
    }
}
