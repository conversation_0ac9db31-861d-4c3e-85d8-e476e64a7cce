package com.envision.gravity.common.util;

import com.envision.gravity.common.exception.InternalException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2024/2/20
 * @description
 */
public class Base36Converter {
    private static final String BASE36_DIGITS = "0123456789abcdefghijklmnopqrstuvwxyz";
    private static final int BASE = 36;
    private static final LocalDate BASE_DATE = LocalDate.of(2024, 1, 1);
    private static final LocalDateTime BASE_DATE_TIME = LocalDateTime.of(2024, 1, 1, 0, 0);

    public static String convertDecimalToBase36(long decimalNumber, int length) {
        return convertToBase36(decimalNumber, length);
    }

    public static String convertDateToBase36() {
        LocalDate currentDate = LocalDate.now();
        long daysDiff = ChronoUnit.DAYS.between(BASE_DATE, currentDate);

        return convertToBase36(daysDiff, 3);
    }

    public static String convertDateTimeToBase36() {
        LocalDateTime currentLocalDateTime = LocalDateTime.now();
        long minutesDiff = ChronoUnit.MINUTES.between(BASE_DATE_TIME, currentLocalDateTime);
        return convertToBase36(minutesDiff, 5);
    }

    public static String convertDateTimeToBase36(LocalDateTime localDateTime) {
        long minutesDiff = ChronoUnit.MINUTES.between(BASE_DATE_TIME, localDateTime);
        return convertToBase36(minutesDiff, 5);
    }

    private static String convertToBase36(long number, int length) {
        StringBuilder base36String = new StringBuilder();

        while (number > 0) {
            int remainder = (int) (number % BASE);
            base36String.insert(0, BASE36_DIGITS.charAt(remainder));
            number /= BASE;
        }

        if (base36String.length() > length) {
            throw new InternalException("Exceeding digit limit!");
        }

        while (base36String.length() < length) {
            base36String.insert(0, BASE36_DIGITS.charAt(0));
        }

        return base36String.toString();
    }
}
