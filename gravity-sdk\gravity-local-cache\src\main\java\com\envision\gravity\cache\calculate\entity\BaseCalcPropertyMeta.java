package com.envision.gravity.cache.calculate.entity;


import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseCalcPropertyMeta {

    private String prefRuleId;

    private String targetCategoryId;

    private String targetCompId;

    private String targetPrefId;

    private String srcCategoryId;

    private String expression;

    private Integer calcType;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((targetCategoryId == null) ? 0 : targetCategoryId.hashCode());
        result = prime * result + ((targetCompId == null) ? 0 : targetCompId.hashCode());
        result = prime * result + ((targetPrefId == null) ? 0 : targetPrefId.hashCode());
        result = prime * result + ((srcCategoryId == null) ? 0 : srcCategoryId.hashCode());
        result = prime * result + ((expression == null) ? 0 : expression.hashCode());
        result = prime * result + ((calcType == null) ? 0 : calcType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        BaseCalcPropertyMeta other = (BaseCalcPropertyMeta) obj;

        if (targetCategoryId == null) {
            if (other.targetCategoryId != null) {
                return false;
            }
        } else if (!targetCategoryId.equals(other.targetCategoryId)) {
            return false;
        }

        if (targetCompId == null) {
            if (other.targetCompId != null) {
                return false;
            }
        } else if (!targetCompId.equals(other.targetCompId)) {
            return false;
        }

        if (targetPrefId == null) {
            if (other.targetPrefId != null) {
                return false;
            }
        } else if (!targetPrefId.equals(other.targetPrefId)) {
            return false;
        }

        if (srcCategoryId == null) {
            if (other.srcCategoryId != null) {
                return false;
            }
        } else if (!srcCategoryId.equals(other.srcCategoryId)) {
            return false;
        }

        if (expression == null) {
            if (other.expression != null) {
                return false;
            }
        } else if (!expression.equals(other.expression)) {
            return false;
        }

        if (calcType == null) {
            if (other.calcType != null) {
                return false;
            }
        } else if (!calcType.equals(other.calcType)) {
            return false;
        }

        return true;
    }
}
