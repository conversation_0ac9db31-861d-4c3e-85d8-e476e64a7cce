package com.envision.gravity.flink.streaming.calculate.meta;

import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingKey;
import com.envision.gravity.flink.streaming.calculate.dto.FieldMappingRecord;

import java.util.Map;
import java.util.Set;


import org.apache.ibatis.annotations.UpdateProvider;

public interface TblFieldMappingMapper {

    @UpdateProvider(type = TblFieldMappingSqlProvider.class, method = "batchUpdate")
    int batchUpdate(String orgId, Map<FieldMappingKey, FieldMappingRecord> fieldMappingRecordMap);

    @UpdateProvider(type = TblFieldMappingSqlProvider.class, method = "batchDelete")
    int batchDelete(String orgId, Set<FieldMappingKey> fieldMappingKeys);
}
