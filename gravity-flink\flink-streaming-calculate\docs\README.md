# CalculateProcessor Documentation

## 📚 Documentation Index

This directory contains comprehensive documentation for the `CalculateProcessor` operator implementation.

### 📖 Available Documents

#### 1. Complete Design Documents

| Document | Language | Description |
|----------|----------|-------------|
| [CalculateProcessor-设计方案-中文.md](./CalculateProcessor-设计方案-中文.md) | 中文 | 完整的 CalculateProcessor 算子设计方案，包含直白映射和非直白映射的详细实现设计 |
| [CalculateProcessor-Design-Document-English.md](./CalculateProcessor-Design-Document-English.md) | English | Complete CalculateProcessor operator design document, including detailed implementation design for both direct and non-direct mapping |

#### 2. Legacy Documents

| Document | Language | Description |
|----------|----------|-------------|
| [non-direct-mapping-design.md](./non-direct-mapping-design.md) | English | Initial design document focusing only on non-direct mapping (superseded by complete design documents) |

### 🎯 Document Overview

#### CalculateProcessor Complete Design (Latest)

**Key Features Covered:**
- ✅ **Direct Mapping**: Simple one-to-one value copying
- 🚧 **Non-Direct Mapping**: Complex expression-based calculations
- 🔧 **SQL Gateway Integration**: Query external data sources
- 📊 **Performance Optimization**: Batch processing and connection pooling
- 🛡️ **Error Handling**: Comprehensive fault tolerance mechanisms
- 📈 **Monitoring**: Key metrics and alerting strategies

**Architecture Highlights:**
```
Input Stream -> Message Processing -> Calculation Logic -> Output Stream
                      ↓                      ↓
              Asset Mapping Query    Direct/Non-Direct Mapping
                      ↓                      ↓
              Target Property Rules   Expression Calculation
```

### 🔍 Quick Navigation

#### For Developers
- **Implementation Guide**: See sections 4-7 in the design documents
- **API Reference**: Check section 16 for method signatures
- **Testing Strategy**: Review section 11.2 for test approaches

#### For Operations
- **Deployment Guide**: See section 13 for deployment requirements
- **Monitoring Setup**: Check section 9 for key metrics
- **Troubleshooting**: Review section 14 for common issues

#### For Architects
- **System Design**: See section 3 for overall architecture
- **Performance Considerations**: Check section 8 for optimization strategies
- **Security**: Review section 18 for security considerations

### 📋 Implementation Status

| Component | Status | Description |
|-----------|--------|-------------|
| Direct Mapping | ✅ Completed | Simple value copying functionality |
| Message Processing | ✅ Completed | Core message handling pipeline |
| Cache Integration | ✅ Completed | Calculation rule caching |
| Non-Direct Mapping | 🚧 In Progress | Complex expression calculations |
| SQL Gateway Query | 🚧 In Progress | External data source integration |
| Performance Optimization | ⏳ Planned | Advanced optimization features |

### 🛠️ Development Workflow

1. **Read Design Documents**: Start with the complete design document in your preferred language
2. **Understand Current Implementation**: Review existing direct mapping code
3. **Implement Non-Direct Mapping**: Follow the detailed implementation plan
4. **Write Tests**: Use the testing strategy outlined in the documents
5. **Performance Tuning**: Apply optimization techniques from the design
6. **Deploy and Monitor**: Follow deployment and monitoring guidelines

### 📞 Support and Feedback

For questions or feedback about the documentation:
- **Technical Questions**: Contact the System Architecture Team
- **Documentation Updates**: Submit pull requests with improvements
- **Implementation Issues**: Check the troubleshooting section first

### 📝 Document Maintenance

- **Version Control**: All documents are version-controlled with the codebase
- **Update Frequency**: Documents are updated with each major implementation milestone
- **Review Process**: Technical reviews are conducted for all documentation changes

### 🔄 Recent Updates

#### 2024-12-19 - Dynamic SQL Template Optimization (v2)
- **Fixed**: Simplified SET Hint logic in Velocity template (removed unnecessary if-else for attributes vs measurement points)
- **Corrected**: Removed `gravity-flink.calc-stream.cache.ttl` configuration (calcPrefCache is maintained by CDC module)
- **Updated**: Cache strategy documentation to reflect CDC-managed rule cache
- **Enhanced**: SQL generation examples with corrected template logic

#### 2024-12-19 - Dynamic SQL Template Optimization (v1)
- **Updated**: Dynamic SQL template design using Velocity engine
- **Improved**: Direct iteration through `assetPropertyInfoMap.values()`
- **Enhanced**: Proper handling of measurement points vs attributes based on `prefType`
- **Added**: Complete query method implementation with error handling
- **Added**: SQL generation examples and result parsing logic

#### Key Technical Improvements
- ✅ **Simplified SQL Construction**: Removed complex pre-grouping logic and unnecessary SET Hint conditions
- ✅ **Velocity Template Integration**: Proper template-based SQL generation with optimized logic
- ✅ **CDC-Managed Cache**: Clarified that calcPrefCache is maintained by CDC module, no TTL configuration needed
- ✅ **Robust Error Handling**: Comprehensive null checking and exception handling
- ✅ **Performance Optimization**: Efficient property deduplication and batch querying

---

**Last Updated**: 2024-12-19
**Maintained By**: System Architecture Team
