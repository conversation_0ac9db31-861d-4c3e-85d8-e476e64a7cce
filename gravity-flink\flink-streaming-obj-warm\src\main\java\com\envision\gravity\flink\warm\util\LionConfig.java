package com.envision.gravity.flink.warm.util;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class LionConfig {

    public static final HashMap<String, Class<?>> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put("VARCHAR", String.class);
        TYPE_MAP.put("BIGINT", Long.class);
        TYPE_MAP.put("DOUBLE", Double.class);
        TYPE_MAP.put("BOOLEAN", Boolean.class);
        TYPE_MAP.put("TIMESTAMP", Timestamp.class);
    }

    public static Class<?> getTypeClass(String typeName) {
        return TYPE_MAP.get(typeName);
    }

    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getIgniteAddress() {
        return getStringValue("gravity-common.ignite.address", null);
    }

    public static String getIgniteUsername() {
        return getStringValue("gravity-common.ignite.username", "ignite");
    }

    public static String getIgnitePassword() {
        return getStringValue("gravity-common.ignite.password", "ignite");
    }

    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.warm.pg.cdc.database", "gravity");
    }

    public static String getPgSchemaList() {
        return getStringValue("gravity-flink.warm.pg.cdc.schema.list", "^(o|sysenos2018).*");
    }

    public static String getPgTableList() {
        return getStringValue(
                "gravity-flink.warm.pg.cdc.table.list", "^o.*\\.object_detail_origin$");
    }

    public static String getSlotName() {
        return getStringValue("gravity-flink.warm.pg.cdc.slot.name", "gravity_warm");
    }

    public static String getMaxBatchSize() {
        return getStringValue("gravity-flink.warm.source.max.batch.size", "1000");
    }

    public static String getMaxQueueSize() {
        return getStringValue("gravity-flink.warm.source.max.queue.size", "2000");
    }

    public static Integer getModelCacheExpireTimeSeconds() {
        return getIntValue("gravity-flink.warm.mode.cache.expired.seconds", 1800);
    }

    public static List<Tuple2<String, Class<?>>> getObjFixedCols() {
        String cols =
                getStringValue(
                        "gravity-flink.warm.obj.fixed.col_and_type",
                        "SYSTEM_DISPLAY_NAME:VARCHAR,CATEGORY_ID:VARCHAR,"
                                + "CREATED_TIME:TIMESTAMP,CREATED_USER:VARCHAR,MODIFIED_TIME:TIMESTAMP,"
                                + "POINT_LAST_PROCESS_TIME:TIMESTAMP,MODIFIED_USER:VARCHAR,"
                                + "DCM__DEVICEKEY__VARCHAR:VARCHAR,point_last_process_time:TIMESTAMP,"
                                + "system_display_name:VARCHAR,category_id:VARCHAR,"
                                + "created_time:TIMESTAMP,created_user:VARCHAR,"
                                + "modified_time:TIMESTAMP,modified_user:VARCHAR");
        if (cols != null) {
            return Arrays.stream(cols.split(","))
                    .map(String::trim)
                    .map(
                            col -> {
                                String[] arr = col.split(":");
                                if (arr.length != 2) {
                                    log.error("Invalid fixed column: " + col);
                                    System.out.println("Invalid fixed column: " + col);
                                    throw new RuntimeException("Invalid fixed column: " + col);
                                }
                                Class<?> type = TYPE_MAP.get(arr[1].trim().toUpperCase());
                                return new Tuple2<String, Class<?>>(arr[0].trim(), type);
                            })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
