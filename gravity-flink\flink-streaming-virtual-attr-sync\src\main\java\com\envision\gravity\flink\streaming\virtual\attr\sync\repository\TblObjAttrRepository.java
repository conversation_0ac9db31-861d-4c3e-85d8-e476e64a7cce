package com.envision.gravity.flink.streaming.virtual.attr.sync.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.TblObjAttrMapper;
import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.ObjAttrValue;

import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/9/18
 * @description
 */
@Slf4j
public class TblObjAttrRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblObjAttrRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<ObjAttrValue> queryAttrValue(
            String schemeName, String fieldId, List<String> systemIdList) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Objects.requireNonNull(schemeName, "Scheme name cannot be null.");

            TblObjAttrMapper tblObjAttrMapper = session.getMapper(TblObjAttrMapper.class);
            return tblObjAttrMapper.queryAttrValue(schemeName, fieldId, systemIdList);
        } catch (Exception e) {
            log.error("Query obj attr value error.", e);
            throw new GravityRuntimeException("Query obj attr value error.", e);
        }
    }
}
