package com.envision.gravity.low.level.api.sql.ignite;

import com.envision.gravity.common.exception.InternalException;
import com.envision.gravity.common.po.TblBO;

import java.lang.reflect.Field;
import java.util.*;


import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.*;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.binary.BinaryObjectBuilder;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.transactions.Transaction;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/4/16
 * @description
 */
@Slf4j
public class IgniteCacheTest {

    private static Ignite ignite;

    @BeforeAll
    public static void init() {
        ignite = Ignition.start("config/default-config-dev.xml");
    }

    @Test
    void cachePutAllApiTest() {
        IgniteCache<BinaryObject, BinaryObject> tblBoModelCache =
                ignite.cache("GRAVITY_TBL_BO_MODEL");

        if (tblBoModelCache == null) {
            log.error("Cache TBL_BO_MODEL is null!");
            throw new InternalException("Table TBL_BO_MODEL does not exist!");
        }

        BinaryObjectBuilder tblBoModelKey = ignite.binary().builder("GRAVITY_TBL_BO_MODEL_KEY");
        BinaryObjectBuilder tblBoModelValue = ignite.binary().builder("GRAVITY_TBL_BO_MODEL_VALUE");
        Map<BinaryObject, BinaryObject> tblBoModelCacheMap = new HashMap<>(8);
        tblBoModelKey.setField("MODEL_ID", "EnOS_Solar_Inverter3", String.class);
        tblBoModelValue.setField(
                "MODEL_DISPLAY_NAME",
                "{\"en_US\": \"Inverter\", \"zh_CN\": \"逆变器\", \"default\": \"逆变器\"}",
                String.class);
        tblBoModelValue.setField("CREATED_USER", "gravity", String.class);
        tblBoModelValue.setField("MODIFIED_USER", "gravity", String.class);
        //        tblBoModelValue.setField("modified_time", new
        // Timestamp(System.currentTimeMillis()), Timestamp.class);
        tblBoModelCacheMap.put(tblBoModelKey.build(), tblBoModelValue.build());

        IgniteCache<Object, Object> tblBOCache = ignite.cache("GRAVITY_TBL_BO");

        if (tblBOCache == null) {
            log.error("Cache TBL_BO is null!");
            throw new InternalException("Table TBL_BO does not exist!");
        }

        BinaryObjectBuilder tblBOKey = ignite.binary().builder("GRAVITY_TBL_BO_KEY");
        BinaryObjectBuilder tblBOValue = ignite.binary().builder("GRAVITY_TBL_BO_VALUE");
        Map<BinaryObject, BinaryObject> tblBOMap = new HashMap<>(8);
        tblBOKey.setField("ASSET_ID", "XXX", String.class);
        tblBOValue.setField("ASSET_DISPLAY_NAME", "{\"default\": \"XXX\"}", String.class);
        tblBOValue.setField("CREATED_USER", "gravity", String.class);
        tblBOValue.setField("MODIFIED_USER", "gravity", String.class);
        tblBOMap.put(tblBOKey.build(), tblBOValue.build());

        IgniteTransactions transactions = ignite.transactions();
        try (Transaction tx = transactions.txStart()) {
            tblBoModelCache.withKeepBinary().putAll(tblBoModelCacheMap);
            tblBOCache.withKeepBinary().putAll(tblBOMap);

            tx.commit();
        } catch (IgniteException e) {
            log.error("", e);
        }
    }

    @Test
    void cacheDeleteApiTest() {
        IgniteCache<BinaryObject, BinaryObject> tblBoCache = ignite.cache("GRAVITY_TBL_BO");

        if (tblBoCache == null) {
            log.error("Cache TBL_BO is null!");
            throw new InternalException("Table TBL_BO does not exist!");
        }

        Set<BinaryObject> keySet = new HashSet<>();
        BinaryObjectBuilder tblBoKey = ignite.binary().builder("GRAVITY_TBL_BO_KEY");
        tblBoKey.setField("asset_id", "bo1", String.class);
        keySet.add(tblBoKey.build());

        try (Transaction tx = ignite.transactions().txStart()) {
            tblBoCache.withKeepBinary().removeAll(keySet);

            tx.commit();
        } catch (IgniteException e) {
            log.error("", e);
        }
    }

    @Test
    void cacheQueryDeleteApiTest() {
        IgniteCache<BinaryObject, BinaryObject> tblBoCache = ignite.cache("GRAVITY_TBL_BO");

        if (tblBoCache == null) {
            log.error("Cache TBL_BO is null!");
            throw new InternalException("Table TBL_BO does not exist!");
        }

        FieldsQueryCursor<List<?>> fieldsQueryCursor =
                tblBoCache
                        .withKeepBinary()
                        .query(new SqlFieldsQuery("select * from tbl_bo").setSchema("GRAVITY"));

        List<TblBO> results = new ArrayList<>();
        List<List<?>> rowData = fieldsQueryCursor.getAll();
        int columnsCount = fieldsQueryCursor.getColumnsCount();
        for (List<?> rowDatum : rowData) {
            TblBO tblBO = new TblBO();
            for (int i = 0; i < columnsCount; i++) {
                String columnName = fieldsQueryCursor.getFieldName(i);
                try {
                    Field declaredField = tblBO.getClass().getDeclaredField(columnName);
                    declaredField.setAccessible(true);
                    declaredField.set(tblBO, rowDatum.get(i));
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    log.error(
                            "Failed to populate {} {} field value.",
                            tblBO.getClass().getSimpleName(),
                            columnName,
                            e);
                    throw new InternalException("Failed to populate PO class.", e);
                }
            }
            results.add(tblBO);
        }

        System.out.println(results);
    }

    @Test
    void cacheLoadTest() {
        IgniteCache<BinaryObject, BinaryObject> tblBoModelCache =
                ignite.cache("GRAVITY_TBL_BO_MODEL");

        if (tblBoModelCache == null) {
            log.error("Cache TBL_BO_MODEL is null!");
            throw new InternalException("Table TBL_BO_MODEL does not exist!");
        }

        BinaryObjectBuilder tblBoModelKey = ignite.binary().builder("GRAVITY_TBL_BO_MODEL_KEY");
        BinaryObjectBuilder tblBoModelValue = ignite.binary().builder("GRAVITY_TBL_BO_MODEL_VALUE");
        Map<BinaryObject, BinaryObject> tblBoModelCacheMap = new HashMap<>(8);
        tblBoModelKey.setField("MODEL_ID", "EnOS_Solar_Inverter3", String.class);
        tblBoModelValue.setField(
                "MODEL_DISPLAY_NAME",
                "{\"en_US\": \"Inverter\", \"zh_CN\": \"逆变器\", \"default\": \"逆变器\"}",
                String.class);
        tblBoModelValue.setField("CREATED_USER", "gravity", String.class);
        tblBoModelValue.setField("MODIFIED_USER", "gravity", String.class);
        //        tblBoModelValue.setField("modified_time", new
        // Timestamp(System.currentTimeMillis()), Timestamp.class);
        tblBoModelCacheMap.put(tblBoModelKey.build(), tblBoModelValue.build());

        IgniteTransactions transactions = ignite.transactions();
        try (Transaction tx = transactions.txStart()) {
            tblBoModelCache.withKeepBinary().putAll(tblBoModelCacheMap);

            tx.commit();
        } catch (IgniteException e) {
            log.error("", e);
        }
    }
}
