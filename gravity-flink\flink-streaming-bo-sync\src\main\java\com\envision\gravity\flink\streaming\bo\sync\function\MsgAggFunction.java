package com.envision.gravity.flink.streaming.bo.sync.function;

import com.envision.gravity.common.definition.bo.BODefinition;
import com.envision.gravity.flink.streaming.bo.sync.entity.*;
import com.envision.gravity.flink.streaming.bo.sync.enums.OPEnum;
import com.envision.gravity.flink.streaming.bo.sync.enums.SourceEnum;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;

/**
 * <AUTHOR>
 * @date 2025/3/2
 * @description
 */
@Slf4j
public class MsgAggFunction
        extends ProcessFunction<
                Iterable<Tuple2<Msg, MsgMeta>>, Map<PayloadKey, Map<SourceKey, List<Msg>>>> {
    private static final long serialVersionUID = 1774842074216249188L;
    private static final String SYNC_ID_CONSTANT = "syncId";

    @Override
    public void processElement(
            Iterable<Tuple2<Msg, MsgMeta>> value,
            ProcessFunction<
                                    Iterable<Tuple2<Msg, MsgMeta>>,
                                    Map<PayloadKey, Map<SourceKey, List<Msg>>>>
                            .Context
                    ctx,
            Collector<Map<PayloadKey, Map<SourceKey, List<Msg>>>> out)
            throws Exception {
        try {
            Map<PayloadKey, Map<SourceKey, List<Msg>>> results = new HashMap<>();

            // Step 1: Group messages by PayloadKey and SourceKey, then sort by timestamp
            Map<PayloadKey, Map<SourceKey, List<Msg>>> groupedMessages =
                    StreamSupport.stream(value.spliterator(), false)
                            .filter(
                                    tuple -> {
                                        String syncId = getSyncId(tuple.f0.getHeaders());
                                        String orgId = tuple.f0.getValue().getOrgId();
                                        boolean isValid =
                                                StringUtils.isNotEmpty(syncId)
                                                        && StringUtils.isNotEmpty(orgId);
                                        if (!isValid) {
                                            log.warn(
                                                    "Invalid record - syncId: {}, orgId: {}",
                                                    syncId,
                                                    orgId);
                                        }
                                        return isValid;
                                    })
                            .collect(
                                    Collectors.groupingBy(
                                            // Group by PayloadKey (syncId + orgId)
                                            tuple ->
                                                    PayloadKey.builder()
                                                            .syncId(
                                                                    getSyncId(
                                                                            tuple.f0.getHeaders()))
                                                            .orgId(tuple.f0.getValue().getOrgId())
                                                            .build(),
                                            // Subgroup by SourceKey (source + boId) and sort by
                                            // timestamp
                                            Collectors.groupingBy(
                                                    msg ->
                                                            new SourceKey(
                                                                    msg.f0.getValue().getSource(),
                                                                    msg.f0.getKey()),
                                                    Collectors.collectingAndThen(
                                                            Collectors.toList(),
                                                            list ->
                                                                    list.stream()
                                                                            .map(tuple -> tuple.f0)
                                                                            .sorted(
                                                                                    Comparator
                                                                                            .comparingLong(
                                                                                                    m ->
                                                                                                            m.getValue()
                                                                                                                    .getTsMs()))
                                                                            .collect(
                                                                                    Collectors
                                                                                            .toList())))));

            // Step 2: Process each group of messages
            processGroupedMessages(results, groupedMessages);

            if (!results.isEmpty()) {
                out.collect(results);
            }
        } catch (Exception e) {
            log.error("Failed to aggregate kafka messages", e);
        }
    }

    private String getSyncId(Headers headers) {
        if (headers != null) {
            for (Header header : headers.toArray()) {
                if (SYNC_ID_CONSTANT.equals(header.key())) {
                    return new String(header.value(), StandardCharsets.UTF_8);
                }
            }
        }
        return null;
    }

    /**
     * Process grouped messages and update results
     *
     * @param results target map to store processed messages
     * @param groupedMessages grouped messages to process
     */
    private void processGroupedMessages(
            Map<PayloadKey, Map<SourceKey, List<Msg>>> results,
            Map<PayloadKey, Map<SourceKey, List<Msg>>> groupedMessages) {
        groupedMessages.forEach(
                (payloadKey, sourceKeyMsgsMap) ->
                        sourceKeyMsgsMap.forEach(
                                (sourceKey, groupedMsgList) -> {
                                    if (groupedMsgList.isEmpty()) {
                                        log.warn(
                                                "Message list is empty for payloadKey: {}, sourceKey: {}",
                                                payloadKey,
                                                sourceKey);
                                        return;
                                    }

                                    Msg lastMessage = groupedMsgList.get(groupedMsgList.size() - 1);
                                    String operation = lastMessage.getValue().getOp();
                                    boolean isBusinessObject =
                                            SourceEnum.BO
                                                    .name()
                                                    .equalsIgnoreCase(sourceKey.getSource());

                                    if (OPEnum.DELETE.name().equalsIgnoreCase(operation)
                                            || groupedMsgList.size() == 1) {
                                        // For DELETE operations, use the last message directly
                                        addToResults(
                                                results,
                                                payloadKey,
                                                sourceKey,
                                                Collections.singletonList(lastMessage));
                                        return;
                                    }

                                    if (OPEnum.UPSERT.name().equalsIgnoreCase(operation)) {
                                        if (isBusinessObject) {
                                            handleBOUpsert(
                                                    results,
                                                    payloadKey,
                                                    sourceKey,
                                                    groupedMsgList,
                                                    lastMessage);
                                        } else {
                                            handleNonBOUpsert(
                                                    results,
                                                    payloadKey,
                                                    sourceKey,
                                                    groupedMsgList,
                                                    lastMessage);
                                        }
                                    } else {
                                        log.warn(
                                                "Unsupported operation type: {}, payloadKey: {}, sourceKey: {}",
                                                operation,
                                                payloadKey,
                                                sourceKey);
                                    }
                                }));
    }

    @Getter
    private static class FieldCheckConfig {
        private final String fieldName;
        private final Object currentValue;
        private final Function<Object, Object> fieldChecker;

        FieldCheckConfig(
                String fieldName, Object currentValue, Function<Object, Object> fieldChecker) {
            this.fieldName = fieldName;
            this.currentValue = currentValue;
            this.fieldChecker = fieldChecker;
        }
    }

    private void handleBOUpsert(
            Map<PayloadKey, Map<SourceKey, List<Msg>>> results,
            PayloadKey payloadKey,
            SourceKey sourceKey,
            List<Msg> groupedMsgList,
            Msg lastMessage) {
        Set<Msg> validMessages = new HashSet<>();
        validMessages.add(lastMessage);
        BODefinition lastPayload = (BODefinition) lastMessage.getValue().getPayload();
        boolean forceUpdate = lastMessage.getValue().isForceUpdate();

        // Define field check configurations
        FieldCheckConfig[] fieldConfigs = {
            new FieldCheckConfig(
                    "BusinessObject",
                    lastPayload.getBusinessObject(),
                    payload -> ((BODefinition) payload).getBusinessObject()),
            new FieldCheckConfig(
                    "DataObject",
                    lastPayload.getDataObject(),
                    payload -> ((BODefinition) payload).getDataObject()),
            new FieldCheckConfig(
                    "BusinessObjectRelation",
                    lastPayload.getBusinessObjectRelation(),
                    payload -> ((BODefinition) payload).getBusinessObjectRelation())
        };

        // Process each field
        for (FieldCheckConfig config : fieldConfigs) {
            if (forceUpdate) {
                // When forceUpdate is true, only check null fields
                if (config.getCurrentValue() == null) {
                    findValidHistoricalMessage(groupedMsgList, config.getFieldChecker(), true)
                            .ifPresent(validMessages::add);
                }
            } else {
                // When forceUpdate is false, check all fields
                findValidHistoricalMessage(
                                groupedMsgList,
                                config.getFieldChecker(),
                                config.getCurrentValue() == null)
                        .ifPresent(validMessages::add);
            }
        }

        // Add messages to results
        addToResults(results, payloadKey, sourceKey, new ArrayList<>(validMessages));
    }

    /** Find valid historical message for a specific field */
    private Optional<Msg> findValidHistoricalMessage(
            List<Msg> groupedMsgList,
            Function<Object, Object> fieldChecker,
            boolean isCurrentFieldNull) {

        Msg validMsg = null;
        // Iterate from second last to first (excluding the last element)
        for (int i = groupedMsgList.size() - 2; i >= 0; i--) {
            Msg preMsg = groupedMsgList.get(i);
            String preOpName = preMsg.getValue().getOp();

            if (OPEnum.DELETE.name().equalsIgnoreCase(preOpName)) {
                break;
            }

            Object fieldValue = fieldChecker.apply(preMsg.getValue().getPayload());
            if (fieldValue != null) {
                if (isCurrentFieldNull) {
                    if (validMsg == null) {
                        validMsg = preMsg;
                    }
                }
                if (preMsg.getValue().isForceUpdate()) {
                    validMsg = preMsg;
                    break;
                }
            }
        }

        return Optional.ofNullable(validMsg);
    }

    private void handleNonBOUpsert(
            Map<PayloadKey, Map<SourceKey, List<Msg>>> results,
            PayloadKey payloadKey,
            SourceKey sourceKey,
            List<Msg> groupedMsgList,
            Msg lastMessage) {
        if (lastMessage.getValue().isForceUpdate()) {
            addToResults(results, payloadKey, sourceKey, Collections.singletonList(lastMessage));
            return;
        }

        Optional<Msg> forceUpdateMsg = findForceUpdateMsg(groupedMsgList);
        addToResults(
                results,
                payloadKey,
                sourceKey,
                Collections.singletonList(forceUpdateMsg.orElse(lastMessage)));
    }

    private Optional<Msg> findForceUpdateMsg(List<Msg> msgList) {
        // Iterate from second last to first (excluding the last element)
        for (int i = msgList.size() - 2; i >= 0; i--) {
            Msg msg = msgList.get(i);
            if (OPEnum.DELETE.name().equalsIgnoreCase(msg.getValue().getOp())) {
                break;
            }
            if (msg.getValue().isForceUpdate()) {
                return Optional.of(msg);
            }
        }
        return Optional.empty();
    }

    private void addToResults(
            Map<PayloadKey, Map<SourceKey, List<Msg>>> results,
            PayloadKey payloadKey,
            SourceKey sourceKey,
            List<Msg> messages) {
        // Get or create the list for this sourceKey
        List<Msg> existingMessages =
                results.computeIfAbsent(payloadKey, k -> new HashMap<>())
                        .computeIfAbsent(sourceKey, k -> new ArrayList<>());

        // Add new messages
        existingMessages.addAll(messages);

        // Sort all messages by timestamp ascending
        existingMessages.sort(Comparator.comparingLong(msg -> msg.getValue().getTsMs()));
    }
}
