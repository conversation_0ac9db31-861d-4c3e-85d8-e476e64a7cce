package com.envision.gravity.flink.warm.util;

import com.envision.gravity.common.util.IgniteUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AssetIdToSystemIdCache {

    public static final String SQL_QUERY_ASSET_ID_TO_SYSTEM_ID_PATTERN =
            "SELECT asset_id, system_id FROM tbl_bo_part where asset_id in ('%s')";

    private final Map<String, Map<String, String>> assetIdToSystemIdCache = new HashMap<>();

    private Map<String, String> getOrgMap(String ou) {
        return assetIdToSystemIdCache.computeIfAbsent(ou, k -> new HashMap<>());
    }

    public String getSystemId(String ou, String assetId) {

        Map<String, String> orgMap = getOrgMap(ou);
        String systemId = orgMap.get(assetId);

        if (systemId == null) {
            systemId = querySystemIdFromIgnite(ou, assetId);
            if (systemId == null) {
                return null;
            }
            orgMap.put(assetId, systemId);
        }

        return systemId;
    }

    private static String querySystemIdFromIgnite(String ou, String assetId) {

        try {
            String querySql = String.format(SQL_QUERY_ASSET_ID_TO_SYSTEM_ID_PATTERN, assetId);
            List<List<?>> systemIdResult = IgniteUtil.query(ou.toUpperCase(), querySql);

            if (systemIdResult != null
                    && !systemIdResult.isEmpty()
                    && !systemIdResult.get(0).isEmpty()) {
                return systemIdResult.get(0).get(1).toString();
            }
        } catch (Throwable t) {
            log.error("query systemId for assetId {} error. {}", assetId, t.getMessage());
        }
        return null;
    }
}
