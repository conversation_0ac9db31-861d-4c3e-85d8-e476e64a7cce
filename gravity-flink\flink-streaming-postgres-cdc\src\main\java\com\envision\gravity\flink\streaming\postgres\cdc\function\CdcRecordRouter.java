package com.envision.gravity.flink.streaming.postgres.cdc.function;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.CDCTableName;
import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.*;

import java.util.Optional;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/** <AUTHOR> 2024/6/26 */
@Slf4j
public class CdcRecordRouter extends ProcessFunction<String, ParsedCdcRecord> {

    private static final long serialVersionUID = -1916182024702900740L;

    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, ParsedCdcRecord>.Context ctx,
            Collector<ParsedCdcRecord> out)
            throws Exception {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(value);
            JsonNode sourceNode = jsonNode.get("source");

            String table = sourceNode.get("table").asText();
            Optional<CDCTableName> tableInfo = CDCTableName.find(table);
            if (tableInfo.isPresent()) {
                ParsedCdcRecord parsedCdcRecord =
                        ParsedCdcRecord.builder()
                                .schema(sourceNode.get("schema").asText())
                                .db(sourceNode.get("db").asText())
                                .table(tableInfo.get())
                                .op(jsonNode.get("op").asText())
                                .tsMs(jsonNode.get("ts_ms").asLong())
                                .build();

                switch (tableInfo.get()) {
                    case TBL_BO_MODEL:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblBOModelInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblBOModelInfo.class));
                        break;
                    case TBL_COMPONENT:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblComponentInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblComponentInfo.class));
                        break;
                    case TBL_BO_MODEL_COMP:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblBOModelCompInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblBOModelCompInfo.class));
                        break;
                    case TBL_COMPONENT_PREF:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblComponentPrefInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblComponentPrefInfo.class));
                        break;
                    case TBL_PREF:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblPrefInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblPrefInfo.class));
                        break;
                    case TBL_BO:
                        parsedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblBOInfo.class));
                        parsedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblBOInfo.class));
                        break;
                    default:
                        return;
                }

                ctx.output(tableInfo.get().getOutputTag(), parsedCdcRecord);
            } else {
                log.warn("Unrecognized table {}", table);
            }

        } catch (Exception e) {
            log.error("Parse cdc record failed, value: {}, cause: {}", value, e.getMessage(), e);
        }
    }
}
