package com.envision.gravity.flink.streaming.postgres.cdc.mapper;

import java.util.List;


import com.google.common.collect.Multimap;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
public interface TblBOModelMapper {
    @UpdateProvider(
            type = TblBOModelSqlProvider.class,
            method = "updateSysModifiedTimeByPrimaryKeys")
    int updateSysModifiedTimeByPrimaryKeys(String schemaName, List<String> modelIdList);

    /**
     * select asset_id list by model_id and group_id
     *
     * @param schemaName schemaName
     * @param modelGroupMap key: model_id, value: group_id list
     * @return asset_id list
     */
    @SelectProvider(type = TblBOModelSqlProvider.class, method = "selectAssetIdList")
    List<String> selectAssetIdList(String schemaName, Multimap<String, String> modelGroupMap);
}
