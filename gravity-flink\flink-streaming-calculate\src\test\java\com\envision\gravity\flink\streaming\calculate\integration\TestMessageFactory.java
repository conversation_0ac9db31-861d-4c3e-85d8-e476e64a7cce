package com.envision.gravity.flink.streaming.calculate.integration;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * YAML-based test message factory that supports complex test scenarios including multiple outputs
 * per input, direct/non-direct mapping separation, and asset-level result merging for non-direct
 * mappings.
 */
@Slf4j
public class TestMessageFactory {

    @Data
    public static class YamlTestScenario {
        private String name;
        private String description;
        private String orgId;
        private String inputMessage;
        private List<String> expectedOutputMessages;

        // Parsed JSON objects
        private JsonNode inputJson;
        private List<JsonNode> expectedOutputJsons;

        // Categorized outputs
        private List<JsonNode> directMappingOutputs;
        private List<JsonNode> nonDirectMappingOutputs;

        // Merged outputs for validation
        private List<JsonNode> mergedNonDirectMappingOutputs;
    }

    @Data
    public static class TestInput {
        private String orgId;
        private String modelId;
        private String modelIdPath;
        private List<TestPayload> payload;
    }

    @Data
    public static class TestPayload {
        private String assetId;
        private Long time;
        private Map<String, Object> points;
    }

    @Data
    public static class ExpectedOutputs {
        @JsonProperty("direct_mapping")
        private List<JsonNode> directMapping;

        @JsonProperty("non_direct_mapping")
        private List<JsonNode> nonDirectMapping;
    }

    @Data
    public static class TestCase {
        private String name;
        private String description;
        private TestInput input;

        @JsonProperty("expected_outputs")
        private ExpectedOutputs expectedOutputs;
    }

    @Data
    public static class YamlTestData {
        @JsonProperty("test_cases")
        private List<TestCase> testCases;
    }

    private final ObjectMapper jsonMapper = new ObjectMapper();

    /** Load test scenarios from JSON file */
    public List<YamlTestScenario> loadScenariosFromYaml(String jsonPath) throws IOException {
        List<YamlTestScenario> scenarios = new ArrayList<>();

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(jsonPath)) {
            if (inputStream == null) {
                throw new IOException("JSON file not found: " + jsonPath);
            }

            YamlTestData testData = jsonMapper.readValue(inputStream, YamlTestData.class);

            for (TestCase testCase : testData.getTestCases()) {
                YamlTestScenario scenario = new YamlTestScenario();
                scenario.setName(testCase.getName());
                scenario.setDescription(testCase.getDescription());

                // Extract orgId from input
                scenario.setOrgId(testCase.getInput().getOrgId());

                // Convert input to JSON string
                JsonNode inputJsonNode = jsonMapper.valueToTree(testCase.getInput());
                scenario.setInputMessage(jsonMapper.writeValueAsString(inputJsonNode));
                scenario.setInputJson(inputJsonNode);

                // Process expected outputs
                ExpectedOutputs expectedOutputs = testCase.getExpectedOutputs();
                List<String> outputMessages = new ArrayList<>();
                List<JsonNode> outputJsons = new ArrayList<>();

                // Add direct mapping outputs
                if (expectedOutputs.getDirectMapping() != null) {
                    for (JsonNode output : expectedOutputs.getDirectMapping()) {
                        outputMessages.add(jsonMapper.writeValueAsString(output));
                        outputJsons.add(output);
                    }
                }

                // Add non-direct mapping outputs
                if (expectedOutputs.getNonDirectMapping() != null) {
                    for (JsonNode output : expectedOutputs.getNonDirectMapping()) {
                        outputMessages.add(jsonMapper.writeValueAsString(output));
                        outputJsons.add(output);
                    }
                }

                scenario.setExpectedOutputMessages(outputMessages);
                scenario.setExpectedOutputJsons(outputJsons);

                // Set categorized outputs directly from YAML structure
                scenario.setDirectMappingOutputs(
                        expectedOutputs.getDirectMapping() != null
                                ? expectedOutputs.getDirectMapping()
                                : new ArrayList<>());
                scenario.setNonDirectMappingOutputs(
                        expectedOutputs.getNonDirectMapping() != null
                                ? expectedOutputs.getNonDirectMapping()
                                : new ArrayList<>());

                // Process merged non-direct mapping outputs
                scenario.setMergedNonDirectMappingOutputs(
                        mergeNonDirectMappingOutputsByAssetId(
                                scenario.getNonDirectMappingOutputs()));

                scenarios.add(scenario);
            }
        }

        log.info("Loaded {} JSON test scenarios from {}", scenarios.size(), jsonPath);
        return scenarios;
    }

    /**
     * Merge non-direct mapping outputs by asset ID This simulates the expected behavior where
     * non-direct mapping results for the same asset are merged into a single message
     */
    private List<JsonNode> mergeNonDirectMappingOutputsByAssetId(List<JsonNode> nonDirectOutputs) {
        if (nonDirectOutputs == null || nonDirectOutputs.isEmpty()) {
            return new ArrayList<>();
        }

        // Group outputs by orgId, modelId, and assetId
        Map<String, List<JsonNode>> groupedOutputs = new HashMap<>();

        for (JsonNode output : nonDirectOutputs) {
            String orgId = output.get("orgId").asText();
            String modelId = output.get("modelId").asText();
            String assetId = output.get("payload").get(0).get("assetId").asText();

            String groupKey = orgId + "|" + modelId + "|" + assetId;
            groupedOutputs.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(output);
        }

        List<JsonNode> mergedOutputs = new ArrayList<>();

        for (List<JsonNode> group : groupedOutputs.values()) {
            if (group.size() == 1) {
                // No merging needed
                mergedOutputs.add(group.get(0));
            } else {
                // Merge multiple outputs for the same asset
                JsonNode merged = mergeOutputsForSameAsset(group);
                mergedOutputs.add(merged);
            }
        }

        return mergedOutputs;
    }

    /** Merge multiple outputs for the same asset into a single output */
    private JsonNode mergeOutputsForSameAsset(List<JsonNode> outputs) {
        if (outputs.isEmpty()) {
            return null;
        }

        if (outputs.size() == 1) {
            return outputs.get(0);
        }

        // Use the first output as the base and merge points from others
        JsonNode baseOutput = outputs.get(0).deepCopy();
        Map<String, Object> mergedPoints = new HashMap<>();

        // Collect all points from all outputs
        for (JsonNode output : outputs) {
            JsonNode points = output.get("payload").get(0).get("points");
            points.fields()
                    .forEachRemaining(
                            entry -> {
                                String pointName = entry.getKey();
                                JsonNode pointValue = entry.getValue();

                                if (pointValue.isNumber()) {
                                    mergedPoints.put(pointName, pointValue.asDouble());
                                } else if (pointValue.isTextual()) {
                                    mergedPoints.put(pointName, pointValue.asText());
                                } else if (pointValue.isBoolean()) {
                                    mergedPoints.put(pointName, pointValue.asBoolean());
                                } else {
                                    mergedPoints.put(pointName, pointValue);
                                }
                            });
        }

        // Update the base output with merged points
        ((com.fasterxml.jackson.databind.node.ObjectNode) baseOutput.get("payload").get(0))
                .set("points", jsonMapper.valueToTree(mergedPoints));

        return baseOutput;
    }

    /** Determine if an output is from direct mapping by comparing point names with input */
    private boolean isDirectMappingOutput(JsonNode input, JsonNode output) {
        JsonNode inputPoints = input.get("payload").get(0).get("points");
        JsonNode outputPoints = output.get("payload").get(0).get("points");

        // Check if all output point names exist in input (direct mapping)
        boolean allPointsExistInInput = true;
        java.util.Iterator<String> iterator = outputPoints.fieldNames();
        while (iterator.hasNext()) {
            String outputPointName = iterator.next();
            if (!inputPoints.has(outputPointName)) {
                allPointsExistInInput = false;
                break;
            }
        }

        return allPointsExistInInput;
    }

    /** Check if the output contains calculated point names */
    private boolean hasCalculatedPointNames(JsonNode outputPoints) {
        java.util.Iterator<String> iterator = outputPoints.fieldNames();
        while (iterator.hasNext()) {
            String pointName = iterator.next();
            if (pointName.contains("_Plus")
                    || pointName.contains("_Calc")
                    || pointName.contains("_Minus")
                    || pointName.contains("_Multiply")
                    || pointName.contains("Efficiency")
                    || pointName.contains("Ratio")
                    || pointName.contains("Index")
                    || pointName.contains("Doubled")) {
                return true;
            }
        }
        return false;
    }

    /** Get input topic name for a scenario */
    public String getInputTopicName(YamlTestScenario scenario) {
        String modelId = scenario.getInputJson().get("modelId").asText();
        return "MEASURE_POINT_ORIGIN_GRAVITY_" + scenario.getOrgId();
    }

    /** Get output topic name for a scenario */
    public String getOutputTopicName(YamlTestScenario scenario) {
        return "MEASURE_POINT_CAL_" + scenario.getOrgId();
    }

    /** Get all unique input topics from scenarios */
    public List<String> getAllInputTopics(List<YamlTestScenario> scenarios) {
        return scenarios.stream()
                .map(this::getInputTopicName)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
    }

    /** Get all unique output topics from scenarios */
    public List<String> getAllOutputTopics(List<YamlTestScenario> scenarios) {
        return scenarios.stream()
                .map(this::getOutputTopicName)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
    }

    /** Filter scenarios by name pattern */
    public List<YamlTestScenario> filterByName(
            List<YamlTestScenario> scenarios, String namePattern) {
        return scenarios.stream()
                .filter(s -> s.getName().contains(namePattern))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get merged non-direct mapping outputs for the same asset ID This returns the pre-computed
     * merged outputs from the scenario
     */
    public List<JsonNode> getMergedNonDirectMappingOutputs(YamlTestScenario scenario) {
        return scenario.getMergedNonDirectMappingOutputs();
    }

    /** Validate that direct mapping and non-direct mapping outputs are properly separated */
    public boolean validateOutputSeparation(YamlTestScenario scenario) {
        List<JsonNode> directOutputs = scenario.getDirectMappingOutputs();
        List<JsonNode> nonDirectOutputs = scenario.getNonDirectMappingOutputs();

        // Check that direct mapping outputs only contain points that exist in input
        for (JsonNode directOutput : directOutputs) {
            if (!isDirectMappingOutput(scenario.getInputJson(), directOutput)) {
                log.warn("Direct mapping output contains calculated points: {}", directOutput);
                return false;
            }
        }

        // Check that non-direct mapping outputs contain calculated points
        for (JsonNode nonDirectOutput : nonDirectOutputs) {
            JsonNode outputPoints = nonDirectOutput.get("payload").get(0).get("points");
            if (!hasCalculatedPointNames(outputPoints)) {
                log.warn(
                        "Non-direct mapping output contains only direct mapping points: {}",
                        nonDirectOutput);
                return false;
            }
        }

        return true;
    }

    /** Get all unique asset IDs from a scenario's outputs */
    public Set<String> getOutputAssetIds(YamlTestScenario scenario) {
        Set<String> assetIds = new HashSet<>();

        for (JsonNode output : scenario.getExpectedOutputJsons()) {
            JsonNode payload = output.get("payload");
            if (payload.isArray()) {
                for (JsonNode payloadItem : payload) {
                    assetIds.add(payloadItem.get("assetId").asText());
                }
            }
        }

        return assetIds;
    }

    /** Validate that non-direct mapping results for the same asset are properly merged */
    public boolean validateAssetMerging(YamlTestScenario scenario) {
        List<JsonNode> nonDirectOutputs = scenario.getNonDirectMappingOutputs();
        List<JsonNode> mergedOutputs = scenario.getMergedNonDirectMappingOutputs();

        // Count unique assets in original outputs
        Set<String> originalAssets = new HashSet<>();
        for (JsonNode output : nonDirectOutputs) {
            String assetId = output.get("payload").get(0).get("assetId").asText();
            originalAssets.add(assetId);
        }

        // Count unique assets in merged outputs
        Set<String> mergedAssets = new HashSet<>();
        for (JsonNode output : mergedOutputs) {
            String assetId = output.get("payload").get(0).get("assetId").asText();
            mergedAssets.add(assetId);
        }

        // Merged outputs should have same or fewer messages (due to merging)
        boolean validMerging =
                mergedOutputs.size() <= nonDirectOutputs.size()
                        && originalAssets.equals(mergedAssets);

        if (!validMerging) {
            log.warn(
                    "Asset merging validation failed. Original assets: {}, Merged assets: {}",
                    originalAssets,
                    mergedAssets);
        }

        return validMerging;
    }
}
