package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobTypeEnum;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.RecCalcMetaInfo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TblCalcJobInfo {

    private String jobId;

    private String prefRuleId;

    private RecCalcMetaInfo ruleInfo;

    private long calcStartTime;

    private long calcEndTime;

    private int status = ReCalcJobStatusEnum.INIT.getCode();

    private int type = ReCalcJobTypeEnum.DEFAULT.getCode();

    private String srcOrgId;

    private String targetOrgId;

    private int progress = 0;

    private long startedTime;
    private long finishedTime;
    private long cancelledTime;

    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
