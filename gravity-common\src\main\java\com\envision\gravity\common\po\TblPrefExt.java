package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblPrefExt {
    @KeyColumn(name = "pref_id")
    @ColumnName("pref_id")
    @RequiredField(message = "pref_id field is required")
    private String prefId;

    @KeyColumn(name = "comp_id")
    @ColumnName("comp_id")
    @RequiredField(message = "comp_id field is required")
    private String compId;

    @KeyColumn(name = "category")
    @ColumnName("category")
    private String category;

    @ValueColumn(name = "source_type")
    @ColumnName("source_type")
    private String sourceType;

    @ValueColumn(name = "source_value")
    @ColumnName("source_value")
    private String sourceValue;

    @ValueColumn(name = "metric_type", type = Integer.class)
    @ColumnName("metric_type")
    private Long metricType;

    @ValueColumn(name = "db_name")
    @ColumnName("db_name")
    private String dbName;

    @ValueColumn(name = "table_type", type = Integer.class)
    @ColumnName("table_type")
    private Long tableType;

    @ValueColumn(name = "table_name")
    @ColumnName("table_name")
    private String tableName;

    @ValueColumn(name = "raw_expression")
    @ColumnName("raw_expression")
    private String rawExpression;

    @ValueColumn(name = "agg_expression")
    @ColumnName("agg_expression")
    private String aggExpression;

    @ValueColumn(name = "filter")
    @ColumnName("filter")
    private String filter;

    @ValueColumn(name = "extra_tables")
    @ColumnName("extra_tables")
    private String extraTables;

    @ValueColumn(name = "extra_attributes")
    @ColumnName("extra_attributes")
    private String extraAttributes;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
