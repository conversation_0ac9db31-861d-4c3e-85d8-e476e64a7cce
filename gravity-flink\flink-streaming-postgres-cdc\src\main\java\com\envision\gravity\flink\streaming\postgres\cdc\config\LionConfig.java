package com.envision.gravity.flink.streaming.postgres.cdc.config;


import com.envision.arch.lion.client.ConfigCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@Slf4j
public class LionConfig {

    public static String getStringValue(String configName, String defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        return val != null ? val : defaultVal;
    }

    public static int getIntValue(String configName, Integer defaultVal) {
        String val = null;

        try {
            val = ConfigCache.getInstance().getProperty(configName);
        } catch (Exception var4) {
            log.error("get key error, key = " + configName, var4);
        }

        if (null != val) {
            return Integer.parseInt(val);
        }
        return defaultVal;
    }

    public static String getIgniteAddress() {
        return getStringValue("gravity-common.ignite.address", null);
    }

    public static String getIgniteUsername() {
        return getStringValue("gravity-common.ignite.username", "ignite");
    }

    public static String getIgnitePassword() {
        return getStringValue("gravity-common.ignite.password", "ignite");
    }

    public static String getPgHostname() {
        return getStringValue("gravity-common.postgresql.hostname", null);
    }

    public static int getPgPort() {
        return getIntValue("gravity-common.postgresql.port", 5432);
    }

    public static String getPgUsername() {
        return getStringValue("gravity-common.postgresql.username", "postgres");
    }

    public static String getPgPassword() {
        return getStringValue("gravity-common.postgresql.password", "postgres");
    }

    public static String getPgDriverClassName() {
        return getStringValue("gravity-common.postgresql.jdbc-driver", "org.postgresql.Driver");
    }

    public static int getPgMaxPoolSize() {
        return getIntValue("gravity-flink.gravity-core.pg.datasource.max.pool.size", 8);
    }

    public static String getPgJdbcUrl() {
        return getStringValue("gravity-common.postgresql.jdbc-url", null);
    }

    public static String getPgDatabase() {
        return getStringValue("gravity-flink.pg.cdc.database", "gravity");
    }

    public static String getPgSchemaList() {
        return getStringValue(
                "gravity-flink.gravity-core.pg.cdc.schema.list", "^(o|sysenos2018).*");
    }

    public static String getPgTableList() {
        return getStringValue(
                "gravity-flink.gravity-core.pg.cdc.table.list",
                "^o.*\\.tbl_bo$,^o.*\\.tbl_bo_model$,^o.*\\.tbl_component$,^o.*\\.tbl_component_pref$,^o.*\\.tbl_pref$,^o.*\\.tbl_bo_model_comp$");
    }

    public static String getSlotName() {
        return getStringValue("gravity-flink.gravity-core.pg.cdc.slot.name", "gravity_core_pg_cdc");
    }

    public static int getRefreshTimeWindowIntervalInMs() {
        return getIntValue("gravity-flink.gravity-core.refresh.time.window.interval.in.ms", 1000);
    }

    public static String getNebulaAddr() {
        return getStringValue("gravity-common.nebula.address", null);
    }

    public static String getNebulaUsername() {
        return getStringValue("gravity-common.nebula.username", "root");
    }

    public static String getNebulaPwd() {
        return getStringValue("gravity-common.nebula.password", "Envisi0n4321!");
    }

    public static int getNebulaSessionCacheSize() {
        return getIntValue("gravity-flink.gravity-core.nebula.session.cache.size", 10);
    }

    public static int getNebulaSessionDurationInMinutes() {
        return getIntValue(
                "gravity-flink.gravity-core.nebula.session.cache.duration.in.minutes", 30);
    }

    public static int getRefreshObjectBatchSize() {
        return getIntValue("gravity-flink.gravity-core.refresh.object.batch.size", 100);
    }

    public static String getMaxBatchSize() {
        return getStringValue("gravity-flink.gravity-core.source.max.batch.size", "1000");
    }

    public static String getMaxQueueSize() {
        return getStringValue("gravity-flink.gravity-core.source.max.queue.size", "2000");
    }
}
