package com.envision.gravity.flink.streaming.calculate.stream.serde;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LegacyMsgParser implements MsgParser {

    private static final Logger logger = LoggerFactory.getLogger(LegacyMsgParser.class);

    private static final String JSON_KEY_PAYLOAD = "payload";

    private final ObjectMapper jacksonMapper;

    public LegacyMsgParser() {
        this.jacksonMapper = new ObjectMapper();
    }

    @Override
    public Msg parse(String pointJson) {
        JsonNode pointTree;
        try {
            pointTree = jacksonMapper.readTree(pointJson);
        } catch (JsonProcessingException e) {
            logger.error("Invalid json format, original json: " + pointJson);
            throw new PointParseException(pointJson, e);
        }

        JsonNode payloadNode = pointTree.get(JSON_KEY_PAYLOAD);
        if (payloadNode == null) {
            logger.error(
                    "Illegal point, payload json key is not found, original json: " + pointJson);
            throw new PointParseException(
                    pointJson, "Illegal point, payload json key is not found.");
        }

        LegacyMsg rawPoint;
        if (payloadNode.isArray()) {
            try {
                rawPoint = jacksonMapper.readValue(pointJson, LegacyMsgWithMultiAssets.class);
            } catch (JsonProcessingException e) {
                logger.error(
                        "Invalid V2LegacyMultiAssetsPoint schema, original json is: " + pointJson);
                throw new PointParseException(pointJson, e);
            }
        } else {
            try {
                rawPoint = jacksonMapper.readValue(pointJson, LegacyMsgWithSingleAsset.class);
            } catch (JsonProcessingException e) {
                logger.error(
                        "Invalid V2LegacyOneAssetPoint schema, original json is: " + pointJson);
                throw new PointParseException(pointJson, e);
            }
        }

        if (StringUtils.isEmpty(rawPoint.getOrgId())
                || StringUtils.isEmpty(rawPoint.getModelId())) {
            logger.error("orgId or modelId is empty, original json is: " + pointJson);
            throw new PointParseException(pointJson, "orgId or modelId is empty");
        }

        return rawPoint;
    }
}
