package com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.pool;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.config.LionConfig;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.SessionsManagerConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
@Slf4j
public class NebulaSessionPool {

    private String nebulaUser;
    private String nebulaPwd;
    private String nebulaAddr;

    private LoadingCache<String, EnvisionSessionsManager> cache;
    private static volatile NebulaSessionPool NEBULA_SESSION_POOL;

    public static NebulaSessionPool getInstance() {
        if (NEBULA_SESSION_POOL == null) {
            synchronized (NebulaSessionPool.class) {
                if (NEBULA_SESSION_POOL == null) {
                    NEBULA_SESSION_POOL = new NebulaSessionPool();
                }
            }
        }
        return NEBULA_SESSION_POOL;
    }

    private NebulaSessionPool() {
        initNebulaSessionCache();
    }

    private void initNebulaSessionCache() {
        nebulaUser = LionConfig.getNebulaUsername();
        nebulaPwd = LionConfig.getNebulaPwd();
        nebulaAddr = LionConfig.getNebulaAddr();
        int sessionCacheSize = LionConfig.getNebulaSessionCacheSize();
        int sessionCacheDuration = LionConfig.getNebulaSessionDurationInMinutes();

        cache =
                Caffeine.newBuilder()
                        .initialCapacity(1)
                        .maximumSize(sessionCacheSize)
                        .expireAfterAccess(sessionCacheDuration, TimeUnit.MINUTES)
                        .expireAfterWrite(sessionCacheDuration, TimeUnit.MINUTES)
                        .removalListener(
                                (RemovalListener<? super Object, ? super Object>)
                                        (key, value, cause) -> {
                                            log.info(
                                                    "Session cache remove, key:{}, value:{}, cause:{}",
                                                    key,
                                                    value,
                                                    cause);
                                            if (value instanceof EnvisionSessionsManager) {
                                                EnvisionSessionsManager manager =
                                                        (EnvisionSessionsManager) value;
                                                manager.close();
                                            }
                                        })
                        .build(new SessionQueueCacheLoader());

        log.info("Init nebula session cache success.");
    }

    private class SessionQueueCacheLoader implements CacheLoader<String, EnvisionSessionsManager> {
        @Override
        @NonNull
        public EnvisionSessionsManager load(@NonNull String spaceName) {
            SessionsManagerConfig config = new SessionsManagerConfig();
            config.setPoolConfig(genPoolConfig());
            List<HostAddress> addresses =
                    Arrays.stream(nebulaAddr.split(","))
                            .map(
                                    addr -> {
                                        String[] ipPort = addr.split(":");
                                        if (ipPort.length != 2) {
                                            throw new GravityRuntimeException(
                                                    "Invalid nebula address config!");
                                        }
                                        return new HostAddress(
                                                ipPort[0], Integer.parseInt(ipPort[1]));
                                    })
                            .collect(Collectors.toList());
            config.setAddresses(addresses);
            config.setSpaceName(spaceName);
            config.setUserName(nebulaUser);
            config.setPassword(nebulaPwd);
            config.setReconnect(true);
            return new EnvisionSessionsManager(config);
        }
    }

    public EnvisionSessionWrapper getSession(String spaceName) {
        return doGetSession(spaceName);
    }

    private EnvisionSessionWrapper doGetSession(String spaceName) {
        String sessionCacheKey = buildSessionCacheKey(spaceName);
        try {
            // maybe trigger removalListener
            EnvisionSessionsManager manager = cache.get(sessionCacheKey);
            if (manager != null) {
                EnvisionSessionWrapper ret = manager.getSessionWrapper();
                while (ret.noAvailable()) {
                    manager.returnSessionWrapper(ret);
                    ret = manager.getSessionWrapper();
                }

                if (!ret.ping()) {
                    throw new GravityRuntimeException(
                            String.format("Session can not ping out, spaceName: %s", spaceName));
                }
                return ret;
            } else {
                throw new GravityRuntimeException("SessionsManager is null!");
            }
        } catch (RuntimeException | ClientServerIncompatibleException e) {
            log.error("Get session cache error, fail: {}", e.getMessage());
            throw new GravityRuntimeException(
                    "Get session cache error, fail: " + e.getMessage(), e);
        }
    }

    public void returnSession(EnvisionSessionWrapper session, String spaceName) {
        if (null == session) {
            return;
        }
        String sessionCacheKey = buildSessionCacheKey(spaceName);
        EnvisionSessionsManager manager = cache.get(sessionCacheKey);
        if (manager != null) {
            manager.returnSessionWrapper(session);
        }
    }

    /** By spaceName */
    public String buildSessionCacheKey(String spaceName) {
        return spaceName;
    }

    private static NebulaPoolConfig genPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMinConnSize(1);
        nebulaPoolConfig.setMaxConnSize(50);
        nebulaPoolConfig.setIdleTime(0);
        nebulaPoolConfig.setWaitTime(0);
        nebulaPoolConfig.setTimeout(0);
        nebulaPoolConfig.setIntervalIdle(-1);
        nebulaPoolConfig.setMinClusterHealthRate(0.5);
        return nebulaPoolConfig;
    }

    public void refreshSession(String spaceName) {
        String key = buildSessionCacheKey(spaceName);
        cache.invalidate(key);
    }

    public void refreshAllSession() {
        cache.invalidateAll();
    }
}
