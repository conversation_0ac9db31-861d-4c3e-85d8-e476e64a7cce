package com.envision.gravity.low.level.api.rest.dao.pg;

import com.envision.gravity.common.vo.search.Sorter;
import com.envision.gravity.low.level.api.rest.dto.SearchSubGraphCondition;
import com.envision.gravity.low.level.api.rest.dto.SearchSubGraphEdgesCondition;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.enums.SubGraphSearchSupportFieldEnum;

import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/04/17 19:39 @Description: */
public class SearchSubGraphSqlProvider {

    public String searchVidInSubGraph(List<String> subGraphIds, List<String> vids, String orgId) {
        List<String> replaceSubGraphIds = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            replaceSubGraphIds.add("'" + subGraphId + "'");
        }

        SQL fromVidSql = new SQL();
        fromVidSql.SELECT_DISTINCT("sub_graph_id");
        fromVidSql.SELECT("from_vid");
        fromVidSql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        fromVidSql.WHERE(
                "from_vid in ("
                        + String.join(", ", vids)
                        + ") and sub_graph_id in ("
                        + String.join(", ", replaceSubGraphIds)
                        + ")");

        SQL toVidSql = new SQL();
        toVidSql.SELECT("sub_graph_id");
        toVidSql.SELECT("to_vid");
        toVidSql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        toVidSql.WHERE(
                "to_vid in ("
                        + String.join(", ", vids)
                        + ") and sub_graph_id in ("
                        + String.join(", ", replaceSubGraphIds)
                        + ")");

        SQL startVidSql = new SQL();
        startVidSql.SELECT("sub_graph_id");
        startVidSql.SELECT("start_vid");
        startVidSql.FROM(orgId + Constants.TBL_START_VID_TABLE_NAME);
        startVidSql.WHERE(
                "start_vid in ("
                        + String.join(", ", vids)
                        + ") and sub_graph_id in ("
                        + String.join(", ", replaceSubGraphIds)
                        + ")");
        return fromVidSql + " union " + toVidSql + " union " + startVidSql;
    }

    public String countEdgesTotalSizeWithCondition(
            SearchSubGraphEdgesCondition condition, String orgId) {
        return "select count(1) from (" + getEdgeSql(condition, orgId) + ") as tmp";
    }

    public String searchEdgesWithCondition(SearchSubGraphEdgesCondition condition, String orgId) {
        return getEdgeSql(condition, orgId);
    }

    public String searchEdgesWithPaginationAndCondition(
            SearchSubGraphEdgesCondition condition, int limit, int offset, String orgId) {
        return getEdgeSql(condition, orgId) + " limit " + limit + " offset " + offset;
    }

    public String searchAllEdges(String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("edge_type_id");
        sql.SELECT("prop_value");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        return sql.toString();
    }

    public String searchEdgesByPagination(int limit, int offset, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(*) over () as total_size");
        sql.SELECT("sub_graph_id");
        sql.SELECT("from_vid");
        sql.SELECT("to_vid");
        sql.SELECT("edge_type_id");
        sql.SELECT("prop_value");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_EDGE_TABLE_NAME);
        sql.LIMIT(limit);
        sql.OFFSET(offset);
        return sql.toString();
    }

    private String getEdgeSql(SearchSubGraphEdgesCondition condition, String orgId) {
        List<String> fromVidConditions = condition.getFromVidConditions();
        List<String> toVidConditions = condition.getToVidConditions();
        int maxStep = condition.getMaxStep();
        boolean fromEmpty = fromVidConditions == null || fromVidConditions.isEmpty();
        boolean toEmpty = toVidConditions == null || toVidConditions.isEmpty();
        String sql;
        if (fromEmpty && toEmpty) {
            String allCondition = condition.getBaseCondition();
            sql =
                    "select from_vid,to_vid,edge_type_id,sub_graph_id,prop_value,created_time,created_user,modified_time,modified_user from (with recursive edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id inner join "
                            + orgId
                            + Constants.TBL_START_VID_TABLE_NAME
                            + " s on g.sub_graph_id = s.sub_graph_id and e.from_vid = s.start_vid where "
                            + allCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder f where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + ") search breadth first by from_vid set breadth_order select *,ROW_NUMBER() OVER (PARTITION BY e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id ORDER BY steps) as row_num from edge_finder e order by e.sub_graph_id, breadth_order, e.prop_value -> 'order') query where row_num = 1";
        } else if (fromEmpty) {
            String allCondition = String.join(" and ", toVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                allCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "select from_vid,to_vid,edge_type_id,sub_graph_id,prop_value,created_time,created_user,modified_time,modified_user from (with recursive edge_finder_reverse as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + allCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder_reverse f where f.from_vid = e.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + ") search breadth first by from_vid set breadth_order select *,ROW_NUMBER() OVER (PARTITION BY e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id ORDER BY steps) as row_num from edge_finder_reverse e order by e.sub_graph_id, breadth_order, e.prop_value -> 'order') query where row_num = 1";
        } else if (toEmpty) {
            String allCondition = String.join(" and ", fromVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                allCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "select from_vid,to_vid,edge_type_id,sub_graph_id,prop_value,created_time,created_user,modified_time,modified_user from (with recursive edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + allCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder f where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + ") search breadth first by from_vid set breadth_order select *,ROW_NUMBER() OVER (PARTITION BY e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id ORDER BY steps) as row_num from edge_finder e order by e.sub_graph_id, breadth_order, e.prop_value -> 'order') query where row_num = 1";
        } else {
            String fromAllCondition = String.join(" and ", fromVidConditions);
            String toAllCondition = String.join(" and ", toVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                fromAllCondition += " and " + condition.getBaseCondition();
                toAllCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "select from_vid,to_vid,edge_type_id,sub_graph_id,prop_value,created_time,created_user,modified_time,modified_user from ("
                            + "with recursive edge_finder_reverse as "
                            + "(select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, 1 as reverse_steps "
                            + String.format(
                                    "from %s.tbl_edge e inner join %s.tbl_sub_graph g on e.sub_graph_id = g.sub_graph_id where (%s) ",
                                    orgId, orgId, toAllCondition)
                            + "union "
                            + "select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user, f.reverse_steps+1 as reverse_steps "
                            + String.format(
                                    "from %s.tbl_edge e, edge_finder_reverse f where e.to_vid = f.from_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and reverse_steps < %s), ",
                                    orgId, maxStep)
                            + "edge_finder as "
                            + "(select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user,e.reverse_steps, 1 as steps "
                            + String.format(
                                    "from edge_finder_reverse e inner join %s.tbl_sub_graph g on e.sub_graph_id = g.sub_graph_id where (%s) ",
                                    orgId, fromAllCondition)
                            + "union "
                            + "select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value,e.created_time,e.created_user,e.modified_time,e.modified_user,e.reverse_steps, f.steps+1 as steps "
                            + String.format(
                                    "from edge_finder f, edge_finder_reverse e where f.to_vid = e.from_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.steps < %s) ",
                                    maxStep)
                            + "search breadth first by from_vid set breadth_order select *,ROW_NUMBER() OVER (PARTITION BY e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id ORDER BY steps) as row_num from edge_finder e order by e.sub_graph_id, breadth_order, e.prop_value -> 'order') query where row_num = 1";
        }
        return sql;
    }

    public String searchSubGraphWithCondition(
            SearchSubGraphCondition condition, List<Sorter> sorters, String orgId) {
        return getSubGraphSql(condition, sorters, orgId);
    }

    public String searchSubGraphBySubGraphIds(
            List<String> subGraphIds, List<Sorter> sorters, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("sub_graph_display_name");
        sql.SELECT("sub_graph_tags");
        sql.SELECT("tree");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_SUB_GRAPH_TABLE_NAME);
        if (!subGraphIds.isEmpty()) {
            List<String> replaceSubGraphIds = new ArrayList<>();
            for (String subGraphId : subGraphIds) {
                replaceSubGraphIds.add("'" + subGraphId + "'");
            }
            sql.WHERE("sub_graph_id in (" + String.join(", ", replaceSubGraphIds) + ")");
        }
        if (sorters != null && !sorters.isEmpty()) {
            StringBuilder order = new StringBuilder();
            for (int i = 0; i < sorters.size(); i++) {
                Sorter sorter = sorters.get(i);
                order.append(sorter.getField()).append(" ").append(sorter.getOrder());
                if (i < sorters.size() - 1) {
                    order.append(", ");
                }
            }
            if (!order.toString().isEmpty()) {
                sql.ORDER_BY(order.toString());
            }
        }
        return sql.toString();
    }

    public String searchSubGraphWithPaginationAndCondition(
            SearchSubGraphCondition condition,
            List<Sorter> sorters,
            int limit,
            int offset,
            String orgId) {
        return getSubGraphSql(condition, sorters, orgId) + " limit " + limit + " offset " + offset;
    }

    public String countSubGraphTotalSizeWithCondition(
            SearchSubGraphCondition condition, String orgId) {
        return "select count(1) from (" + getSubGraphSql(condition, null, orgId) + ") as tmp";
    }

    private String getSubGraphSql(
            SearchSubGraphCondition condition, List<Sorter> sorters, String orgId) {
        List<String> fromVidConditions = condition.getFromVidConditions();
        List<String> toVidConditions = condition.getToVidConditions();
        int maxStep = condition.getMaxStep();
        boolean fromEmpty = fromVidConditions == null || fromVidConditions.isEmpty();
        boolean toEmpty = toVidConditions == null || toVidConditions.isEmpty();
        String sql;
        String sorterStr = "";
        if (sorters != null && !sorters.isEmpty()) {
            StringBuilder order = new StringBuilder("order by ");
            for (int i = 0; i < sorters.size(); i++) {
                Sorter sorter = sorters.get(i);
                order.append(String.format("g.%s %s", sorter.getField(), sorter.getOrder()));
                if (i < sorters.size() - 1) {
                    order.append(", ");
                }
            }
            sorterStr = order.toString();
        }
        if (fromEmpty && toEmpty) {
            String allCondition = condition.getBaseConditionWithVid();
            if (allCondition.contains(SubGraphSearchSupportFieldEnum.EDGE_TYPE.getSqlField())
                    || allCondition.contains(
                            SubGraphSearchSupportFieldEnum.VID.getSupportField())) {
                sql =
                        "select distinct g.sub_graph_id, g.sub_graph_display_name, g.sub_graph_tags, g.tree, g.created_time, g.created_user, g.modified_time, g.modified_user from "
                                + orgId
                                + Constants.TBL_EDGE_TABLE_NAME
                                + " e right join "
                                + orgId
                                + Constants.TBL_SUB_GRAPH_TABLE_NAME
                                + " g on e.sub_graph_id = g.sub_graph_id left join "
                                + orgId
                                + Constants.TBL_START_VID_TABLE_NAME
                                + " s on g.sub_graph_id = s.sub_graph_id where "
                                + allCondition
                                + " "
                                + sorterStr;
            } else {
                sql =
                        "select g.sub_graph_id, g.sub_graph_display_name, g.sub_graph_tags, g.tree, g.created_time, g.created_user, g.modified_time, g.modified_user from "
                                + orgId
                                + Constants.TBL_SUB_GRAPH_TABLE_NAME
                                + " g where "
                                + allCondition
                                + " "
                                + sorterStr;
            }
        } else if (fromEmpty) {
            String allCondition = String.join(" and ", toVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                allCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "with recursive edge_finder_reverse as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + allCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder_reverse f where f.from_vid = e.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + ") select distinct g.sub_graph_id, g.sub_graph_display_name, g.sub_graph_tags, g.tree, g.created_time, g.created_user, g.modified_time, g.modified_user from edge_finder_reverse e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id left join "
                            + orgId
                            + Constants.TBL_START_VID_TABLE_NAME
                            + " s on g.sub_graph_id = s.sub_graph_id where "
                            + condition.getBaseConditionWithVid()
                            + " "
                            + sorterStr;
        } else if (toEmpty) {
            String allCondition = String.join(" and ", fromVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                allCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "with recursive edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + allCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder f where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + ") select distinct g.sub_graph_id, g.sub_graph_display_name, g.sub_graph_tags, g.tree, g.created_time, g.created_user, g.modified_time, g.modified_user from edge_finder e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id left join "
                            + orgId
                            + Constants.TBL_START_VID_TABLE_NAME
                            + " s on g.sub_graph_id = s.sub_graph_id where "
                            + condition.getBaseConditionWithVid()
                            + " "
                            + sorterStr;
        } else {
            String fromAllCondition = String.join(" and ", fromVidConditions);
            String toAllCondition = String.join(" and ", toVidConditions);
            if (!condition.getBaseCondition().isEmpty()) {
                fromAllCondition += " and " + condition.getBaseCondition();
                toAllCondition += " and " + condition.getBaseCondition();
            }
            sql =
                    "with recursive edge_finder as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + fromAllCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps from "
                            + orgId
                            + Constants.TBL_EDGE_TABLE_NAME
                            + " e, edge_finder f where e.from_vid = f.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and steps < "
                            + maxStep
                            + "), edge_finder_reverse as (select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, 1 as steps from edge_finder e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id where "
                            + toAllCondition
                            + " union select e.from_vid,e.to_vid,e.edge_type_id,e.sub_graph_id,e.prop_value, f.steps+1 as steps from edge_finder e, edge_finder_reverse f where f.from_vid = e.to_vid and e.sub_graph_id = f.sub_graph_id and e.edge_type_id = f.edge_type_id and f.steps < "
                            + maxStep
                            + ") select distinct g.sub_graph_id, g.sub_graph_display_name, g.sub_graph_tags, g.tree, g.created_time, g.created_user, g.modified_time, g.modified_user from edge_finder_reverse e inner join "
                            + orgId
                            + Constants.TBL_SUB_GRAPH_TABLE_NAME
                            + " g on e.sub_graph_id = g.sub_graph_id left join "
                            + orgId
                            + Constants.TBL_START_VID_TABLE_NAME
                            + " s on g.sub_graph_id = s.sub_graph_id where "
                            + condition.getBaseConditionWithVid()
                            + " "
                            + sorterStr;
        }
        return sql;
    }

    public String searchSubGraphByPagination(
            List<Sorter> sorters, int limit, int offset, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("sub_graph_display_name");
        sql.SELECT("sub_graph_tags");
        sql.SELECT("tree");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_SUB_GRAPH_TABLE_NAME);
        if (sorters != null && !sorters.isEmpty()) {
            StringBuilder order = new StringBuilder();
            for (int i = 0; i < sorters.size(); i++) {
                Sorter sorter = sorters.get(i);
                order.append(sorter.getField()).append(" ").append(sorter.getOrder());
                if (i < sorters.size() - 1) {
                    order.append(", ");
                }
            }
            if (!order.toString().isEmpty()) {
                sql.ORDER_BY(order.toString());
            }
        }
        sql.LIMIT(limit);
        sql.OFFSET(offset);
        return sql.toString();
    }

    public String countSubGraphTotalSizeNotCondition(String orgId) {
        SQL sql = new SQL();
        sql.SELECT("count(1)");
        sql.FROM(orgId + Constants.TBL_SUB_GRAPH_TABLE_NAME);
        return sql.toString();
    }

    public String searchAllSubGraphs(List<Sorter> sorters, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("sub_graph_display_name");
        sql.SELECT("sub_graph_tags");
        sql.SELECT("tree");
        sql.SELECT("created_time");
        sql.SELECT("created_user");
        sql.SELECT("modified_time");
        sql.SELECT("modified_user");
        sql.FROM(orgId + Constants.TBL_SUB_GRAPH_TABLE_NAME);
        if (sorters != null && !sorters.isEmpty()) {
            StringBuilder order = new StringBuilder();
            for (int i = 0; i < sorters.size(); i++) {
                Sorter sorter = sorters.get(i);
                order.append(sorter.getField()).append(" ").append(sorter.getOrder());
                if (i < sorters.size() - 1) {
                    order.append(", ");
                }
            }
            if (!order.toString().isEmpty()) {
                sql.ORDER_BY(order.toString());
            }
        }
        return sql.toString();
    }
}
