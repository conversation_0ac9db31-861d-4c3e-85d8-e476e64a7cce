package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.common.po.TblCategory;

import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/03/04 16:44 @Description: */
public interface TblCategoryMapper {

    void getExistCategory(List<String> categoryIds);

    @SelectProvider(type = TblCategorySqlProvider.class, method = "queryCategoryByCategoryId")
    @Results({
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "category_display_name",
                property = "categoryDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    TblCategory queryCategoryByCategoryId(String categoryId, String orgId);

    void queryCategoryByCategoryName(String categoryName);

    void queryCategoryByCategoryNameList(List<String> categoryNameList);

    @SelectProvider(type = TblCategorySqlProvider.class, method = "queryAllCategory")
    @Results({
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "category_display_name",
                property = "categoryDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblCategory> queryAllCategory(String orgId);

    @SelectProvider(type = TblCategorySqlProvider.class, method = "queryCategoryWithPagination")
    @Results({
        @Result(column = "category_id", property = "categoryId", jdbcType = JdbcType.VARCHAR),
        @Result(
                column = "category_display_name",
                property = "categoryDisplayName",
                jdbcType = JdbcType.VARCHAR),
        @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "created_user", property = "createdUser", jdbcType = JdbcType.VARCHAR),
        @Result(column = "modified_time", property = "modifiedTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "modified_user", property = "modifiedUser", jdbcType = JdbcType.VARCHAR)
    })
    List<TblCategory> queryCategoryWithPagination(int limit, int offset, String orgId);

    @SelectProvider(type = TblCategorySqlProvider.class, method = "countCategoryNum")
    int countCategoryNum(String orgId);
}
