package com.envision.gravity.common.ignite.service;

import com.envision.gravity.common.vo.api.LoadCacheReq;

import java.util.List;


import org.apache.ignite.services.Service;

/**
 * <AUTHOR>
 * @date 2024/6/19
 * @description
 */
public interface BOLowLevelService extends Service {
    String SERVICE_NAME = "BOLowLevelIgniteService";

    /**
     * load cache within the ignite cluster
     *
     * @param req {@link LoadCacheReq}
     */
    void loadCache(LoadCacheReq req);

    void initOU(String ouId, String resourceLevel);

    /** register cache table and then load data from pg */
    void restoreCacheTable();

    /**
     * load external storage by cache list
     *
     * @param cacheNameList cache name list
     */
    void loadExternalStorage(List<String> cacheNameList);
}
