package com.envision.gravity.flink.streaming.calculate.stream;

import java.io.StringWriter;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Basic unit tests for Velocity SQL template rendering
 *
 * <p>Tests the query-latest-values.vm template without complex dependencies: 1. Template loading 2.
 * Basic SQL generation 3. Variable substitution
 */
public class VelocityTemplateBasicTest {

    private VelocityEngine velocityEngine;

    @BeforeEach
    void setUp() {
        velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        velocityEngine.setProperty(
                "classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        velocityEngine.init();
    }

    @Test
    void testTemplateExists() {
        // Test that the template file exists and can be loaded
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        assertNotNull(template, "Template should be loaded successfully");
    }

    @Test
    void testBasicTemplateRendering() {
        // Test basic template rendering with minimal context
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", Collections.singleton("asset001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify basic SQL structure
        assertNotNull(sql);
        assertFalse(sql.trim().isEmpty());
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("FROM"));
        assertTrue(sql.contains("o123456"));
        assertTrue(sql.contains("testModel"));
        assertTrue(sql.contains("asset001"));
    }

    @Test
    void testTemplateWithMultipleAssets() {
        // Test template with multiple assets
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", new HashSet<>(Arrays.asList("asset001", "asset002", "asset003")));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Should contain all asset IDs
        assertTrue(sql.contains("asset001"));
        assertTrue(sql.contains("asset002"));
        assertTrue(sql.contains("asset003"));
        assertTrue(sql.contains("WHERE asset_id IN ("));
    }

    @Test
    void testTemplateWithEmptyAssets() {
        // Test template with empty asset list
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", new HashSet<>());

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Should still have basic structure
        assertTrue(sql.contains("SELECT"));
        assertTrue(sql.contains("FROM"));
        assertTrue(sql.contains("testModel"));
    }

    @Test
    void testTemplateWithSpecialCharacters() {
        // Test template with special characters in names
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "test_model_with_underscores");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", Collections.singleton("asset_001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify special characters are handled correctly
        assertTrue(sql.contains("test_model_with_underscores"));
        assertTrue(sql.contains("asset_001"));
    }

    @Test
    void testTemplateVariableSubstitution() {
        // Test that all required variables are properly substituted
        VelocityContext context = new VelocityContext();
        context.put("orgId", "testOrg");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", Collections.singleton("testAsset"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Should not contain any unresolved variables
        assertFalse(sql.contains("$orgId"));
        assertFalse(sql.contains("$modelId"));
        assertFalse(sql.contains("$assetIds"));
        assertFalse(sql.contains("${"));

        // Should contain the actual values
        assertTrue(sql.contains("testOrg"));
        assertTrue(sql.contains("testModel"));
        assertTrue(sql.contains("testAsset"));
    }

    @Test
    void testSqlStructureIntegrity() {
        // Test that generated SQL has proper structure
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());
        context.put("assetIds", Collections.singleton("asset001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString().trim();

        // Basic SQL structure checks
        assertTrue(sql.startsWith("SELECT"), "SQL should start with SELECT");
        assertTrue(sql.contains("FROM"), "SQL should contain FROM clause");
        assertTrue(sql.contains("WHERE"), "SQL should contain WHERE clause");

        // Check for proper SQL syntax elements
        assertTrue(sql.contains("asset_id"), "SQL should select asset_id");
        assertTrue(sql.contains("/*+ ORG("), "SQL should contain ORG hint");

        // Should not have syntax errors (basic check)
        assertFalse(sql.contains(",,"), "SQL should not have double commas");
        // Note: Some whitespace is normal in SQL, so we check for very excessive whitespace (5+
        // spaces)
        assertFalse(sql.contains("     "), "SQL should not have very excessive whitespace");
    }

    @Test
    void testTemplatePerformance() {
        // Test that template rendering is reasonably fast
        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", new HashSet<>());

        // Create a large asset list
        Set<String> assetIds = new HashSet<>();
        for (int i = 0; i < 1000; i++) {
            assetIds.add("asset" + String.format("%04d", i));
        }
        context.put("assetIds", assetIds);

        Template template = velocityEngine.getTemplate("query-latest-values.vm");

        long startTime = System.currentTimeMillis();
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        long endTime = System.currentTimeMillis();

        String sql = writer.toString();

        // Should complete within reasonable time (less than 1 second)
        assertTrue(endTime - startTime < 1000, "Template rendering should be fast");

        // Should contain all assets
        assertTrue(sql.contains("asset0000"));
        assertTrue(sql.contains("asset0999"));
        // Count actual asset references (each asset appears multiple times in SQL)
        int assetCount = 0;
        for (int i = 0; i < 1000; i++) {
            String assetId = "asset" + String.format("%04d", i);
            if (sql.contains(assetId)) {
                assetCount++;
            }
        }
        assertEquals(1000, assetCount, "Should contain all 1000 unique assets");
    }

    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
