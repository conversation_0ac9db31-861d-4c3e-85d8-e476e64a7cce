package com.envision.gravity.low.level.api.sql.table.cache;

import com.envision.gravity.low.level.api.sql.table.CacheTableInfo;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.*;


import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.QueryIndexType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
@Data
@Builder
@NoArgsConstructor
public class TblBOCacheTableInfo implements CacheTableInfo {
    public static final List<JdbcTypeField> KEY_FIELDS;
    public static final List<JdbcTypeField> VALUE_FIELDS;
    public static final Set<String> QUERY_ENTITY_KEY_FIELDS;
    public static final Set<String> NOT_NULL_FIELDS;
    public static final List<QueryIndex> INDEXES;
    public static final LinkedHashMap<String, String> QUERY_ENTITY_FIELDS;

    static {
        // keyFields
        KEY_FIELDS =
                Collections.singletonList(
                        new JdbcTypeField(Types.VARCHAR, "asset_id", String.class, "asset_id"));

        // valueFields
        VALUE_FIELDS =
                Arrays.asList(
                        new JdbcTypeField(Types.VARCHAR, "asset_id", String.class, "asset_id"),
                        new JdbcTypeField(
                                Types.VARCHAR,
                                "asset_display_name",
                                String.class,
                                "asset_display_name"),
                        new JdbcTypeField(Types.VARCHAR, "system_id", String.class, "system_id"),
                        new JdbcTypeField(Types.VARCHAR, "site_id", String.class, "site_id"),
                        new JdbcTypeField(
                                Types.VARCHAR, "created_user", String.class, "created_user"),
                        new JdbcTypeField(
                                Types.VARCHAR, "modified_user", String.class, "modified_user"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "created_time", Timestamp.class, "created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP, "modified_time", Timestamp.class, "modified_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_created_time",
                                Timestamp.class,
                                "sys_created_time"),
                        new JdbcTypeField(
                                Types.TIMESTAMP,
                                "sys_modified_time",
                                Timestamp.class,
                                "sys_modified_time"));

        // keyFields
        QUERY_ENTITY_KEY_FIELDS = new LinkedHashSet<>();
        QUERY_ENTITY_KEY_FIELDS.add("asset_id");

        // notNullFields
        NOT_NULL_FIELDS = new LinkedHashSet<>();
        NOT_NULL_FIELDS.add("asset_id");
        NOT_NULL_FIELDS.add("asset_display_name");

        // indexes
        INDEXES =
                Arrays.asList(
                        new QueryIndex("asset_id").setInlineSize(20),
                        new QueryIndex("system_id").setInlineSize(20),
                        new QueryIndex(
                                Arrays.asList("asset_id", "site_id"), QueryIndexType.SORTED));

        // fields
        QUERY_ENTITY_FIELDS = new LinkedHashMap<>();
        QUERY_ENTITY_FIELDS.put("asset_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("asset_display_name", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("system_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("site_id", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("modified_user", "java.lang.String");
        QUERY_ENTITY_FIELDS.put("created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("modified_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_created_time", "java.sql.Timestamp");
        QUERY_ENTITY_FIELDS.put("sys_modified_time", "java.sql.Timestamp");
    }

    @Override
    public List<JdbcTypeField> getKeyFields() {
        return KEY_FIELDS;
    }

    @Override
    public List<JdbcTypeField> getValueFields() {
        return VALUE_FIELDS;
    }

    @Override
    public Set<String> getQueryEntityKeyFields() {
        return QUERY_ENTITY_KEY_FIELDS;
    }

    @Override
    public Set<String> getNotNullFields() {
        return NOT_NULL_FIELDS;
    }

    @Override
    public List<QueryIndex> getIndexes() {
        return INDEXES;
    }

    @Override
    public LinkedHashMap<String, String> getQueryEntityFields() {
        return QUERY_ENTITY_FIELDS;
    }

    @Override
    public Map<String, Object> getDefaultFieldValues() {
        return null;
    }

    @Override
    public String getAffKeyFieldName() {
        return "ASSET_ID";
    }
}
