package com.envision.gravity.common.response;

import java.util.List;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/11
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryResult {
    @JsonProperty private List<BaseColumn> columns;
    @JsonProperty private List<List<Object>> data;
}
