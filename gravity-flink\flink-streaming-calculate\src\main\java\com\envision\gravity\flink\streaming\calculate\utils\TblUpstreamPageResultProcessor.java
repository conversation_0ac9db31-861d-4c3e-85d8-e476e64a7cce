package com.envision.gravity.flink.streaming.calculate.utils;

import com.envision.gravity.cache.calculate.entity.BaseCalcPropertyMeta;
import com.envision.gravity.common.calculate.PropertyId;

import java.util.List;
import java.util.Map;

public interface TblUpstreamPageResultProcessor<O> {
    /**
     * 处理分页查询结果
     *
     * @param orgId 组织ID
     * @param pageResult 当前页的处理结果
     */
    void process(
            String orgId,
            Map<PropertyId, List<BaseCalcPropertyMeta>> pageResult,
            List<O> collector);
}
