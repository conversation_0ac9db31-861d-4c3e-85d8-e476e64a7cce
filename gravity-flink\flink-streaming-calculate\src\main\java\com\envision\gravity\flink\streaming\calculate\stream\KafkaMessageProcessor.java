package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsg;
import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgParser;
import com.envision.gravity.flink.streaming.calculate.stream.serde.MsgParser;


import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple Kafka message processor that only handles Kafka messages without CDC connection. This is a
 * temporary solution to bypass the ConnectedStreams issue.
 *
 * <AUTHOR>
 */
public class KafkaMessageProcessor
        extends ProcessFunction<Tuple2<String, OffsetInfo>, Tuple2<LegacyMsg, OffsetInfo>> {

    private static final Logger logger = LoggerFactory.getLogger(KafkaMessageProcessor.class);

    private static MsgParser parser = new LegacyMsgParser();

    private final String identity;

    public KafkaMessageProcessor(String identity) {
        this.identity = identity;
    }

    @Override
    public void open(Configuration parameters) throws Exception {}

    @Override
    public void processElement(
            Tuple2<String, OffsetInfo> value,
            ProcessFunction<Tuple2<String, OffsetInfo>, Tuple2<LegacyMsg, OffsetInfo>>.Context
                    context,
            Collector<Tuple2<LegacyMsg, OffsetInfo>> out)
            throws Exception {
        logger.debug("Received Kafka message: {}", value.f0);
        LegacyMsg msg = (LegacyMsg) parser.parse(value.f0);
        if (msg == null) {
            logger.warn("Message parsing failed: {}", value.f0);
        } else {
            logger.debug(
                    "Message parsing successful, orgId: {}, modelId: {}",
                    msg.getOrgId(),
                    msg.getModelId());
            logger.info(
                    "KafkaMessageProcessor sending message to downstream: orgId={}, modelId={}",
                    msg.getOrgId(),
                    msg.getModelId());
            out.collect(new Tuple2<>(msg, value.f1));
            System.out.println("=== KafkaMessageProcessor.processElement completed ===");
        }
    }
}
