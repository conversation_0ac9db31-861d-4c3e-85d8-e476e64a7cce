package com.envision.gravity.common.po;

import com.envision.gravity.common.annotation.ColumnName;
import com.envision.gravity.common.annotation.KeyColumn;
import com.envision.gravity.common.annotation.RequiredField;
import com.envision.gravity.common.annotation.ValueColumn;

import java.sql.Timestamp;


import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * @description
 */
@Data
@Builder
public class TblBOGroup {
    @KeyColumn(name = "group_id")
    @ColumnName("group_id")
    private String groupId;

    @ValueColumn(name = "group_name")
    @ColumnName("group_name")
    @RequiredField(message = "group_name field is required")
    private String groupName;

    @ValueColumn(name = "group_display_name")
    @ColumnName("group_display_name")
    @RequiredField(message = "group_display_name field is required")
    private String groupDisplayName;

    @ValueColumn(name = "description")
    @ColumnName("description")
    private String description;

    @ValueColumn(name = "comment")
    @ColumnName("comment")
    private String comment;

    @ValueColumn(name = "created_time", type = Timestamp.class)
    @ColumnName("created_time")
    private Timestamp createdTime;

    @ValueColumn(name = "created_user")
    @ColumnName("created_user")
    @RequiredField(message = "created_user field is required")
    private String createdUser;

    @ValueColumn(name = "modified_time", type = Timestamp.class)
    @ColumnName("modified_time")
    private Timestamp modifiedTime;

    @ValueColumn(name = "modified_user")
    @ColumnName("modified_user")
    @RequiredField(message = "modified_user field is required")
    private String modifiedUser;
}
