package com.envision.gravity.flink.streaming.postgres.cdc.aggregator;

import com.envision.gravity.flink.streaming.postgres.cdc.entity.ParsedCdcRecord;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;
import com.envision.gravity.flink.streaming.postgres.cdc.model.po.TblPrefInfo;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshModelReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.RefreshObjectReq;
import com.envision.gravity.flink.streaming.postgres.cdc.model.resp.AggregatedResults;
import com.envision.gravity.flink.streaming.postgres.cdc.repository.TblPrefRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description
 */
public class PrefAggregator implements Aggregator {
    private static final String ATTRIBUTE = "ATTRIBUTE";
    private final TblPrefRepository tblPrefRepository;

    public PrefAggregator(SqlSessionFactory sqlSessionFactory) {
        tblPrefRepository = new TblPrefRepository(sqlSessionFactory);
    }

    @Override
    public AggregatedResults aggregateCreatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        RefreshModelReq refreshModelReq = genRefreshModelReq(schemaName, records);

        // 2.refresh object detail
        List<String> filteredPrefIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblPrefInfo after = (TblPrefInfo) record.getAfter();
                                    if (ATTRIBUTE.equals(after.getPrefType())) {
                                        return after.getPrefId();
                                    }
                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        List<ModelGroup> modelUpdateRefresh =
                tblPrefRepository.selectModelGroupByPref(schemaName, filteredPrefIdList);

        return AggregatedResults.builder()
                .refreshModelReq(refreshModelReq)
                .refreshObjectReq(
                        modelUpdateRefresh.isEmpty()
                                ? null
                                : RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelUpdateRefresh)
                                        .build())
                .build();
    }

    @Override
    public AggregatedResults aggregateDeletedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1. delete property vertex
        List<String> prefIdList =
                records.stream()
                        .map(record -> ((TblPrefInfo) record.getBefore()).getPrefId())
                        .distinct()
                        .collect(Collectors.toList());

        // 2.refresh model detail
        List<ModelGroup> modelGroupList =
                tblPrefRepository.selectModelGroupList(schemaName, prefIdList);
        List<String> modelIdList =
                modelGroupList.stream()
                        .map(ModelGroup::getModelId)
                        .distinct()
                        .collect(Collectors.toList());

        // 3.refresh object detail
        List<String> filteredPrefIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblPrefInfo before = (TblPrefInfo) record.getBefore();
                                    if (ATTRIBUTE.equals(before.getPrefType())) {
                                        return before.getPrefId();
                                    }
                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        List<ModelGroup> modelUpdateRefresh =
                tblPrefRepository.selectModelGroupList(schemaName, filteredPrefIdList);

        return AggregatedResults.builder()
                .refreshModelReq(
                        modelIdList.isEmpty()
                                ? null
                                : RefreshModelReq.builder().updateRefresh(modelIdList).build())
                .refreshObjectReq(
                        modelUpdateRefresh.isEmpty()
                                ? null
                                : RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelUpdateRefresh)
                                        .build())
                .nebulaGraphReq(NebulaGraphReq.builder().deletedVertexVidList(prefIdList).build())
                .build();
    }

    @Override
    public AggregatedResults aggregateUpdatedData(List<ParsedCdcRecord> records) {
        if (records.isEmpty()) {
            return null;
        }

        // key by Schema
        String schemaName = records.get(0).getSchema();
        // 1.refresh model detail
        RefreshModelReq refreshModelReq = genRefreshModelReq(schemaName, records);

        // 2.refresh object detail
        List<String> filteredPrefIdList =
                records.stream()
                        .map(
                                record -> {
                                    TblPrefInfo before = (TblPrefInfo) record.getBefore();
                                    TblPrefInfo after = (TblPrefInfo) record.getAfter();
                                    if (ATTRIBUTE.equals(after.getPrefType())
                                            && !Objects.equals(
                                                    before.getPrefName(), after.getPrefName())) {
                                        return after.getPrefId();
                                    }
                                    return null;
                                })
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

        List<ModelGroup> modelUpdateRefresh = new ArrayList<>();
        if (!filteredPrefIdList.isEmpty()) {
            modelUpdateRefresh =
                    tblPrefRepository.selectModelGroupByPref(schemaName, filteredPrefIdList);
        }

        if (refreshModelReq == null && modelUpdateRefresh.isEmpty()) {
            return null;
        }

        return AggregatedResults.builder()
                .refreshModelReq(refreshModelReq)
                .refreshObjectReq(
                        modelUpdateRefresh.isEmpty()
                                ? null
                                : RefreshObjectReq.builder()
                                        .modelUpdateRefresh(modelUpdateRefresh)
                                        .build())
                .build();
    }

    private RefreshModelReq genRefreshModelReq(String schemaName, List<ParsedCdcRecord> records) {
        List<String> prefIdList =
                records.stream()
                        .map(record -> ((TblPrefInfo) record.getAfter()).getPrefId())
                        .distinct()
                        .collect(Collectors.toList());

        if (prefIdList.isEmpty()) {
            return null;
        }

        List<ModelGroup> modelGroupList =
                tblPrefRepository.selectModelGroupByPref(schemaName, prefIdList);

        if (modelGroupList.isEmpty()) {
            return null;
        }

        List<String> modelIdList =
                modelGroupList.stream()
                        .map(ModelGroup::getModelId)
                        .distinct()
                        .collect(Collectors.toList());

        if (modelIdList.isEmpty()) {
            return null;
        }

        return RefreshModelReq.builder().updateRefresh(modelIdList).build();
    }
}
