package com.envision.gravity.flink.streaming.calculate.dto.calc;

import com.envision.gravity.flink.streaming.calculate.stream.serde.LegacyMsgWithMultiAssets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 计算结果消息
 * 
 * 用于：
 * 1. ReCalcJobTaskProcessor 和 AspectCalcJobTaskProcessor 的计算结果
 * 2. 封装目标模型到消息的映射关系
 * 
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcResultMsg implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 目标模型ID到消息的映射
     * Key: 目标模型ID
     * Value: 对应的消息对象
     */
    private Map<String, LegacyMsgWithMultiAssets> targetModel2MsgMap;
    
    /**
     * 获取结果数量
     */
    public int getResultCount() {
        return targetModel2MsgMap != null ? targetModel2MsgMap.size() : 0;
    }
    
    /**
     * 检查是否有结果
     */
    public boolean hasResults() {
        return targetModel2MsgMap != null && !targetModel2MsgMap.isEmpty();
    }
    
    /**
     * 获取所有目标模型ID
     */
    public java.util.Set<String> getTargetModelIds() {
        return targetModel2MsgMap != null ? targetModel2MsgMap.keySet() : java.util.Collections.emptySet();
    }
    
    /**
     * 根据模型ID获取消息
     */
    public LegacyMsgWithMultiAssets getMessageByModelId(String modelId) {
        return targetModel2MsgMap != null ? targetModel2MsgMap.get(modelId) : null;
    }
    
    /**
     * 添加结果消息
     */
    public void addResult(String modelId, LegacyMsgWithMultiAssets message) {
        if (targetModel2MsgMap == null) {
            targetModel2MsgMap = new java.util.HashMap<>();
        }
        targetModel2MsgMap.put(modelId, message);
    }
    
    @Override
    public String toString() {
        return String.format("CalcResultMsg{resultCount=%d, modelIds=%s}", 
                           getResultCount(), getTargetModelIds());
    }
}
