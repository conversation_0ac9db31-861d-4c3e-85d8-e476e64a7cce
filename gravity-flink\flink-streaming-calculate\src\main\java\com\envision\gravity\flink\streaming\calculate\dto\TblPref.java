package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblPref implements CdcTableEntity {
    private static final long serialVersionUID = 8681576702669271360L;

    private String prefId;
    private String prefName;
    private String prefDisplayName;
    private String prefType;
    private String description;
    private String comment;
    private boolean writable;
    private boolean required;
    private String defaultValue;
    private boolean hasQuality;
    private String prefDataType;
    private String prefSignalType;
    private String dataDefinition;
    private String request;
    private String response;
    private String unit;
    private String lowerLimit;
    private String upperLimit;
    private String dimensions;
    private String intervals;
    private String defaultAggMethod;

    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
