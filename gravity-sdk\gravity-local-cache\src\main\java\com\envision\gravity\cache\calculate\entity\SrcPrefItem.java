package com.envision.gravity.cache.calculate.entity;

import com.envision.gravity.common.enums.PrefType;


import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SrcPrefItem {

    private String modelId;

    private String prefName;

    private PrefType prefType;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((modelId == null) ? 0 : modelId.hashCode());
        result = prime * result + ((prefName == null) ? 0 : prefName.hashCode());
        result = prime * result + ((prefType == null) ? 0 : prefType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        SrcPrefItem other = (SrcPrefItem) obj;

        if (modelId == null) {
            if (other.modelId != null) {
                return false;
            }
        } else if (!modelId.equals(other.modelId)) {
            return false;
        }

        if (prefName == null) {
            if (other.prefName != null) {
                return false;
            }
        } else if (!prefName.equals(other.prefName)) {
            return false;
        }

        if (prefType == null) {
            if (other.prefType != null) {
                return false;
            }
        } else if (!prefType.equals(other.prefType)) {
            return false;
        }

        return true;
    }
}
