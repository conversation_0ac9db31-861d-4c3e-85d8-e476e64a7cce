package com.envision.gravity.flink.streaming.bo.view.operator;

import com.envision.gravity.flink.streaming.bo.view.operator.entity.ModelDetailOriginCdcRecord;

import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
class JsonToJavaObjectTest {
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    private static final Map<String, String> DATA_TYPE_MAP = new HashMap<>();

    static {
        DATA_TYPE_MAP.put("BOOLEAN", "boolean");
        DATA_TYPE_MAP.put("DATE", "date");
        DATA_TYPE_MAP.put("DATETIME", "timestamp");
        DATA_TYPE_MAP.put("DOUBLE", "double precision");
        DATA_TYPE_MAP.put("DURATION", "bigint");
        DATA_TYPE_MAP.put("FLOAT", "real");
        DATA_TYPE_MAP.put("INTEGER", "integer");
        DATA_TYPE_MAP.put("LONG", "bigint");
        DATA_TYPE_MAP.put("STRING", "varchar");
        DATA_TYPE_MAP.put("TIME", "time");
        DATA_TYPE_MAP.put("OBJECT", "varchar");
        DATA_TYPE_MAP.put("MAP", "varchar");
        DATA_TYPE_MAP.put("ENUM", "varchar");
        DATA_TYPE_MAP.put("ARRAY", "varchar");
    }

    @Test
    void parseCdcRecord() {
        String jsonStr =
                "{\n"
                        + "    \"before\": null,\n"
                        + "    \"after\": {\n"
                        + "        \"model_id\": \"EnOS_Solar_Inverter\",\n"
                        + "        \"model_display_name\": \"{\\\"en_US\\\": \\\"Inverter\\\", \\\"zh_CN\\\": \\\"逆变器\\\", \\\"default\\\": \\\"逆变器\\\"}\",\n"
                        + "        \"model_tags\": \"{\\\"PROVINCEID\\\": \\\"山东\\\"}\",\n"
                        + "        \"components\": \"{\\\"comp1\\\": [\\\"INV.ActPowOut\\\", \\\"INV.SCADATagState\\\"]}\",\n"
                        + "        \"properties\": \"{\\\"INV.ActPowOut\\\": {\\\"tags\\\": {\\\"address\\\": \\\"江西\\\"}, \\\"unit\\\": \\\"kW\\\", \\\"prefId\\\": \\\"pref1\\\", \\\"comment\\\": null, \\\"request\\\": null, \\\"prefName\\\": \\\"INV.ActPowOut\\\", \\\"prefType\\\": \\\"ATTRIBUTE\\\", \\\"required\\\": true, \\\"response\\\": null, \\\"writable\\\": true, \\\"component\\\": {\\\"compId\\\": \\\"comp1\\\", \\\"compName\\\": \\\"comp1\\\", \\\"compDisplayName\\\": {\\\"default\\\": \\\"comp1\\\"}}, \\\"hasQuality\\\": false, \\\"lowerLimit\\\": null, \\\"upperLimit\\\": null, \\\"description\\\": null, \\\"defaultValue\\\": null, \\\"prefDataType\\\": \\\"DOUBLE\\\", \\\"dataDefinition\\\": null, \\\"prefSignalType\\\": \\\"AI\\\", \\\"prefDisplayName\\\": {\\\"default\\\": \\\"INV.ActPowOut\\\"}}, \\\"INV.SCADATagState\\\": {\\\"tags\\\": {\\\"lob\\\": \\\"false\\\"}, \\\"unit\\\": \\\"--\\\", \\\"prefId\\\": \\\"pref2\\\", \\\"comment\\\": null, \\\"request\\\": null, \\\"prefName\\\": \\\"INV.SCADATagState\\\", \\\"prefType\\\": \\\"ATTRIBUTE\\\", \\\"required\\\": true, \\\"response\\\": null, \\\"writable\\\": true, \\\"component\\\": {\\\"compId\\\": \\\"comp1\\\", \\\"compName\\\": \\\"comp1\\\", \\\"compDisplayName\\\": {\\\"default\\\": \\\"comp1\\\"}}, \\\"hasQuality\\\": false, \\\"lowerLimit\\\": null, \\\"upperLimit\\\": null, \\\"description\\\": null, \\\"defaultValue\\\": null, \\\"prefDataType\\\": \\\"INTEGER\\\", \\\"dataDefinition\\\": null, \\\"prefSignalType\\\": \\\"DI\\\", \\\"prefDisplayName\\\": {\\\"default\\\": \\\"INV.SCADATagState\\\"}}}\",\n"
                        + "        \"attribute_tags\": \"{\\\"lob\\\": \\\"false\\\", \\\"address\\\": \\\"江西\\\"}\",\n"
                        + "        \"measurepoint_tags\": \"{}\",\n"
                        + "        \"command_tags\": \"{}\",\n"
                        + "        \"created_time\": 1715102168410105,\n"
                        + "        \"created_user\": \"ignite\",\n"
                        + "        \"modified_time\": 1716930084923126,\n"
                        + "        \"modified_user\": \"ignite\",\n"
                        + "        \"org_id\": null,\n"
                        + "        \"model_path\": null,\n"
                        + "        \"model_created_time\": 1716373330941000,\n"
                        + "        \"model_modified_time\": 1716373330941000,\n"
                        + "        \"model_created_user\": \"Gravity\",\n"
                        + "        \"model_modified_user\": \"Gravity\",\n"
                        + "        \"description\": null\n"
                        + "    },\n"
                        + "    \"source\": {\n"
                        + "        \"version\": \"1.6.4.Final\",\n"
                        + "        \"connector\": \"postgresql\",\n"
                        + "        \"name\": \"postgres_cdc_source\",\n"
                        + "        \"ts_ms\": 1716901284923,\n"
                        + "        \"snapshot\": \"false\",\n"
                        + "        \"db\": \"ignite\",\n"
                        + "        \"sequence\": \"[\\\"450991104\\\",\\\"450991104\\\"]\",\n"
                        + "        \"schema\": \"gravity\",\n"
                        + "        \"table\": \"model_detail_origin\",\n"
                        + "        \"txId\": 2588,\n"
                        + "        \"lsn\": 451003232,\n"
                        + "        \"xmin\": null\n"
                        + "    },\n"
                        + "    \"op\": \"u\",\n"
                        + "    \"ts_ms\": 1716901285239,\n"
                        + "    \"transaction\": null\n"
                        + "}";

        try {
            ModelDetailOriginCdcRecord modelDetailOriginCdcRecord =
                    OBJECT_MAPPER.readValue(jsonStr, ModelDetailOriginCdcRecord.class);
            System.out.println(modelDetailOriginCdcRecord);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void parseJsonData() {
        String jsonData =
                "{\n"
                        + "    \"_name\": {\n"
                        + "        \"tags\": null,\n"
                        + "        \"unit\": null,\n"
                        + "        \"prefId\": \"dtmi:custom:cshan0412a;1__attribute___name\",\n"
                        + "        \"comment\": null,\n"
                        + "        \"request\": null,\n"
                        + "        \"prefName\": \"_name\",\n"
                        + "        \"prefType\": \"ATTRIBUTE\",\n"
                        + "        \"required\": true,\n"
                        + "        \"response\": null,\n"
                        + "        \"writable\": true,\n"
                        + "        \"component\": {\n"
                        + "            \"compId\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compName\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compDisplayName\": {\n"
                        + "                \"en_US\": \"cshan\",\n"
                        + "                \"zh_CN\": \"cshan\",\n"
                        + "                \"default\": \"cshan\"\n"
                        + "            }\n"
                        + "        },\n"
                        + "        \"hasQuality\": false,\n"
                        + "        \"lowerLimit\": null,\n"
                        + "        \"upperLimit\": null,\n"
                        + "        \"description\": null,\n"
                        + "        \"defaultValue\": null,\n"
                        + "        \"prefDataType\": \"STRING\",\n"
                        + "        \"dataDefinition\": \"{\\\"@type\\\":[\\\"string\\\"],\\\"upperLimit\\\":\\\"4000\\\"}\",\n"
                        + "        \"prefSignalType\": null,\n"
                        + "        \"prefDisplayName\": {\n"
                        + "            \"default\": \"_name\"\n"
                        + "        }\n"
                        + "    },\n"
                        + "    \"_timezone\": {\n"
                        + "        \"tags\": null,\n"
                        + "        \"unit\": null,\n"
                        + "        \"prefId\": \"dtmi:custom:cshan0412a;1__attribute___timezone\",\n"
                        + "        \"comment\": null,\n"
                        + "        \"request\": null,\n"
                        + "        \"prefName\": \"_timezone\",\n"
                        + "        \"prefType\": \"ATTRIBUTE\",\n"
                        + "        \"required\": true,\n"
                        + "        \"response\": null,\n"
                        + "        \"writable\": true,\n"
                        + "        \"component\": {\n"
                        + "            \"compId\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compName\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compDisplayName\": {\n"
                        + "                \"en_US\": \"cshan\",\n"
                        + "                \"zh_CN\": \"cshan\",\n"
                        + "                \"default\": \"cshan\"\n"
                        + "            }\n"
                        + "        },\n"
                        + "        \"hasQuality\": false,\n"
                        + "        \"lowerLimit\": null,\n"
                        + "        \"upperLimit\": null,\n"
                        + "        \"description\": null,\n"
                        + "        \"defaultValue\": null,\n"
                        + "        \"prefDataType\": \"STRING\",\n"
                        + "        \"dataDefinition\": \"{\\\"@type\\\":[\\\"string\\\"],\\\"upperLimit\\\":\\\"4000\\\"}\",\n"
                        + "        \"prefSignalType\": null,\n"
                        + "        \"prefDisplayName\": {\n"
                        + "            \"default\": \"_timezone\"\n"
                        + "        }\n"
                        + "    },\n"
                        + "    \"_description\": {\n"
                        + "        \"tags\": null,\n"
                        + "        \"unit\": null,\n"
                        + "        \"prefId\": \"dtmi:custom:cshan0412a;1__attribute___description\",\n"
                        + "        \"comment\": null,\n"
                        + "        \"request\": null,\n"
                        + "        \"prefName\": \"_description\",\n"
                        + "        \"prefType\": \"ATTRIBUTE\",\n"
                        + "        \"required\": false,\n"
                        + "        \"response\": null,\n"
                        + "        \"writable\": true,\n"
                        + "        \"component\": {\n"
                        + "            \"compId\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compName\": \"dtmi:custom:cshan0412a;1\",\n"
                        + "            \"compDisplayName\": {\n"
                        + "                \"en_US\": \"cshan\",\n"
                        + "                \"zh_CN\": \"cshan\",\n"
                        + "                \"default\": \"cshan\"\n"
                        + "            }\n"
                        + "        },\n"
                        + "        \"hasQuality\": false,\n"
                        + "        \"lowerLimit\": null,\n"
                        + "        \"upperLimit\": null,\n"
                        + "        \"description\": null,\n"
                        + "        \"defaultValue\": null,\n"
                        + "        \"prefDataType\": \"STRING\",\n"
                        + "        \"dataDefinition\": \"{\\\"@type\\\":[\\\"string\\\"],\\\"upperLimit\\\":\\\"4000\\\"}\",\n"
                        + "        \"prefSignalType\": null,\n"
                        + "        \"prefDisplayName\": {\n"
                        + "            \"default\": \"_description\"\n"
                        + "        }\n"
                        + "    }\n"
                        + "}";

        try {
            JsonNode rootNode = OBJECT_MAPPER.readTree(jsonData);

            StringJoiner attributesJoiner = new StringJoiner(",\n       ");
            rootNode.fields()
                    .forEachRemaining(
                            entry -> {
                                String key = entry.getKey();
                                JsonNode node = entry.getValue();

                                if ("ATTRIBUTE".equalsIgnoreCase(node.get("prefType").asText())) {
                                    String dataType =
                                            DATA_TYPE_MAP.getOrDefault(
                                                    node.get("prefDataType").asText(), "text");
                                    attributesJoiner.add(
                                            "(attributes ->> '"
                                                    + key
                                                    + "')::"
                                                    + dataType
                                                    + " AS "
                                                    + key);
                                }
                            });

            if (attributesJoiner.length() > 0) {
                System.out.println(attributesJoiner);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void parseModelTags() throws JsonProcessingException {
        String json = "{ \"modelForMdmType\": \"true\"}";

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(json);

        String mdmType = jsonNode.get("mdmType").asText();
        String modelForMdmType = jsonNode.get("modelForMdmType").asText();

        System.out.println("mdmType: " + mdmType);
        System.out.println("modelForMdmType: " + modelForMdmType);
    }
}
