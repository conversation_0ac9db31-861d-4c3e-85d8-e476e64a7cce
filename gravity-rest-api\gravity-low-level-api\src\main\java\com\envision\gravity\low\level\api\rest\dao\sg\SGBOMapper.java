package com.envision.gravity.low.level.api.rest.dao.sg;

import com.envision.gravity.common.po.TblBO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18
 * @description
 */
@Mapper
public interface SGBOMapper {
    List<TblBO> queryByAssetIds(
            @Param("orgId") String orgId, @Param("boIds") List<String> assetIdList);

    int batchReplace(@Param("orgId") String orgId, @Param("bos") List<TblBO> bos);

    int deleteByBOList(@Param("orgId") String orgId, @Param("boIds") List<String> boIds);
}
