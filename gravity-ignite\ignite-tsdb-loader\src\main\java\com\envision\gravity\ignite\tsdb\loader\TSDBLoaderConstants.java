package com.envision.gravity.ignite.tsdb.loader;

/** <AUTHOR> 2024/3/29 */
public class TSDBLoaderConstants {

    public static final String REQUESTID = "REQUESTID";

    public static final String ORGID = "ORGID";

    public static final String MDMID = "MDMID";

    public static final String TIMEZONE = "TIMEZONE";

    public static final String KPI = "KPI";

    public static final String TS = "TS";

    public static final String LOCAL_TIME = "LOCAL_TIME";

    public static final String TIMEGROUP = "TIMEGROUP";

    public static final String AGGTYPE = "AGGTYPE";

    public static final String QUALITY = "QUALITY";

    public static final String VALUEBOOL = "VALUEBOOL";

    public static final String VALUEINT = "VALUEINT";

    public static final String VALUELONG = "VALUELONG";

    public static final String VALUEDOUBLE = "VALUEDOUBLE";

    public static final String VALUESTRING = "VALUESTRING";

    // ------------------------------------------------------------------------------------------------
    public static final String COLUMN_TIME = "_time";

    public static final String COLUMN_MEASUREMENT = "_measurement";

    public static final String COLUMN_ASSETID = "asset_id";

    public static final String COLUMN_VALUE = "_value";
}
