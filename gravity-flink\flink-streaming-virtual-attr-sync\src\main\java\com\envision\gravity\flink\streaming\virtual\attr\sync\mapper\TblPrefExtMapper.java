package com.envision.gravity.flink.streaming.virtual.attr.sync.mapper;

import com.envision.gravity.flink.streaming.virtual.attr.sync.model.resp.VirtualAttrInfo;

import java.util.List;


import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2024/7/8
 * @description
 */
public interface TblPrefExtMapper {

    /** @return {@link VirtualAttrInfo} */
    @SelectProvider(type = TblPrefExtSqlProvider.class, method = "queryVirtualAttrInfo")
    @Results({
        @Result(column = "model_id", property = "modelId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "pref_id", property = "prefId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "raw_field_id", property = "rawFieldId", jdbcType = JdbcType.VARCHAR),
        @Result(column = "attr_name", property = "attrName", jdbcType = JdbcType.VARCHAR)
    })
    List<VirtualAttrInfo> queryVirtualAttrInfo(String schemeName);
}
