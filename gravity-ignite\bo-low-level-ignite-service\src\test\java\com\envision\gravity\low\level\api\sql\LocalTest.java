package com.envision.gravity.low.level.api.sql;

import com.envision.gravity.low.level.api.sql.common.Constants;

import java.util.Arrays;
import java.util.stream.Collectors;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024/7/11
 * @description
 */
class LocalTest {
    @Test
    void getAllBOCacheList() {
        String[] ouIds = new String[] {"o123", "o234", "o345"};
        String[] tableNames = {
            Constants.TBL_BO_MODEL_TABLE_NAME,
            Constants.TBL_BO_MODEL_COMP_TABLE_NAME,
            Constants.TBL_COMPONENT_TABLE_NAME,
            Constants.TBL_COMPONENT_PREF_TABLE_NAME,
            Constants.TBL_PREF_TABLE_NAME,
            Constants.TBL_PREF_EXT_TABLE_NAME,
            Constants.TBL_MODEL_RELATION_TABLE_NAME,
            Constants.TBL_BO_GROUP_TABLE_NAME,
            Constants.TBL_BO_GROUP_RELATION_TABLE_NAME,
            Constants.TBL_BO_TABLE_NAME,
            Constants.TBL_TAG_TABLE_NAME,
            Constants.TBL_OBJ_ATTR_TABLE_NAME,
            Constants.TBL_SUB_GRAPH_TABLE_NAME,
            Constants.TBL_EDGE_TABLE_NAME,
            Constants.TBL_EDGE_TYPE_TABLE_NAME,
            Constants.TBL_EDGE_TYPE_PROP_TABLE_NAME,
            Constants.TBL_START_VID_TABLE_NAME
        };
        System.out.println(
                Arrays.stream(ouIds)
                        .flatMap(
                                ouId ->
                                        Arrays.stream(tableNames)
                                                .map(tableName -> ouId + "_" + tableName))
                        .collect(Collectors.toList()));
    }
}
