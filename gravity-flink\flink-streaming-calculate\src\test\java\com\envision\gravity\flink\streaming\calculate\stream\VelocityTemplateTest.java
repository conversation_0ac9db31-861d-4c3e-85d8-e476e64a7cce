package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.common.calculate.PropertyInfo;
import com.envision.gravity.common.enums.PrefType;

import java.io.StringWriter;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for Velocity SQL template rendering
 *
 * <p>Tests the query-latest-values.vm template with various scenarios: 1. Attributes only 2.
 * Measurement points only 3. Mixed attributes and measurement points 4. Multiple assets 5. Property
 * deduplication
 */
public class VelocityTemplateTest {

    private VelocityEngine velocityEngine;

    @BeforeEach
    void setUp() {
        velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        velocityEngine.setProperty(
                "classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        velocityEngine.init();
    }

    /** Create test PropertyInfo */
    private PropertyInfo createPropertyInfo(String modelId, String prefName, PrefType prefType) {
        return PropertyInfo.builder()
                .modelId(modelId)
                .prefName(prefName)
                .prefType(prefType)
                .build();
    }

    @Test
    void testTemplate_AttributesOnly() {
        // Test template with attributes only
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(createPropertyInfo("testModel", "rated_power", PrefType.ATTRIBUTE));
        allProperties.add(createPropertyInfo("testModel", "manufacturer", PrefType.ATTRIBUTE));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", new HashSet<>(Arrays.asList("asset001", "asset002")));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify SQL structure
        assertTrue(sql.contains("SELECT /*+ ORG('o123456') */"));
        assertTrue(sql.contains("FROM `testModel`"));
        assertTrue(
                sql.contains("WHERE asset_id IN ('asset001', 'asset002')")
                        || sql.contains("WHERE asset_id IN ('asset002', 'asset001')"));

        // Verify SET hints for attributes
        assertTrue(sql.contains("/*+ SET(`rated_power`=TS(`rated_power`)) */"));
        assertTrue(sql.contains("/*+ SET(`manufacturer`=TS(`manufacturer`)) */"));

        // Verify SELECT clauses for attributes
        assertTrue(sql.contains("`rated_power` AS `rated_power`"));
        assertTrue(sql.contains("`manufacturer` AS `manufacturer`"));

        // Should not contain measurement point patterns
        assertFalse(sql.contains("_time"));
        assertFalse(sql.contains("_value"));
    }

    @Test
    void testTemplate_MeasurementPointsOnly() {
        // Test template with measurement points only
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(createPropertyInfo("testModel", "temperature", PrefType.MEASUREPOINT));
        allProperties.add(createPropertyInfo("testModel", "pressure", PrefType.MEASUREPOINT));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", Collections.singleton("asset001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify SQL structure
        assertTrue(sql.contains("SELECT /*+ ORG('o123456') */"));
        assertTrue(sql.contains("FROM `testModel`"));
        assertTrue(sql.contains("WHERE asset_id IN ('asset001')"));

        // Verify SET hints for measurement points
        assertTrue(sql.contains("/*+ SET(`temperature`=TS(`temperature`)) */"));
        assertTrue(sql.contains("/*+ SET(`pressure`=TS(`pressure`)) */"));

        // Verify SELECT clauses for measurement points
        assertTrue(sql.contains("`temperature.time` AS `temperature_time`"));
        assertTrue(sql.contains("`temperature.value` AS `temperature_value`"));
        assertTrue(sql.contains("`pressure.time` AS `pressure_time`"));
        assertTrue(sql.contains("`pressure.value` AS `pressure_value`"));
    }

    @Test
    void testTemplate_MixedAttributesAndMeasurementPoints() {
        // Test template with both attributes and measurement points
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(createPropertyInfo("testModel", "rated_power", PrefType.ATTRIBUTE));
        allProperties.add(createPropertyInfo("testModel", "temperature", PrefType.MEASUREPOINT));
        allProperties.add(createPropertyInfo("testModel", "manufacturer", PrefType.ATTRIBUTE));
        allProperties.add(createPropertyInfo("testModel", "pressure", PrefType.MEASUREPOINT));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", new HashSet<>(Arrays.asList("asset001", "asset002")));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify SQL structure
        assertTrue(sql.contains("SELECT /*+ ORG('o123456') */"));
        assertTrue(sql.contains("FROM `testModel`"));
        assertTrue(sql.contains("asset_id"));

        // Verify SET hints (should be same for both types)
        assertTrue(sql.contains("/*+ SET(`rated_power`=TS(`rated_power`)) */"));
        assertTrue(sql.contains("/*+ SET(`temperature`=TS(`temperature`)) */"));
        assertTrue(sql.contains("/*+ SET(`manufacturer`=TS(`manufacturer`)) */"));
        assertTrue(sql.contains("/*+ SET(`pressure`=TS(`pressure`)) */"));

        // Verify SELECT clauses for attributes
        assertTrue(sql.contains("`rated_power` AS `rated_power`"));
        assertTrue(sql.contains("`manufacturer` AS `manufacturer`"));

        // Verify SELECT clauses for measurement points
        assertTrue(sql.contains("`temperature.time` AS `temperature_time`"));
        assertTrue(sql.contains("`temperature.value` AS `temperature_value`"));
        assertTrue(sql.contains("`pressure.time` AS `pressure_time`"));
        assertTrue(sql.contains("`pressure.value` AS `pressure_value`"));
    }

    @Test
    void testTemplate_EmptyProperties() {
        // Test template with empty properties
        Set<PropertyInfo> allProperties = new HashSet<>();

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", Collections.singleton("asset001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Should still have basic structure
        assertTrue(sql.contains("SELECT /*+ ORG('o123456') */"));
        assertTrue(sql.contains("asset_id"));
        assertTrue(sql.contains("FROM `testModel`"));
        assertTrue(sql.contains("WHERE asset_id IN ('asset001')"));

        // Should not have any SET hints or property SELECT clauses
        assertFalse(sql.contains("/*+ SET("));
        assertFalse(sql.contains("AS `"));
    }

    @Test
    void testTemplate_SingleAsset() {
        // Test template with single asset
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(createPropertyInfo("testModel", "temperature", PrefType.MEASUREPOINT));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", Collections.singleton("asset001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        assertTrue(sql.contains("WHERE asset_id IN ('asset001')"));
        assertFalse(sql.contains("asset002"));
    }

    @Test
    void testTemplate_MultipleAssets() {
        // Test template with multiple assets
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(createPropertyInfo("testModel", "temperature", PrefType.MEASUREPOINT));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "testModel");
        context.put("allProperties", allProperties);
        context.put("assetIds", new HashSet<>(Arrays.asList("asset001", "asset002", "asset003")));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Should contain all asset IDs in WHERE clause
        assertTrue(sql.contains("asset001"));
        assertTrue(sql.contains("asset002"));
        assertTrue(sql.contains("asset003"));
        assertTrue(sql.contains("WHERE asset_id IN ("));
    }

    @Test
    void testTemplate_SpecialCharactersInNames() {
        // Test template with special characters in names
        Set<PropertyInfo> allProperties = new HashSet<>();
        allProperties.add(
                createPropertyInfo("test_model", "temp_sensor_01", PrefType.MEASUREPOINT));
        allProperties.add(createPropertyInfo("test_model", "power_rating_kw", PrefType.ATTRIBUTE));

        VelocityContext context = new VelocityContext();
        context.put("orgId", "o123456");
        context.put("modelId", "test_model");
        context.put("allProperties", allProperties);
        context.put("assetIds", Collections.singleton("asset_001"));

        StringWriter writer = new StringWriter();
        Template template = velocityEngine.getTemplate("query-latest-values.vm");
        template.merge(context, writer);

        String sql = writer.toString();

        // Verify special characters are handled correctly
        assertTrue(sql.contains("FROM `test_model`"));
        assertTrue(sql.contains("/*+ SET(`temp_sensor_01`=TS(`temp_sensor_01`)) */"));
        assertTrue(sql.contains("/*+ SET(`power_rating_kw`=TS(`power_rating_kw`)) */"));
        assertTrue(sql.contains("`temp_sensor_01.time` AS `temp_sensor_01_time`"));
        assertTrue(sql.contains("`power_rating_kw` AS `power_rating_kw`"));
        assertTrue(sql.contains("WHERE asset_id IN ('asset_001')"));
    }
}
