package com.envision.gravity.flink.steaming.bo.event.entity.table;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/14
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ObjectDetailOrigin implements CdcTableEntity {
    private static final long serialVersionUID = -5346422768891525292L;
    private String assetId;
    private String modelId;
    private String assetDisplayName;
    private String assetTags;
    private String attributes;
    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private String description;
    private String orgId;
    private long assetCreatedTime;
    private String assetCreatedUser;
    private long assetModifiedTime;
    private String assetModifiedUser;
    private String uniqueId;
    private String attributesLob;
    private String topoIds;
}
