package com.envision.gravity.flink.streaming.calculate.utils;

import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

/**
 * CalcLionConfig 配置测试
 *
 * <p>测试功能： 1. ReCalc配置项的默认值 2. AspectCalc配置项的默认值 3. 配置项的数据类型正确性
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
class CalcLionConfigTest {

    @Test
    void testReCalcBatchJobConfigs() {
        // ✅ 测试ReCalc批处理配置项

        // 并行度配置
        assertTrue(CalcLionConfig.getReCalcDefaultParallelism() >= 1);
        assertTrue(CalcLionConfig.getReCalcJobTaskProcessorParallelism() >= 1);

        // Checkpoint配置
        assertTrue(CalcLionConfig.getReCalcCheckpointIntervalMs() > 0);
        assertTrue(CalcLionConfig.getReCalcCheckpointTimeoutMs() > 0);
        assertTrue(CalcLionConfig.getReCalcCheckpointMaxConcurrent() >= 1);

        // 重启策略配置
        assertTrue(CalcLionConfig.getReCalcRestartAttempts() >= 0);
        assertTrue(CalcLionConfig.getReCalcRestartDelayMs() >= 0);

        // 任务配置
        assertTrue(CalcLionConfig.getReCalcJobTaskTimeSplitSeconds() > 0);
        assertTrue(CalcLionConfig.getTaskCompletionCheckIntervalMs() > 0);
        assertTrue(CalcLionConfig.getCalcQueryAssetPageSize() > 0);

        // Kafka配置
        assertNotNull(CalcLionConfig.getReCalcKafkaBootstrapServers());
        assertNotNull(CalcLionConfig.getReCalcKafkaSinkTopicPattern());
        assertFalse(CalcLionConfig.getReCalcKafkaBootstrapServers().isEmpty());
        assertFalse(CalcLionConfig.getReCalcKafkaSinkTopicPattern().isEmpty());
    }

    @Test
    void testAspectCalcFlowConfigs() {
        // ✅ 测试AspectCalc流式配置项

        // 并行度配置
        assertTrue(CalcLionConfig.getAspectCalcDefaultParallelism() >= 1);
        assertTrue(CalcLionConfig.getCalcJobSourceReaderParallelism() >= 1);
        assertTrue(CalcLionConfig.getAspectCalcJobTaskProcessorParallelism() >= 1);
        assertTrue(CalcLionConfig.getKafkaSinkParallelism() >= 1);

        // 定时器配置
        assertTrue(CalcLionConfig.getJobInfoCheckIntervalMs() > 0);

        // Checkpoint配置
        assertTrue(CalcLionConfig.getAspectCalcCheckpointIntervalMs() > 0);
        assertTrue(CalcLionConfig.getAspectCalcCheckpointTimeoutMs() > 0);
        assertTrue(CalcLionConfig.getAspectCalcCheckpointMaxConcurrent() >= 1);

        // 重启策略配置
        assertTrue(CalcLionConfig.getAspectCalcRestartAttempts() >= 0);
        assertTrue(CalcLionConfig.getAspectCalcRestartDelayMs() >= 0);

        // 任务配置
        assertTrue(CalcLionConfig.getMaxJobsPerReader() > 0);
        assertTrue(CalcLionConfig.getTaskBatchSize() > 0);

        // Kafka配置
        assertNotNull(CalcLionConfig.getAspectCalcKafkaBootstrapServers());
        assertNotNull(CalcLionConfig.getAspectCalcKafkaSinkTopicPattern());
        assertFalse(CalcLionConfig.getAspectCalcKafkaBootstrapServers().isEmpty());
        assertFalse(CalcLionConfig.getAspectCalcKafkaSinkTopicPattern().isEmpty());
    }

    @Test
    void testConfigDefaultValues() {
        // ✅ 测试配置项的默认值

        // ReCalc默认值
        assertEquals(1, CalcLionConfig.getReCalcDefaultParallelism());
        assertEquals(4, CalcLionConfig.getReCalcJobTaskProcessorParallelism());
        assertEquals(30000L, CalcLionConfig.getReCalcCheckpointIntervalMs());
        assertEquals(600000L, CalcLionConfig.getReCalcCheckpointTimeoutMs());
        assertEquals(1, CalcLionConfig.getReCalcCheckpointMaxConcurrent());
        assertEquals(3, CalcLionConfig.getReCalcRestartAttempts());
        assertEquals(10000L, CalcLionConfig.getReCalcRestartDelayMs());
        assertEquals(3600L, CalcLionConfig.getReCalcJobTaskTimeSplitSeconds());
        assertEquals(1000L, CalcLionConfig.getTaskCompletionCheckIntervalMs());
        assertEquals("localhost:9092", CalcLionConfig.getReCalcKafkaBootstrapServers());
        assertEquals("MEASURE_POINT_CAL_", CalcLionConfig.getReCalcKafkaSinkTopicPattern());
        assertEquals(1000, CalcLionConfig.getCalcQueryAssetPageSize());

        // AspectCalc默认值
        assertEquals(4, CalcLionConfig.getAspectCalcDefaultParallelism());
        assertEquals(10000L, CalcLionConfig.getJobInfoCheckIntervalMs());
        assertEquals(4, CalcLionConfig.getCalcJobSourceReaderParallelism());
        assertEquals(8, CalcLionConfig.getAspectCalcJobTaskProcessorParallelism());
        assertEquals(4, CalcLionConfig.getKafkaSinkParallelism());
        assertEquals(30000L, CalcLionConfig.getAspectCalcCheckpointIntervalMs());
        assertEquals(600000L, CalcLionConfig.getAspectCalcCheckpointTimeoutMs());
        assertEquals(1, CalcLionConfig.getAspectCalcCheckpointMaxConcurrent());
        assertEquals(3, CalcLionConfig.getAspectCalcRestartAttempts());
        assertEquals(10000L, CalcLionConfig.getAspectCalcRestartDelayMs());
        assertEquals(5, CalcLionConfig.getMaxJobsPerReader());
        assertEquals(10, CalcLionConfig.getTaskBatchSize());
        assertEquals("localhost:9092", CalcLionConfig.getAspectCalcKafkaBootstrapServers());
        assertEquals("MEASURE_POINT_CAL_", CalcLionConfig.getAspectCalcKafkaSinkTopicPattern());
    }

    @Test
    void testConfigConsistency() {
        // ✅ 测试配置项的一致性

        // Kafka配置应该一致
        assertEquals(
                CalcLionConfig.getReCalcKafkaSinkTopicPattern(),
                CalcLionConfig.getAspectCalcKafkaSinkTopicPattern());

        // Checkpoint超时时间应该大于间隔时间
        assertTrue(
                CalcLionConfig.getReCalcCheckpointTimeoutMs()
                        > CalcLionConfig.getReCalcCheckpointIntervalMs());
        assertTrue(
                CalcLionConfig.getAspectCalcCheckpointTimeoutMs()
                        > CalcLionConfig.getAspectCalcCheckpointIntervalMs());

        // 任务检查间隔应该合理
        assertTrue(
                CalcLionConfig.getTaskCompletionCheckIntervalMs()
                        <= CalcLionConfig.getJobInfoCheckIntervalMs());
    }

    @Test
    void testConfigRanges() {
        // ✅ 测试配置项的合理范围

        // 并行度应该在合理范围内
        assertTrue(CalcLionConfig.getReCalcDefaultParallelism() <= 32);
        assertTrue(CalcLionConfig.getAspectCalcDefaultParallelism() <= 32);
        assertTrue(CalcLionConfig.getReCalcJobTaskProcessorParallelism() <= 64);
        assertTrue(CalcLionConfig.getAspectCalcJobTaskProcessorParallelism() <= 64);

        // 时间间隔应该在合理范围内（毫秒）
        assertTrue(CalcLionConfig.getReCalcCheckpointIntervalMs() >= 1000); // 至少1秒
        assertTrue(CalcLionConfig.getReCalcCheckpointIntervalMs() <= 300000); // 最多5分钟
        assertTrue(CalcLionConfig.getAspectCalcCheckpointIntervalMs() >= 1000);
        assertTrue(CalcLionConfig.getAspectCalcCheckpointIntervalMs() <= 300000);

        // 页面大小应该在合理范围内
        assertTrue(CalcLionConfig.getCalcQueryAssetPageSize() >= 100);
        assertTrue(CalcLionConfig.getCalcQueryAssetPageSize() <= 10000);

        // 批量大小应该在合理范围内
        assertTrue(CalcLionConfig.getTaskBatchSize() >= 1);
        assertTrue(CalcLionConfig.getTaskBatchSize() <= 100);

        // 每个Reader的最大Job数应该在合理范围内
        assertTrue(CalcLionConfig.getMaxJobsPerReader() >= 1);
        assertTrue(CalcLionConfig.getMaxJobsPerReader() <= 50);
    }
}
