package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseCodeEnum;
import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.search.graph.EdgeCreateOrUpdateReq;
import com.envision.gravity.common.vo.search.graph.EdgeDeleteReq;
import com.envision.gravity.common.vo.topo.BatchPatchEdgeReq;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.model.AuditHeader;
import com.envision.gravity.low.level.api.rest.service.EdgeService;
import com.envision.gravity.low.level.api.rest.util.JsonUtil;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import static com.envision.gravity.low.level.api.rest.common.Constants.MAX_REQUEST_SIZE;
import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.checkDeleteEdgeReqEdgeType;

/** @Author: qi.jiang2 @Date: 2024/04/08 11:02 @Description: */
@Api(tags = "Edge")
@Slf4j
@RestController
@Validated
@RequestMapping("/edges")
public class EdgeController {

    @Resource private EdgeService edgeService;

    @PostMapping
    public ResponseResult<?> createOrUpdateEdges(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid EdgeCreateOrUpdateReq edgeCreateOrUpdateReq,
            HttpServletRequest request) {
        log.debug(
                String.format(
                        "Start create or update edges, orgId: %s, request: %s",
                        orgId, edgeCreateOrUpdateReq));

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        if (edgeCreateOrUpdateReq.getEdges() == null
                || edgeCreateOrUpdateReq.getEdges().isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (edgeCreateOrUpdateReq.getEdges().size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of create or update edges exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    edgeCreateOrUpdateReq.getEdges().size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of create or update edges must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, edgeCreateOrUpdateReq.getEdges().size()))
                    .build();
        }

        return edgeService.createOrUpdateEdges(orgId, edgeCreateOrUpdateReq, auditHeader);
    }

    @PatchMapping
    public ResponseResult<?> patchEdges(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid BatchPatchEdgeReq edges,
            HttpServletRequest request) {
        log.info(String.format("Start patch edges, orgId: %s, edges: %s", orgId, edges));

        String auditInfo = request.getHeader(Constants.HTTP_HEAD_AUDIT);
        AuditHeader auditHeader = null;
        if (auditInfo != null && !auditInfo.isEmpty()) {
            auditHeader = JsonUtil.parseAuditHeader(auditInfo);
        }

        boolean upsert = edges.getUpsertEdges() == null || edges.getUpsertEdges().isEmpty();
        boolean delete = edges.getDeleteEdges() == null || edges.getDeleteEdges().isEmpty();
        if (upsert && delete) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        int upsertSize = edges.getUpsertEdges() == null ? 0 : edges.getUpsertEdges().size();
        int deleteSize = edges.getDeleteEdges() == null ? 0 : edges.getDeleteEdges().size();
        int totalReqSize = upsertSize + deleteSize;
        if (totalReqSize > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of patch edges exceeds the limit of {}, total size: {}, upsert size: {}, delete size: {}",
                    MAX_REQUEST_SIZE,
                    totalReqSize,
                    upsertSize,
                    deleteSize);
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of patch edges must be less than or equal to %d, total size: %d, upsert size: %d, delete size: %d",
                                    MAX_REQUEST_SIZE, totalReqSize, upsertSize, deleteSize))
                    .build();
        }

        return edgeService.patchEdges(orgId, edges, auditHeader);
    }

    @DeleteMapping
    public ResponseResult<?> deleteEdges(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody @Valid EdgeDeleteReq edgeDeleteReq) {
        log.info(String.format("Start delete edges, orgId: %s, request: %s", orgId, edgeDeleteReq));
        if (edgeDeleteReq.getEdges() == null || edgeDeleteReq.getEdges().isEmpty()) {
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.SUCCESS.getCode())
                    .message(ResponseCodeEnum.SUCCESS.getMessage())
                    .build();
        }

        if (edgeDeleteReq.getEdges().size() > MAX_REQUEST_SIZE) {
            log.warn(
                    "The size of delete edges exceeds the limit of {}, current size: {}",
                    MAX_REQUEST_SIZE,
                    edgeDeleteReq.getEdges().size());
            return ResponseResult.builder()
                    .code(ResponseCodeEnum.INVALID_PARAMETERS.getCode())
                    .message(
                            String.format(
                                    "The size of delete edges must be less than or equal to %d, current size: %d",
                                    MAX_REQUEST_SIZE, edgeDeleteReq.getEdges().size()))
                    .build();
        }

        checkDeleteEdgeReqEdgeType(edgeDeleteReq.getEdges());
        return edgeService.deleteEdges(orgId, edgeDeleteReq);
    }
}
