package com.envision.gravity.common.util;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


import org.apache.commons.lang3.tuple.Pair;
import org.apache.ignite.client.ClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** <AUTHOR> 2024/10/23 */
public class FieldIndexUtils {

    private static final Logger log = LoggerFactory.getLogger(FieldIndexUtils.class);

    private static final int FIELD_INDEX_RESERVATION = 100000;

    public static final String SQL_INSERT_INTO_NON_STANDARD_LIB =
            "insert into field_index_alloc values %s;";

    public static final String SQL_QUERY_FIELD_ID_FROM_STANDARD_LIB =
            "select field_index, raw_field_id from standard_field_lib where raw_field_id in (%s);";

    public static final String SQL_QUERY_FIELD_ID_FROM_NON_STANDARD_LIB =
            "select field_index, raw_field_id from field_index_alloc where raw_field_id in (%s);";

    public static Map<String, Pair<Integer, Boolean>> generateFieldIndexAndHorizontal(
            Set<String> rawFieldIdSet) {

        Map<String, Pair<Integer, Boolean>> rawFieldIdToPairMap = new HashMap<>();
        if (rawFieldIdSet.isEmpty()) {
            return rawFieldIdToPairMap;
        }
        Set<String> leftRawFieldId = new HashSet<>(rawFieldIdSet);

        // check if raw field id in standard_field_lib
        List<List<?>> standardFieldIdList =
                IgniteUtil.query(
                        "GRAVITY",
                        String.format(
                                SQL_QUERY_FIELD_ID_FROM_STANDARD_LIB, toSqlFormat(leftRawFieldId)));

        if (standardFieldIdList != null && standardFieldIdList.get(0) != null) {

            for (List<?> row : standardFieldIdList) {
                int fieldIndex = Integer.parseInt(row.get(0).toString());
                String rawFieldId = row.get(1).toString();
                log.info(
                        "Generate field index [{}] for rawFieldId [{}] using standard lib",
                        fieldIndex,
                        rawFieldId);
                rawFieldIdToPairMap.put(rawFieldId, Pair.of(fieldIndex, true));
            }
            if (rawFieldIdToPairMap.size() == rawFieldIdSet.size()) {
                return rawFieldIdToPairMap;
            }
            leftRawFieldId.removeAll(rawFieldIdToPairMap.keySet());
        }

        // field index not exists in standard_field_lib, try field_index_alloc
        List<List<?>> nonStandardFieldIdList =
                IgniteUtil.query(
                        "GRAVITY",
                        String.format(
                                SQL_QUERY_FIELD_ID_FROM_NON_STANDARD_LIB,
                                toSqlFormat(leftRawFieldId)));
        if (nonStandardFieldIdList != null && nonStandardFieldIdList.get(0) != null) {
            for (List<?> row : nonStandardFieldIdList) {
                int fieldIndex = Integer.parseInt(row.get(0).toString());
                String rawFieldId = row.get(1).toString();
                log.info(
                        "Generate field index [{}] for rawFieldId [{}] using non standard lib",
                        fieldIndex,
                        rawFieldId);
                // RawFieldIdUtil.getPrefNameAndDataType(rawFieldId).getKey());
                rawFieldIdToPairMap.put(rawFieldId, Pair.of(fieldIndex, false));
            }

            if (rawFieldIdToPairMap.size() == rawFieldIdSet.size()) {
                return rawFieldIdToPairMap;
            }
            leftRawFieldId.removeAll(rawFieldIdToPairMap.keySet());
        }

        // generate fieldIndex
        Map<String, Integer> toInsertMap = getFieldIndexUsingHashcode(leftRawFieldId);
        Set<Integer> exceptSet = new HashSet<>(toInsertMap.values());
        while (true) {
            // step 1: insert into field_index_alloc
            try {
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                String sqlStr =
                        toInsertMap.entrySet().stream()
                                .map(
                                        entry -> {
                                            return "("
                                                    + entry.getValue()
                                                    + ", '"
                                                    + entry.getKey()
                                                    + "', '"
                                                    + timestamp
                                                    + "')";
                                        })
                                .collect(Collectors.joining(","));
                IgniteUtil.query(
                        "GRAVITY", String.format(SQL_INSERT_INTO_NON_STANDARD_LIB, sqlStr));
            } catch (ClientException e) {
                if (e.getMessage().contains("Duplicate key during INSERT")) {
                    log.warn(
                            "Generate field index insert error. May be the some prefName generating field index in other thread. Continuing..",
                            e);
                }
            }

            // step 2: check field_index_alloc
            List<List<?>> finalCheckResult =
                    IgniteUtil.query(
                            "GRAVITY",
                            String.format(
                                    SQL_QUERY_FIELD_ID_FROM_NON_STANDARD_LIB,
                                    toSqlFormat(leftRawFieldId)));

            if (finalCheckResult != null && !finalCheckResult.isEmpty()) {
                for (List<?> row : finalCheckResult) {
                    int fieldIndex = Integer.parseInt(row.get(0).toString());
                    String rawFieldId = row.get(1).toString();
                    rawFieldIdToPairMap.put(rawFieldId, Pair.of(fieldIndex, false));
                    leftRawFieldId.remove(rawFieldId);
                    exceptSet.add(fieldIndex);
                }
            }

            if (leftRawFieldId.isEmpty()) {
                break;
            } else {
                toInsertMap = getFieldIndexUsingHashcode(leftRawFieldId, exceptSet);
            }
        }

        return rawFieldIdToPairMap;
    }

    private static Map<String, Integer> getFieldIndexUsingHashcode(
            Set<String> rawFieldIdSet, Set<Integer> exceptSet) {
        Set<Integer> generatedFieldIndex =
                exceptSet == null ? new HashSet<>() : new HashSet<>(exceptSet);
        Map<String, Integer> result = new HashMap<>();

        for (String rawFieldId : rawFieldIdSet) {
            int fieldIndex = rawFieldId.hashCode();
            int retryNum = 0;
            while (true) {
                if (fieldIndex >= 0 && fieldIndex <= FIELD_INDEX_RESERVATION) {
                    fieldIndex = fieldIndex + FIELD_INDEX_RESERVATION + 1;
                }
                if (generatedFieldIndex.contains(fieldIndex)) {
                    fieldIndex = (rawFieldId + Integer.toString(retryNum)).hashCode();
                    retryNum++;
                    continue;
                }
                generatedFieldIndex.add(fieldIndex);
                result.put(rawFieldId, fieldIndex);
                break;
            }
        }
        return result;
    }

    private static Map<String, Integer> getFieldIndexUsingHashcode(Set<String> rawFieldIdSet) {
        return getFieldIndexUsingHashcode(rawFieldIdSet, null);
    }

    private static String toSqlFormat(Collection<String> list) {
        return list.stream().map(str -> "'" + str + "'").collect(Collectors.joining(", "));
    }
}
