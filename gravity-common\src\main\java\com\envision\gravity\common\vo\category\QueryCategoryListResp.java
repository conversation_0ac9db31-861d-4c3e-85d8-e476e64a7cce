package com.envision.gravity.common.vo.category;

import com.envision.gravity.common.po.TblCategory;

import java.util.List;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/26 20:05 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryCategoryListResp {

    private List<TblCategory> categoryList;

    private int totalSize;
}
