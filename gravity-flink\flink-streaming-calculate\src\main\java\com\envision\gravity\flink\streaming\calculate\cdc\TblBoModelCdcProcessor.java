package com.envision.gravity.flink.streaming.calculate.cdc;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.cdc.OPEnum;
import com.envision.gravity.flink.streaming.calculate.dto.TblBoModel;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TblBoModelCdcProcessor {

    private static final Logger logger = LoggerFactory.getLogger(TblBoModelCdcProcessor.class);

    private static volatile TblBoModelCdcProcessor uniqueInstance;

    private final CalcPrefCache calcPrefCache;

    public static TblBoModelCdcProcessor getInstance() {
        if (uniqueInstance == null) {
            synchronized (TblBoModelCdcProcessor.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new TblBoModelCdcProcessor();
                }
            }
        }
        return uniqueInstance;
    }

    private TblBoModelCdcProcessor() {
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
    }

    public void process(String orgId, TblBoModel before, TblBoModel after, OPEnum op) {
        if (op == OPEnum.c) {
            logger.info("New created model, skip it...");
        } else if (op == OPEnum.u) {
            if (before == null || after == null) {
                logger.error("Model cdc params invalid when update, before or after is null");
                return;
            }
            // Meta cache use modelId, no need to process update event
        } else if (op == OPEnum.d) {
            if (before == null || StringUtils.isEmpty(before.getModelId())) {
                logger.error(
                        "Model cdc params invalid when delete, before is null or modelId is empty ...");
                return;
            }

            // Target model deleted
            this.calcPrefCache.deleteByTargetModelId(orgId, before.getModelId());

            // Source model deleted
            this.calcPrefCache.deleteBySrcModelId(orgId, before.getModelId());
        }
    }
}
