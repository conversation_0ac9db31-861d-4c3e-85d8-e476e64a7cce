package com.envision.gravity.flink.streaming.virtual.attr.sync.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.virtual.attr.sync.mapper.PGCatalogMapper;

import java.util.List;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/7/10
 * @description
 */
@Slf4j
public class PGCatalogRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public PGCatalogRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<String> queryScheme() {
        try (SqlSession session = sqlSessionFactory.openSession()) {

            PGCatalogMapper pgCatalogMapper = session.getMapper(PGCatalogMapper.class);
            return pgCatalogMapper.queryScheme();
        } catch (Exception e) {
            log.error("Query pg scheme error.", e);
            throw new GravityRuntimeException("Query pg scheme error.", e);
        }
    }
}
