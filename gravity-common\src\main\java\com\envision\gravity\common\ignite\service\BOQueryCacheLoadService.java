package com.envision.gravity.common.ignite.service;

import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.bo.BOQueryCacheAbstractLoadRequest;

import java.util.List;


import org.apache.ignite.services.Service;

public interface BOQueryCacheLoadService extends Service {
    String SERVICE_NAME = "BOQueryCacheLoadService";

    ResponseResult<Void> loadAll(List<BOQueryCacheAbstractLoadRequest> loadRequests);
}
