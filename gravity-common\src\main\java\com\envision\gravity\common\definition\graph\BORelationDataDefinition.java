package com.envision.gravity.common.definition.graph;

import com.envision.gravity.common.definition.DefinitionEntity;

import javax.validation.constraints.NotBlank;

import java.util.Map;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/12
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BORelationDataDefinition implements DefinitionEntity {
    private static final long serialVersionUID = -6646012790101235896L;

    @NotBlank(message = "BORelation: graphId can not be blank")
    private String graphId;

    private JSONObject name;
    private Map<String, String> tags;
    private Boolean tree;
    private Long createdTime;
    private Long modifiedTime;
    private BORelationDataDefinition before;
}
