package com.envision.gravity.low.level.api.sql.table;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


import org.apache.ignite.cache.QueryIndex;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @description
 */
public interface CacheTableInfo {
    List<JdbcTypeField> getKeyFields();

    List<JdbcTypeField> getValueFields();

    Set<String> getQueryEntityKeyFields();

    Set<String> getNotNullFields();

    List<QueryIndex> getIndexes();

    LinkedHashMap<String, String> getQueryEntityFields();

    Map<String, Object> getDefaultFieldValues();

    String getAffKeyFieldName();
}
