package com.envision.gravity.common.response;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/30
 * @description
 */
@Getter
public enum ResponseCodeEnum {

    // SUCCESS
    SUCCESS(0, "OK"),

    // INTERNAL_ERROR
    INTERNAL_ERROR(18901, "System internal error!"),

    // UNKNOWN_EXCEPTION
    //    UNKNOWN_EXCEPTION(500010, "Unknown exception!"),

    // ID_CACHE_INIT_FALSE
    ID_CACHE_INIT_FALSE(500040, "ID cache failed to initialize!"),

    // ID_TYPE_NOT_EXISTS
    ID_TYPE_NOT_EXISTS(400140, "The id type does not exist!"),

    // ID_TWO_SEGMENTS_ARE_NULL
    ID_TWO_SEGMENTS_ARE_NULL(500041, "Both segments failed to load from DB!"),

    // REACH_UPPER_LIMIT
    REACH_UPPER_LIMIT(500042, "The number of IDs has reached the upper limit!"),

    // PARAMETER_INVALID
    //    PARAMETER_INVALID(400100, "Parameter invalid"),

    // EXPRESSION_INVALID
    //    EXPRESSION_INVALID(400101, "Parameter invalid"),

    // EDGE_TYPE_INVALID
    //    EDGE_TYPE_INVALID(400102, "Parameter invalid"),

    // EDGE_DELETE_PARAM_INVALID
    //    EDGE_DELETE_PARAM_INVALID(400103, "Parameter invalid"),

    // CATEGORY_METADATA_AMBIGUOUS
    //    CATEGORY_METADATA_AMBIGUOUS(400104, "Parameter invalid"),

    // FIELD_INVALID
    //    FIELD_INVALID(400105, "Parameter invalid"),

    // FIELD_NOT_UNIQUE
    //    FIELD_NOT_UNIQUE(400106, "Parameter invalid"),

    // FIELD_METADATA_AMBIGUOUS
    //    FIELD_METADATA_AMBIGUOUS(400107, "Parameter invalid"),

    // FIELD_SET_VALUE_FAILED
    //    FIELD_SET_VALUE_FAILED(400108, "Only when fieldType is attribute can set value"),

    // ORDER_TYPE_INVALID
    //    ORDER_TYPE_INVALID(400109, "Order type not support"),

    // FIELD_DATATYPE_CAN_NOT_CHANGE
    //    FIELD_DATATYPE_CAN_NOT_CHANGE(400110, "DataType can not change"),

    // SORT_FIELD_NOT_SUPPORT
    //    SORT_FIELD_NOT_SUPPORT(400111, "Sort field not support"),

    // NOT_SUPPORT_SEARCH
    //    NOT_SUPPORT_SEARCH(400112, "Search not support"),

    // INVALID_PARAMETERS
    INVALID_PARAMETERS(18002, "Invalid parameters!"),

    // INVALID_SQL
    INVALID_SQL(400120, "Invalid sql"),

    // PARSE_SQL_ERROR
    PARSE_SQL_ERROR(400121, "Parse sql error"),

    // UNSUPPORTED_SQL
    UNSUPPORTED_SQL(18502, "Unsupported sql"),

    // REQUEST_IGNITE_ERROR
    //    REQUEST_IGNITE_ERROR(500111, "Request ignite error!"),

    PARAM_MISSING(18001, "Param missing"),

    PARAM_CHECK_FAILED(18002, "Param check failed"),

    FIELD_DATATYPE_CAN_NOT_CHANGE(18101, "Field datatype can not change"),

    FIELD_NAME_CATEGORY_ID_NOT_UNIQUE(18102, "FieldName && CategoryId not unique"),

    ONLY_ATTRIBUTE_CAN_SET_VALUE(18103, "Only attribute can set value"),

    FROM_AND_TO_VID_ALL_BE_NULL(18104, "FromVid and toVid can not all be null"),

    TREE_CHECK_FAILED(18105, "Tree check failed"),

    PROPERTY_VALUE_NOT_UNIQUE(18106, "Property value not unique"),

    SDK_REQUEST_TIMEOUT(18801, ""),

    SQL_GRAMMAR_NOT_SUPPORT(18501, ""),

    SQL_NOT_SUPPORT(18502, ""),

    SQL_ENGINE_NOT_FOUND(18503, ""),

    SQL_EXECUTE_ERROR(18902, ""),

    BO_MODEL_NOT_FOUND(18504, ""),

    BO_FIELD_DEFINE_CONFLICT(18505, ""),

    BO_FIELD_NOT_FOUNT(18506, ""),

    HINT_PARAM_MISSING(18507, ""),

    HINT_PARAM_CHECK_FAILED(18508, ""),

    SYNC_FAILED(18600, "Sync failed!"),

    SYNC_DELETION_FAILED(18601, "Sync deletion failed!"),

    // Calculate API
    DOWNSTREAM_CALC_PREF_TYPE_NOT_SUPPORT(18911, "Downstream rule only support measurement type"),
    DOWNSTREAM_CALC_INVALID_RULE(18912, "Invalid downstream rule's expression definition"),
    DOWNSTREAM_CALC_FAILED(18913, "Downstream rule calculation failed");

    private final int code;
    private final String message;

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    ResponseCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ResponseCodeEnum parse(int code) {
        ResponseCodeEnum[] arr = values();
        for (ResponseCodeEnum v : arr) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }
}
