package com.envision.gravity.calculate.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownstreamRequest {

    @NotBlank(message = "assetId can not be blank")
    private String assetId;

    @NotBlank(message = "pointId can not be blank")
    private String pointId;

    @NotNull(message = "value can not be null")
    private Object value;
}
