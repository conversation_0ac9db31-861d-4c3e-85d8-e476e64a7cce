package com.envision.gravity.common.vo.api;

import java.util.List;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/19
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoadCacheReq {
    private String cacheName;
    private String keyTypeName;
    private String valueTypeName;
    private Map<String, Class<?>> cacheKeyCols;
    private Map<String, Class<?>> cacheValueCols;
    private List<Map<String, Object>> keyValues;
}
