package com.envision.gravity.flink.steaming.bo.event.entity.table;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblStartVid implements CdcTableEntity {
    private static final long serialVersionUID = -637063573770661780L;
    private String subGraphId;
    private String startVid;
    private boolean retain;
    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
