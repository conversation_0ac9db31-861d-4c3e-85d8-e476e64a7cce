package com.envision.gravity.low.level.api.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/05/14 20:32 @Description: */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VidConditionPart {

    private List<String> startVidCondition = new ArrayList<>();
    private List<String> toVidCondition = new ArrayList<>();
    private List<String> fromVidCondition = new ArrayList<>();
}
