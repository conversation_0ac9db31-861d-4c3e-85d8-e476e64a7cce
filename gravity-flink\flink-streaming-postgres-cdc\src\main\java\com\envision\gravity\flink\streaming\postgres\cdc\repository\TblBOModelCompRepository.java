package com.envision.gravity.flink.streaming.postgres.cdc.repository;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.mapper.TblBOModelCompMapper;
import com.envision.gravity.flink.streaming.postgres.cdc.model.params.ModelGroup;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @description
 */
@Slf4j
public class TblBOModelCompRepository {
    private final SqlSessionFactory sqlSessionFactory;

    public TblBOModelCompRepository(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public List<ModelGroup> selectModelGroupList(
            String schemaName, List<String> modelIdList, List<String> compIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {

            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (modelIdList.isEmpty() || compIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblBOModelCompMapper tblBOModelCompMapper =
                    session.getMapper(TblBOModelCompMapper.class);
            return tblBOModelCompMapper.selectModelGroupList(schemaName, modelIdList, compIdList);
        } catch (Exception e) {
            log.error("Select model group list error.", e);
            throw new GravityRuntimeException("Select model group list error.", e);
        }
    }

    public List<ModelGroup> selectModelGroupListByModelId(
            String schemaName, List<String> modelIdList) {
        try (SqlSession session = sqlSessionFactory.openSession(true)) {

            Objects.requireNonNull(schemaName, "Schema name cannot be null.");

            if (modelIdList.isEmpty()) {
                return Collections.emptyList();
            }

            TblBOModelCompMapper tblBOModelCompMapper =
                    session.getMapper(TblBOModelCompMapper.class);
            return tblBOModelCompMapper.selectModelGroupListByModelId(schemaName, modelIdList);
        } catch (Exception e) {
            log.error("Select model group list by model ids error.", e);
            throw new GravityRuntimeException("Select model group list by model ids error.", e);
        }
    }
}
