package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.TblCalcJobInfo;
import com.envision.gravity.flink.streaming.calculate.dto.recalc.ReCalcJobStatusEnum;
import com.envision.gravity.flink.streaming.calculate.flink.CalcLionConfig;
import com.envision.gravity.flink.streaming.calculate.flink.CalcPGSourceConfig;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;


import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReCalc Job Trigger Operator Handles job conflict resolution and Flink batch job submission
 *
 * <AUTHOR>
 */
public class ReCalcJobTrigger extends ProcessFunction<TblCalcJobInfo, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ReCalcJobTrigger.class);

    private TblCalcJobInfoMapper mapper;
    private FlinkJobSubmitter jobSubmitter;
    private ExecutorService executorService;

    @Override
    public void open(Configuration parameters) throws Exception {
        // Initialize database connection
        SqlSessionFactory sqlSessionFactory = CalcPGSourceConfig.getSqlSessionFactory();
        SqlSession sqlSession = sqlSessionFactory.openSession(true); // auto-commit
        this.mapper = sqlSession.getMapper(TblCalcJobInfoMapper.class);

        // Initialize Flink job submitter
        this.jobSubmitter = new FlinkJobSubmitter();

        // Initialize executor service for async job submission
        this.executorService =
                Executors.newFixedThreadPool(
                        Runtime.getRuntime().availableProcessors(),
                        r -> {
                            Thread t =
                                    new Thread(r, "ReCalcJobTrigger-" + System.currentTimeMillis());
                            t.setDaemon(true);
                            return t;
                        });

        logger.info("ReCalcJobTrigger initialized successfully");
    }

    @Override
    public void processElement(
            TblCalcJobInfo jobInfo,
            ProcessFunction<TblCalcJobInfo, Void>.Context ctx,
            Collector<Void> out)
            throws Exception {

        logger.info(
                "Processing ReCalc job trigger: jobId={}, prefRuleId={}",
                jobInfo.getJobId(),
                jobInfo.getPrefRuleId());

        try {
            // 1. Pre-submission validation
            validateJobSubmission(jobInfo);

            // 2. Cancel conflicting jobs
            cancelConflictingJobs(jobInfo);

            // 3. Submit batch job asynchronously
            submitReCalcBatchJobAsync(jobInfo);

            logger.info("ReCalc job trigger completed successfully: jobId={}", jobInfo.getJobId());

        } catch (Exception e) {
            logger.error(
                    "Failed to trigger ReCalc job: jobId={}, error={}",
                    jobInfo.getJobId(),
                    e.getMessage(),
                    e);

            // Update job status to submission failed
            updateJobStatus(jobInfo.getJobId(), ReCalcJobStatusEnum.SUBMIT_FAILED);
            throw e;
        }
    }

    /** Validate job submission prerequisites */
    private void validateJobSubmission(TblCalcJobInfo jobInfo) throws Exception {
        // Check if Flink cluster is available
        if (!jobSubmitter.isClusterAvailable()) {
            throw new RuntimeException("Flink cluster is not available for job submission");
        }

        // Validate job parameters
        if (jobInfo.getCalcStartTime() >= jobInfo.getCalcEndTime()) {
            throw new IllegalArgumentException(
                    "Invalid time range: start time must be before end time");
        }

        // Check if job info is complete
        if (jobInfo.getRuleInfo() == null) {
            throw new IllegalArgumentException("Rule info cannot be null");
        }

        logger.debug("Job submission validation passed: jobId={}", jobInfo.getJobId());
    }

    /** Cancel conflicting jobs with the same prefRuleId */
    private void cancelConflictingJobs(TblCalcJobInfo newJob) {
        try {
            // Query conflicting jobs
            List<TblCalcJobInfo> conflictingJobs =
                    mapper.findConflictingJobs(
                            newJob.getPrefRuleId(),
                            newJob.getJobId(),
                            Arrays.asList(
                                    ReCalcJobStatusEnum.INIT.getCode(),
                                    ReCalcJobStatusEnum.RUNNING.getCode()));

            if (conflictingJobs.isEmpty()) {
                logger.debug(
                        "No conflicting jobs found for prefRuleId: {}", newJob.getPrefRuleId());
                return;
            }

            // Cancel conflicting jobs
            for (TblCalcJobInfo conflictJob : conflictingJobs) {
                updateJobStatus(conflictJob.getJobId(), ReCalcJobStatusEnum.CANCELLED);
                logger.info(
                        "Cancelled conflicting job: jobId={}, prefRuleId={}",
                        conflictJob.getJobId(),
                        conflictJob.getPrefRuleId());
            }

            logger.info(
                    "Cancelled {} conflicting jobs for prefRuleId: {}",
                    conflictingJobs.size(),
                    newJob.getPrefRuleId());

        } catch (Exception e) {
            logger.error(
                    "Failed to cancel conflicting jobs for prefRuleId: {}",
                    newJob.getPrefRuleId(),
                    e);
            // Don't throw exception here, continue with job submission
        }
    }

    /** Submit ReCalc batch job asynchronously with retry mechanism */
    private void submitReCalcBatchJobAsync(TblCalcJobInfo jobInfo) {
        CompletableFuture.supplyAsync(
                        () -> {
                            return submitReCalcBatchJobWithRetry(jobInfo);
                        },
                        executorService)
                .whenComplete(
                        (flinkJobId, throwable) -> {
                            if (throwable != null) {
                                logger.error(
                                        "Async job submission failed: jobId={}, error={}",
                                        jobInfo.getJobId(),
                                        throwable.getMessage(),
                                        throwable);
                                updateJobStatus(
                                        jobInfo.getJobId(), ReCalcJobStatusEnum.SUBMIT_FAILED);
                            } else {
                                logger.info(
                                        "Async job submission completed: jobId={}, flinkJobId={}",
                                        jobInfo.getJobId(),
                                        flinkJobId);
                                // Note: Job status will be updated to RUNNING by the batch job
                                // itself
                            }
                        });
    }

    /** Submit ReCalc batch job with retry mechanism */
    private String submitReCalcBatchJobWithRetry(TblCalcJobInfo jobInfo) {
        int retryLimit = CalcLionConfig.getReCalcJobSubmitRetryLimit();
        int retryInterval = CalcLionConfig.getReCalcJobSubmitRetryIntervalSeconds();

        Exception lastException = null;

        for (int attempt = 1; attempt <= retryLimit; attempt++) {
            try {
                logger.info(
                        "Attempting to submit ReCalc batch job: jobId={}, attempt={}/{}",
                        jobInfo.getJobId(),
                        attempt,
                        retryLimit);

                // Submit batch job
                String flinkJobId = jobSubmitter.submitReCalcBatchJob(jobInfo);

                logger.info(
                        "Successfully submitted ReCalc batch job: jobId={}, flinkJobId={}, attempt={}",
                        jobInfo.getJobId(),
                        flinkJobId,
                        attempt);

                return flinkJobId;

            } catch (Exception e) {
                lastException = e;
                logger.warn(
                        "Failed to submit ReCalc batch job, attempt {}/{}: jobId={}, error={}",
                        attempt,
                        retryLimit,
                        jobInfo.getJobId(),
                        e.getMessage());

                if (attempt < retryLimit) {
                    try {
                        Thread.sleep(retryInterval * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry", ie);
                    }
                }
            }
        }

        throw new RuntimeException(
                "Failed to submit job after " + retryLimit + " attempts", lastException);
    }

    /** Update job status in database */
    private void updateJobStatus(String jobId, ReCalcJobStatusEnum status) {
        try {
            long currentTime = System.currentTimeMillis();
            int result = mapper.updateStatus(jobId, status.getCode(), currentTime);

            if (result > 0) {
                logger.debug("Updated job status: jobId={}, status={}", jobId, status);
            } else {
                logger.warn("Failed to update job status: jobId={}, status={}", jobId, status);
            }

        } catch (Exception e) {
            logger.error(
                    "Database error while updating job status: jobId={}, status={}",
                    jobId,
                    status,
                    e);
        }
    }

    /** Update job status with specific timestamp field */
    private void updateJobStatusWithTime(
            String jobId, ReCalcJobStatusEnum status, String timeField) {
        try {
            long currentTime = System.currentTimeMillis();
            int result =
                    mapper.updateStatusWithTime(jobId, status.getCode(), timeField, currentTime);

            if (result > 0) {
                logger.debug(
                        "Updated job status with time: jobId={}, status={}, timeField={}",
                        jobId,
                        status,
                        timeField);
            } else {
                logger.warn(
                        "Failed to update job status with time: jobId={}, status={}, timeField={}",
                        jobId,
                        status,
                        timeField);
            }

        } catch (Exception e) {
            logger.error(
                    "Database error while updating job status with time: jobId={}, status={}, timeField={}",
                    jobId,
                    status,
                    timeField,
                    e);
        }
    }

    @Override
    public void close() throws Exception {
        logger.info("Closing ReCalcJobTrigger...");

        // Close job submitter
        if (jobSubmitter != null) {
            jobSubmitter.close();
        }

        // Shutdown executor service
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // Close database resources
        // SqlSession will be closed automatically when SqlSessionFactory is closed

        logger.info("ReCalcJobTrigger closed successfully");
        super.close();
    }
}
