package com.envision.gravity;

import com.envision.gravity.common.util.IgniteUtil;
import com.envision.gravity.flink.warm.cdc.WarmSink;
import com.envision.gravity.flink.warm.model.ModelCache;
import com.envision.gravity.flink.warm.model.PrefValue;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


import org.apache.flink.api.java.tuple.Tuple2;

public class DataProcessor {

    private static final String DATA_DEFINITION_FILE = "data_definition.csv";
    private static final String PROCESSED_FILE = "processed_systemids.txt";
    private static final String WORKING_DIR = "/tmp/warmup/";
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ModelCache modelCache = new ModelCache();

    public static void main(String[] args) {
        Map<String, String> config = parseArgs(args);
        String dryRun = "false";
        if (config.get("dry_run") != null) {
            dryRun = config.get("dry_run");
        }
        String workingDir = WORKING_DIR;
        if (config.get("work_dir") != null) {
            workingDir = config.get("work_dir");
        }
        log("Using working dir " + workingDir);
        Set<String> processedSystemIds = readProcessedSystemIds(workingDir);

        try (BufferedReader reader =
                new BufferedReader(new FileReader(workingDir + "/" + DATA_DEFINITION_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length < 3) {
                    log("Invalid line: " + line);
                    continue;
                }
                String orgId = parts[0].trim();
                String systemId = parts[1].trim();
                String modelId = parts[2].trim();

                if (!processedSystemIds.contains(orgId + ":" + systemId)) {
                    if (dryRun.equalsIgnoreCase("true")) {
                        processDry(orgId, systemId, modelId);
                    } else {
                        process(orgId, systemId, modelId);
                    }
                    writeProcessedSystemId(workingDir, orgId, systemId);
                    log("Processed: " + systemId);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.exit(1);
        }
        IgniteUtil.close();
    }

    // 自定义处理逻辑
    private static void process(String orgId, String systemId, String modelId) {
        log("Processing orgId: " + orgId + ", SystemId: " + systemId + ", ModelID: " + modelId);
        List<PrefValue> allPrefs = modelCache.getOrLoadModelPref(orgId, modelId);
        List<Tuple2<String, Class<?>>> wideTableColumns =
                WarmSink.getWideTableColumn(orgId, allPrefs);
        WarmSink.warnUpObjectCache(orgId, systemId, wideTableColumns);
    }

    private static void processDry(String orgId, String systemId, String modelId) {
        log(
                "Dry ryn processing orgId: "
                        + orgId
                        + ", SystemId: "
                        + systemId
                        + ", ModelID: "
                        + modelId);
        List<PrefValue> allPrefs = modelCache.getOrLoadModelPref(orgId, modelId);
        List<Tuple2<String, Class<?>>> wideTableColumns =
                WarmSink.getWideTableColumn(orgId, allPrefs);
        WarmSink.dryWarnUpObjectCache(orgId, systemId, wideTableColumns);
    }

    // 读取已完成的systemid
    private static Set<String> readProcessedSystemIds(String workingDir) {
        Set<String> set = new HashSet<>();
        Path path = Paths.get(workingDir, PROCESSED_FILE);
        if (Files.exists(path)) {
            try (BufferedReader reader = Files.newBufferedReader(path)) {
                String line;
                while ((line = reader.readLine()) != null) {
                    set.add(line.trim());
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return set;
    }

    // 写入已完成的systemid
    private static void writeProcessedSystemId(String workingDir, String orgId, String systemId) {
        try (BufferedWriter writer =
                new BufferedWriter(new FileWriter(workingDir + "/" + PROCESSED_FILE, true))) {
            writer.write(orgId + ":" + systemId);
            writer.newLine();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Map<String, String> parseArgs(String[] args) {
        Map<String, String> result = new HashMap<>();

        for (String arg : args) {
            if (arg.startsWith("--") && arg.contains("=")) {
                int idx = arg.indexOf('=');
                String key = arg.substring(2, idx); // 去掉 "--"
                String value = arg.substring(idx + 1);
                result.put(key, value);
            } else {
                System.err.println("Invalid argument format: " + arg);
            }
        }

        return result;
    }

    public static void log(String message) {
        String timestamp = LocalDateTime.now().format(formatter);
        System.out.println("[" + timestamp + "] " + message);
    }
}
