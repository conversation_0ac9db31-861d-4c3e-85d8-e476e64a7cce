SELECT
    /*+ ENGINE('bo') */
    /*+ ORG('$orgId') */
#foreach($property in $allProperties)
    /*+ SET(`$property.prefName`=LATEST(`$property.prefName`)) */
#end
    asset_id#if($allProperties.size() > 0),#end
#foreach($property in $allProperties)
    #if($property.prefType.name() == "ATTRIBUTE")
    `$property.prefName` AS `$property.prefName`#if($foreach.hasNext),#end
    #elseif($property.prefType.name() == "MEASUREPOINT")
    `${property.prefName}.time` AS `${property.prefName}_time`,
    `${property.prefName}.value` AS `${property.prefName}_value`#if($foreach.hasNext),#end
    #end
#end
FROM `$modelId`
WHERE asset_id IN (#foreach($assetId in $assetIds)'$assetId'#if($foreach.hasNext), #end#end)
