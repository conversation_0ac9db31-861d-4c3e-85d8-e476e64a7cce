package com.envision.gravity.low.level.api.rest.controller;

import com.envision.gravity.common.response.ResponseResult;
import com.envision.gravity.common.vo.search.model.SearchModelReq;
import com.envision.gravity.low.level.api.rest.aspect.GravityLog;
import com.envision.gravity.low.level.api.rest.enums.Constants;
import com.envision.gravity.low.level.api.rest.service.impl.SearchModelsServiceImpl;

import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

import static com.envision.gravity.low.level.api.rest.util.DataCheckUtil.checkModelPagination;

/** @Author: qi.jiang2 @Date: 2024/03/05 15:32 @Description: */
@Api(tags = "Models search")
@Validated
@RestController
@RequestMapping("/models")
public class ModelSearchController {

    @Resource private SearchModelsServiceImpl searchModelsService;

    @GravityLog
    @PostMapping("/search")
    public ResponseResult<?> searchModels(
            @NotBlank(message = "orgId can not be blank") @RequestParam String orgId,
            @RequestBody SearchModelReq searchModelReq,
            HttpServletRequest request) {
        checkModelPagination(searchModelReq.getPagination());
        String language = request.getHeader(Constants.HTTP_HEAD_ACCEPT_LANGUAGE);
        if (language == null || language.isEmpty()) {
            language = Constants.DEFAULT_LANGUAGE;
        }
        return searchModelsService.searchModels(searchModelReq, language, orgId);
    }
}
