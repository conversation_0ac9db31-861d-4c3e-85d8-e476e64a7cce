package com.envision.gravity.cache.calculate;

import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.ClassSize;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;

public class CalcPrefCacheUtils {

    public static long estimateTargetCacheSize(CalcPropertyMeta meta) {
        if (meta == null) {
            return 0;
        }

        // Base object overhead
        long size = ClassSize.OBJECT;

        // String fields from CalcPropertyMeta
        size +=
                ClassSize.STRING
                        + (meta.getPrefName() != null ? meta.getPrefName().length() * 2L : 0);

        // Boolean field
        size += ClassSize.BOOLEAN;

        // List<SrcPrefItem> field
        if (meta.getSrcPrefItems() != null) {
            size += ClassSize.ARRAYLIST;
            for (SrcPrefItem item : meta.getSrcPrefItems()) {
                // SrcPrefItem object overhead
                size += ClassSize.OBJECT;
                // String fields in SrcPrefItem
                size += ClassSize.STRING * 2;
                if (item.getModelId() != null) {
                    size += item.getModelId().length() * 2L;
                }
                if (item.getPrefName() != null) {
                    size += item.getPrefName().length() * 2L;
                }
            }
        }

        // Fields from BaseCalcPropertyMeta
        size += ClassSize.STRING * 5; // 5 String fields
        if (meta.getTargetCategoryId() != null) {
            size += meta.getTargetCategoryId().length() * 2L;
        }
        if (meta.getTargetCompId() != null) {
            size += meta.getTargetCompId().length() * 2L;
        }
        if (meta.getTargetPrefId() != null) {
            size += meta.getTargetPrefId().length() * 2L;
        }
        if (meta.getSrcCategoryId() != null) {
            size += meta.getSrcCategoryId().length() * 2L;
        }
        if (meta.getExpression() != null) {
            size += meta.getExpression().length() * 2L;
        }

        return ClassSize.align(size);
    }

    public static long estimateSrcCacheSize(String targetPrefKey) {
        if (targetPrefKey == null) {
            return 0;
        }

        // Base object overhead for String
        long size = ClassSize.STRING;

        // String content size (2 bytes per character in UTF-16)
        size += targetPrefKey.length() * 2L;

        return ClassSize.align(size);
    }
}
