package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.recalc.CalcJobTask;


import org.apache.flink.api.connector.source.SourceSplit;

/**
 * 计算作业任务分片
 *
 * <AUTHOR>
 */
public class CalcJobTaskSplit implements SourceSplit {

    private final String splitId;
    private final CalcJobTask task;
    private boolean isCompleted = false;

    public CalcJobTaskSplit(String splitId, CalcJobTask task) {
        this.splitId = splitId;
        this.task = task;
        this.isCompleted = false;
    }

    @Override
    public String splitId() {
        return splitId;
    }

    public CalcJobTask getTask() {
        return task;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void markCompleted() {
        this.isCompleted = true;
    }

    @Override
    public String toString() {
        return "CalcJobTaskSplit{"
                + "splitId='"
                + splitId
                + '\''
                + ", taskId='"
                + (task != null ? task.getTaskId() : "null")
                + '\''
                + ", isCompleted="
                + isCompleted
                + '}';
    }
}
