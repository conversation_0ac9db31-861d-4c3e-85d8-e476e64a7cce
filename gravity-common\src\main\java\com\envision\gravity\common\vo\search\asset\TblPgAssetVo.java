package com.envision.gravity.common.vo.search.asset;

import java.sql.Timestamp;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/20 11:09 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TblPgAssetVo {

    private String assetId;

    private String systemId;

    private String assetDisplayName;

    private String description;

    private String orgId;

    private JSONObject assetTags;

    private JSONObject models;

    private String uniqueId;

    private Timestamp assetCreatedTime;

    private String assetCreatedUser;

    private Timestamp assetModifiedTime;

    private String assetModifiedUser;
}
