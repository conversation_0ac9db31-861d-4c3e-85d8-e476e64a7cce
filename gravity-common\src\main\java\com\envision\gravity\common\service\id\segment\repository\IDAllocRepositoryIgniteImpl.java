package com.envision.gravity.common.service.id.segment.repository;

import com.envision.gravity.common.service.id.segment.model.IDAlloc;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;


import org.apache.ignite.IgniteCache;
import org.apache.ignite.cache.query.SqlFieldsQuery;

/**
 * <AUTHOR>
 * @date 2024/3/15
 * @description
 */
public class IDAllocRepositoryIgniteImpl implements IDAllocRepository {

    private final IgniteCache<?, ?> igniteCache;

    public IDAllocRepositoryIgniteImpl(IgniteCache<?, ?> igniteCache) {
        // TODO unified query
        this.igniteCache = igniteCache;
    }

    @Override
    public IDAlloc updateSegmentMaxIdAndGetIDAlloc(String idType) {
        SqlFieldsQuery updateQuery =
                new SqlFieldsQuery(
                                "UPDATE ID_ALLOC SET SEGMENT_MAX_ID = CASE WHEN DATEDIFF('DAY', MODIFIED_TIME, CURRENT_DATE)>0  THEN 1 + STEP ELSE SEGMENT_MAX_ID + STEP END, MODIFIED_TIME=? WHERE ID_TYPE=?;")
                        .setSchema("GRAVITY")
                        .setArgs(new Timestamp(System.currentTimeMillis()), idType);
        SqlFieldsQuery selectQuery =
                new SqlFieldsQuery(
                                "SELECT ID_TYPE, SEGMENT_MAX_ID, STEP FROM ID_ALLOC WHERE ID_TYPE=?;")
                        .setArgs(idType);

        return updateAndGetIDAlloc(updateQuery, selectQuery);
    }

    @Override
    public IDAlloc updateSegmentMaxIdByCustomStepAndGetIDAlloc(IDAlloc idAlloc) {
        SqlFieldsQuery updateQuery =
                new SqlFieldsQuery(
                                "UPDATE ID_ALLOC SET SEGMENT_MAX_ID = CASE WHEN DATEDIFF('DAY', MODIFIED_TIME, CURRENT_DATE)>0  THEN 1 + ? ELSE SEGMENT_MAX_ID + ? END, MODIFIED_TIME=? WHERE ID_TYPE=?;")
                        .setSchema("GRAVITY")
                        .setArgs(
                                idAlloc.getStep(),
                                idAlloc.getStep(),
                                new Timestamp(System.currentTimeMillis()),
                                idAlloc.getIdType());
        SqlFieldsQuery selectQuery =
                new SqlFieldsQuery(
                                "SELECT ID_TYPE, SEGMENT_MAX_ID, STEP FROM ID_ALLOC WHERE ID_TYPE=?;")
                        .setArgs(idAlloc.getIdType());

        return updateAndGetIDAlloc(updateQuery, selectQuery);
    }

    @Override
    public List<String> getAllIDTypes() {
        List<List<?>> results =
                igniteCache
                        .query(
                                new SqlFieldsQuery("SELECT ID_TYPE FROM ID_ALLOC;")
                                        .setSchema("GRAVITY"))
                        .getAll();

        return results.stream()
                .map(rowData -> String.valueOf(rowData.get(0)))
                .collect(Collectors.toList());
    }

    @Override
    public IDAlloc getIDAllocByIDType(String idType) {
        SqlFieldsQuery selectQuery =
                new SqlFieldsQuery(
                                "SELECT ID_TYPE, ID_PREFIX, ID_SUFFIX, SEGMENT_MAX_ID, STEP, DESCRIPTION, CREATED_TIME, MODIFIED_TIME FROM ID_ALLOC WHERE ID_TYPE=?;")
                        .setSchema("GRAVITY")
                        .setArgs(idType);

        List<List<?>> queryResult = igniteCache.query(selectQuery).getAll();

        if (queryResult.isEmpty()) {
            return null;
        }

        List<?> columns = queryResult.get(0);
        return IDAlloc.builder()
                .idType(String.valueOf(columns.get(0)))
                .idPrefix(String.valueOf(columns.get(1)))
                .idSuffix(String.valueOf(columns.get(2)))
                .segmentMaxId(Integer.parseInt(String.valueOf(columns.get(3))))
                .step(Integer.parseInt(String.valueOf(columns.get(4))))
                .description(String.valueOf(columns.get(5)))
                .createdTime(Timestamp.valueOf(String.valueOf(columns.get(6))))
                .modifiedTime(Timestamp.valueOf(String.valueOf(columns.get(7))))
                .build();
    }

    private IDAlloc updateAndGetIDAlloc(SqlFieldsQuery updateQuery, SqlFieldsQuery selectQuery) {
        // TODO: transaction
        igniteCache.query(updateQuery).getAll();
        List<List<?>> queryResult = igniteCache.query(selectQuery).getAll();

        if (queryResult.isEmpty()) {
            return null;
        }

        List<?> columns = queryResult.get(0);
        return IDAlloc.builder()
                .idType(String.valueOf(columns.get(0)))
                .segmentMaxId(Integer.parseInt(String.valueOf(columns.get(1))))
                .step(Integer.parseInt(String.valueOf(columns.get(2))))
                .build();
    }
}
