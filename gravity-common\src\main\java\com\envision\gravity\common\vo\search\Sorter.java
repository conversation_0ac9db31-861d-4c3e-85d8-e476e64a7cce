package com.envision.gravity.common.vo.search;

import javax.validation.constraints.NotBlank;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/03/19 10:05 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Sorter {

    @NotBlank(message = "sort field can not blank")
    private String field;

    @NotBlank(message = "sort order can not blank")
    private String order;
}
