package com.envision.gravity.low.level.api.rest.dao.ignite;

import com.envision.gravity.low.level.api.rest.enums.Constants;

import org.apache.ibatis.jdbc.SQL;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/** @Author: qi.jiang2 @Date: 2024/04/12 15:49 @Description: */
public class TblSubGraphSqlProvider {

    public String selectSubGraphBySubGraphId(List<String> subGraphIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("rank");
        sql.SELECT("tree");
        sql.FROM(orgId + Constants.TBL_SUB_GRAPH_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            placeHolder.add("'" + subGraphId + "'");
        }
        sql.WHERE("sub_graph_id in (" + String.join(", ", placeHolder) + ")");
        return sql.toString();
    }

    public String selectStartVidBySubGraphId(List<String> subGraphIds, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id");
        sql.SELECT("start_vid");
        sql.SELECT("retain");
        sql.FROM(orgId + Constants.TBL_START_VID_TABLE_NAME);
        List<String> placeHolder = new ArrayList<>();
        for (String subGraphId : subGraphIds) {
            placeHolder.add("'" + subGraphId + "'");
        }
        sql.WHERE("sub_graph_id in (" + String.join(", ", placeHolder) + ")");
        return sql.toString();
    }

    public String selectByStartVidS(List<String> startVidS, String orgId) {
        SQL sql = new SQL();
        sql.SELECT("sub_graph_id")
                .SELECT("start_vid")
                .SELECT("retain")
                .FROM(orgId + Constants.TBL_START_VID_TABLE_NAME);

        String placeholders =
                startVidS.stream()
                        .map(startVid -> "'" + startVid + "'")
                        .collect(Collectors.joining(", "));

        sql.WHERE("start_vid in (" + placeholders + ")");
        return sql.toString();
    }
}
