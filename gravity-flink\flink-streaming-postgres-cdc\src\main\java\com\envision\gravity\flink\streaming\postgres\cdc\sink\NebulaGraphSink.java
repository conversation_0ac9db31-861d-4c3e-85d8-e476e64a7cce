package com.envision.gravity.flink.streaming.postgres.cdc.sink;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.flink.streaming.postgres.cdc.model.req.NebulaGraphReq;
import com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.NebulaGraphExecutor;

import java.util.List;
import java.util.stream.Collectors;


import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

/**
 * <AUTHOR>
 * @date 2024/7/17
 * @description
 */
@Slf4j
public class NebulaGraphSink extends RichSinkFunction<Tuple2<String, List<NebulaGraphReq>>> {
    private static final long serialVersionUID = -4229531998785557718L;

    private transient NebulaGraphExecutor nebulaGraphExecutor;

    @Override
    public void open(Configuration params) throws Exception {
        nebulaGraphExecutor = new NebulaGraphExecutor();
    }

    @Override
    public void close() throws Exception {}

    @Override
    public void invoke(Tuple2<String, List<NebulaGraphReq>> value, Context context) {
        String spaceName = value.f0;
        List<NebulaGraphReq> nebulaGraphReqList = value.f1;
        if (spaceName == null || nebulaGraphReqList.isEmpty()) {
            return;
        }

        try {
            List<String> deletedVertexVidList =
                    nebulaGraphReqList.stream()
                            .map(NebulaGraphReq::getDeletedVertexVidList)
                            .flatMap(List::stream)
                            .collect(Collectors.toList());

            if (deletedVertexVidList.isEmpty()) {
                log.warn("Need nebula vertices are empty, spaceName: [{}].", spaceName);
                return;
            }

            nebulaGraphExecutor.deleteVertices(spaceName.toUpperCase(), deletedVertexVidList);
            log.info(
                    "Delete nebula vertices success, spaceName: [{}], vid list: [{}]",
                    spaceName,
                    deletedVertexVidList);
        } catch (Exception e) {
            log.error("Delete nebula vertices error, spaceName: [{}].", spaceName, e);
            throw new GravityRuntimeException("Delete nebula vertices error.", e);
        }
    }
}
