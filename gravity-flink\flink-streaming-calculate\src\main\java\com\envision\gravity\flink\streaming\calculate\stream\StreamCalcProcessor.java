package com.envision.gravity.flink.streaming.calculate.stream;

import com.envision.gravity.cache.calculate.CalcPrefCache;
import com.envision.gravity.cache.calculate.entity.CalcPropertyMeta;
import com.envision.gravity.cache.calculate.entity.InvalidCalcMetaType;
import com.envision.gravity.cache.calculate.entity.SrcPrefItem;
import com.envision.gravity.common.CacheFactory;
import com.envision.gravity.common.calculate.*;
import com.envision.gravity.common.enums.PrefType;
import com.envision.gravity.flink.streaming.calculate.flink.offset.OffsetInfo;
import com.envision.gravity.flink.streaming.calculate.meta.CalcMetaProcessor;
import com.envision.gravity.flink.streaming.calculate.stream.serde.*;
import com.envision.gravity.flink.streaming.calculate.utils.SqlGatewayQueryService;

import java.util.*;
import java.util.stream.Collectors;


import com.univers.business.object.calc.dto.ExpressionCalculatorInput;
import com.univers.business.object.calc.dto.ExpressionCalculatorOutput;
import com.univers.business.object.calc.request.CalcExpressionRequest;
import com.univers.business.object.calc.response.CalcExpressionResponse;
import com.univers.business.object.calc.util.ExpressionUtil;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Calculate processor responsible for processing message batches and executing calculation logic.
 * Handles direct mapping and complex calculation rules for streaming data.
 *
 * <AUTHOR>
 */
public class StreamCalcProcessor
        extends ProcessFunction<
                Tuple2<LegacyMsgList, OffsetInfo>, Tuple2<CalcResultMsg, OffsetInfo>> {

    private static final Logger logger = LoggerFactory.getLogger(StreamCalcProcessor.class);
    private static volatile int processElementCallCount = 0;

    private CalcPrefCache calcPrefCache;
    private transient SqlGatewayQueryService sqlGatewayQueryService;

    @Override
    public void open(Configuration parameters) throws Exception {
        logger.info("CalculateProcessor initialization started");
        // TODO 并发问题
        CalcMetaProcessor.getInstance().batchLoad();
        this.calcPrefCache = CacheFactory.getCalcPrefCache();
        // Get calculation service helper singleton instance
        this.sqlGatewayQueryService = SqlGatewayQueryService.getInstance();

        logger.info("CalculateProcessor initialization completed, cache loaded");
    }

    @Override
    public void close() throws Exception {
        if (sqlGatewayQueryService != null) {
            sqlGatewayQueryService.close();
        }
        super.close();
    }

    @Override
    public void processElement(
            Tuple2<LegacyMsgList, OffsetInfo> value,
            ProcessFunction<Tuple2<LegacyMsgList, OffsetInfo>, Tuple2<CalcResultMsg, OffsetInfo>>
                            .Context
                    context,
            Collector<Tuple2<CalcResultMsg, OffsetInfo>> out)
            throws Exception {

        System.out.println("=== CalculateProcessor.processElement called ===");
        try {
            processElementCallCount++;
            logger.info(
                    "CalculateProcessor.processElement called - Processing message batch #{}, containing {} messages",
                    processElementCallCount,
                    value.f0.getLegacyMsgList().size());

            List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> allCalcResult = new ArrayList<>();

            for (LegacyMsg msg : value.f0.getLegacyMsgList()) {
                logger.debug(
                        "Processing message: orgId={}, modelId={}",
                        msg.getOrgId(),
                        msg.getModelId());
                LegacyMsgWithMultiAssets multiAssetsMsg;

                // Handle different types of LegacyMsg
                if (msg instanceof LegacyMsgWithSingleAsset) {
                    // Convert LegacyMsgWithSingleAsset to LegacyMsgWithMultiAssets
                    LegacyMsgWithSingleAsset singleAssetMsg = (LegacyMsgWithSingleAsset) msg;
                    multiAssetsMsg = new LegacyMsgWithMultiAssets();
                    multiAssetsMsg.setOrgId(singleAssetMsg.getOrgId());
                    multiAssetsMsg.setModelId(singleAssetMsg.getModelId());
                    multiAssetsMsg.setModelIdPath(singleAssetMsg.getModelIdPath());

                    // Convert single payload to list
                    List<LegacyPayload> payloadList = new ArrayList<>();
                    LegacyPayload payload = singleAssetMsg.getPayload();
                    if (payload != null) {
                        payloadList.add(payload);
                    }
                    multiAssetsMsg.setPayload(payloadList);
                } else if (msg instanceof LegacyMsgWithMultiAssets) {
                    multiAssetsMsg = (LegacyMsgWithMultiAssets) msg;
                } else {
                    logger.warn("Unsupported LegacyMsg type: {}", msg.getClass().getName());
                    continue;
                }

                String orgId = multiAssetsMsg.getOrgId();
                String srcModelId = multiAssetsMsg.getModelId();
                String srcCategoryId =
                        CalcCommonUtils.extractCategoryId(multiAssetsMsg.getModelIdPath());

                logger.debug(
                        "Processing message: orgId={}, srcModelId={}, srcCategoryId={}",
                        orgId,
                        srcModelId,
                        srcCategoryId);

                // Collect all asset IDs
                Set<String> srcAssetIds =
                        multiAssetsMsg.getPayload().stream()
                                .map(LegacyPayload::getAssetId)
                                .collect(Collectors.toSet());

                // TODO bo cache
                // Get associated asset ID mappings
                Map<String, Set<String>> srcAssetId2TargetAssetIdMap =
                        ModelMetaQueryHandler.getInstance()
                                .getAppAssetBySystemIds(orgId, srcAssetIds);
                logger.debug(
                        "Source asset mapping: {} -> {}", srcAssetIds, srcAssetId2TargetAssetIdMap);

                // Get information for all target assets
                Set<String> allTargetAssetIds =
                        srcAssetId2TargetAssetIdMap.values().stream()
                                .flatMap(Set::stream)
                                .collect(Collectors.toSet());
                Map<String, AssetInfoWithModelPath> targetAssetInfoMap =
                        ModelMetaQueryHandler.getInstance()
                                .getAssetInfosWithModelPath(orgId, allTargetAssetIds);
                logger.debug("Target asset information: {}", targetAssetInfoMap.keySet());

                // Process each source asset_id
                for (LegacyPayload payload : multiAssetsMsg.getPayload()) {
                    List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> results =
                            processEachSourceAssetId(
                                    orgId, srcModelId, srcCategoryId, payload, targetAssetInfoMap);
                    allCalcResult.addAll(results);
                }
            }

            logger.info(
                    "CalculateProcessor processing completed, generated {} calculation results",
                    allCalcResult.size());
            System.out.println(
                    "=== CalculateProcessor: About to call out.collect() for each message ===");

            // Send each message separately instead of batching them
            for (int i = 0; i < allCalcResult.size(); i++) {
                Tuple2<LegacyMsgWithMultiAssets, Boolean> msgTuple = allCalcResult.get(i);

                // Create a single-message CalcResultMsg for each result
                CalcResultMsg singleResultMsg = new CalcResultMsg();
                List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> singleMsgList = new ArrayList<>();
                singleMsgList.add(msgTuple);
                singleResultMsg.setLegacyMsgList(singleMsgList);

                logger.debug(
                        "Sending message #{}: direct mapping={}, modelId={}",
                        i + 1,
                        msgTuple.f1,
                        msgTuple.f0.getModelId());

                out.collect(new Tuple2<>(singleResultMsg, value.f1));
            }

            System.out.println(
                    "=== CalculateProcessor: out.collect() completed for all messages ===");
            logger.info(
                    "CalculateProcessor successfully sent {} results to downstream",
                    allCalcResult.size());

        } catch (Exception e) {
            logger.error("Processing exception: {}", e.getMessage(), e);
            throw e;
        }
    }

    private List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> processEachSourceAssetId(
            String orgId,
            String srcModelId,
            String srcCategoryId,
            LegacyPayload srcPayload,
            Map<String, AssetInfoWithModelPath> targetAssetInfoMap) {

        Map<String, LegacyMsgWithMultiAssets> mappingTargetModel2MsgMap = new HashMap<>();
        Map<String, LegacyMsgWithMultiAssets> nonMappingTargetModel2MsgMap = new HashMap<>();

        // Process each source point
        for (Map.Entry<String, Object> srcPointEntry : srcPayload.getPoints().entrySet()) {
            String srcPrefName = srcPointEntry.getKey();
            logger.debug(
                    "Processing source property: {} = {}", srcPrefName, srcPointEntry.getValue());

            // Step 1: Get associated target properties
            Set<PropertyId> srcRelatedTargetPropertyIdSet =
                    this.calcPrefCache.getTargetBySrcPref(orgId, srcPrefName, srcModelId);
            logger.debug("Found target properties: {}", srcRelatedTargetPropertyIdSet);

            if (srcRelatedTargetPropertyIdSet.isEmpty()) {
                logger.warn(
                        "No target properties found, srcPrefName: {}, srcModelId: {}",
                        srcPrefName,
                        srcModelId);
                continue;
            }

            // Step 2: Filter target properties
            for (Map.Entry<String, AssetInfoWithModelPath> entry : targetAssetInfoMap.entrySet()) {
                String targetAssetId = entry.getKey();
                AssetInfoWithModelPath targetAssetInfo = entry.getValue();
                String targetModelId = targetAssetInfo.getModelId();

                // Filter by target model ID
                List<PropertyId> filteredTargetPropertyIdSet =
                        srcRelatedTargetPropertyIdSet.stream()
                                .filter(prop -> prop.getModelId().equals(targetModelId))
                                .collect(Collectors.toList());

                // Further filter by source category ID
                List<CalcPropertyMeta> filteredTargetPropertyList = new ArrayList<>();
                for (PropertyId targetPropId : filteredTargetPropertyIdSet) {
                    Optional<CalcPropertyMeta> targetRuleMeta =
                            this.calcPrefCache.getByTargetPref(
                                    orgId,
                                    targetModelId,
                                    targetPropId.getCompId(),
                                    targetPropId.getPrefId(),
                                    srcCategoryId);
                    targetRuleMeta.ifPresent(filteredTargetPropertyList::add);
                }

                if (filteredTargetPropertyList.isEmpty()) {
                    logger.warn(
                            "Src related rules exist, however filtered by target asset, related rules: {}, target asset: {}",
                            filteredTargetPropertyList,
                            targetAssetInfo);
                    continue;
                }

                // Step 3: Process each target property
                for (CalcPropertyMeta targetProp : filteredTargetPropertyList) {
                    logger.debug(
                            "Processing target property: {}, direct mapping: {}, expression valid: {}",
                            targetProp.getPrefName(),
                            targetProp.isDirectMapping(),
                            targetProp.isValidExpr());

                    if (!targetProp.isValidExpr()) {
                        logger.error(
                                "Upstream rule is invalid, rule info: {}, invalid type: {}",
                                targetProp,
                                InvalidCalcMetaType.getByCode(targetProp.getInvalidType()));
                        continue;
                    }

                    if (targetProp.getPrefType().equals(PrefType.ATTRIBUTE)) {
                        logger.warn(
                                "Rule's target property is attribute, skip it, rule info: {}",
                                targetProp);
                        continue;
                    }

                    if (targetProp.isDirectMapping()) {
                        logger.debug(
                                "Executing direct mapping: {} -> {}",
                                srcPrefName,
                                targetProp.getPrefName());
                        // Direct mapping processing
                        LegacyMsgWithMultiAssets targetMsg =
                                mappingTargetModel2MsgMap.computeIfAbsent(
                                        targetModelId,
                                        k -> {
                                            LegacyMsgWithMultiAssets msg =
                                                    new LegacyMsgWithMultiAssets();
                                            msg.setOrgId(orgId);
                                            msg.setModelId(targetModelId);
                                            msg.setModelIdPath(targetAssetInfo.getModelPath());
                                            msg.setPayload(new ArrayList<>());
                                            return msg;
                                        });

                        // Create or update LegacyPayload
                        LegacyPayload targetPayload =
                                targetMsg.getPayload().stream()
                                        .filter(p -> p.getAssetId().equals(targetAssetId))
                                        .findFirst()
                                        .orElseGet(
                                                () -> {
                                                    LegacyPayload newPayload = new LegacyPayload();
                                                    newPayload.setAssetId(targetAssetId);
                                                    newPayload.setTime(srcPayload.getTime());
                                                    newPayload.setPoints(new HashMap<>());
                                                    targetMsg.getPayload().add(newPayload);
                                                    return newPayload;
                                                });

                        // Add new measurement point
                        targetPayload
                                .getPoints()
                                .put(targetProp.getPrefName(), srcPointEntry.getValue());
                    } else {
                        // Non-direct mapping processing
                        logger.debug(
                                "Executing non-direct mapping: expression={}, target={}",
                                targetProp.getExpression(),
                                targetProp.getPrefName());

                        try {
                            processNonDirectMapping(
                                    orgId,
                                    srcModelId,
                                    srcPayload,
                                    targetProp,
                                    targetAssetId,
                                    targetAssetInfo,
                                    nonMappingTargetModel2MsgMap);
                        } catch (Exception e) {
                            logger.error(
                                    "Non-direct mapping failed for target property: {}, error: {}",
                                    targetProp.getPrefName(),
                                    e.getMessage(),
                                    e);
                            // Continue processing other properties
                        }
                    }
                }
            }
        }

        List<Tuple2<LegacyMsgWithMultiAssets, Boolean>> calcRes = new ArrayList<>();
        calcRes.addAll(
                mappingTargetModel2MsgMap.values().stream()
                        .map(
                                legacyMsgWithMultiAssets ->
                                        new Tuple2<>(legacyMsgWithMultiAssets, true))
                        .collect(Collectors.toList()));
        calcRes.addAll(
                nonMappingTargetModel2MsgMap.values().stream()
                        .map(
                                legacyMsgWithMultiAssets ->
                                        new Tuple2<>(legacyMsgWithMultiAssets, false))
                        .collect(Collectors.toList()));

        return calcRes;
    }

    /** Process non-direct mapping calculation */
    private void processNonDirectMapping(
            String orgId,
            String srcModelId,
            LegacyPayload srcPayload,
            CalcPropertyMeta targetProp,
            String targetAssetId,
            AssetInfoWithModelPath targetAssetInfo,
            Map<String, LegacyMsgWithMultiAssets> nonMappingTargetModel2MsgMap)
            throws Exception {

        String targetModelId = targetAssetInfo.getModelId();

        // Step 1: Group SrcPrefItems by modelId
        // modelId => <assetId, propInfo>
        Map<String, Map<String, Set<PropertyInfo>>> groupedSrcPrefs =
                sqlGatewayQueryService.groupSrcPrefItemsForSqlGateway(
                        targetProp.getSrcPrefItems(), srcModelId, targetAssetId, srcPayload);

        // Step 2: Query data from SQL Gateway for external models
        Map<String, LegacyPayload> queryResults = new HashMap<>();
        for (Map.Entry<String, Map<String, Set<PropertyInfo>>> modelEntry :
                groupedSrcPrefs.entrySet()) {
            String modelId = modelEntry.getKey();
            Map<String, Set<PropertyInfo>> assetPropertyInfoMap = modelEntry.getValue();

            if (!assetPropertyInfoMap.isEmpty()) {
                Map<String, LegacyPayload> modelResults =
                        sqlGatewayQueryService.queryLatestValues(
                                orgId, modelId, assetPropertyInfoMap);
                queryResults.putAll(modelResults);
            }
        }

        // Step 3: Build calculation data
        Map<String, Object> calcData =
                buildCalcData(srcPayload, queryResults, targetProp, srcModelId, targetModelId);

        // Step 4: Execute expression calculation
        CalcExpressionRequest calcRequest = new CalcExpressionRequest();
        List<ExpressionCalculatorInput> calcInputs = new ArrayList<>(1);

        calcInputs.add(
                ExpressionCalculatorInput.builder()
                        .expression(targetProp.getExpression())
                        .data(calcData)
                        .build());
        calcRequest.setCalcInputs(calcInputs);

        logger.debug(
                "Expression calculation request: expression={}, data={}",
                targetProp.getExpression(),
                calcData);

        List<CalcExpressionResponse> calcResult;
        try {
            calcResult = ExpressionUtil.calcExpression(calcRequest);
        } catch (Exception e) {
            logger.error(
                    "Calculate failed, orgId: {}, param: {}, reason {}",
                    orgId,
                    calcRequest,
                    e.getMessage());
            throw new RuntimeException("Expression calculation failed", e);
        }

        ExpressionCalculatorOutput calcOutput = calcResult.get(0).getExpressionCalcResult();
        Object calculatedValue = calcOutput.getValue();

        // Step 5: Build result message
        LegacyMsgWithMultiAssets targetMsg =
                nonMappingTargetModel2MsgMap.computeIfAbsent(
                        targetModelId,
                        k -> {
                            LegacyMsgWithMultiAssets msg = new LegacyMsgWithMultiAssets();
                            msg.setOrgId(orgId);
                            msg.setModelId(targetModelId);
                            msg.setModelIdPath(targetAssetInfo.getModelPath());
                            msg.setPayload(new ArrayList<>());
                            return msg;
                        });

        // Create or update LegacyPayload
        LegacyPayload targetPayload =
                targetMsg.getPayload().stream()
                        .filter(p -> p.getAssetId().equals(targetAssetId))
                        .findFirst()
                        .orElseGet(
                                () -> {
                                    LegacyPayload newPayload = new LegacyPayload();
                                    newPayload.setAssetId(targetAssetId);
                                    newPayload.setTime(srcPayload.getTime());
                                    newPayload.setPoints(new HashMap<>());
                                    targetMsg.getPayload().add(newPayload);
                                    return newPayload;
                                });

        // Add calculated result
        targetPayload.getPoints().put(targetProp.getPrefName(), calculatedValue);

        logger.debug(
                "Non-direct mapping completed: {} = {}", targetProp.getPrefName(), calculatedValue);
    }

    /** Build calculation data for expression evaluation */
    private Map<String, Object> buildCalcData(
            LegacyPayload srcPayload,
            Map<String, LegacyPayload> queryResults,
            CalcPropertyMeta targetProp,
            String srcPayloadModelId,
            String targetModelId) {

        Map<String, Object> calcData = new HashMap<>();

        if (targetProp.getSrcPrefItems() == null) {
            logger.debug("No srcPrefItems found for target property: {}", targetProp.getPrefName());
            return calcData;
        }

        logger.debug(
                "Building calc data for target property: {}, srcPrefItems count: {}",
                targetProp.getPrefName(),
                targetProp.getSrcPrefItems().size());

        for (SrcPrefItem srcPrefItem : targetProp.getSrcPrefItems()) {
            String srcModelId = srcPrefItem.getModelId();
            String srcPrefName = srcPrefItem.getPrefName();
            PrefType srcPrefType = srcPrefItem.getPrefType();

            logger.debug(
                    "Processing srcPrefItem: modelId={}, prefName={}, prefType={}",
                    srcModelId,
                    srcPrefName,
                    srcPrefType);

            Object value = null;

            if (srcModelId.equals(srcPayloadModelId)
                    && srcPayload.getPoints().containsKey(srcPrefName)) {
                // Get from current source payload
                value = srcPayload.getPoints().get(srcPrefName);
                logger.debug("Getting from current source payload: {}={}", srcPrefName, value);
            } else {
                // Get from query results
                logger.debug(
                        "Getting from query results for modelId={}, prefName={}",
                        srcModelId,
                        srcPrefName);
                logger.debug("Available query results: {}", queryResults.keySet());

                for (Map.Entry<String, LegacyPayload> entry : queryResults.entrySet()) {
                    String assetId = entry.getKey();
                    LegacyPayload queryPayload = entry.getValue();
                    logger.debug(
                            "Checking query result for asset {}: points={}",
                            assetId,
                            queryPayload.getPoints().keySet());

                    if (queryPayload.getPoints().containsKey(srcPrefName)) {
                        value = queryPayload.getPoints().get(srcPrefName);
                        logger.debug("Found value in query results: {}={}", srcPrefName, value);
                        break;
                    }
                }

                if (value == null) {
                    logger.debug("Value not found in query results for: {}", srcPrefName);
                }
            }

            if (value != null) {
                String key;
                if (targetProp.isExprUseCurrentModel() && srcModelId.equals(targetModelId)) {
                    // Use "this." prefix for current model expressions when modelId matches
                    // targetModelId
                    key = CalcConstant.THIS + "." + srcPrefName;
                    logger.debug("Using 'this' prefix for target model: {}={}", key, value);
                } else {
                    // Use full modelId.prefName format
                    key = srcModelId + "." + srcPrefName;
                    logger.debug("Using full model prefix: {}={}", key, value);
                }
                calcData.put(key, value);
            } else {
                logger.debug("Skipping null value for: {}.{}", srcModelId, srcPrefName);
            }
        }

        logger.debug("Built calculation data: {}", calcData);
        return calcData;
    }
}
