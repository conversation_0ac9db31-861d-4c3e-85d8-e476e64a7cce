package com.envision.gravity.common.vo.search.asset;

import com.envision.gravity.common.vo.search.Scroll;
import com.envision.gravity.common.vo.search.SearchPagination;


import lombok.Data;

/** @Author: qi.jiang2 @Date: 2024/03/05 15:48 @Description: */
@Data
public class SearchAssetReq {

    private String expression;

    private SearchPagination pagination;

    private Scroll scroll;

    private GroupBy groupBy;

    private boolean needAssetRelationShip;

    private AttributeProjection attributeProjection;
}
