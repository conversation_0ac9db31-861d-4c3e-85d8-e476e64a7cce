package com.envision.gravity.flink.steaming.bo.event.entity.table;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblSubGraph implements CdcTableEntity {
    private static final long serialVersionUID = 8095630877757953376L;
    private String subGraphId;
    private String subGraphDisplayName;
    private String subGraphTags;
    private long rank;
    private boolean tree;
    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
