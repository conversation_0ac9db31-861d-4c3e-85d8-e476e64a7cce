package com.envision.gravity.flink.streaming.postgres.cdc.sink.nebula.pool;

import com.envision.gravity.common.exception.GravityRuntimeException;
import com.envision.gravity.common.exception.InvalidConfigException;

import java.io.Serializable;
import java.net.UnknownHostException;
import java.util.BitSet;
import java.util.concurrent.CopyOnWriteArrayList;


import com.vesoft.nebula.client.graph.SessionsManagerConfig;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.NotValidConnectionException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description
 */
@Slf4j
public class EnvisionSessionsManager implements Serializable {
    private static final long serialVersionUID = 1034916901682177730L;
    private final SessionsManagerConfig config;
    private NebulaPool pool = null;
    private final CopyOnWriteArrayList<EnvisionSessionWrapper> sessionList;
    private BitSet canUseBitSet;
    private Boolean isClose = false;
    private Boolean isInitialized = false;

    public EnvisionSessionsManager(SessionsManagerConfig config) {
        this.config = config;
        this.sessionList = new CopyOnWriteArrayList<>();
        checkConfig();
    }

    private void checkConfig() {
        if (config.getAddresses().isEmpty()) {
            throw new InvalidConfigException("Empty graph addresses");
        }

        if (config.getSpaceName().isEmpty()) {
            throw new InvalidConfigException("Empty space name");
        }
    }

    /**
     * getSessionWrapper: return a SessionWrapper from sessionManager, the SessionWrapper couldn't
     * use by multi-thread
     *
     * @return SessionWrapper
     * @throws RuntimeException the exception when get SessionWrapper
     */
    public synchronized EnvisionSessionWrapper getSessionWrapper()
            throws RuntimeException, ClientServerIncompatibleException {
        checkClose();
        if (!isInitialized) {
            init();
        }
        if (canUseBitSet.isEmpty()
                && sessionList.size() >= config.getPoolConfig().getMaxConnSize()) {
            throw new GravityRuntimeException(
                    "The SessionsManager does not have available sessions.");
        }
        if (!canUseBitSet.isEmpty()) {
            int index = canUseBitSet.nextSetBit(0);
            if (index >= 0) {
                if (canUseBitSet.get(index)) {
                    canUseBitSet.set(index, false);
                    return sessionList.get(index);
                }
            }
        }
        // create new session
        try {
            Session session =
                    pool.getSession(
                            config.getUserName(), config.getPassword(), config.getReconnect());
            if (config.getSpaceName() != null && !config.getSpaceName().isEmpty()) {
                ResultSet resultSet = session.execute("USE `" + config.getSpaceName() + "`");
                if (!resultSet.isSucceeded()) {
                    log.warn("Release session, cause: {}. ", resultSet.getErrorMessage());
                    session.release();
                    throw new GravityRuntimeException(
                            "Switch space `"
                                    + config.getSpaceName()
                                    + "` failed: "
                                    + resultSet.getErrorMessage());
                }
            }
            EnvisionSessionWrapper sessionWrapper = new EnvisionSessionWrapper(session);
            sessionList.add(sessionWrapper);
            return sessionWrapper;
        } catch (AuthFailedException | NotValidConnectionException | IOErrorException e) {
            throw new GravityRuntimeException("Get session failed: " + e.getMessage());
        }
    }

    /**
     * returnSessionWrapper: return the EnvisionSessionWrapper to the sessionManger, the old
     * EnvisionSessionWrapper couldn't use again.
     *
     * @param session The EnvisionSessionWrapper
     */
    public synchronized void returnSessionWrapper(EnvisionSessionWrapper session) {
        checkClose();
        if (session == null) {
            return;
        }
        int index = sessionList.indexOf(session);
        if (index >= 0) {
            Session ses = session.getSession();
            sessionList.set(index, new EnvisionSessionWrapper(ses));
            session.setNoAvailable();
            canUseBitSet.set(index, true);
        }
    }

    /** close: release all sessions and close the connection pool */
    public synchronized void close() {
        for (EnvisionSessionWrapper session : sessionList) {
            session.release();
        }
        pool.close();
        sessionList.clear();
        isClose = true;
    }

    private void init() throws RuntimeException {
        try {
            NebulaPool tem = new NebulaPool();
            if (!tem.init(config.getAddresses(), config.getPoolConfig())) {
                tem.close();
                throw new GravityRuntimeException("Init pool failed: services are broken.");
            }
            pool = tem;
            canUseBitSet = new BitSet(config.getPoolConfig().getMaxConnSize());
            canUseBitSet.set(0, config.getPoolConfig().getMaxConnSize(), false);
        } catch (UnknownHostException e) {
            throw new GravityRuntimeException("Init the pool failed: " + e.getMessage());
        }
        isInitialized = true;
    }

    private void checkClose() {
        if (isClose) {
            throw new GravityRuntimeException("The SessionsManager was closed.");
        }
    }
}
