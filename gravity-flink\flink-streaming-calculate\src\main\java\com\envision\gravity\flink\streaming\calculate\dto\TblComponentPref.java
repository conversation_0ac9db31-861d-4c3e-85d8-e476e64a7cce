package com.envision.gravity.flink.streaming.calculate.dto;

import com.envision.gravity.common.cdc.CdcTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TblComponentPref implements CdcTableEntity {
    private static final long serialVersionUID = 3866535384987387629L;

    private String compId;
    private String prefId;
    private String fieldId;

    private String metricId;
    private String rawFieldId;
    private int fieldIndex;
    private boolean horizontal;

    private long createdTime;
    private String createdUser;
    private long modifiedTime;
    private String modifiedUser;
    private long sysCreatedTime;
    private long sysModifiedTime;
}
