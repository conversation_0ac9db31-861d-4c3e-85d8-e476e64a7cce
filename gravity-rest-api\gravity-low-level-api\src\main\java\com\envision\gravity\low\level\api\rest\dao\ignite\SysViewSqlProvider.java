package com.envision.gravity.low.level.api.rest.dao.ignite;

import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/22
 * @description
 */
public class SysViewSqlProvider {
    public String selectObjColumns(List<String> columnNameList, String orgId) {
        String columnNames =
                columnNameList.stream()
                        .distinct()
                        .map(key -> "'" + key + "'")
                        .collect(Collectors.joining(", "));

        SQL sql = new SQL();
        sql.SELECT("COLUMN_NAME");
        sql.FROM("SYS.TABLE_COLUMNS");
        sql.WHERE(
                "SCHEMA_NAME = '"
                        + orgId.toUpperCase()
                        + "' AND TABLE_NAME = 'TBL_OBJ_PART' "
                        + "AND COLUMN_NAME IN ( "
                        + columnNames
                        + " )");
        return sql.toString();
    }
}
