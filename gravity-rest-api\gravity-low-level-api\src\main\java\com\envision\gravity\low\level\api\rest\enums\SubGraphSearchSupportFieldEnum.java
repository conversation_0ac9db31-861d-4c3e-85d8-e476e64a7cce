package com.envision.gravity.low.level.api.rest.enums;

/** @Author: qi.jiang2 @Date: 2024/04/17 11:10 @Description: */
public enum SubGraphSearchSupportFieldEnum {

    // SUBGRAPH_IDS
    SUBGRAPH_IDS("subGraphIds", "sub_graph_id", PgDataTypeEnum.VARCHAR.getName(), "g."),

    // GRAPH_DISPLAY_NAME
    GRAPH_DISPLAY_NAME(
            "graphDisplayName", "sub_graph_display_name", PgDataTypeEnum.JSONB.getName(), "g."),

    // TAGS
    TAGS("tags", "sub_graph_tags", PgDataTypeEnum.JSONB.getName(), "g."),

    // FROM_VID
    FROM_VID("fromVid", "from_vid", PgDataTypeEnum.VARCHAR.getName(), "e."),

    // TO_VID
    TO_VID("toVid", "to_vid", PgDataTypeEnum.VARCHAR.getName(), "e."),

    // EDGE_TYPE
    EDGE_TYPE("edgeType", "edge_type_id", PgDataTypeEnum.VARCHAR.getName(), "e."),

    // VID
    VID("vid", "", "", "e."),

    // ORDER
    ORDER("order", "(prop_value -> 'order')::int", "", ""),

    // MAX_STEP
    MAX_STEP("maxStep", "", "", "");

    private final String supportField;

    private final String sqlField;

    private final String dataType;

    private final String table;

    SubGraphSearchSupportFieldEnum(
            String supportField, String sqlField, String dataType, String table) {
        this.supportField = supportField;
        this.sqlField = sqlField;
        this.dataType = dataType;
        this.table = table;
    }

    public String getSupportField() {
        return supportField;
    }

    public String getSqlField() {
        return sqlField;
    }

    public String getDataType() {
        return dataType;
    }

    public String getTable() {
        return table;
    }
}
