package com.envision.gravity.flink.streaming.postgres.cdc.model.po;

import com.envision.gravity.flink.streaming.postgres.cdc.model.CDCTableEntity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TblBOInfo implements CDCTableEntity {
    private static final long serialVersionUID = -6544049111199797234L;
    private String assetId;
    private String assetDisplayName;
    private String systemId;
    private String createdUser;
    private String modifiedUser;
    private Long createdTime;
    private Long modifiedTime;
    private Long sysCreatedTime;
    private Long sysModifiedTime;
}
