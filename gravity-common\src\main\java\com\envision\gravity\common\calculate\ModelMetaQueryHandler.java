package com.envision.gravity.common.calculate;

import com.envision.gravity.common.enums.PrefType;
import com.envision.gravity.common.util.GTCommonUtils;
import com.envision.gravity.common.util.IgniteUtil;

import java.util.*;
import java.util.stream.Collectors;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ModelMetaQueryHandler {

    private static final Logger logger = LoggerFactory.getLogger(ModelMetaQueryHandler.class);

    private static volatile ModelMetaQueryHandler uniqueInstance;

    public static ModelMetaQueryHandler getInstance() {
        if (uniqueInstance == null) {
            synchronized (ModelMetaQueryHandler.class) {
                if (uniqueInstance == null) {
                    uniqueInstance = new ModelMetaQueryHandler();
                }
            }
        }
        return uniqueInstance;
    }

    private ModelMetaQueryHandler() {}

    public List<String> getSchemas() {
        try {
            String sql = "SELECT DISTINCT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA";
            List<List<?>> results = IgniteUtil.query("PUBLIC", sql);

            if (GTCommonUtils.emptyCollection(results)) {
                logger.error("Maybe new ignite cluster, no schema init ...");
                return Collections.emptyList();
            }

            // Filter prefix 'O'
            List<String> schemas =
                    results.stream()
                            .map(row -> (String) row.get(0))
                            .filter(schema -> schema.startsWith("O"))
                            .map(schema -> "o" + schema.substring(1)) // Replace first 'O' with 'o'
                            .collect(Collectors.toList());
            return schemas;
        } catch (Exception e) {
            logger.error("getSchemas() failed", e);
            throw e;
        }
    }

    public boolean isTableExists(String schema, String tableName) {
        String sql =
                String.format(
                        "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES "
                                + "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'",
                        schema.toUpperCase(), tableName);
        List<List<?>> results = IgniteUtil.query("PUBLIC", sql);

        return !GTCommonUtils.emptyCollection(results) && ((Long) results.get(0).get(0)) > 0;
    }

    public Map<String, AssetInfoWithModelPath> getAssetInfosWithModelPath(
            String orgId, Set<String> assetIds) {
        String sql = queryAssetInfoSql(orgId, assetIds);
        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            return Collections.emptyMap();
        }

        Map<String, AssetInfoWithModelPath> assetInfos = new HashMap<>(results.size());
        for (List<?> row : results) {
            String modelId = (String) row.get(0);
            String modelPath = (String) row.get(1);
            String assetId = (String) row.get(2);
            String systemId = (String) row.get(3);

            if (StringUtils.isEmpty(modelId)
                    || StringUtils.isEmpty(modelPath)
                    || StringUtils.isEmpty(systemId)) {
                logger.error("Invalid asset info: " + assetId);
                continue;
            }

            assetInfos.put(
                    assetId,
                    AssetInfoWithModelPath.builder()
                            .modelId(modelId)
                            .assetId(assetId)
                            .categoryId(CalcCommonUtils.extractCategoryId(modelPath))
                            .systemId(systemId)
                            .modelPath(modelPath)
                            .build());
        }

        return assetInfos;
    }

    public Map<String, AssetInfo> getAssetInfos(String orgId, Set<String> assetIds) {
        String sql = queryAssetInfoSql(orgId, assetIds);
        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            return Collections.emptyMap();
        }

        Map<String, AssetInfo> assetInfos = new HashMap<>(results.size());
        for (List<?> row : results) {
            String modelId = (String) row.get(0);
            String modelPath = (String) row.get(1);
            String assetId = (String) row.get(2);
            String systemId = (String) row.get(3);

            if (StringUtils.isEmpty(modelId)
                    || StringUtils.isEmpty(modelPath)
                    || StringUtils.isEmpty(systemId)) {
                logger.error("Invalid asset info: " + assetId);
                continue;
            }

            assetInfos.put(
                    assetId,
                    AssetInfo.builder()
                            .modelId(modelId)
                            .assetId(assetId)
                            .categoryId(CalcCommonUtils.extractCategoryId(modelPath))
                            .systemId(systemId)
                            .build());
        }

        return assetInfos;
    }

    public Map<PropertyId, PropertyInfo> getPropertyByCompIdAndPrefId(
            String orgId, Set<PropertyId> propertyIdSet) {
        String sql = queryPropertyInfoSql(orgId, propertyIdSet);
        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            return Collections.emptyMap();
        }

        Map<PropertyId, PropertyInfo> propertyInfos = new HashMap<>(results.size());
        for (List<?> row : results) {
            String modelId = (String) row.get(0);
            String compId = (String) row.get(1);
            String prefId = (String) row.get(2);
            String prefName = (String) row.get(3);
            PrefType prefType = PrefType.valueOf((String) row.get(4));
            propertyInfos.put(
                    PropertyId.builder().modelId(modelId).compId(compId).prefId(prefId).build(),
                    PropertyInfo.builder()
                            .modelId(modelId)
                            .compId(compId)
                            .prefId(prefId)
                            .prefName(prefName)
                            .prefType(prefType)
                            .build());
        }
        return propertyInfos;
    }

    // modelId => categoryId
    public Map<String, String> getModelCategory(String orgId, Set<String> modelIds) {
        String sql =
                String.format(
                        "SELECT MODEL_ID, MODEL_PATH FROM %s.TBL_BO_MODEL WHERE MODEL_ID IN (%s)",
                        orgId, GTCommonUtils.concatStr(modelIds));
        List<List<?>> queryResult = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(queryResult)) {
            return Collections.emptyMap();
        }

        Map<String, String> model2Category = new HashMap<>(queryResult.size());
        for (List<?> row : queryResult) {
            String modelId = String.valueOf(row.get(0));
            String modelPath = String.valueOf(row.get(1));
            String categoryId = CalcCommonUtils.extractCategoryId(modelPath);
            model2Category.put(modelId, categoryId);
        }
        return model2Category;
    }

    // <modelId, prefName> => info
    public Map<Pair<String, String>, PropertyFieldInfo> getPropertyFieldInfoByModelPrefName(
            String orgId, Set<PropertyInfo> modelPrefNames) {
        String sql = queryPropertyFieldInfoSql(orgId, modelPrefNames);
        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            return Collections.emptyMap();
        }

        Map<Pair<String, String>, PropertyFieldInfo> resultsMap = new HashMap<>(results.size());
        for (List<?> row : results) {
            PropertyFieldInfo propertyFieldInfo =
                    PropertyFieldInfo.builder()
                            .modelId((String) row.get(0))
                            .prefName((String) row.get(1))
                            .fieldId((String) row.get(2))
                            .fieldIndex(row.get(3) != null ? (Integer) row.get(3) : null)
                            .horizontal(row.get(4) != null ? (Boolean) row.get(4) : null)
                            .rawFieldId(row.get(5) != null ? (String) row.get(5) : null)
                            .prefType(
                                    row.get(6) != null
                                            ? PrefType.valueOf((String) row.get(6))
                                            : null)
                            .build();
            Pair<String, String> key =
                    Pair.of(propertyFieldInfo.getModelId(), propertyFieldInfo.getPrefName());
            resultsMap.put(key, propertyFieldInfo);
        }

        return resultsMap;
    }

    private String queryPropertyFieldInfoSql(String orgId, Set<PropertyInfo> modelPrefNames) {
        if (GTCommonUtils.emptyCollection(modelPrefNames)) {
            throw new IllegalArgumentException("Model pref names cannot be null or empty");
        }

        Set<String> modelIds = new HashSet<>();
        Set<String> prefNames = new HashSet<>();
        for (PropertyInfo item : modelPrefNames) {
            if (StringUtils.isNotEmpty(item.getModelId())) {
                modelIds.add(item.getModelId());
            }
            if (StringUtils.isNotEmpty(item.getPrefName())) {
                prefNames.add(item.getPrefName());
            }
        }

        String modelIdList =
                modelIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(", "));

        String prefNameList =
                prefNames.stream().map(name -> "'" + name + "'").collect(Collectors.joining(", "));

        String sql =
                String.format(
                        "SELECT\n"
                                + "    bo_model_comp.model_id         AS \"model_id\",\n"
                                + "    CASE WHEN tbl_component.anonymous THEN tbl_pref.pref_name ELSE CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name) END AS \"pref_name\",\n"
                                + "    tbl_component_pref.field_id    AS \"field_id\",\n"
                                + "    tbl_component_pref.field_index AS \"field_index\",\n"
                                + "    tbl_component_pref.horizontal  AS \"horizontal\",\n"
                                + "    tbl_component_pref.raw_field_id AS \"raw_field_id\",\n"
                                + "    tbl_pref.pref_type AS \"pref_type\"\n"
                                + "FROM\n"
                                + "    (\n"
                                + "        SELECT\n"
                                + "            model_id,\n"
                                + "            comp_id\n"
                                + "        FROM\n"
                                + "            %s.tbl_bo_model_comp\n"
                                + "        WHERE\n"
                                + "            model_id IN (%s)\n"
                                + "    ) AS bo_model_comp\n"
                                + "    LEFT JOIN %s.tbl_component ON tbl_component.comp_id = bo_model_comp.comp_id\n"
                                + "    LEFT JOIN %s.tbl_component_pref ON tbl_component_pref.comp_id = tbl_component.comp_id\n"
                                + "    LEFT JOIN %s.tbl_pref ON tbl_pref.pref_id = tbl_component_pref.pref_id\n"
                                + "WHERE\n"
                                + "    (tbl_pref.pref_name IN (%s) AND tbl_component.anonymous = true) OR\n"
                                + "        (\n"
                                + "            tbl_component.anonymous = false\n"
                                + "            AND CONCAT(tbl_component.comp_name, ':', tbl_pref.pref_name) IN (%s)\n"
                                + "        )",
                        orgId, modelIdList, orgId, orgId, orgId, prefNameList, prefNameList);
        return sql;
    }

    private static String queryAssetInfoSql(String orgId, Set<String> assetIds) {
        String sql =
                String.format(
                        "SELECT\n"
                                + "    m.model_id   AS \"model_id\",\n"
                                + "    m.model_path AS \"model_path\",\n"
                                + "    bo.asset_id  AS \"asset_id\",\n"
                                + "    bo.system_id AS \"system_id\"\n"
                                + "FROM\n"
                                + "%s.tbl_bo_part bo\n"
                                + "JOIN %s.tbl_bo_group_relation_part g ON g.asset_id = bo.asset_id\n"
                                + "JOIN %s.tbl_bo_model m ON m.group_id = g.group_id\n"
                                + "WHERE bo.asset_id IN (%s)",
                        orgId, orgId, orgId, GTCommonUtils.concatStr(assetIds));
        return sql;
    }

    private static String queryAssetInfoByModelIdsSqlWithPagination(
            String orgId, Set<String> modelIds, int pageSize, int offset) {
        String sql =
                String.format(
                        "SELECT\n"
                                + "    m.model_id   AS \"model_id\",\n"
                                + "    m.model_path AS \"model_path\",\n"
                                + "    bo.asset_id  AS \"asset_id\",\n"
                                + "    bo.system_id AS \"system_id\"\n"
                                + "FROM\n"
                                + "%s.tbl_bo_part bo\n"
                                + "JOIN %s.tbl_bo_group_relation_part g ON g.asset_id = bo.asset_id\n"
                                + "JOIN %s.tbl_bo_model m ON m.group_id = g.group_id\n"
                                + "WHERE m.model_id IN (%s)\n"
                                + "ORDER BY bo.asset_id\n"
                                + "LIMIT %d OFFSET %d",
                        orgId, orgId, orgId, GTCommonUtils.concatStr(modelIds), pageSize, offset);
        return sql;
    }

    /**
     * Concatenates SQL query with dynamic conditions based on TargetPropMeta
     *
     * @param orgId
     * @param propertyIdSet
     * @return The complete SQL query string
     */
    private static String queryPropertyInfoSql(String orgId, Set<PropertyId> propertyIdSet) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n")
                .append("    mc.model_id AS \"model_id\",\n")
                .append("    mc.comp_id  AS \"comp_id\",\n")
                .append("    p.pref_id   AS \"pref_id\",\n")
                .append(
                        "    CASE WHEN c.anonymous THEN p.pref_name ELSE CONCAT(c.comp_name, ':', p.pref_name) END AS \"pref_name\",\n")
                .append("    p.pref_type AS \"pref_type\"\n")
                .append("FROM\n")
                .append("    ")
                .append(orgId)
                .append(".TBL_BO_MODEL_COMP mc\n")
                .append("  JOIN ")
                .append(orgId)
                .append(".TBL_COMPONENT c ON c.comp_id = mc.comp_id\n")
                .append("    JOIN ")
                .append(orgId)
                .append(".TBL_COMPONENT_PREF cp ON cp.comp_id = mc.comp_id\n")
                .append("    JOIN ")
                .append(orgId)
                .append(".TBL_PREF p ON p.pref_id = cp.pref_id\n")
                .append("WHERE ");

        // Build WHERE clause conditions
        StringJoiner conditions = new StringJoiner(" OR ");
        for (PropertyId pk : propertyIdSet) {
            String compId = pk.getCompId();
            String prefId = pk.getPrefId();
            conditions.add(
                    String.format("(cp.comp_id = '%s' AND cp.pref_id = '%s')", compId, prefId));
        }

        sql.append(conditions);
        return sql.toString();
    }

    /**
     * Query asset information by model IDs with pagination support
     *
     * @param orgId organization ID
     * @param modelIds set of model IDs to query
     * @param pageSize page size for pagination
     * @return Map of model ID to list of asset information
     */
    public Map<String, List<AssetInfo>> getAssetInfoByModelIdsWithPagination(
            String orgId, Set<String> modelIds, int pageSize) {
        if (GTCommonUtils.emptyCollection(modelIds)) {
            return Collections.emptyMap();
        }

        Map<String, List<AssetInfo>> assetInfosByModel = new HashMap<>();
        int offset = 0;
        boolean hasMore = true;
        int totalProcessed = 0;

        while (hasMore) {
            String sql =
                    queryAssetInfoByModelIdsSqlWithPagination(orgId, modelIds, pageSize, offset);
            List<List<?>> results = IgniteUtil.query(orgId, sql);

            if (GTCommonUtils.emptyCollection(results)) {
                hasMore = false;
                logger.info(
                        "Finished loading assets for orgId {}, modelIds {}, total assets processed: {}",
                        orgId,
                        modelIds,
                        totalProcessed);
                continue;
            }

            for (List<?> row : results) {
                String modelId = (String) row.get(0);
                String modelPath = (String) row.get(1);
                String assetId = (String) row.get(2);
                String systemId = (String) row.get(3);

                if (StringUtils.isEmpty(modelId)
                        || StringUtils.isEmpty(modelPath)
                        || StringUtils.isEmpty(systemId)) {
                    logger.error("Invalid asset info: " + assetId);
                    continue;
                }

                AssetInfo assetInfo =
                        AssetInfo.builder()
                                .modelId(modelId)
                                .assetId(assetId)
                                .categoryId(CalcCommonUtils.extractCategoryId(modelPath))
                                .systemId(systemId)
                                .build();

                // 按 modelId 分组
                assetInfosByModel.computeIfAbsent(modelId, k -> new ArrayList<>()).add(assetInfo);
                totalProcessed++;
            }

            offset += pageSize;

            // If the number of results is less than the page size, we've reached the last page
            if (results.size() < pageSize) {
                hasMore = false;
                logger.info(
                        "Finished loading assets for orgId {}, modelIds {}, total assets processed: {}",
                        orgId,
                        modelIds,
                        totalProcessed);
            }
        }

        return assetInfosByModel;
    }

    /**
     * Query asset IDs by system IDs from TBL_BO_PART table
     *
     * @param orgId organization ID
     * @param systemIds set of system IDs to query
     * @return Map of system ID to set of asset IDs
     */
    public Map<String, Set<String>> getAppAssetBySystemIds(String orgId, Set<String> systemIds) {
        if (GTCommonUtils.emptyCollection(systemIds)) {
            return Collections.emptyMap();
        }

        String sql =
                String.format(
                        "SELECT SYSTEM_ID, ASSET_ID FROM %s.TBL_BO_PART WHERE SYSTEM_ID IN (%s)",
                        orgId, GTCommonUtils.concatStr(systemIds));

        List<List<?>> results = IgniteUtil.query(orgId, sql);
        if (GTCommonUtils.emptyCollection(results)) {
            return Collections.emptyMap();
        }

        Map<String, Set<String>> systemIdToAssetIds = new HashMap<>();
        for (List<?> row : results) {
            String systemId = (String) row.get(0);
            String assetId = (String) row.get(1);

            if (StringUtils.isEmpty(systemId) || StringUtils.isEmpty(assetId)) {
                logger.warn(
                        "Invalid system_id or asset_id in query result: system_id={}, asset_id={}",
                        systemId,
                        assetId);
                continue;
            }

            if (systemId.equals(assetId)) {
                logger.warn(
                        "Filter onboarding asset_id record: system_id={}, asset_id={}",
                        systemId,
                        assetId);
                continue;
            }

            systemIdToAssetIds.computeIfAbsent(systemId, k -> new HashSet<>()).add(assetId);
        }

        return systemIdToAssetIds;
    }
}
