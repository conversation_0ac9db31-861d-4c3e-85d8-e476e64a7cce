package com.envision.gravity.common.vo.task;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import java.util.List;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/14
 * @description
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchRefreshObjDetailReq {
    @NotEmpty(message = "taskIds must not be empty")
    private List<String> taskIds;

    @NotBlank(message = "operationName must not be null")
    private String operationName;

    @NotEmpty(message = "requestParams must not be empty")
    private List<Map<String, Object>> requestParams;

    @NotBlank(message = "databaseName must not be blank")
    private String schemeName;

    private String tableName;
}
