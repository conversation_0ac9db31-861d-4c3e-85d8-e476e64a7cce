package com.envision.gravity.low.level.api.sql.metric;

import com.envision.gravity.common.exception.InternalException;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.envision.gravity.low.level.api.sql.common.Constants.QUERY_LOST_PARTITIONS;

import org.apache.ignite.IgniteLogger;
import org.apache.ignite.cache.query.FieldsQueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.internal.IgniteEx;
import org.apache.ignite.internal.processors.metric.MetricRegistry;

/**
 * <AUTHOR>
 * @date 2024/12/17
 * @description
 */
public class MetricManager {
    /** Cache metrics prefix. */
    public static final String CACHE_METRICS = "gravity.cache";

    /** PartitionStates metrics prefix. */
    public static final String PARTITION_STATES_METRICS = "gravity.partitionStates";

    public static void registerCacheMetrics(IgniteEx igniteEx, IgniteLogger log) {
        try {
            MetricRegistry reg = igniteEx.context().metric().registry(CACHE_METRICS);

            int totalCacheCount = igniteEx.cacheNames().size();
            reg.register("TotalCacheCount", () -> totalCacheCount, "Total cache count.");
            log.info(String.format(">>> Register metric, TotalCacheCount: %d.", totalCacheCount));
        } catch (Exception e) {
            log.error(">>>>>> Register cache metrics error.", e);
            throw new InternalException(">>>>>> Register cache metrics error.", e);
        }
    }

    public static void registerPartitionStatesMetrics(IgniteEx igniteEx, IgniteLogger log) {
        try {
            MetricRegistry reg = igniteEx.context().metric().registry(PARTITION_STATES_METRICS);

            try (FieldsQueryCursor<List<?>> cursor =
                    igniteEx.context()
                            .query()
                            .querySqlFields(new SqlFieldsQuery(QUERY_LOST_PARTITIONS), false)) {
                List<List<?>> queryResult = cursor.getAll();

                if (!queryResult.isEmpty()) {
                    Set<String> cacheNames =
                            queryResult.stream()
                                    .filter(row -> !row.isEmpty())
                                    .map(row -> String.valueOf(row.get(0)))
                                    .collect(Collectors.toSet());
                    if (!cacheNames.isEmpty()) {
                        // try rest lost partitions first
                        try {
                            igniteEx.resetLostPartitions(cacheNames);
                        } catch (Exception e) {
                            log.error(">>>>>> Scheduled reset lost partitions error.", e);
                            cacheNames.forEach(
                                    cacheName -> {
                                        reg.register(
                                                "LostPartitionsCacheName_" + cacheName,
                                                () -> cacheName,
                                                String.class,
                                                "Lost partitions cache Name.");
                                        log.info(
                                                String.format(
                                                        ">>> Register metric, LostPartitionsCacheName_%s: %s.",
                                                        cacheName, cacheName));
                                    });
                        }
                    }
                }
            }

            try (FieldsQueryCursor<List<?>> cursor =
                    igniteEx.context()
                            .query()
                            .querySqlFields(new SqlFieldsQuery(QUERY_LOST_PARTITIONS), false)) {
                List<List<?>> queryResult = cursor.getAll();
                int totalLostPartitionsCacheCount = queryResult.size();
                reg.register(
                        "TotalLostPartitionsCacheCount",
                        () -> totalLostPartitionsCacheCount,
                        "Total lost partitions cache count.");
                log.info(
                        String.format(
                                ">>> Register metric, TotalLostPartitionsCacheCount: %d.",
                                totalLostPartitionsCacheCount));
            }

        } catch (Exception e) {
            log.error(">>>>>> Register partition states metrics error.", e);
            throw new InternalException(">>>>>> Register partition states metrics error.", e);
        }
    }
}
