package com.envision.gravity.common.vo.field;

import java.sql.Timestamp;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/9
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FieldResp {
    private String fieldId;
    private String categoryId;
    private String fieldDisplayName;
    private String fieldType;
    private String dataType;
    private String unit;
    private boolean calcField;
    private String calcFieldExp;
    private String createdUser;
    private String modifiedUser;
    private Timestamp createdTime;
    private Timestamp modifiedTime;
}
