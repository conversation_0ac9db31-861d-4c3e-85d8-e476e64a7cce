package com.envision.gravity.common.vo.obj;

import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpsertObjAttrValueReq {

    private String systemId;
    private Map<String, Object> attrMap;
    private Map<String, String> uniqueMap;
}
