package com.envision.gravity.common.vo.search.asset;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** @Author: qi.jiang2 @Date: 2024/04/24 11:16 @Description: */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetRelationship {

    private String fromAssetId;

    private String toAssetId;

    private String edgeType;

    private String subGraphId;

    private int order;
}
