package com.envision.gravity.flink.steaming.bo.event.function;

import com.envision.gravity.common.cdc.ConvertedCdcRecord;
import com.envision.gravity.flink.steaming.bo.event.entity.EventTableName;
import com.envision.gravity.flink.steaming.bo.event.entity.table.ObjectDetailOrigin;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblEdge;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblStartVid;
import com.envision.gravity.flink.steaming.bo.event.entity.table.TblSubGraph;

import java.util.Optional;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @date 2025/4/11
 * @description
 */
@Slf4j
public class CdcRecordRouter extends ProcessFunction<String, ConvertedCdcRecord> {
    private static final long serialVersionUID = 4864582650231222562L;
    private static final ObjectMapper OBJECT_MAPPER =
            new ObjectMapper()
                    .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processElement(
            String value,
            ProcessFunction<String, ConvertedCdcRecord>.Context ctx,
            Collector<ConvertedCdcRecord> out) {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(value);
            JsonNode sourceNode = jsonNode.get("source");

            String table = sourceNode.get("table").asText();
            Optional<EventTableName> tableInfo = EventTableName.find(table);
            if (tableInfo.isPresent()) {
                ConvertedCdcRecord convertedCdcRecord =
                        ConvertedCdcRecord.builder()
                                .schema(sourceNode.get("schema").asText())
                                .db(sourceNode.get("db").asText())
                                .table(tableInfo.get().getName())
                                .op(jsonNode.get("op").asText())
                                .tsMs(jsonNode.get("ts_ms").asLong())
                                .build();

                switch (tableInfo.get()) {
                    case OBJECT_DETAIL_ORIGIN:
                        convertedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), ObjectDetailOrigin.class));
                        convertedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), ObjectDetailOrigin.class));
                        break;
                    case TBL_START_VID:
                        convertedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblStartVid.class));
                        convertedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblStartVid.class));
                        break;
                    case TBL_EDGE:
                        convertedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(jsonNode.get("before"), TblEdge.class));
                        convertedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(jsonNode.get("after"), TblEdge.class));
                        break;
                    case TBL_SUB_GRAPH:
                        convertedCdcRecord.setBefore(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("before"), TblSubGraph.class));
                        convertedCdcRecord.setAfter(
                                OBJECT_MAPPER.treeToValue(
                                        jsonNode.get("after"), TblSubGraph.class));
                        break;
                    default:
                        return;
                }

                out.collect(convertedCdcRecord);
            } else {
                log.warn("Unrecognized table: {}", table);
            }

        } catch (Exception e) {
            log.error("Failed to parse cdc record, value: {}", value, e);
        }
    }
}
