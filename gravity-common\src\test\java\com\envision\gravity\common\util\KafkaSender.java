package com.envision.gravity.common.util;

import java.util.Properties;


import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;

/** <AUTHOR> 2024/11/29 */
public class KafkaSender {

    public static void main(String[] args) {
        Properties producerProperties = new Properties();
        // producerProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "10.65.104.57:9092");
        producerProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, ":9092");
        producerProperties.put(
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.ByteArraySerializer");
        producerProperties.put(
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.ByteArraySerializer");

        KafkaProducer<byte[], byte[]> producer = new KafkaProducer<>(producerProperties);
        // String topic ="MEASURE_POINT_ORIGIN_o16227961710541858";
        String topic = "MEASURE_POINT_ORIGIN_o16227961710541858";
        String msg =
                "{\"payload\":[{\"measurepoints\":{\"chuanjian-point01\":1129.1444},\"assetId\":\"kzg001_device_test001_8\",\"time\":1732705201363}],\"modelId\":\"kzg001\",\"modelIdPath\":\"/kzg001\",\"orgId\":\"o16227961710541858\"}";

        System.out.println("start send");
        while (true) {
            long time = System.currentTimeMillis();
            String msg111 = String.format(msg, time);
            // System.out.println(msg111);
            for (int i = 0; i < 16; i++) {
                producer.send(
                        new ProducerRecord<>(
                                topic,
                                i,
                                System.currentTimeMillis(),
                                "".getBytes(),
                                msg111.getBytes()));
            }

            System.out.println("send suc");
        }
    }
}
