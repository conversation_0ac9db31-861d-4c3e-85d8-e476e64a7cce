package com.envision.gravity.flink.streaming.calculate.recalc;

import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTask;
import com.envision.gravity.flink.streaming.calculate.dto.job.CalcJobTaskStatusEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;


import org.apache.flink.api.connector.source.ReaderOutput;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.core.io.InputStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 计算作业任务源读取器
 *
 * <AUTHOR>
 */
public class CalcJobTaskSourceReader implements SourceReader<CalcJobTask, CalcJobTaskSplit> {

    private static final Logger logger = LoggerFactory.getLogger(CalcJobTaskSourceReader.class);

    private final SourceReaderContext context;
    private final ConcurrentLinkedQueue<CalcJobTaskSplit> assignedSplits =
            new ConcurrentLinkedQueue<>();
    private volatile boolean noMoreSplits = false;

    public CalcJobTaskSourceReader(SourceReaderContext context) {
        this.context = context;
    }

    @Override
    public void start() {
        logger.info("CalcJobTaskSourceReader started");
    }

    @Override
    public InputStatus pollNext(ReaderOutput<CalcJobTask> output) throws Exception {
        CalcJobTaskSplit split = assignedSplits.poll();
        if (split != null) {
            CalcJobTask task = split.getTask();
            task.setStatus(CalcJobTaskStatusEnum.RUNNING);

            logger.debug("Emitting task: {}", task.getTaskId());
            output.collect(task);

            return InputStatus.MORE_AVAILABLE;
        }

        if (noMoreSplits) {
            logger.info("No more splits available, finishing");
            return InputStatus.END_OF_INPUT;
        }

        return InputStatus.NOTHING_AVAILABLE;
    }

    @Override
    public List<CalcJobTaskSplit> snapshotState(long checkpointId) {
        logger.debug(
                "Snapshotting state for checkpoint: {}, pending splits: {}",
                checkpointId,
                assignedSplits.size());
        return new ArrayList<>(assignedSplits);
    }

    @Override
    public CompletableFuture<Void> isAvailable() {
        if (!assignedSplits.isEmpty() || noMoreSplits) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public void addSplits(List<CalcJobTaskSplit> splits) {
        logger.debug("Adding {} splits to reader", splits.size());
        assignedSplits.addAll(splits);
    }

    @Override
    public void notifyNoMoreSplits() {
        logger.info("Notified no more splits");
        noMoreSplits = true;
    }

    @Override
    public void close() throws Exception {
        logger.info("CalcJobTaskSourceReader closed");
    }

    /** 通知任务完成 */
    public void notifyTaskCompleted(String taskId) {
        // TODO: 实现任务完成通知机制
        logger.debug("Task completed: {}", taskId);
    }

    /** 通知任务失败 */
    public void notifyTaskFailed(String taskId, String errorMessage) {
        // TODO: 实现任务失败通知机制
        logger.debug("Task failed: {}, error: {}", taskId, errorMessage);
    }
}
