<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.envision.gravity</groupId>
        <artifactId>gravity-all</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>gravity-flink</artifactId>
    <packaging>pom</packaging>
    <name>gravity-flink</name>
    <description>gravity-flink</description>

    <modules>
        <module>flink-streaming-postgres-cdc</module>
        <module>flink-streaming-bo-view-operator</module>
        <module>flink-streaming-virtual-attr-sync</module>
        <module>flink-streaming-bo-sync</module>
        <module>flink-streaming-bo-event</module>
        <module>flink-streaming-obj-warm</module>
        <module>flink-streaming-calculate</module>
        <module>flink-common</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.envision.gravity</groupId>
                <artifactId>flink-common</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                    </archive>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
