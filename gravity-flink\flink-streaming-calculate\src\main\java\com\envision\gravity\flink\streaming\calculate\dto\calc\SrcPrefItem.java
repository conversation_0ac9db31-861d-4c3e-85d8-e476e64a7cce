package com.envision.gravity.flink.streaming.calculate.dto.calc;

import java.io.Serializable;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 源属性项
 *
 * <p>用于： 1. CalcPropertyMeta 的 srcPrefItems 字段 2. 表示计算表达式中的依赖属性
 *
 * <AUTHOR> Agent
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SrcPrefItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 模型ID */
    private String modelId;

    /** 属性名称 */
    private String prefName;

    /** 属性类型 */
    private PrefType prefType;

    /** 数据类型 */
    private String dataType;

    /** 是否为当前模型的属性 */
    private boolean isCurrentModel;

    /** 检查是否为有效的源属性项 */
    public boolean isValid() {
        return modelId != null
                && !modelId.isEmpty()
                && prefName != null
                && !prefName.isEmpty()
                && prefType != null;
    }

    /** 获取属性的完整标识 */
    public String getFullPrefId() {
        return modelId + "." + prefName;
    }

    @Override
    public String toString() {
        return String.format(
                "SrcPrefItem{modelId='%s', prefName='%s', prefType=%s, " + "isCurrentModel=%s}",
                modelId, prefName, prefType, isCurrentModel);
    }
}
