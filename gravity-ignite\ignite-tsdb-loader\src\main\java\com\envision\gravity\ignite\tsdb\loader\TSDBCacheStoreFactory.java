package com.envision.gravity.ignite.tsdb.loader;

import javax.cache.configuration.Factory;


import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.cache.store.jdbc.JdbcType;

/** <AUTHOR> 2024/3/19 */
@Slf4j
@Getter
@Setter
public class TSDBCacheStoreFactory implements Factory<TSDBCacheStoreAdapter> {

    private static final long serialVersionUID = -8135351572069533661L;

    private JdbcType[] types;

    private String connectionURL;

    private String userName;

    private String password;

    private String clusterMetaURL;

    private String clusterMetaUserName;

    private String clusterMetaPassword;

    private String clusterReplicaFactor;

    private String clusterShardingRule;

    private String cacheSize = "100000";

    private String cacheExpireSeconds = "600";

    // 1000 rows
    private String putCacheSplitSize = "1000";

    private String putCacheParallelism = "2";

    public TSDBCacheStoreFactory() {}

    @Override
    public TSDBCacheStoreAdapter create() {
        int cacheSize = Integer.parseInt(this.cacheSize);
        int cacheExpireSeconds = Integer.parseInt(this.cacheExpireSeconds);

        TSDBCacheStoreAdapter adapter = new TSDBCacheStoreAdapter(cacheSize, cacheExpireSeconds);
        adapter.setTypes(this.types);
        adapter.setConnectionURL(this.connectionURL);
        adapter.setUserName(this.userName);
        adapter.setPassword(this.password);
        adapter.setClusterMetaURL(this.clusterMetaURL);
        adapter.setClusterMetaUserName(this.clusterMetaUserName);
        adapter.setClusterMetaPassword(this.clusterMetaPassword);
        adapter.setClusterReplicaFactor(this.clusterReplicaFactor);
        adapter.setClusterShardingRule(this.clusterShardingRule);
        adapter.setPutCacheSplitSize(
                Integer.parseInt(this.putCacheSplitSize != null ? this.putCacheSplitSize : "1000"));
        adapter.setPutCacheParallelism(
                Integer.parseInt(
                        this.putCacheParallelism != null ? this.putCacheParallelism : "4"));
        return adapter;
    }
}
