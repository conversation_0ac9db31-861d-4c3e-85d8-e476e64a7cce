package com.envision.gravity.low.level.api.sql.table.persistent;

import com.envision.gravity.low.level.api.sql.table.PersistentTableInfo;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
public class TblObjPointPersistentTableInfo implements PersistentTableInfo {
    public static final String CREATE_TABLE_SQL_PATTERN =
            "CREATE TABLE IF NOT EXISTS %s.TBL_OBJ_POINT_PART\n"
                    + "(\n"
                    + "    SERIES_ID         VARCHAR NOT NULL,\n"
                    + "    SYSTEM_ID         VARCHAR NOT NULL,\n"
                    + "    FIELD_INDEX       INTEGER NOT NULL,\n"
                    + "    VALUE_BOOL        BOOLEAN,\n"
                    + "    VALUE_STRING      VARCHAR,\n"
                    + "    VALUE_LONG        BIGINT,\n"
                    + "    VALUE_DOUBLE      DOUBLE,\n"
                    + "    QUALITY           BIGINT,\n"
                    + "    TIME              TIMESTAMP,\n"
                    + "    LAST_CHANGED_TIME TIMESTAMP,\n"
                    + "    LAST_UPDATE_TIME  TIMESTAMP,\n"
                    + "    CREATED_USER      VARCHAR,\n"
                    + "    MODIFIED_USER     VARCHAR,\n"
                    + "    CREATED_TIME      TIMESTAMP,\n"
                    + "    MODIFIED_TIME     TIMESTAMP,\n"
                    + "    PRIMARY KEY (SERIES_ID)\n"
                    + ") WITH \"template=%s,atomicity=%s,AFFINITY_KEY=SERIES_ID,BACKUPS=1,KEY_TYPE=%s,VALUE_TYPE=%s,CACHE_NAME=%s\";";

    public static final List<String> CREATE_INDEX_SQL_PATTERN_LIST;

    static {
        CREATE_INDEX_SQL_PATTERN_LIST =
                Arrays.asList(
                        "CREATE INDEX IF NOT EXISTS IDX_SYSTEM_ID_FIELD_INDEX ON %s.TBL_OBJ_POINT_PART (SYSTEM_ID, FIELD_INDEX) INLINE_SIZE 65;",
                        "CREATE INDEX IF NOT EXISTS IDX_SYSTEM_ID ON %s.TBL_OBJ_POINT_PART (SYSTEM_ID) INLINE_SIZE 35;",
                        "CREATE INDEX IF NOT EXISTS IDX_FIELD_INDEX ON %s.TBL_OBJ_POINT_PART (FIELD_INDEX) INLINE_SIZE 20;");
    }

    @Override
    public String getCreateTableSQLPattern() {
        return CREATE_TABLE_SQL_PATTERN;
    }

    @Override
    public List<String> getCreateIndexSQLPatternList() {
        return CREATE_INDEX_SQL_PATTERN_LIST;
    }
}
