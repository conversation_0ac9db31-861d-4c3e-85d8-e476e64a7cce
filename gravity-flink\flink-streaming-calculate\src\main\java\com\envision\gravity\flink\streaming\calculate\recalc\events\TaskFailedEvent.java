package com.envision.gravity.flink.streaming.calculate.recalc.events;


import org.apache.flink.api.connector.source.SourceEvent;

/**
 * 任务失败事件
 *
 * <AUTHOR>
 */
public class TaskFailedEvent implements SourceEvent {

    private static final long serialVersionUID = 1L;

    private final String taskId;
    private final String errorMessage;
    private final long failedTime;

    public TaskFailedEvent(String taskId, String errorMessage) {
        this.taskId = taskId;
        this.errorMessage = errorMessage;
        this.failedTime = System.currentTimeMillis();
    }

    public String getTaskId() {
        return taskId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public long getFailedTime() {
        return failedTime;
    }

    @Override
    public String toString() {
        return "TaskFailedEvent{"
                + "taskId='"
                + taskId
                + '\''
                + ", errorMessage='"
                + errorMessage
                + '\''
                + ", failedTime="
                + failedTime
                + '}';
    }
}
