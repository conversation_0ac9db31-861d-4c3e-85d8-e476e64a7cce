package com.envision.gravity.low.level.api.rest.config;

import com.envision.gravity.low.level.api.rest.util.LionUtil;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/** @Author: qi.jiang2 @Date: 2024/04/01 18:20 @Description: */
@Component
public class LocaleCfg {

    @Bean("localeList")
    public List<String> localeList() {
        String lowLevelRestLocale =
                LionUtil.getStringValue(GTRestLionConfig.LOCALE, GTRestLionConfig.LOCALE_DEFAULT);
        String[] localeArray = lowLevelRestLocale.split(",");
        return Arrays.asList(localeArray);
    }
}
